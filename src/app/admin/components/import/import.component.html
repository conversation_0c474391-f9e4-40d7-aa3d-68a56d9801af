<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>



<div class="max-w-4xl mx-auto p-6 bg-white shadow-lg rounded-lg">
  <h2 class="text-2xl font-bold mb-6 text-gray-800">Importer des données (CSV)</h2>

  <!-- Sélection du type d'import -->
  <div class="mb-6">
    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
      Quel type de données souhaitez-vous importer ?
    </label>
    <p class="text-xs text-gray-500 mb-3">
      Sélectionnez le type de fichier que vous voulez traiter. Chaque type correspond à un format de données spécifique.
    </p>
    <select
      id="category"
      [(ngModel)]="selectedCategory"
      (change)="onCategoryChange()"
      class="mt-1 block w-full p-3 bg-white border border-gray-300 rounded-md shadow-sm focus:ring-cyan-500 focus:border-cyan-500 text-gray-900"
    >
      <option value="" disabled>-- Choisissez le type de données à importer --</option>
      <option *ngFor="let category of categories" [value]="category.value">
        {{ category.label }}
      </option>
    </select>
  </div>

  <!-- Fichier modèle recommandé (affiché seulement si une catégorie est sélectionnée) -->
  <div *ngIf="selectedCategory && currentFileSample" class="mt-6 bg-cyan-50 border border-cyan-200 p-4 mb-4 rounded-md shadow-md">
    <h3 class="text-lg font-bold mb-4 text-cyan-800 flex items-center">
      <i class="pi pi-download mr-2 text-cyan-600"></i>
      Modèle de fichier pour {{ currentCategoryLabel }}
    </h3>
    <div class="flex items-center gap-6">
      <!-- Logo Excel -->
      <img src="assets/icon/excel-logo.svg" alt="Excel Logo" class="w-12 h-12" />
      <div class="flex-1">
        <p class="text-cyan-700 mb-3">
          <strong>Étape 1 :</strong> Téléchargez d'abord le fichier modèle <span class="font-semibold bg-cyan-100 px-2 py-1 rounded">{{ currentFileName }}</span> pour voir le format attendu.
        </p>
        <p class="text-cyan-600 text-sm mb-3">
          Ce fichier contient les colonnes exactes et le format requis pour l'import de type "{{ currentCategoryLabel }}".
        </p>
        <div class="flex gap-4 items-center">
          <!-- Lien Télécharger -->
          <a [href]="currentFileSample" download class="bg-cyan-600 text-white px-4 py-2 rounded-md hover:bg-cyan-700 flex items-center transition-colors">
            <i class="pi pi-download mr-2"></i>Télécharger le modèle
          </a>
          <!-- Lien Tutoriel -->
          <a  (click)="openTutorialDrawer()"
              class="text-cyan-600 cursor-pointer underline hover:text-cyan-700 flex items-center">
            <i class="pi pi-book mr-2"></i>Guide d'utilisation
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'instruction si aucune catégorie sélectionnée -->
  <div *ngIf="!selectedCategory" class="mb-6 p-6 bg-gray-50 border border-gray-200 rounded-lg text-center">
    <div class="text-gray-500 mb-4">
      <i class="pi pi-info-circle text-3xl"></i>
    </div>
    <h3 class="text-lg font-semibold text-gray-700 mb-2">Prêt à importer les données ?</h3>
    <p class="text-gray-600 mb-2">
      Commencez par sélectionner le type de données que vous souhaitez traiter dans la liste déroulante ci-dessus.
    </p>
    <p class="text-gray-500 text-sm">
      Chaque type correspond à un format de fichier spécifique avec son propre modèle à télécharger.
    </p>
  </div>

  <!-- Composant d'importation (affiché seulement si une catégorie est sélectionnée) -->
  <div *ngIf="selectedCategory" class="bg-gray-100 p-4 rounded-lg shadow-md">
    <p-toast></p-toast>
    <p-fileUpload
      #fileUpload
      name="files[]"
      [url]="currentUrl"
      [multiple]="false"
      accept=".csv"
      maxFileSize="5000000"
      (onUpload)="onTemplatedUpload()"
      (onSelect)="onSelectedFiles($event)"
    >
      <ng-template pTemplate="header" let-files let-chooseCallback="chooseCallback" let-clearCallback="clearCallback" let-uploadCallback="uploadCallback">
        <div class="flex justify-between items-center">
          <div class="flex gap-2">
            <p-button (onClick)="choose($event, chooseCallback)" label="Choisir" icon="pi pi-upload" severity="info"></p-button>
            <p-button
              (onClick)="sendDataToServer()"
              label="Envoyer"
              icon="pi pi-cloud-upload"
              severity="success"
              [disabled]="!convertedJson || convertedJson.length === 0 || isImporting"
              [loading]="isImporting">
            </p-button>
            <p-button (onClick)="resetToDefault()" label="Réinitialiser" icon="pi pi-times" severity="danger"></p-button>
          </div>
          <p-progressBar
            [value]="totalSizePercent"
            [showValue]="true"
            class="w-full md:w-64 h-10 mt-2"
            styleClass="rounded-full bg-gray-200"
          ></p-progressBar>
        </div>
      </ng-template>
    </p-fileUpload>
  </div>

  <!-- Résultat JSON -->

  <div *ngIf="convertedJson.length > 0" class="mt-8">
    <h3 class="text-lg font-bold mb-4 text-gray-800">Résultat JSON :</h3>
    <div *ngIf="postRequest.url" class="mt-8 flex items-center gap-4 p-4 rounded-md bg-gray-100 shadow-md">
      <span
        class="px-3 py-1 text-sm font-semibold text-white rounded-md"
        [ngClass]="{ 'bg-green-500': postRequest.method === 'POST', 'bg-blue-500': postRequest.method === 'GET' }"
      >
        {{ postRequest.method }}
      </span>
      <div class="text-gray-800 text-sm font-medium">
        {{ postRequest.url }}
        <span class="text-gray-500">- Enverra  ces données au serveur</span>
      </div>
    </div>

    <div class="bg-gray-900 text-white p-4 rounded-md">
      <app-json-editor [jsonData]="convertedJson"></app-json-editor>
    </div>

    <!-- Boutons d'action -->
    <div class="mt-4 flex justify-between items-center" *ngIf="convertedJson && convertedJson.length > 0">
      <!-- Bouton de réinitialisation -->
      <button
        (click)="resetToDefault()"
        [disabled]="isImporting"
        class="px-4 py-2 bg-gray-500 text-white font-medium rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition-colors duration-200 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <i class="pi pi-refresh"></i>
        Nouveau fichier
      </button>

      <!-- Bouton pour envoyer les données avec loader -->
      <button
        (click)="sendDataToServer()"
        [disabled]="isImporting"
        class="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <i *ngIf="!isImporting" class="pi pi-cloud-upload"></i>
        <i *ngIf="isImporting" class="pi pi-spin pi-spinner"></i>
        <span *ngIf="!isImporting">Envoyer au serveur ({{ convertedJson.length }} enregistrements)</span>
        <span *ngIf="isImporting">Envoi en cours...</span>
      </button>
    </div>

    <!-- Loader overlay pour la section d'import -->
    <div *ngIf="isImporting" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center gap-3">
      <p-progressSpinner
        styleClass="w-8 h-8"
        strokeWidth="4"
        fill="transparent"
        animationDuration="1s">
      </p-progressSpinner>
      <div class="text-blue-700">
        <p class="font-medium">Import en cours...</p>
        <p class="text-sm">Traitement de {{ convertedJson.length }} enregistrements</p>
      </div>
    </div>
  </div>


</div>


<!--Drawer qui va montrer le tutorial-->
<!-- Drawer (p-sidebar) -->
<p-sidebar
  [(visible)]="isTutorialDrawerVisible" position="right" [modal]="true" [style]="{ width: '30vw' }" class="shadow-lg">
  <h3 class="text-lg section-title font-bold mb-4 text-gray-800 flex items-center">
    <i class="pi pi-book mr-2"></i> Tutoriel Import/Export
  </h3>
  <div class="text-gray-700">
    <p class="mb-4">
      Suivez les étapes ci-dessous pour importer vos fichiers Excel dans l'application Supra.
    </p>
    <ol class="list-decimal ml-6">
      <li class="mb-2">
        Préparez votre fichier Excel en suivant le modèle recommandé. Téléchargez un modèle via le bouton <span class="font-semibold">"Télécharger le modèle"</span>.
      </li>
      <li class="mb-2">
        Ouvrez le fichier dans Excel et remplissez les colonnes nécessaires. Assurez-vous de ne pas modifier la structure.
      </li>
      <li class="mb-2">
        Enregistrez votre fichier en sélectionnant le format <span class="font-semibold">"CSV UTF-8 (délimité par des virgules)"</span>.
      </li>
      <li class="mb-2">
        Importez le fichier CSV en le sélectionnant via le formulaire d'import de Supra.
      </li>
      <li>
        Vérifiez le résultat JSON pour confirmer que les données sont correctes.
      </li>
    </ol>
    <div class="mt-4">
      <h4 class="text-md font-bold mb-2">Astuce :</h4>
      <p>
        Pour plus de détails, consultez notre documentation ou contactez votre référent métier pour Supra dans votre entité juridique si vous rencontrez des problèmes.
      </p>
    </div>
  </div>
  <!-- Bouton pour fermer -->
  <div class="mt-6 flex justify-end">
    <p-button label="Fermer" icon="pi pi-times" class="p-button-danger" (onClick)="closeTutorialDrawer()"></p-button>
  </div>
</p-sidebar>

<!-- Modal d'aperçu des résultats SIGAPS -->
<p-dialog
  [(visible)]="showSigapsResultsModal"
  [modal]="true"
  [closable]="true"
  [draggable]="false"
  [resizable]="false"
  styleClass="w-11/12 md:w-3/4 lg:w-1/2"
  header="Import SIGAPS réussi - Aperçu des données"
>
  <div class="p-4">
    <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
      <p class="text-green-800 font-medium">Votre import SIGAPS s'est déroulé avec succès.
      </p>
      <p class="text-green-600 text-sm mt-1">
        {{ sigapsResults.length }} enregistrement(s) ont été traités.
        <span *ngIf="sigapsResults.length > SIGAPS_PREVIEW_LIMIT">
          Voici un aperçu des {{ SIGAPS_PREVIEW_LIMIT }} premiers résultats :
        </span>
        <span *ngIf="sigapsResults.length <= SIGAPS_PREVIEW_LIMIT">
          Voici un aperçu des données importées :
        </span>
      </p>
      <div *ngIf="sigapsResults.length > SIGAPS_PREVIEW_LIMIT" class="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-blue-700 text-xs">
         Seuls les {{ SIGAPS_PREVIEW_LIMIT }} premiers résultats sont affichés pour des raisons de performance. Tous les {{ sigapsResults.length }} enregistrements ont bien été importés.
      </div>
    </div>

    <!-- Tableau des résultats -->
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Date Début
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Date Fin
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Score
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Catégorie
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Publications
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr *ngFor="let result of getDisplayedResults(); let i = index"
              [ngClass]="{'bg-gray-50': i % 2 === 1}">
            <td class="px-4 py-3 text-sm text-gray-900">
              {{ formatDate(result.dateDebut) }}
            </td>
            <td class="px-4 py-3 text-sm text-gray-900">
              {{ formatDate(result.dateFin) }}
            </td>
            <td class="px-4 py-3 text-sm font-medium text-blue-600">
              {{ formatValue(result.score) }}
            </td>
            <td class="px-4 py-3 text-sm">
              <span class="px-2 py-1 text-xs font-semibold rounded-full"
                    [ngClass]="{
                      'bg-purple-100 text-purple-800': result.categorie === 'A+',
                      'bg-blue-100 text-blue-800': result.categorie === 'A',
                      'bg-green-100 text-green-800': result.categorie === 'B',
                      'bg-yellow-100 text-yellow-800': result.categorie === 'C',
                      'bg-red-100 text-red-800': result.categorie === 'D',
                      'bg-gray-100 text-gray-800': !['A+', 'A', 'B', 'C', 'D'].includes(result.categorie)
                    }">
                {{ formatValue(result.categorie) }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm text-gray-900">
              {{ formatValue(result.nombrePublication) }}
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Indicateur s'il y a plus de résultats -->
      <div *ngIf="sigapsResults.length > SIGAPS_PREVIEW_LIMIT" class="mt-3 p-3 bg-gray-50 border-t border-gray-200 text-center">
        <p class="text-gray-600 text-sm">
          <i class="pi pi-info-circle mr-2"></i>
          {{ sigapsResults.length - SIGAPS_PREVIEW_LIMIT }} autre(s) enregistrement(s) importé(s) avec succès
          <span class="text-gray-500">(non affiché(s) dans cet aperçu)</span>
        </p>
      </div>
    </div>

    <!-- Actions -->
    <div class="mt-6 flex justify-end gap-3">
      <button
        (click)="closeSigapsResultsModal()"
        class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
        Fermer
      </button>
      <button
        (click)="resetToDefault(); closeSigapsResultsModal()"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
        Nouveau fichier
      </button>
    </div>
  </div>
</p-dialog>

<!-- Modal d'aperçu des résultats Garde et Astreinte -->
<p-dialog
  [(visible)]="showGardeAstreinteResultsModal"
  [modal]="true"
  [closable]="true"
  [draggable]="false"
  [resizable]="false"
  styleClass="w-11/12 md:w-3/4 lg:w-1/2"
  header="Import Garde et Astreinte réussi - Aperçu des données"
>
  <div class="p-4">
    <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
      <p class="text-green-800 font-medium">
         Votre import Garde et Astreinte s'est déroulé avec succès.
      </p>
      <p class="text-green-600 text-sm mt-1">
        {{ gardeAstreinteResults.length }} enregistrement(s) ont été traités.
        <span *ngIf="gardeAstreinteResults.length > GARDE_ASTREINTE_PREVIEW_LIMIT">
          Voici un aperçu des {{ GARDE_ASTREINTE_PREVIEW_LIMIT }} premiers résultats :
        </span>
        <span *ngIf="gardeAstreinteResults.length <= GARDE_ASTREINTE_PREVIEW_LIMIT">
          Voici un aperçu des données importées :
        </span>
      </p>
      <div *ngIf="gardeAstreinteResults.length > GARDE_ASTREINTE_PREVIEW_LIMIT" class="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-blue-700 text-xs">
        💡 Seuls les {{ GARDE_ASTREINTE_PREVIEW_LIMIT }} premiers résultats sont affichés pour des raisons de performance. Tous les {{ gardeAstreinteResults.length }} enregistrements ont bien été importés.
      </div>
    </div>

    <!-- Tableau des résultats -->
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Date Début
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Date Fin
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Date Garde
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Type Garde
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Total Garde
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Total Astreinte
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr *ngFor="let result of getDisplayedGardeAstreinteResults(); let i = index"
              [ngClass]="{'bg-gray-50': i % 2 === 1}">
            <td class="px-4 py-3 text-sm text-gray-900">
              {{ formatDate(result.dateDebut) }}
            </td>
            <td class="px-4 py-3 text-sm text-gray-900">
              {{ formatDate(result.dateFin) }}
            </td>
            <td class="px-4 py-3 text-sm text-gray-900">
              {{ formatDate(result.dateGarde) }}
            </td>
            <td class="px-4 py-3 text-sm text-gray-700">
              {{ formatValue(result.typeGarde) }}
            </td>
            <td class="px-4 py-3 text-sm font-medium text-blue-600">
              {{ formatValue(result.totalGarde) }}
            </td>
            <td class="px-4 py-3 text-sm font-medium text-green-600">
              {{ formatValue(result.totalAstreinte) }}
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Indicateur s'il y a plus de résultats -->
      <div *ngIf="gardeAstreinteResults.length > GARDE_ASTREINTE_PREVIEW_LIMIT" class="mt-3 p-3 bg-gray-50 border-t border-gray-200 text-center">
        <p class="text-gray-600 text-sm">
          <i class="pi pi-info-circle mr-2"></i>
          {{ gardeAstreinteResults.length - GARDE_ASTREINTE_PREVIEW_LIMIT }} autre(s) enregistrement(s) importé(s) avec succès
          <span class="text-gray-500">(non affiché(s) dans cet aperçu)</span>
        </p>
      </div>
    </div>

    <!-- Actions -->
    <div class="mt-6 flex justify-end gap-3">
      <button
        (click)="closeGardeAstreinteResultsModal()"
        class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
        Fermer
      </button>
      <button
        (click)="resetToDefault(); closeGardeAstreinteResultsModal()"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
        Nouveau fichier
      </button>
    </div>
  </div>
</p-dialog>

<!-- Modal d'aperçu des résultats Libérale -->
<p-dialog
  [(visible)]="showLiberaleResultsModal"
  [modal]="true"
  [closable]="true"
  [draggable]="false"
  [resizable]="false"
  styleClass="w-11/12 md:w-3/4 lg:w-2/3"
  header="Import Libérale réussi - Aperçu des données"
>
  <div class="p-4">
    <div class="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
      <p class="text-green-800 font-medium">
        Félicitations ! Votre import Libérale s'est déroulé avec succès.
      </p>
      <p class="text-green-600 text-sm mt-1">
        {{ liberaleResults.length }} enregistrement(s) ont été traités.
        <span *ngIf="liberaleResults.length > LIBERALE_PREVIEW_LIMIT">
          Voici un aperçu des {{ LIBERALE_PREVIEW_LIMIT }} premiers résultats :
        </span>
        <span *ngIf="liberaleResults.length <= LIBERALE_PREVIEW_LIMIT">
          Voici un aperçu des données importées :
        </span>
      </p>
      <div *ngIf="liberaleResults.length > LIBERALE_PREVIEW_LIMIT" class="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-blue-700 text-xs">
        💡 Seuls les {{ LIBERALE_PREVIEW_LIMIT }} premiers résultats sont affichés pour des raisons de performance. Tous les {{ liberaleResults.length }} enregistrements ont bien été importés.
      </div>
    </div>

    <!-- Tableau des résultats -->
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Code Acte
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Nom Acte
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Type Acte
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Date Réalisation
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
              Tarif
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr *ngFor="let result of getDisplayedLiberaleResults(); let i = index"
              [ngClass]="{'bg-gray-50': i % 2 === 1}">
            <td class="px-4 py-3 text-sm font-medium text-blue-600">
              {{ formatValue(result.codeActe) }}
            </td>
            <td class="px-4 py-3 text-sm text-gray-900 max-w-xs">
              <div class="truncate" [title]="result.nomActe">
                {{ formatValue(result.nomActe) }}
              </div>
            </td>
            <td class="px-4 py-3 text-sm text-gray-700">
              {{ formatValue(result.typeActe) }}
            </td>
            <td class="px-4 py-3 text-sm text-gray-900">
              {{ formatDate(result.dateRealisation) }}
            </td>
            <td class="px-4 py-3 text-sm font-medium text-green-600">
              {{ formatValue(result.tarif) }}€
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Indicateur s'il y a plus de résultats -->
      <div *ngIf="liberaleResults.length > LIBERALE_PREVIEW_LIMIT" class="mt-3 p-3 bg-gray-50 border-t border-gray-200 text-center">
        <p class="text-gray-600 text-sm">
          <i class="pi pi-info-circle mr-2"></i>
          {{ liberaleResults.length - LIBERALE_PREVIEW_LIMIT }} autre(s) enregistrement(s) importé(s) avec succès
          <span class="text-gray-500">(non affiché(s) dans cet aperçu)</span>
        </p>
      </div>
    </div>

    <!-- Actions -->
    <div class="mt-6 flex justify-end gap-3">
      <button
        (click)="closeLiberaleResultsModal()"
        class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
        Fermer
      </button>
      <button
        (click)="resetToDefault(); closeLiberaleResultsModal()"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
        Nouveau fichier
      </button>
    </div>
  </div>
</p-dialog>

<!-- Modal d'aperçu des résultats Actes -->
<p-dialog
  [(visible)]="showActesResultsModal"
  [modal]="true"
  [closable]="true"
  [draggable]="false"
  [resizable]="false"
  header="Aperçu des résultats - Import Actes"
  [style]="{width: '90vw', maxWidth: '1200px'}"
  styleClass="custom-dialog">

  <div class="space-y-4">
    <!-- Statistiques -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <h3 class="text-lg font-semibold text-green-800">Import réussi !</h3>
      </div>
      <p class="text-green-700 mt-2">
        <strong>{{ actesResults.length }}</strong> actes importés avec succès.
        <span *ngIf="actesResults.length > ACTES_PREVIEW_LIMIT" class="text-sm">
          (Affichage des {{ ACTES_PREVIEW_LIMIT }} premiers résultats)
        </span>
      </p>
    </div>

    <!-- Tableau des résultats -->
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Code</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Description</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Type</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Date Réalisation</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Nb Réalisation</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Internum</th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Période</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
          <tr *ngFor="let result of getDisplayedActesResults(); let i = index"
              [class]="i % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
            <td class="px-4 py-3 text-sm font-medium text-gray-900">{{ result.code }}</td>
            <td class="px-4 py-3 text-sm text-gray-700">{{ result.description || 'N/A' }}</td>
            <td class="px-4 py-3 text-sm text-gray-700">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    [class]="result.typeActe === 'CCAM' ? 'bg-blue-100 text-blue-800' :
                             result.typeActe === 'NGAP' ? 'bg-green-100 text-green-800' :
                             result.typeActe === 'LABO' ? 'bg-purple-100 text-purple-800' :
                             'bg-gray-100 text-gray-800'">
                {{ result.typeActe || 'N/A' }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm text-gray-700">{{ formatDate(result.dateRealisation) }}</td>
            <td class="px-4 py-3 text-sm text-gray-700">{{ result.nombreDeRealisation || 'N/A' }}</td>
            <td class="px-4 py-3 text-sm text-gray-700">{{ result.internum || 'N/A' }}</td>
            <td class="px-4 py-3 text-sm text-gray-700">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                {{ result.periodeType || 'N/A' }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Actions -->
    <div class="flex justify-end space-x-3 pt-4 border-t">
      <button
        (click)="closeActesResultsModal()"
        class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
        Fermer
      </button>
      <button
        (click)="resetToDefault(); closeActesResultsModal()"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
        Nouveau fichier
      </button>
    </div>
  </div>
</p-dialog>
