<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

<!-- SubHeader avec filtres intelligents en cascade - Sticky lors du scroll -->
<div class="sticky top-0 bg-white shadow-sm" style="z-index: 9999 !important;">
  <app-subheader
    [useServerSideFilters]="true"
    (filtersChanged)="onFiltersChanged($event)"
    (requestPageChange)="onRequestPageChange($event)">
  </app-subheader>
</div>

<!-- Header Premium PMSI -->
<section class="mb-6">
  <div class="bg-gradient-to-r from-white via-gray-50 to-cyan-50 rounded-xl shadow-lg border border-gray-200 overflow-hidden">
    <!-- Barre de statut supérieure -->
    <div class="bg-gradient-to-r from-cyan-600 to-cyan-700 px-6 py-2">
      <div class="flex items-center justify-between text-white">
        <div class="flex items-center space-x-2">
          <!-- Indicateur de santé dynamique -->
          <div class="w-2 h-2 rounded-full"
               [ngClass]="{
                 'bg-green-400 animate-pulse': systemHealthStatus === 'excellent',
                 'bg-white animate-pulse': systemHealthStatus === 'good',
                 'bg-orange-400 animate-pulse': systemHealthStatus === 'slow',
                 'bg-red-400': systemHealthStatus === 'offline'
               }"></div>
          <span class="text-sm font-medium text-white"
                [pTooltip]="'Temps de réponse de supra: ' + generationTimeMs + 'ms'">
            {{ systemHealthMessage }}
          </span>
        </div>
        <div class="flex items-center space-x-4 text-sm">
          <!-- Périodes actives -->
          <div *ngIf="globalFilters?.p1" class="flex items-center space-x-2">
            <span class="bg-white/20 px-2 py-1 rounded text-xs">P1: {{ formatPeriod(globalFilters?.p1) }}</span>
            <span *ngIf="globalFilters?.p2" class="bg-white/20 px-2 py-1 rounded text-xs">P2: {{ formatPeriod(globalFilters?.p2) }}</span>
            <span *ngIf="globalFilters?.p3" class="bg-white/20 px-2 py-1 rounded text-xs">P3: {{ formatPeriod(globalFilters?.p3) }}</span>
          </div>
          <span class="hidden md:inline">•</span>
          <span class="hidden md:inline">CHRU Nancy</span>
        </div>
      </div>
    </div>

    <!-- Contenu principal -->
    <div class="p-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">

        <!-- Section titre et description -->
        <div class="flex-1">
          <div class="flex items-center space-x-4 mb-3">
            <!-- Icône principale avec effet -->
            <div class="relative">
              <div class="absolute inset-0 bg-cyan-600 rounded-full opacity-20 "></div>
              <div class="relative bg-gradient-to-br from-cyan-600 to-cyan-700 p-3 rounded-full shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
              </div>
            </div>

            <!-- Titre et breadcrumb -->
            <div>
              <div class="flex items-center space-x-2 text-sm text-gray-500 mb-1">
                <span>Supra</span>
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span>Activités</span>
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-cyan-600 font-medium">Consultation</span>
              </div>
              <h1 class="text-2xl font-bold text-gray-800">Gestion des Activités</h1>
              <p class="text-gray-600 mt-1">Consultation et analyse des actes médicaux</p>
            </div>
          </div>
        </div>

        <!-- Métriques et Graphique de Répartition -->
        <div class="flex flex-wrap lg:flex-nowrap items-center gap-6">

          <!-- Graphique de répartition CCAM/NGAP/LABO -->
          <div class="bg-white rounded-lg p-4 shadow-md border border-gray-100 min-w-[200px] relative cursor-help"
               pTooltip="Répartition globale des actes par type : CCAM, NGAP  et LABO sur les périodes choisies"
               tooltipPosition="top">
            <!-- Icône d'information -->
            <div class="absolute top-2 right-2">
              <svg class="h-3 w-3 text-gray-400 hover:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h3 class="text-sm font-semibold text-gray-700 mb-3">Répartition par Type</h3>
            <div class="space-y-2">
              <!-- CCAM -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span class="text-xs font-medium text-gray-600">CCAM</span>
                </div>
                <span class="text-sm font-bold text-gray-800">{{ totalCcamActivites | number }}</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-500 h-2 rounded-full transition-all duration-500"
                     [style.width.%]="getPercentage(totalCcamActivites, totalAllActivites)"></div>
              </div>

              <!-- NGAP -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span class="text-xs font-medium text-gray-600">NGAP</span>
                </div>
                <span class="text-sm font-bold text-gray-800">{{ totalNgapActivites | number }}</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-green-500 h-2 rounded-full transition-all duration-500"
                     [style.width.%]="getPercentage(totalNgapActivites, totalAllActivites)"></div>
              </div>

              <!-- LABO -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span class="text-xs font-medium text-gray-600">LABO</span>
                </div>
                <span class="text-sm font-bold text-gray-800">{{ totalLaboActivites | number }}</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-purple-500 h-2 rounded-full transition-all duration-500"
                     [style.width.%]="getPercentage(totalLaboActivites, totalAllActivites)"></div>
              </div>
            </div>
          </div>

          <!-- Total Activités -->
          <div class="bg-white rounded-lg p-4 shadow-md border border-gray-100 min-w-[120px] relative cursor-help"
               pTooltip="Nombre total (global) d'actes par intervention connue de Supra sur les périodes choisies. Format : K = milliers, M = millions (ex: 1.5K = 1 500, 2M = 2 000 000)"
               tooltipPosition="top">
            <!-- Icône d'information -->
            <div class="absolute top-2 right-2">
              <svg class="h-3 w-3 text-gray-400 hover:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-blue-100 rounded-lg">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <div>
                <p class="text-2xl font-bold text-gray-800">{{ formatLargeNumber(totalAllActivites) }}</p>
                <p class="text-xs text-gray-500 font-medium">Actes Total</p>
              </div>
            </div>
          </div>

          <!-- Actes filtrés -->
          <div class="bg-white rounded-lg p-4 shadow-md border border-gray-100 min-w-[120px] relative cursor-help"
               pTooltip="Nombre total d'actes visibles dans le tableau selon le type sélectionné (CCAM, NGAP, LABO ou Tous)."
               tooltipPosition="top">
            <!-- Icône d'information -->
            <div class="absolute top-2 right-2">
              <svg class="h-3 w-3 text-gray-400 hover:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="flex items-center space-x-3">
              <div class="p-2 bg-green-100 rounded-lg">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                </svg>
              </div>
              <div>
                <p class="text-2xl font-bold text-gray-800">{{ formatLargeNumber(activites.length) }}</p>
                <div class="flex items-center space-x-1">
                  <p class="text-xs text-gray-500 font-medium">Dans le Tableau</p>
                  <span *ngIf="selectedActivityType !== 'ALL'"
                        class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {{ selectedActivityType }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Logo CHRU avec effet premium -->
        <div class="hidden xl:block">
          <div class="relative group">
            <!-- Effet de fond -->
            <div class="absolute inset-0 bg-gradient-to-br from-cyan-100 to-blue-100 rounded-2xl transform rotate-3 group-hover:rotate-6 transition-transform duration-300"></div>

            <!-- Contenu -->
            <div class="relative bg-white rounded-2xl p-6 shadow-lg border border-gray-200 group-hover:shadow-xl transition-all duration-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="w-16 h-16 text-cyan-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-300"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5"
                />
              </svg>
              <p class="text-sm font-bold text-gray-700 text-center">CHRU</p>
              <p class="text-xs text-gray-500 text-center">Nancy</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Type Filter Buttons -->
<div class="flex flex-wrap gap-3 mb-5">
  <div *ngFor="let type of activityTypes"
       class="p-3 rounded-lg cursor-pointer transition-all duration-200 text-center relative min-w-[140px]"
       [ngClass]="{
         'bg-blue-600 text-white shadow-lg': selectedActivityType === type.value && type.value === 'CCAM',
         'bg-green-600 text-white shadow-lg': selectedActivityType === type.value && type.value === 'NGAP',
         'bg-purple-600 text-white shadow-lg': selectedActivityType === type.value && (type.value === 'LABO' || type.value === 'NABM'),
         'bg-cyan-700 text-white shadow-lg': selectedActivityType === type.value && type.value === 'ALL',
         'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-md': selectedActivityType !== type.value
       }"
       (click)="selectedActivityType = type.value; onActivityTypeChange()">
    <!-- Icon based on type -->
    <div class="flex justify-center mb-1">
      <i *ngIf="type.value === 'ALL'" class="pi pi-list text-lg"></i>
      <i *ngIf="type.value === 'CCAM'" class="pi pi-file-check text-lg"></i>
      <i *ngIf="type.value === 'NGAP'" class="pi pi-file text-lg"></i>
      <i *ngIf="type.value === 'LABO'" class="pi pi-receipt text-lg"></i> <!-- Icon for LABO or NABM activities -->
    </div>

    <!-- Type label -->
    <div class="font-medium text-sm">{{ type.label }}</div>

    <!-- Count display for each type -->
<!--    <div class="text-xs mt-2 font-semibold">-->
<!--      <ng-container *ngIf="type.value === 'ALL'">{{ totalAllActivites | number }} actes</ng-container>-->
<!--      <ng-container *ngIf="type.value === 'CCAM'">{{ totalCcamActivites | number }} actes</ng-container>-->
<!--      <ng-container *ngIf="type.value === 'NGAP'">{{ totalNgapActivites | number }} actes</ng-container>-->
<!--      <ng-container *ngIf="type.value === 'LABO'">{{ totalLaboActivites | number }} actes</ng-container>-->
<!--    </div>-->

    <!-- Active indicator dot -->
    <div *ngIf="selectedActivityType === type.value"
         class="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-white border-2"
         [ngClass]="{
           'border-blue-600': type.value === 'CCAM',
           'border-green-600': type.value === 'NGAP',
           'border-purple-600': type.value === 'LABO' || type.value === 'NABM',
           'border-cyan-700': type.value === 'ALL'
         }">
    </div>
  </div>
</div>

<!-- Activities Table -->
<div class="p-card mb-6">
  <div class="p-4 rounded-t-lg border-b"
       [ngClass]="{
         'bg-blue-50 border-blue-200': selectedActivityType === 'CCAM',
         'bg-green-50 border-green-200': selectedActivityType === 'NGAP',
         'bg-purple-50 border-purple-200': selectedActivityType === 'LABO' || selectedActivityType === 'NABM',
         'bg-cyan-50 border-cyan-200': selectedActivityType === 'ALL'
       }">
    <h2 class="text-lg font-bold"
        [ngClass]="{
          'text-blue-800': selectedActivityType === 'CCAM',
          'text-green-800': selectedActivityType === 'NGAP',
          'text-purple-800': selectedActivityType === 'LABO' || selectedActivityType === 'NABM',
          'text-cyan-800': selectedActivityType === 'ALL'
        }">
      <ng-container *ngIf="selectedActivityType === 'ALL'">Tous les actes</ng-container>
      <ng-container *ngIf="selectedActivityType === 'CCAM'">Actes CCAM</ng-container>
      <ng-container *ngIf="selectedActivityType === 'NGAP'">Actes NGAP</ng-container>
      <ng-container *ngIf="selectedActivityType === 'LABO' || selectedActivityType === 'NABM'">Actes LABO/NABM</ng-container>
    </h2>
  </div>

  <!-- Table container with horizontal scroll -->
  <div class="bg-white border rounded-b-lg shadow overflow-x-auto">
    <p-table
    #activitesTable
    [value]="activites"
    [paginator]="true"
    [rows]="pageSize"
    [rowsPerPageOptions]="rowsPerPageOptions"
    [lazy]="true"
    [loading]="loading"
    [totalRecords]="totalRecords"
    (onPage)="onPageChange($event)"
    (onLazyLoad)="onLazyLoad($event)"
    [first]="first"
    [responsiveLayout]="'scroll'"
    [globalFilterFields]="['code', 'description', 'typeActe', 'ufIntervention.libelle', 'ufIntervention.ufcode', 'agent.prenom', 'agent.nom', 'agent.titre']"
    [exportHeader]="'customExportHeader'"
    [scrollable]="true"
    scrollHeight="auto"
    styleClass="p-datatable-sm"
  >
    <!-- Caption with Export and View Details Buttons -->
    <ng-template pTemplate="caption">
      <div class="flex justify-between items-center mb-2 bg-white">
        <div class="flex space-x-2" [ngClass]="{'pulse-animation': selectedActivites.length > 0}">
          <!-- View Details Button -->
          <button
            pButton
            icon="pi pi-eye"
            [label]="selectedActivites.length > 0 ? 'Voir détails (' + selectedActivites.length + ')' : 'Voir détails'"
            [class]="selectedActivites.length > 0 ? 'p-button-success shadow-md transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95' : 'p-button-outlined p-button-success transition-all duration-300 hover:bg-gray-100'"
            [disabled]="selectedActivites.length === 0"
            (click)="viewSelectedActivitesDetails()"
            pTooltip="Voir les détails des actes sélectionnés"
            tooltipPosition="top"
            [style]="{'background': selectedActivites.length > 0 ? 'linear-gradient(to right, #10b981, #059669)' : '', 'border': selectedActivites.length > 0 ? 'none' : ''}"
          >
            <span *ngIf="selectedActivites.length > 0" class="ml-2 bg-white text-green-700 rounded-full px-2 py-0.5 text-xs font-bold">
              {{ selectedActivites.length }}
            </span>
          </button>

          <!-- Export Button -->
          <button
            pButton
            [icon]="selectedActivites.length > 0 ? 'pi pi-download' : 'pi pi-file-export'"
            [label]="selectedActivites.length > 0 ? 'Exporter ' + selectedActivites.length + ' acte(s)' : 'Exporter la sélection'"
            [class]="selectedActivites.length > 0 ? 'p-button-primary shadow-md transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95' : 'p-button-outlined transition-all duration-300 hover:bg-gray-100'"
            [disabled]="selectedActivites.length === 0"
            (click)="exportSelectedActivites()"
            pTooltip="Exporter les actes sélectionnés en CSV"
            tooltipPosition="top"
            [style]="{'background': selectedActivites.length > 0 ? 'linear-gradient(to right, #0ea5e9, #0284c7)' : '', 'border': selectedActivites.length > 0 ? 'none' : ''}"
          >
            <span *ngIf="selectedActivites.length > 0" class="ml-2 bg-white text-cyan-700 rounded-full px-2 py-0.5 text-xs font-bold">
              {{ selectedActivites.length }}
            </span>
          </button>
        </div>
        <div class="text-sm text-gray-600 flex items-center" *ngIf="selectedActivites.length > 0">
          <i class="pi pi-check-circle text-green-500 mr-2"></i>
          <span class="bg-cyan-100 text-cyan-800 px-2 py-1 rounded-full font-medium">
            {{ selectedActivites.length }} acte(s) sélectionné(s)
          </span>
        </div>
      </div>
      <div class="relative bg-white">
        <!-- Icône de recherche -->
        <span class="absolute inset-y-0 left-0 flex items-center pl-3">
          <i class="pi pi-search text-gray-400"></i>
        </span>
        <!-- Input de recherche -->
        <input
          type="text"
          (input)="onInput($event)"
          class="block w-full pl-10 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
             ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600
             sm:text-sm"
          placeholder="Filtrer par code, description, UF intervention, praticien"
        />
      </div>
    </ng-template>

    <!-- Column Headers -->
    <ng-template pTemplate="header">
      <tr>
        <th class="w-12 px-4 py-2 border-b text-left text-gray-700 font-semibold sticky left-0 bg-white z-[5]" style="min-width: 50px; max-width: 60px;">
          <p-checkbox
            [binary]="true"
            [ngModel]="isAllSelected()"
            (onChange)="toggleSelectAll($event)"
            inputId="selectAll"
            pTooltip="Sélectionner/Désélectionner tout"
            tooltipPosition="top"
          ></p-checkbox>
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold sticky left-[60px] bg-white z-[5]" style="min-width: 50px; max-width: 60px;">
          <i
            class="pi pi-hashtag cursor-pointer ml-2"
            title="Voir les détails de l'acte"
          ></i>
        </th>
        <th pSortableColumn="code" class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 100px;">
          Code
          <p-sortIcon field="code"></p-sortIcon>
        </th>
        <th pSortableColumn="description" class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 200px;">
          Description
          <p-sortIcon field="description"></p-sortIcon>
        </th>
        <th pSortableColumn="typeActe" class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 100px;">
          Type
          <p-sortIcon field="typeActe"></p-sortIcon>
        </th>
        <th pSortableColumn="dateRealisation" class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 150px;">
          Date de réalisation
          <p-sortIcon field="dateRealisation"></p-sortIcon>
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 150px;">
          Praticien
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 150px;">
          UF Intervention
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 150px;">
          Détails
          <i class="pi pi-info-circle ml-1 text-gray-500" pTooltip="Informations détaillées" tooltipPosition="top"></i>
        </th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template pTemplate="body" let-activite>
      <!-- Only show activities of the selected type or all types if ALL is selected -->
      <tr class="border-b hover:bg-gray-50"
          *ngIf="selectedActivityType === 'ALL' ||
                (selectedActivityType === 'CCAM' && activite.typeActe === 'CCAM') ||
                (selectedActivityType === 'NGAP' && activite.typeActe === 'NGAP') ||
                (selectedActivityType === 'LABO' && (activite.typeActe === 'LABO' || activite.typeActe === 'NABM'))">
        <td class="px-4 py-2 text-center sticky left-0 bg-white z-[5]" style="min-width: 50px; max-width: 60px;">
          <p-checkbox
            [binary]="true"
            [ngModel]="isActiviteSelected(activite)"
            (onChange)="toggleActiviteSelection(activite)"
            [inputId]="'activite_' + activite['@id']"
            styleClass="p-checkbox-lg"
          ></p-checkbox>
        </td>
        <td style="padding: 0.75rem; text-align: left; min-width: 50px; max-width: 60px;" class="sticky left-[60px] bg-white z-[5]">
          <p class="text-sm font-bold text-gray-600 flex items-center">
            <i
              class="pi pi-eye text-lg text-cyan-700 cursor-pointer ml-2"
              title="Voir les détails de l'acte {{activite.code}} {{activite.description}}"
              (click)="viewActiviteDetails(activite['@id'])"
            ></i>
          </p>
        </td>
        <td class="px-4 py-2" style="min-width: 100px;" >
          <a class="text-lg text-cyan-700 hover:cursor-copy cursor-pointer font-semibold">
            {{ activite.code }}
          </a>
        </td>
        <td class="px-4 py-2" style="min-width: 200px;">{{ activite.description }}</td>
        <td class="px-4 py-2" style="min-width: 100px;">
          <span class="px-2 py-1 rounded-full text-xs font-medium"
                [ngClass]="{
                  'bg-blue-100 text-blue-800': activite.typeActe === 'CCAM',
                  'bg-green-100 text-green-800': activite.typeActe === 'NGAP',
                  'bg-purple-100 text-purple-800': activite.typeActe === 'LABO' || activite.typeActe === 'NABM'
                }">
            {{ activite.typeActe }}
          </span>
        </td>
        <td class="px-4 py-2" style="min-width: 150px;">{{ formatDate(activite.dateRealisation) }}</td>
        <td class="px-4 py-2" style="min-width: 150px;">{{ getAgentDisplayName(activite.agent) }}</td>
        <td class="px-4 py-2" style="min-width: 150px;">
          <span *ngIf="activite.ufIntervention; else noUfIntervention">
            {{ activite.ufIntervention.libelle }} ({{ activite.ufIntervention.ufcode }})
          </span>
          <ng-template #noUfIntervention>
            <span class="text-gray-400 italic">Pas connu de supra</span>
          </ng-template>
        </td>
        <td class="px-4 py-2" style="min-width: 150px;">
          <div class="flex flex-wrap gap-2">
            <!-- Nombre de réalisations -->
            <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" pTooltip="nombre de realisation" tooltipPosition="top">
              <i class="pi pi-hashtag mr-1"></i>NB: {{ activite.nombreDeRealisation }}
            </span>

            <!-- Activité ID (si présent) -->
            <span *ngIf="activite.activite"
                  class="px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                  [pTooltip]="'Activité ' + activite.activite + (activite.activiteLib ? ' : ' + activite.activiteLib : '')"
                  tooltipPosition="top">
              <i class="pi pi-id-card mr-1"></i>ACT: {{ activite.activite }}
            </span>

            <!-- Type de venue -->
            <span class="px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800" pTooltip="Type de venue" tooltipPosition="top">
              <i class="pi pi-tag mr-1"></i>{{ activite.libTypeVenue }}
            </span>

            <!-- ICR, Coefficient, Lettre - only for LABO/NABM -->
            <ng-container *ngIf="(selectedActivityType === 'LABO' || selectedActivityType === 'ALL') && activite.icrA">
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800" pTooltip="ICR" tooltipPosition="top">
                <i class="pi pi-chart-line mr-1"></i>ICR: {{ activite.icrA }}
              </span>
            </ng-container>

            <ng-container *ngIf="(selectedActivityType === 'LABO' || selectedActivityType === 'ALL') && activite.coefficient">
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" pTooltip="Coefficient" tooltipPosition="top">
                <i class="pi pi-percentage mr-1"></i>Coef: {{ activite.coefficient }}
              </span>
            </ng-container>

            <ng-container *ngIf="(selectedActivityType === 'LABO' || selectedActivityType === 'ALL') && activite.lettreCoef">
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800" pTooltip="Lettre" tooltipPosition="top">
                <i class="pi pi-bookmark mr-1"></i>{{ activite.lettreCoef }}
              </span>
            </ng-container>
          </div>
        </td>
      </tr>
    </ng-template>

    <!-- Skeleton Loading Template -->
    <ng-template pTemplate="loadingbody">
      <tr class="border-b">
        <td style="padding: 0.75rem; text-align: left;">
          <p-skeleton shape="circle" size="1.5rem"></p-skeleton>
        </td>
        <td style="padding: 0.75rem; text-align: left;">
          <p-skeleton shape="circle" size="2rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton height="2rem" styleClass="mb-2"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="100%" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="6rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="8rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="8rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <div class="flex flex-wrap gap-2">
            <p-skeleton width="4rem" height="1.5rem" styleClass="mr-2"></p-skeleton>
            <p-skeleton width="4rem" height="1.5rem" styleClass="mr-2"></p-skeleton>
            <p-skeleton width="4rem" height="1.5rem"></p-skeleton>
          </div>
        </td>
      </tr>
    </ng-template>

    <!-- Empty Message -->
    <ng-template pTemplate="emptymessage">
      <tr>
        <td [attr.colspan]="9" class="text-center py-4">
          <div *ngIf="loading" class="flex justify-center items-center py-8">
            <!-- Premium Spinner -->
            <div class="premium-spinner-simple">
              <div class="premium-spinner-ring"></div>
              <div class="premium-spinner-ring"></div>
              <div class="premium-spinner-ring"></div>
            </div>
            <span class="ml-3 text-lg font-medium text-cyan-700">Chargement des données...</span>
          </div>
          <div *ngIf="!loading" class="py-8">
            <div class="text-gray-400 text-6xl mb-4">
              <i class="pi pi-inbox"></i>
            </div>
            <p class="text-xl text-gray-600 font-light">Aucun acte dans cette sélection</p>
            <p class="text-sm text-gray-500 mt-2">
              <span *ngIf="totalAllActivites > 0; else noDataAtAll">
                {{ totalAllActivites }} acte(s) trouvé(s) mais aucun ne correspond au type sélectionné.
                <br>Essayez de changer le type d'acte ou vos filtres.
              </span>
              <ng-template #noDataAtAll>
                Aucun acte ne correspond à vos critères de recherche.
                <br>Essayez de modifier vos filtres (praticien, pôle, CR, type de venue).
              </ng-template>
            </p>
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
  </div>
</div>

<style>
  /* Custom styles for horizontal scrolling */
  ::ng-deep .p-datatable-wrapper {
    overflow-x: auto !important;
  }

  ::ng-deep .p-datatable-thead {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  ::ng-deep .p-datatable-scrollable .p-datatable-thead > tr > th {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  /* Ensure the sticky columns have proper z-index and background */
  ::ng-deep .sticky {
    position: sticky !important;
    background-color: white !important;
    z-index: 2 !important;
  }

  /* Fix for hover state on sticky columns */
  ::ng-deep tr:hover .sticky {
    background-color: #f9fafb !important; /* Equivalent to hover:bg-gray-50 */
  }

  /* Ensure sticky columns stay in place during horizontal scroll */
  ::ng-deep .p-datatable-scrollable-body {
    scroll-padding-left: 120px; /* Width of both sticky columns combined */
  }

  /* Make sure horizontal scrolling works properly */
  ::ng-deep .p-datatable-scrollable .p-datatable-wrapper {
    overflow-x: auto !important;
  }

  /* Ensure the table doesn't cause page-level horizontal scrolling */
  ::ng-deep .p-datatable {
    max-width: 100%;
  }



  /* Custom pulse animation for the export button */
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(14, 165, 233, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(14, 165, 233, 0);
    }
  }

  .pulse-animation button {
    animation: pulse 2s infinite;
  }

  /* Improve button styles */
  ::ng-deep .p-button {
    transition: all 0.3s ease;
  }

  ::ng-deep .p-button:active {
    transform: scale(0.95);
  }

  ::ng-deep .p-button:hover {
    filter: brightness(1.05);
  }

  /* Fix z-index for PrimeNG dropdowns to appear above sticky subheader */
  ::ng-deep .p-dropdown-panel,
  ::ng-deep .p-autocomplete-panel,
  ::ng-deep .p-multiselect-panel,
  ::ng-deep .p-calendar-panel,
  ::ng-deep .p-overlay {
    z-index: 10000 !important;
  }

  /* Ensure PrimeNG overlays appear above sticky subheader */
  ::ng-deep .p-component-overlay {
    z-index: 10000 !important;
  }

  /* Fix for tooltip z-index */
  ::ng-deep .p-tooltip {
    z-index: 10001 !important;
  }
</style>
