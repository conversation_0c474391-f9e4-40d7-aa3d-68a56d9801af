describe('filter search', () => {

  const urlWebsite = "http://localhost:4200/organisation/service";
  const ccamSearch1 = "JQQP001";


  it('Access app', () => {
    cy.task("log", "Starting filter search test")
    cy.visit(urlWebsite);
    cy.get('input[name="price"]').eq(0).type(ccamSearch1);
    cy.wait(2500)



    // test connected user
    // test displayed vue d'ensemble & suivi des practiciens

  })

  it('Access app', () => {
    cy.task("log", "Starting logged user test")
    cy.visit(urlWebsite);
    cy.get('input[name="price"]').eq(0).type(ccamSearch1)

    // test connected user

    cy.get('[id="user-menu-button"]').should('exist');
  })
})
