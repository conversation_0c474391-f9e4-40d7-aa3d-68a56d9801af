import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';

export interface FeatureConfig {
  enabled: boolean;
  routes?: string[];
  description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FeatureFlagService {

  // Configuration des features pour cette itération Scrum
  private readonly featureConfig: Record<string, FeatureConfig> = {
    // ✅ Features activées pour cette itération (Sprint actuel)
    'activites-list': {
      enabled: true,
      routes: ['/activites', '/activites/activites-list'],
      description: 'Composant Activités List uniquement - Sprint actuel'
    },
    'import': {
      enabled: true,
      routes: ['/referent/import'],
      description: 'Module Import (admin seulement) - Sprint actuel'
    },
    'auth': {
      enabled: true,
      routes: ['/auth/login', '/auth/logout', '/auth'],
      description: 'Authentification - Toujours actif'
    },
    'dashboard': {
      enabled: true,
      routes: ['/'],
      description: 'Dashboard principal - Toujours actif'
    },

    // 🚧 Features désactivées pour cette itération (prochains sprints)
    'activites-single': {
      enabled: true,
      routes: ['/activites/activite'],
      description: 'Composant Single Activité - Sprint suivant'
    },
    'activites-multi-details': {
      enabled: true,
      routes: ['/activites/activites-multi-details'],
      description: 'Composant Multi Détails Activités - Sprint suivant'
    },
    'activites-metadata': {
      enabled: false,
      routes: ['/activites/metadata-test'],
      description: 'Composant Metadata Test - Sprint suivant'
    },
    'praticiens': {
      enabled: true,
      routes: ['/praticien/praticien-list', '/praticien'],
      description: 'Module Praticiens - Maintenant disponible!'
    },
    'structure': {
      enabled: false,
      routes: ['/structure/pole-list', '/structure/cr-list', '/structure/service-list', '/structure/uf-list', '/structure'],
      description: 'Module Structure - Sprint suivant'
    },
    'sigaps': {
      enabled: false,
      routes: ['/sigaps'],
      description: 'Module SIGAPS - Sprint suivant'
    },
    'garde-astreinte': {
      enabled: false,
      routes: ['/garde-et-astreinte'],
      description: 'Module Garde et Astreinte - Sprint suivant'
    },
    'liberale': {
      enabled: false,
      routes: ['/liberale'],
      description: 'Module Libérale - Sprint suivant'
    },
    'actes': {
      enabled: false,
      routes: ['/graphic/acte-list', '/acte'],
      description: 'Module Actes - Sprint suivant'
    },
    'agent-details': {
      enabled: false,
      routes: ['/agent-details'],
      description: 'Détails Agent - Sprint suivant'
    },
    'particule': {
      enabled: false,
      routes: ['/particule'],
      description: 'Composant Particule - Sprint suivant'
    }
  };

  /**
   * Vérifie si une feature est activée
   */
  isFeatureEnabled(featureName: string): boolean {
    const feature = this.featureConfig[featureName];
    return feature ? feature.enabled : false;
  }

  /**
   * Vérifie si une route est accessible
   */
  isRouteEnabled(route: string): boolean {
    // Nettoyer la route (enlever les paramètres et query params)
    const cleanRoute = route.split('?')[0].split(';')[0];

    // Vérifier chaque feature activée
    for (const [featureName, config] of Object.entries(this.featureConfig)) {
      if (config.enabled && config.routes) {
        const isRouteMatch = config.routes.some(enabledRoute => {
          // Correspondance exacte ou préfixe
          return cleanRoute === enabledRoute || cleanRoute.startsWith(enabledRoute + '/');
        });

        if (isRouteMatch) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Obtient la liste des features activées
   */
  getEnabledFeatures(): string[] {
    return Object.entries(this.featureConfig)
      .filter(([_, config]) => config.enabled)
      .map(([featureName, _]) => featureName);
  }

  /**
   * Obtient la liste des routes activées
   */
  getEnabledRoutes(): string[] {
    const routes: string[] = [];

    Object.values(this.featureConfig)
      .filter(config => config.enabled)
      .forEach(config => {
        if (config.routes) {
          routes.push(...config.routes);
        }
      });

    return routes;
  }

  /**
   * Obtient la configuration complète des features (pour debug/admin)
   */
  getFeatureConfig(): Record<string, FeatureConfig> {
    return { ...this.featureConfig };
  }

  /**
   * Vérifie si le mode maintenance est activé
   */
  isMaintenanceMode(): boolean {
    return environment.production && this.getEnabledFeatures().length === 0;
  }

  /**
   * Obtient le message de maintenance/indisponibilité
   */
  getMaintenanceMessage(featureName?: string): string {
    if (featureName) {
      const feature = this.featureConfig[featureName];
      return feature?.description || `La fonctionnalité "${featureName}" n'est pas encore disponible.`;
    }

    return 'Cette fonctionnalité sera disponible dans une prochaine version.';
  }
}
