import { Component } from '@angular/core';
import {ButtonDirective} from "primeng/button";
import {CalendarModule} from "primeng/calendar";
import {ChartModule} from "primeng/chart";
import {FormsModule} from "@angular/forms";
//
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import {position} from "html2canvas/dist/types/css/property-descriptors/position";

@Component({
  selector: 'app-analytics-dashboard',
  standalone: true,
  imports: [
    ButtonDirective,
    CalendarModule,
    ChartModule,
    FormsModule
  ],
  templateUrl: './analytics-dashboard.component.html',
  styleUrl: './analytics-dashboard.component.scss'
})
export class AnalyticsDashboardComponent {
  dateRange: Date[] = [];

  chartData: any = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Personal Wallet',
        backgroundColor: '#42A5F5',
        data: [6500, 5900, 8000, 8100, 5600, 5500, 4000, 7000, 8500, 9000, 9500, 10200]
      },
      {
        label: 'Corporate Wallet',
        backgroundColor: '#9CCC65',
        data: [3000, 4000, 5000, 4000, 4500, 3800, 3000, 4000, 5000, 4500, 4000, 4200]
      },
      {
        label: 'Investment Wallet',
        backgroundColor: '#FFCA28',
        data: [2000, 2500, 3000, 2000, 2200, 1800, 2500, 2700, 3000, 3300, 3500, 4000]
      }
    ]
  };

  chartOptions: any = {
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 0.8,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        enabled: true,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          color: '#ebedef',
        },
        ticks: {
          stepSize: 5000,
        },
      },
    },
  };


  generatePDF() {
    const element = document.getElementById('pdfContent'); // ID de l'élément HTML que vous souhaitez convertir en PDF
    if (element) {
      html2canvas(element).then((canvas) => {
        const imgData = canvas.toDataURL('image/png'); // Convertit l'élément en image
        const pdf = new jsPDF('p', 'mm', 'a4'); // Configure un PDF au format A4

        // Calcule la largeur et la hauteur en fonction de l'image et de la taille de la page
        const imgWidth = 190; // Largeur d'une page A4 en mm avec une marge
        const pageHeight = 297; // Hauteur d'une page A4 en mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        let position = 0; // Position de départ sur la page

        // Ajoute l'image au PDF
        pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);

        // Si nécessaire, gère les pages multiples
        if (imgHeight > pageHeight) {
          let heightLeft = imgHeight - pageHeight;
          while (heightLeft > 0) {
            position = position - pageHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
          }
        }

        // Télécharge le PDF
        pdf.save('generated.pdf');
      });
    } else {
      console.error('Element with ID "pdfContent" not found!');
    }
  }




}
