<!-- SubHeader pour activites-list avec cascade intelligente -->
<div class="bg-white border-b border-gray-200 px-6 py-4">

  <!-- Section d'aide pour comprendre les filtres en cascade -->
  <div *ngIf="showHelpSection && !isSticky" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
    <div class="flex items-start gap-3">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
      </div>
      <div class="flex-1">
        <h4 class="text-sm font-medium text-blue-900 mb-1">Filtres en cascade avec persistance</h4>
        <p class="text-xs text-blue-800 leading-relaxed">
          Les filtres s'adaptent automatiquement aux <strong>périodes sélectionnées</strong> (P1, P2, P3).
          Seuls les praticiens, pôles et CRs ayant des activités dans ces périodes apparaissent dans les listes.
          <br>
          <span class="inline-flex items-center gap-1 mt-1">
            <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 111.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <em>Sélectionnez un praticien pour voir les pôles et CRs dans lesquels il est impliqué dans ces périodes</em>
          </span>
          <br>
          <span class="inline-flex items-center gap-1 mt-1">
            <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 111.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <em>Vous pouvez aussi filtrer individuellement par praticien, pôle, CR ou type de venue pour cibler vos recherches</em>
          </span>
        </p>
      </div>
      <div class="flex-shrink-0">
        <button
          type="button"
          (click)="closeHelpSection()"
          class="inline-flex items-center justify-center w-6 h-6 rounded-full hover:bg-blue-200 focus:outline-none focus:bg-blue-200 transition-colors"
          pTooltip="Masquer cette aide"
          tooltipPosition="left"
        >
          <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <div class="flex flex-wrap items-center justify-between gap-4">

    <!-- Filtres de recherche -->
    <div class="flex flex-wrap items-center gap-4 flex-1">

      <!-- Filtre Praticien -->
      <div class="flex flex-col min-w-0 flex-1 max-w-xs">
        <label for="practitioner-filter" class="block text-xs font-medium text-gray-700 mb-1 flex items-center gap-2">
          <span
            class="flex items-center gap-1 cursor-help"
            pTooltip="Les praticien ayant des actes dans ces périodes."
            tooltipPosition="top">
            Praticien
            <svg
              class="h-3 w-3 text-gray-400 hover:text-gray-600"
              fill="currentColor"
              viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </span>
          <span *ngIf="availablePractitioners.length > 0"
                class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{ availablePractitioners.length }} impliqués
          </span>
          <div *ngIf="availablePractitioners.length === 0"
               class="inline-flex items-center">
            <svg class="animate-spin h-3 w-3 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </label>
        <!-- Mode client-side (traditionnel) -->
        <p-autoComplete
          *ngIf="!useServerSideFilters"
          id="practitioner-filter"
          [(ngModel)]="selectedPractitioner"
          [suggestions]="practitionerSuggestions"
          (completeMethod)="onPractitionerSearch($event)"
          (onSelect)="onPractitionerSelect($event)"
          (onDropdownClick)="onPractitionerDropdownClick()"
          [forceSelection]="true"
          [dropdown]="true"
          [minLength]="1"
          field="displayName"
          placeholder="ex : Dr. Alex Fontaine"
          [style]="{'width':'100%'}"
          [inputStyle]="{'width':'100%', 'border-radius': '0.375rem', 'border-color': '#e5e7eb', 'padding': '0.375rem 0.75rem', 'font-size': '0.875rem'}"
          styleClass="w-full"
          [appendTo]="'body'"
        ></p-autoComplete>

        <!-- Mode server-side (cascade intelligente) -->
        <p-autoComplete
          *ngIf="useServerSideFilters"
          id="practitioner-filter-server"
          [(ngModel)]="selectedPractitioner"
          [suggestions]="practitionerSuggestions"
          (completeMethod)="onServerSidePractitionerSearch($event)"
          (onSelect)="onServerSidePractitionerSelect($event)"
          (onDropdownClick)="onServerSidePractitionerDropdownClick()"
          [forceSelection]="false"
          [dropdown]="true"
          [minLength]="0"
          field="displayName"
          placeholder="ex : Corentin Villiere"
          [style]="{'width':'100%'}"
          [inputStyle]="{'width':'100%', 'border-radius': '0.375rem', 'border-color': '#e5e7eb', 'padding': '0.375rem 0.75rem', 'font-size': '0.875rem'}"
          styleClass="w-full"
          [appendTo]="'body'"
          [disabled]="serverSideLoading"
        ></p-autoComplete>
      </div>

      <!-- Filtre Pôle -->
      <div class="flex flex-col min-w-0 flex-1 max-w-xs">
        <label for="pole-filter" class="block text-xs font-medium text-gray-700 mb-1 flex items-center gap-2">
          <span
            class="flex items-center gap-1 cursor-help"
            pTooltip="Les pôles impliqués dans ces périodes"
            tooltipPosition="top">
            Pôle
            <svg
              class="h-3 w-3 text-gray-400 hover:text-gray-600"
              fill="currentColor"
              viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </span>
          <span *ngIf="availablePoles.length > 0"
                class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {{ availablePoles.length }} impliqués
          </span>
          <div *ngIf="availablePoles.length === 0"
               class="inline-flex items-center">
            <svg class="animate-spin h-3 w-3 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </label>
        <!-- Mode client-side (traditionnel) -->
        <p-autoComplete
          *ngIf="!useServerSideFilters"
          id="pole-filter"
          [(ngModel)]="selectedPole"
          [suggestions]="poleSuggestions"
          (completeMethod)="onPoleSearch($event)"
          (onSelect)="onPoleSelect($event)"
          (onDropdownClick)="onPoleDropdownClick()"
          [forceSelection]="true"
          [dropdown]="true"
          [minLength]="1"
          field="libelle"
          placeholder="ex : Pôle Médecine"
          [style]="{'width':'100%'}"
          [inputStyle]="{'width':'100%', 'border-radius': '0.375rem', 'border-color': '#e5e7eb', 'padding': '0.375rem 0.75rem', 'font-size': '0.875rem'}"
          styleClass="w-full"
          [appendTo]="'body'"
          emptyMessage=""
        ></p-autoComplete>

        <!-- Mode server-side (cascade intelligente) -->
        <p-autoComplete
          *ngIf="useServerSideFilters"
          id="pole-filter-server"
          [(ngModel)]="selectedPole"
          [suggestions]="poleSuggestions"
          (completeMethod)="onServerSidePoleSearch($event)"
          (onSelect)="onServerSidePoleSelect($event)"
          (onDropdownClick)="onServerSidePoleDropdownClick()"
          [forceSelection]="false"
          [dropdown]="true"
          [minLength]="0"
          field="libelle"
          placeholder="ex : Pôle Médecine"
          [style]="{'width':'100%'}"
          [inputStyle]="{'width':'100%', 'border-radius': '0.375rem', 'border-color': '#e5e7eb', 'padding': '0.375rem 0.75rem', 'font-size': '0.875rem'}"
          styleClass="w-full"
          [appendTo]="'body'"
          [disabled]="serverSideLoading"
          emptyMessage=""
        >
          <!-- Template pour le message vide -->
          <ng-template pTemplate="empty">
            <div class="p-3 text-center">
              <div *ngIf="poleLoading; else noResults" class="flex items-center justify-center space-x-2">
                <i class="pi pi-spin pi-spinner text-blue-500"></i>
                <span class="text-sm text-gray-600">Chargement des pôles...</span>
              </div>
              <ng-template #noResults>
                <div class="text-sm text-gray-500">
                  <i class="pi pi-search text-gray-400 mr-2"></i>
                  Aucun pôle trouvé
                </div>
              </ng-template>
            </div>
          </ng-template>
        </p-autoComplete>
      </div>

      <!-- Filtre CR -->
      <div class="flex flex-col min-w-0 flex-1 max-w-xs">
        <label for="cr-filter" class="block text-xs font-medium text-gray-700 mb-1 flex items-center gap-2">
          <span
            class="flex items-center gap-1 cursor-help"
            pTooltip="Les CRs impliqués dans ces périodes"
            tooltipPosition="top">
            CR
            <svg
              class="h-3 w-3 text-gray-400 hover:text-gray-600"
              fill="currentColor"
              viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </span>
          <span *ngIf="availableCrs.length > 0"
                class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            {{ availableCrs.length }} impliqués
          </span>
          <div *ngIf="availableCrs.length === 0"
               class="inline-flex items-center">
            <svg class="animate-spin h-3 w-3 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </label>
        <!-- Mode client-side (traditionnel) -->
        <p-autoComplete
          *ngIf="!useServerSideFilters"
          id="cr-filter"
          [(ngModel)]="selectedCr"
          [suggestions]="crSuggestions"
          (completeMethod)="onCrSearch($event)"
          (onSelect)="onCrSelect($event)"
          (onDropdownClick)="onCrDropdownClick()"
          [forceSelection]="true"
          [dropdown]="true"
          [minLength]="1"
          field="libelle"
          placeholder="ex : Cardiologie"
          [style]="{'width':'100%'}"
          [inputStyle]="{'width':'100%', 'border-radius': '0.375rem', 'border-color': '#e5e7eb', 'padding': '0.375rem 0.75rem', 'font-size': '0.875rem'}"
          styleClass="w-full"
          [appendTo]="'body'"
          emptyMessage=""
        ></p-autoComplete>

        <!-- Mode server-side (cascade intelligente) -->
        <p-autoComplete
          *ngIf="useServerSideFilters"
          id="cr-filter-server"
          [(ngModel)]="selectedCr"
          [suggestions]="crSuggestions"
          (completeMethod)="onServerSideCrSearch($event)"
          (onSelect)="onServerSideCrSelect($event)"
          (onDropdownClick)="onServerSideCrDropdownClick()"
          [forceSelection]="false"
          [dropdown]="true"
          [minLength]="0"
          field="libelle"
          placeholder="ex : Cardiologie"
          [style]="{'width':'100%'}"
          [inputStyle]="{'width':'100%', 'border-radius': '0.375rem', 'border-color': '#e5e7eb', 'padding': '0.375rem 0.75rem', 'font-size': '0.875rem'}"
          styleClass="w-full"
          [appendTo]="'body'"
          [disabled]="serverSideLoading"
          emptyMessage=""
        >
          <!-- Template pour les éléments de la liste -->
          <ng-template let-cr pTemplate="item">
            <div class="flex items-center space-x-2 p-2">
              <span class="text-sm font-medium">{{ cr.crcode }}</span>
              <span class="text-sm text-gray-600">{{ cr.libelle }}</span>
            </div>
          </ng-template>

          <!-- Template pour le message vide -->
          <ng-template pTemplate="empty">
            <div class="p-3 text-center">
              <div *ngIf="crLoading; else noResults" class="flex items-center justify-center space-x-2">
                <i class="pi pi-spin pi-spinner text-blue-500"></i>
                <span class="text-sm text-gray-600">Chargement des CRs...</span>
              </div>
              <ng-template #noResults>
                <div class="text-sm text-gray-500">
                  <i class="pi pi-search text-gray-400 mr-2"></i>
                  Aucun CR trouvé
                </div>
              </ng-template>
            </div>
          </ng-template>
        </p-autoComplete>
      </div>

      <!-- Filtre Type de Venue -->
      <div class="flex flex-col min-w-0 max-w-48">
        <label for="venue-type-filter" class="block text-xs font-medium text-gray-700 mb-1">
          <span
            class="flex items-center gap-1 cursor-help"
            pTooltip="Filtrer par type de venue"
            tooltipPosition="top">
            Type de venue
            <svg
              class="h-3 w-3 text-gray-400 hover:text-gray-600"
              fill="currentColor"
              viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </span>
        </label>
        <p-dropdown
          id="venue-type-filter"
          [(ngModel)]="selectedVenueType"
          [options]="venueTypeOptions"
          (onChange)="onVenueTypeChange()"
          optionLabel="label"
          optionValue="value"
          placeholder="Sélectionner"
          [style]="{'width':'100%'}"
          [panelStyle]="{'width':'100%'}"
          styleClass="w-full"
          [appendTo]="'body'"
        ></p-dropdown>
      </div>
    </div>

    <!-- Bouton Clear Filters -->
    <div class="flex items-end">
      <button
        type="button"
        (click)="clearFilters()"
        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        pTooltip="Effacer tous les filtres"
        tooltipPosition="top"
      >
        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        Effacer
      </button>
    </div>
  </div>

  <!-- Indicateurs de filtres actifs -->
  <div class="mt-3 flex flex-wrap gap-2" *ngIf="selectedPractitioner || selectedPole || selectedCr || selectedVenueType">
    <div class="text-xs text-gray-500">Filtres actifs :</div>

    <span *ngIf="selectedPractitioner"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors">
      <span>Praticien: {{ getPractitionerDisplayName(selectedPractitioner) }}</span>
      <button
        type="button"
        (click)="clearPractitioner()"
        class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-300 focus:outline-none focus:bg-blue-300"
        pTooltip="Supprimer ce filtre"
        tooltipPosition="top">
        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    </span>

    <span *ngIf="selectedPole"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors">
      <span>Pôle: {{ getPoleDisplayName(selectedPole) }}</span>
      <button
        type="button"
        (click)="clearPole()"
        class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-green-300 focus:outline-none focus:bg-green-300"
        pTooltip="Supprimer ce filtre"
        tooltipPosition="top">
        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    </span>

    <span *ngIf="selectedCr"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors">
      <span>CR: {{ getCrDisplayName(selectedCr) }}</span>
      <button
        type="button"
        (click)="clearCr()"
        class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-purple-300 focus:outline-none focus:bg-purple-300"
        pTooltip="Supprimer ce filtre"
        tooltipPosition="top">
        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    </span>

    <span *ngIf="selectedVenueType"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 hover:bg-orange-200 transition-colors">
      <span>Type: {{ getVenueTypeLabelFromEnum(selectedVenueType) }}</span>
      <button
        type="button"
        (click)="clearVenueType()"
        class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-orange-300 focus:outline-none focus:bg-orange-300"
        pTooltip="Supprimer ce filtre"
        tooltipPosition="top">
        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    </span>
  </div>
</div>
