import { Injectable } from '@angular/core';
import { Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { LoginRequest } from '../../models/auth/login-request.model';
import { LoginResponse } from '../../models/auth/login-response.model';
import { EjResponse, Ej } from '../../models/auth/ej.model';
import { EmailLoginRequest, EmailLoginResponse } from '../../models/auth/email-login-request.model';
import { EmailVerifyRequest, EmailVerifyResponse } from '../../models/auth/email-verify-request.model';

// Interface pour le payload du token JWT
export interface TokenPayload {
  iss: string;
  aud: string;
  sub: string;
  iat: number;
  exp: number;
  nbf: number;
  jti: string;
  username: string;
  roles: string[];
  email: string;
  name: string | null;
  code_ej: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {

  private readonly TOKEN_KEY = 'auth-token'; // Clé utilisée dans le localStorage pour stocker le token
  redirectUrl: string | null = null; // Stocke l'URL vers laquelle rediriger après login

  constructor(private router: Router, private http: HttpClient) {
  }

  // Récupérer le token
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  // Sauvegarder le token dans le localStorage
  setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  // Supprimer le token (déconnexion)
  clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
  }

  // Vérifie si l'utilisateur est authentifié
  isAuthenticated(): boolean {
    return !!this.getToken(); // Retourne true si un token existe
  }

  // Décode le token JWT et retourne le payload
  decodeToken(): TokenPayload | null {
    const token = this.getToken();
    if (!token) {
      return null;
    }

    try {
      // Le token JWT est composé de 3 parties séparées par des points: header.payload.signature
      const payload = token.split('.')[1];
      // Décode la partie payload (base64)
      const decodedPayload = JSON.parse(atob(payload));
      return decodedPayload as TokenPayload;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  // Récupère le nom de l'utilisateur à partir du token
  getUserName(): string {
    const payload = this.decodeToken();
    if (payload && payload.name) {
      return payload.name;
    } else if (payload && payload.email) {
      // Si le nom n'est pas disponible, utiliser l'email comme fallback
      return payload.email;
    }
    return 'Utilisateur';
  }

  // Récupère l'email de l'utilisateur à partir du token
  getUserEmail(): string | null {
    const payload = this.decodeToken();
    return payload ? payload.email : null;
  }

  // Vérifie si l'utilisateur est un visiteur autorisé (ROLE_EMAIL_AUTH)
  isEmailAuthUser(): boolean {
    const payload = this.decodeToken();
    return payload ? payload.roles.includes('ROLE_EMAIL_AUTH') : false;
  }

  /**
   * Check if the current user has admin roles (ROLE_ADMIN, ROLE_FIFAP, or ROLE_CME)
   * @returns true if the user has any of the admin roles, false otherwise
   */
  hasAdminRole(): boolean {
    if (!this.isAuthenticated()) {
      return false;
    }

    const adminRoles = ['ROLE_ADMIN', 'ROLE_FIFAP', 'ROLE_CME'];
    const payload = this.decodeToken();

    if (!payload || !payload.roles) {
      return false;
    }

    return payload.roles.some(role => adminRoles.includes(role));
  }

  /**
   * Get the admin dashboard URL from environment
   * @returns the admin dashboard URL
   */
  getAdminDashboardUrl(): string {
    return environment.admin_dashboard_uri;
  }

  // Récupérer la liste des entités juridiques (hôpitaux)
  getEjs(): Observable<Ej[]> {
    return this.http.get<EjResponse>(`${environment.api_url}/api/ejs`).pipe(
      map(response => response.member),
      catchError(error => {
        console.error('Error fetching EJs:', error);
        return throwError(() => new Error('Failed to fetch hospital entities'));
      })
    );
  }

  // Méthode pour authentifier l'utilisateur avec l'API
  login(username: string, password: string, hopital_code: string): Observable<LoginResponse> {
    const loginData: LoginRequest = {
      username,
      password,
      hopital_code
    };

    return this.http.post<LoginResponse>(`${environment.api_url}/api/login-ad`, loginData).pipe(
      tap(response => {
        if (response.success && response.token) {
          this.setToken(response.token);
        }
      }),
      catchError(error => {
        console.error('Login error:', error);
        return throwError(() => new Error('Authentication failed. Please check your credentials.'));
      })
    );
  }

  // Méthode pour déconnecter l'utilisateur
  logout(): void {
    this.clearToken();
    this.router.navigateByUrl('');
  }

  // Méthode pour initier le processus de connexion par email
  loginWithEmail(email: string): Observable<EmailLoginResponse> {
    const loginData: EmailLoginRequest = {
      email
    };

    return this.http.post<EmailLoginResponse>(`${environment.api_url}/api/auth/email/login`, loginData).pipe(
      catchError(error => {
        console.error('Email login error:', error);
        return throwError(() => new Error('Failed to send verification code. Please try again.'));
      })
    );
  }

  // Méthode pour vérifier le code envoyé par email
  verifyEmailCode(email: string, code: string): Observable<EmailVerifyResponse> {
    const verifyData: EmailVerifyRequest = {
      email,
      code
    };

    return this.http.post<EmailVerifyResponse>(`${environment.api_url}/api/auth/email/verify`, verifyData).pipe(
      tap(response => {
        if (response.success && response.token) {
          this.setToken(response.token);
        }
      }),
      catchError(error => {
        console.error('Email verification error:', error);
        return throwError(() => new Error('Failed to verify code. Please check the code and try again.'));
      })
    );
  }
}
