import { Directive, Input, ElementRef, Renderer2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FeatureFlagService } from '../services/feature-flag.service';

@Directive({
  selector: '[appFeatureFlag]',
  standalone: true
})
export class FeatureFlagDirective implements OnInit, OnDestroy {
  @Input('appFeatureFlag') featureName!: string;
  @Input() disableMode: 'hide' | 'disable' | 'blur' = 'blur';

  private originalClasses: string[] = [];
  private originalStyle: string = '';

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private featureFlagService: FeatureFlagService
  ) {}

  ngOnInit() {
    this.applyFeatureFlag();
  }

  ngOnDestroy() {
    this.restoreOriginalState();
  }

  private applyFeatureFlag() {
    const isEnabled = this.featureFlagService.isFeatureEnabled(this.featureName);
    
    if (isEnabled) {
      return; // Feature activée, rien à faire
    }

    // Sauvegarder l'état original
    this.originalClasses = Array.from(this.el.nativeElement.classList);
    this.originalStyle = this.el.nativeElement.style.cssText;

    switch (this.disableMode) {
      case 'hide':
        this.hideElement();
        break;
      case 'disable':
        this.disableElement();
        break;
      case 'blur':
        this.blurElement();
        break;
    }
  }

  private hideElement() {
    this.renderer.setStyle(this.el.nativeElement, 'display', 'none');
  }

  private disableElement() {
    // Désactiver les interactions
    this.renderer.setStyle(this.el.nativeElement, 'pointer-events', 'none');
    this.renderer.setStyle(this.el.nativeElement, 'opacity', '0.5');
    this.renderer.setStyle(this.el.nativeElement, 'cursor', 'not-allowed');
    
    // Ajouter une classe pour le style
    this.renderer.addClass(this.el.nativeElement, 'feature-disabled');
    
    // Ajouter un titre explicatif
    const message = this.featureFlagService.getMaintenanceMessage(this.featureName);
    this.renderer.setAttribute(this.el.nativeElement, 'title', message);
  }

  private blurElement() {
    // Effet de flou et désactivation
    this.renderer.setStyle(this.el.nativeElement, 'filter', 'blur(1px)');
    this.renderer.setStyle(this.el.nativeElement, 'pointer-events', 'none');
    this.renderer.setStyle(this.el.nativeElement, 'opacity', '0.6');
    this.renderer.setStyle(this.el.nativeElement, 'cursor', 'not-allowed');
    this.renderer.setStyle(this.el.nativeElement, 'position', 'relative');
    
    // Ajouter une classe pour le style
    this.renderer.addClass(this.el.nativeElement, 'feature-blurred');
    
    // Ajouter un titre explicatif
    const message = this.featureFlagService.getMaintenanceMessage(this.featureName);
    this.renderer.setAttribute(this.el.nativeElement, 'title', message);
    
    // Ajouter un overlay avec icône "bientôt disponible"
    this.addComingSoonOverlay();
  }

  private addComingSoonOverlay() {
    const overlay = this.renderer.createElement('div');
    this.renderer.addClass(overlay, 'feature-coming-soon-overlay');
    
    // Styles pour l'overlay
    this.renderer.setStyle(overlay, 'position', 'absolute');
    this.renderer.setStyle(overlay, 'top', '0');
    this.renderer.setStyle(overlay, 'left', '0');
    this.renderer.setStyle(overlay, 'right', '0');
    this.renderer.setStyle(overlay, 'bottom', '0');
    this.renderer.setStyle(overlay, 'background', 'rgba(0, 0, 0, 0.1)');
    this.renderer.setStyle(overlay, 'display', 'flex');
    this.renderer.setStyle(overlay, 'align-items', 'center');
    this.renderer.setStyle(overlay, 'justify-content', 'center');
    this.renderer.setStyle(overlay, 'z-index', '10');
    this.renderer.setStyle(overlay, 'border-radius', '0.5rem');
    
    // Icône "bientôt disponible"
    const icon = this.renderer.createElement('i');
    this.renderer.addClass(icon, 'pi');
    this.renderer.addClass(icon, 'pi-clock');
    this.renderer.setStyle(icon, 'color', '#6b7280');
    this.renderer.setStyle(icon, 'font-size', '1.2rem');
    
    this.renderer.appendChild(overlay, icon);
    this.renderer.appendChild(this.el.nativeElement, overlay);
  }

  private restoreOriginalState() {
    // Restaurer les classes originales
    this.el.nativeElement.className = '';
    this.originalClasses.forEach(className => {
      this.renderer.addClass(this.el.nativeElement, className);
    });
    
    // Restaurer le style original
    this.el.nativeElement.style.cssText = this.originalStyle;
    
    // Supprimer les attributs ajoutés
    this.renderer.removeAttribute(this.el.nativeElement, 'title');
    
    // Supprimer l'overlay s'il existe
    const overlay = this.el.nativeElement.querySelector('.feature-coming-soon-overlay');
    if (overlay) {
      this.renderer.removeChild(this.el.nativeElement, overlay);
    }
  }
}
