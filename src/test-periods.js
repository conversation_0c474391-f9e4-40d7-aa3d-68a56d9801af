// Test script to verify period calculations
// This is just a conceptual test, not meant to be run directly

// Simulate selecting a date range
const dateDebut = new Date(2024, 9, 1); // October 1, 2024
const dateFin = new Date(2025, 2, 31); // March 31, 2025

console.log('Original dates:');
console.log('dateDebut:', dateDebut.toISOString());
console.log('dateFin:', dateFin.toISOString());

// Create p1 (current period)
const p1 = {
  dateDebut: new Date(dateDebut),
  dateFin: new Date(dateFin)
};

// Create p2 (previous year)
const p2DateDebut = new Date(p1.dateDebut);
p2DateDebut.setFullYear(p2DateDebut.getFullYear() - 1);

const p2DateFin = new Date(p1.dateFin);
p2DateFin.setFullYear(p2DateFin.getFullYear() - 1);

const p2 = {
  dateDebut: p2DateDebut,
  dateFin: p2DateFin
};

// Create p3 (two years ago)
const p3DateDebut = new Date(p1.dateDebut);
p3DateDebut.setFullYear(p3DateDebut.getFullYear() - 2);

const p3DateFin = new Date(p1.dateFin);
p3DateFin.setFullYear(p3DateFin.getFullYear() - 2);

const p3 = {
  dateDebut: p3DateDebut,
  dateFin: p3DateFin
};

console.log('\nCalculated periods:');
console.log('p1:', p1.dateDebut.toISOString(), 'to', p1.dateFin.toISOString());
console.log('p2:', p2.dateDebut.toISOString(), 'to', p2.dateFin.toISOString());
console.log('p3:', p3.dateDebut.toISOString(), 'to', p3.dateFin.toISOString());

// Expected results:
// p1: 2024-10-01 to 2025-03-31
// p2: 2023-10-01 to 2024-03-31
// p3: 2022-10-01 to 2023-03-31
