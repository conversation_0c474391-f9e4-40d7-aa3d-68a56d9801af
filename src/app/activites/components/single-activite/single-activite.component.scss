/* Custom styles for single-activite component */

:host ::ng-deep {
  .p-card {
    .p-card-header {
      padding: 0;
    }

    .p-card-body {
      padding: 1.25rem;
    }

    .p-card-content {
      padding: 0;
    }
  }

  .p-tabview {
    .p-tabview-nav {
      border-width: 0 0 1px 0;

      li {
        margin-right: 0.5rem;

        .p-tabview-nav-link {
          padding: 0.75rem 1rem;
          font-weight: 500;
          border-width: 0 0 2px 0;
          transition: all 0.2s;

          &:not(.p-disabled):focus {
            box-shadow: none;
            border-color: #e2e8f0;
          }
        }

        &.p-highlight .p-tabview-nav-link {
          border-color: #0ea5e9;
          color: #0ea5e9;
        }
      }
    }

    .p-tabview-panels {
      padding: 1.25rem 0 0 0;
    }
  }

  .p-button.p-button-secondary {
    background: #f1f5f9;
    border-color: #f1f5f9;
    color: #334155;

    &:hover {
      background: #e2e8f0;
      border-color: #e2e8f0;
      color: #1e293b;
    }
  }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  :host ::ng-deep {
    .p-card .p-card-body {
      padding: 1rem;
    }

    .p-tabview .p-tabview-nav li .p-tabview-nav-link {
      padding: 0.5rem 0.75rem;
    }
  }
}
