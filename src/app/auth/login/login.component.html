<!-- <PERSON>ond animé, toujours derrière tout le reste -->
<app-particles></app-particles>

<div class="min-h-screen flex items-center justify-center relative p-4">
  <div class="w-full max-w-md p-8 rounded-lg shadow-lg relative z-10"
       style="background: rgba(255,255,255,0.8); backdrop-filter: blur(12px); border: 1px solid rgba(255,255,255,0.2); box-shadow: 0 15px 30px rgba(0,0,0,0.3);">
    <!-- Titre -->
    <h2 class="text-2xl font-bold text-gray-800 text-center mb-6 leading-relaxed">Connexion Supra</h2>

    <!-- Message d'erreur -->
    <div *ngIf="errorMessage" class="bg-red-100 text-red-800 text-sm rounded-md p-3 mb-4 leading-relaxed">
      {{ errorMessage }}
    </div>

    <!-- Message de succès -->
    <div *ngIf="successMessage" class="bg-green-100 text-green-800 text-sm rounded-md p-3 mb-4 leading-relaxed">
      {{ successMessage }}
    </div>

    <!-- Système d'onglets -->
    <p-tabView [(activeIndex)]="activeTabIndex" (onChange)="onTabChange($event)" styleClass="login-tabs">

      <!-- Onglet 1: Connexion classique -->
      <p-tabPanel header="Connexion Windows">
        <ng-template pTemplate="header">
          <div class="flex align-items-center">
            <i class="pi pi-desktop mr-2"></i>
            <span>Connexion Windows</span>
          </div>
        </ng-template>
        <!-- Sélecteur pour l'Entité Juridique -->
        <div class="mb-4">
          <label for="hopital" class="block text-sm font-medium text-gray-800 mb-1 leading-relaxed">
            Entité Juridique <span class="text-red-500">*</span>
          </label>

          <div class="relative">
            <select
              id="hopital"
              [(ngModel)]="selectedHopitalCode"
              class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 placeholder-gray-400 leading-relaxed"
              [disabled]="loading || ejOptions.length === 0"
              aria-required="true"
            >
              <option *ngFor="let option of ejOptions" [value]="option.value">{{ option.label }}</option>
            </select>

            <!-- Icône de bâtiment -->
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Identifiant -->
        <div class="mb-4">
          <label for="username" class="block text-sm font-medium text-gray-800 mb-1 leading-relaxed">
            Identifiant <span class="text-red-500">*</span>
          </label>

          <input
            type="text"
            id="username"
            [(ngModel)]="username"
            placeholder="u123456"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 placeholder-gray-400 leading-relaxed"
            [disabled]="loading"
            aria-required="true"
            aria-label="Identifiant"
          />
        </div>

        <!-- Mot de Passe -->
        <div class="mb-6">
          <label for="password" class="block text-sm font-medium text-gray-800 mb-1 leading-relaxed">
            Mot de passe Windows <span class="text-red-500">*</span>
          </label>

          <input
            type="password"
            id="password"
            [(ngModel)]="password"
            placeholder="********"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 placeholder-gray-400 leading-relaxed"
            [disabled]="loading"
            aria-required="true"
            aria-label="Mot de passe Windows"
          />
        </div>

        <!-- Bouton de Connexion -->
        <button
          type="button"
          (click)="onSubmit()"
          class="w-full bg-gradient-to-r from-cyan-600 to-cyan-500 text-white py-3 px-4 rounded-md hover:from-cyan-500 hover:to-cyan-400 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 transition duration-300 flex items-center justify-center leading-relaxed font-medium shadow-md"
          [disabled]="loading"
          aria-label="Se connecter"
        >
          <span *ngIf="loading" class="mr-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          <i *ngIf="!loading" class="pi pi-sign-in mr-2"></i>
          {{ loading ? 'Connexion en cours...' : 'Connexion' }}
        </button>
      </p-tabPanel>

      <!-- Onglet 2: Connexion par email -->
      <p-tabPanel header="Connexion par email">
        <ng-template pTemplate="header">
          <div class="flex align-items-center">
            <i class="pi pi-envelope mr-2"></i>
            <span>Connexion par email</span>
          </div>
        </ng-template>
        <div class="mb-5 p-3 bg-blue-50 rounded-lg border border-blue-100 text-sm text-gray-700">
          <div class="flex items-start mb-2">
            <i class="pi pi-info-circle text-blue-500 mr-2 mt-0.5"></i>
            <span>Saisissez votre adresse email professionnelle pour recevoir un code de vérification.</span>
          </div>
          <div class="text-xs text-gray-500 ml-5">
            Exemples de domaines autorisés: <span class="font-medium">{{ allowedDomains.join(', ') }}</span>
          </div>
        </div>

        <!-- Email -->
        <div class="mb-6">
          <label for="email" class="block text-sm font-medium text-gray-800 mb-1 leading-relaxed">
            Adresse email <span class="text-red-500">*</span>
          </label>

          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="pi pi-envelope text-gray-400"></i>
            </div>
            <input
              type="email"
              id="email"
              [(ngModel)]="email"
              placeholder="<EMAIL>"
              class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 placeholder-gray-400 leading-relaxed transition-all duration-200 hover:border-gray-400"
              [disabled]="loading"
              aria-required="true"
              aria-label="Adresse email"
            />
          </div>
        </div>

        <!-- Bouton d'envoi du code -->
        <button
          type="button"
          (click)="onEmailSubmit()"
          class="w-full bg-gradient-to-r from-cyan-600 to-cyan-500 text-white py-3 px-4 rounded-md hover:from-cyan-500 hover:to-cyan-400 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 transition duration-300 flex items-center justify-center leading-relaxed font-medium shadow-md"
          [disabled]="loading"
          aria-label="Envoyer le code"
        >
          <span *ngIf="loading" class="mr-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          <i *ngIf="!loading" class="pi pi-send mr-2"></i>
          {{ loading ? 'Envoi en cours...' : 'Envoyer le code de vérification' }}
        </button>
      </p-tabPanel>
    </p-tabView>

    <!-- Modal de vérification du code -->
    <p-dialog
      [(visible)]="showVerificationModal"
      [modal]="true"
      [closable]="false"
      [draggable]="false"
      [resizable]="false"
      styleClass="p-fluid"
      [style]="{width: '90%', maxWidth: '400px'}"
      header="Vérification du code"
    >
      <div class="mb-5 p-4 bg-blue-50 rounded-lg border border-blue-100 text-sm text-gray-700">
        <div class="flex items-start">
          <i class="pi pi-envelope-open text-blue-500 mr-2 mt-0.5 text-lg"></i>
          <div>
            <p class="mb-2">Un code de vérification a été envoyé à <strong class="text-blue-700">{{ email }}</strong></p>
            <p>Veuillez saisir ce code ci-dessous.</p>

            <div class="mt-3 flex items-center text-amber-600 font-medium bg-amber-50 p-2 rounded border border-amber-100">
              <i class="pi pi-clock mr-2"></i>
              <span>Temps restant: {{ formatTime() }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Champ de code -->
      <div class="mb-5">
        <label for="verificationCode" class="block text-sm font-medium text-gray-800 mb-2 leading-relaxed">
          Code de vérification <span class="text-red-500">*</span>
        </label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i class="pi pi-lock text-gray-400"></i>
          </div>
          <input
            type="text"
            id="verificationCode"
            [(ngModel)]="verificationCode"
            placeholder="Saisissez le code à 4 chiffres"
            class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-cyan-400 placeholder-gray-400 leading-relaxed text-lg tracking-wider text-center font-medium transition-all duration-200 hover:border-gray-400"
            [disabled]="loading"
            aria-required="true"
            aria-label="Code de vérification"
            maxlength="4"
          />
        </div>
      </div>

      <!-- Message d'erreur dans le modal -->
      <div *ngIf="errorMessage" class="bg-red-100 text-red-800 text-sm rounded-md p-3 mb-4 leading-relaxed">
        {{ errorMessage }}
      </div>

      <div class="flex justify-between mt-6">
        <button
          type="button"
          (click)="closeVerificationModal()"
          class="bg-gray-100 text-gray-700 py-2.5 px-5 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 transition duration-300 flex items-center justify-center leading-relaxed font-medium border border-gray-300 shadow-sm"
          [disabled]="loading"
        >
          <i class="pi pi-times mr-2"></i>
          Annuler
        </button>

        <button
          type="button"
          (click)="onVerifyCode()"
          class="bg-gradient-to-r from-cyan-600 to-cyan-500 text-white py-2.5 px-5 rounded-md hover:from-cyan-500 hover:to-cyan-400 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 transition duration-300 flex items-center justify-center leading-relaxed font-medium shadow-md"
          [disabled]="loading"
        >
          <span *ngIf="loading" class="mr-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          <i *ngIf="!loading" class="pi pi-check mr-2"></i>
          {{ loading ? 'Vérification...' : 'Vérifier le code' }}
        </button>
      </div>
    </p-dialog>
  </div>
</div>
