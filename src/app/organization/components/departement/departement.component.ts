import {Component, OnInit} from '@angular/core';
import {Department} from "../../../core/models/organization/Department.model";
import {DepartementService} from "../../../core/services/organization/DepartmentService";
import {Ng<PERSON>orOf, NgIf} from "@angular/common";

@Component({
  selector: 'app-departement',
  standalone: true,
  imports: [
    NgIf,
    NgForOf
  ],
  templateUrl: './departement.component.html',
  styleUrl: './departement.component.scss'
})
export class DepartementComponent implements OnInit {
  departments: Department[] = [];

  constructor(private departmentService: DepartementService) {}

  ngOnInit(): void {
    this.departmentService.departements$.subscribe((data) => {
      this.departments = data;
    });
  }


}
