import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {GardeAstreinteData} from "../models/garde-astreinte.model";

@Injectable({
  providedIn: 'root',
})
export class GardeAstreinteService {
  private gardeAstreinteUrl = './assets/data/garde-astreinte.json'; // Chemin du fichier JSON
  private gardeAstreinteSubject = new BehaviorSubject<GardeAstreinteData | null>(null);

  constructor(private http: HttpClient) {}

  /**
   * Charger les données initiales de garde et astreinte depuis un fichier JSON.
   */
  loadInitialData(): Observable<GardeAstreinteData> {
    return this.http.get<GardeAstreinteData>(this.gardeAstreinteUrl).pipe(
      tap((data) => this.gardeAstreinteSubject.next(data)),
      catchError((error) => {
        console.error('Erreur lors du chargement des données de garde et astreinte :', error);
        this.gardeAstreinteSubject.next(null);
        throw error;
      })
    );
  }

  /**
   * Récupérer les données de garde et astreinte sous forme d'observable.
   */
  getGardeAstreinteData(): Observable<GardeAstreinteData | null> {
    return this.gardeAstreinteSubject.asObservable();
  }
}
