{"name": "supra-v2", "version": "0.0.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "storybook": "ng run supra:storybook", "build-storybook": "ng run supra:build-storybook", "compodoc": "npx compodoc -p tsconfig.compodoc.json --theme readthedocs -s"}, "private": true, "dependencies": {"@angular/animations": "^18.0.3", "@angular/common": "^18.0.3", "@angular/compiler": "^18.0.3", "@angular/core": "^18.0.3", "@angular/forms": "^18.0.3", "@angular/platform-browser": "^18.0.3", "@angular/platform-browser-dynamic": "^18.0.3", "@angular/router": "^18.0.3", "@tailwindcss/forms": "^0.5.9", "ace-builds": "^1.37.5", "chart.js": "^4.4.4", "fuse.js": "^7.1.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "monaco-editor": "^0.52.2", "ngx-ace-wrapper": "^17.0.0", "ngx-papaparse": "^8.0.0", "primeicons": "^7.0.0", "primeng": "^17.18.11", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.3", "@angular/cli": "^18.0.3", "@angular/compiler-cli": "^18.0.3", "@chromatic-com/storybook": "^3.2.2", "@compodoc/compodoc": "^1.1.26", "@storybook/addon-docs": "^8.4.7", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-onboarding": "^8.4.7", "@storybook/angular": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/test": "^8.4.7", "@types/chart.js": "^2.9.41", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "cypress": "^13.15.2", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.47", "storybook": "^8.4.7", "tailwindcss": "^3.4.14", "typescript": "~5.4.2"}}