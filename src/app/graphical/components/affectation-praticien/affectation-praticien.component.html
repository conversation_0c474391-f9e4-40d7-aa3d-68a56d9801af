
<div class="bg-white shadow">
  <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

  <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">
    <app-resume-profils></app-resume-profils>
  </div>
</div>

<!--<app-overview></app-overview>-->
<!--DEBUT TABLEAU COMPARATIVE -->
<section class="lg:border-t">
  <p-table
    [value]="praticienData"
    [paginator]="true"
    [rows]="5"
    [rowsPerPageOptions]="[5, 10, 20]"
    responsiveLayout="scroll"
  >
    <ng-template pTemplate="header">
      <tr>
        <th pSortableColumn="uf">UF <p-sortIcon field="uf"></p-sortIcon></th>
        <th pSortableColumn="repartition">Répartition <p-sortIcon field="repartition"></p-sortIcon></th>
        <th>Répartition Réelle</th>
        <th pSortableColumn="effectifPraticienDansUF">Effectif Praticien dans UF <p-sortIcon field="effectifPraticienDansUF"></p-sortIcon></th>
        <th pSortableColumn="effectifUF">Effectif UF <p-sortIcon field="effectifUF"></p-sortIcon></th>
        <th pSortableColumn="partPraticienDansUF">Part du Praticien dans UF <p-sortIcon field="partPraticienDansUF"></p-sortIcon></th>
        <th>Part Réelle</th>
        <th>Entrée</th>
        <th pSortableColumn="sortie">Sortie <p-sortIcon field="sortie"></p-sortIcon></th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-uf>
      <tr>
        <td>{{ uf.uf }}</td>
        <td>{{ uf.repartition }}</td>
        <td>
          <span class="block text-xs text-gray-500">CCAM: {{ uf.repartitionReelle.ccam }}</span>
          <span class="block text-xs text-gray-500">NGAP: {{ uf.repartitionReelle.ngap }}</span>
          <span class="block text-xs text-gray-500">LABO: {{ uf.repartitionReelle.labo }}</span>
        </td>
        <td>{{ uf.effectifPraticienDansUF }}</td>
        <td>{{ uf.effectifUF }}</td>
        <td>{{ uf.partPraticienDansUF }}</td>
        <td>
          <span class="block text-xs text-gray-500">CCAM: {{ uf.partReelle.ccam }}</span>
          <span class="block text-xs text-gray-500">NGAP: {{ uf.partReelle.ngap }}</span>
          <span class="block text-xs text-gray-500">LABO: {{ uf.partReelle.labo }}</span>
        </td>
        <td>{{ uf.entree }}</td>
        <td>{{ uf.sortie }}</td>
      </tr>
    </ng-template>
  </p-table>
</section>


<section class="mt-8 ">
<div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">
  <ol class="flex items-center w-full text-sm text-gray-500 font-medium sm:text-base">
    <!-- Step 1: CCAM -->
    <li

      class="flex md:w-full items-center text-indigo-600 sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8"
    >
      <div
        class="flex items-center whitespace-nowrap after:content-['/'] sm:after:hidden after:mx-2 cursor-pointer"
        (click)="showTable('CCAM')"
      >
      <span
        [class.bg-indigo-600]="activeTable === 'CCAM'"
        [class.text-white]="activeTable === 'CCAM'"
        [class.bg-gray-100]="activeTable !== 'CCAM'"
        [class.text-gray-600]="activeTable !== 'CCAM'"
        class="w-6 h-6  border border-indigo-200 rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10"
      >1</span
      >
        CCAM
      </div>
    </li>

    <!-- Step 2: NGAP -->
    <li
      class="flex md:w-full items-center text-indigo-600 sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8"
    >
      <div
        class="flex items-center whitespace-nowrap after:content-['/'] sm:after:hidden after:mx-2 cursor-pointer"
        (click)="showTable('NGAP')"
      >
      <span
        [class.bg-indigo-600]="activeTable === 'NGAP'"
        [class.text-white]="activeTable === 'NGAP'"
        [class.bg-gray-100]="activeTable !== 'NGAP'"
        [class.text-gray-600]="activeTable !== 'NGAP'"
        class="w-6 h-6 bg-gray-100 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10"
      >2</span
      >
        NGAP
      </div>
    </li>

    <!-- Step 3: LABO -->
    <li class="flex md:w-full items-center text-indigo-600">
      <div
        class="flex items-center cursor-pointer"
        (click)="showTable('LABO')"
      >
      <span
        [class.bg-indigo-600]="activeTable === 'LABO'"
        [class.text-white]="activeTable === 'LABO'"
        [class.bg-gray-100]="activeTable !== 'LABO'"
        [class.text-gray-600]="activeTable !== 'LABO'"
        class="w-6 h-6   border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10"
      >3</span
      >
        LABO
      </div>
    </li>
  </ol>
</div>
  <div *ngIf="activeTable === 'CCAM'" class="p-4">
    <h5 class="text-lg font-bold mb-3">
      Analyse comparative des actes CCAM [ {{anneeNmoinsUn}}-{{ anneeN }} ]
    </h5>
    <div>
      <div class="relative mt-2 rounded-md shadow-sm">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
        </div>
        <input
          type="text" name="price"
          (input)="ccamTableParActeParAnnee.filterGlobal(onInput($event), 'contains')"
          class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
          placeholder="  Filtrer par ccam"
        />
      </div>
    </div>
    <p-table
      #ccamTableParActeParAnnee
      [value]="ccamData"
      [scrollable]="true"
      scrollHeight="400px"
      [tableStyle]="{'min-width': '50rem'}"
      [globalFilterFields]="['code', 'description','totalAnneeNMoins1','totalAnneeN']"
    >
      <ng-template pTemplate="header">
        <tr class="border-b hover:bg-gray-50">
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>
          <th pSortableColumn="code">
            Code
            <p-sortIcon field="code"></p-sortIcon>
          </th>
          <th pSortableColumn="description">
            Description
            <p-sortIcon field="description"></p-sortIcon>
          </th>
          <th  pSortableColumn="totalAnneeNmoinsUn" >
            Total actes {{ anneeNmoinsUn }}
            <p-sortIcon field="totalAnneeNmoinsUn"></p-sortIcon>
          </th>
          <th  pSortableColumn="totalAnneeN" >
            Total actes {{ anneeN }}
            <p-sortIcon field="totalAnneeN"></p-sortIcon>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-acte>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                title="Voir les détails de {{acte.code }} {{acte.description}}"
                (click)="viewActeDetails(acte)"
              ></i>
            </p>
          </td>
          <td>{{acte.code}}</td>
          <td>{{acte.description}}</td>
          <td> {{acte.totalAnneeNmoinsUn}}  </td>
          <td> {{acte.totalAnneeN}}  </td>
        </tr>
      </ng-template>
      <!-- Ligne de total -->
      <ng-template pTemplate="footer">
        <tr  >
          <td colspan="3" class="font-bold text-left">TOTAL</td>
          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeNmoinsUn,'CCAM')" severity="info" styleClass="ml-2"></p-badge></td>
          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeN,'CCAM')" severity="info" styleClass="ml-3"></p-badge></td>
          <td></td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div *ngIf="activeTable === 'NGAP'" class="p-4">
    <h5 class="text-lg font-bold mb-3">
      Analyse comparative des actes NGAP [ {{anneeNmoinsUn}}-{{ anneeN }} ]
    </h5>
    <div>
      <div class="relative mt-2 rounded-md shadow-sm">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
        </div>
        <input
          type="text" name="price"
          (input)="ngapTableParActeParAnnee.filterGlobal(onInput($event), 'contains')"
          class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
          placeholder="  Filtrer par ngap"
        />
      </div>
    </div>
    <p-table
      #ngapTableParActeParAnnee
      [value]="ngapData"
      [scrollable]="true"
      scrollHeight="400px"
      [tableStyle]="{'min-width': '50rem'}"
      [globalFilterFields]="['code', 'description','totalAnneeNMoins1','totalAnneeN']"
    >
      <ng-template pTemplate="header">
        <tr class="border-b hover:bg-gray-50">
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>
          <th pSortableColumn="code">
            Code
            <p-sortIcon field="code"></p-sortIcon>
          </th>
          <th pSortableColumn="description">
            Description
            <p-sortIcon field="description"></p-sortIcon>
          </th>
          <th  pSortableColumn="totalAnneeNmoinsUn" >
            Total actes {{ anneeNmoinsUn }}
            <p-sortIcon field="totalAnneeNmoinsUn"></p-sortIcon>
          </th>
          <th  pSortableColumn="totalAnneeN" >
            Total actes {{ anneeN }}
            <p-sortIcon field="totalAnneeN"></p-sortIcon>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-acte>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                title="Voir les détails de {{acte.code }} {{acte.description}}"
                (click)="viewActeDetails(acte)"
              ></i>
            </p>
          </td>
          <td>{{acte.code}}</td>
          <td>{{acte.description}}</td>
          <td> {{acte.totalAnneeNmoinsUn}}  </td>
          <td> {{acte.totalAnneeN}}  </td>
        </tr>
      </ng-template>
      <!-- Ligne de total -->
      <ng-template pTemplate="footer">
        <tr  >
          <td colspan="3" class="font-bold text-left">TOTAL</td>
          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeNmoinsUn,'NGAP')" severity="info" styleClass="ml-2"></p-badge></td>
          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeN,'NGAP')" severity="info" styleClass="ml-3"></p-badge></td>
          <td></td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <div *ngIf="activeTable === 'LABO'" class="p-4">
    <h5 class="text-lg font-bold mb-3">
      Analyse comparative des actes LABO [ {{anneeNmoinsUn}}-{{ anneeN }} ]
    </h5>
    <div>
      <div class="relative mt-2 rounded-md shadow-sm">
        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
        </div>
        <input
          type="text" name="price"
          (input)="laboTableParActeParAnnee.filterGlobal(onInput($event), 'contains')"
          class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
          placeholder="  Filtrer par nabm"
        />
      </div>
    </div>
    <p-table
      #laboTableParActeParAnnee
      [value]="laboData"
      [scrollable]="true"
      scrollHeight="400px"
      [tableStyle]="{'min-width': '50rem'}"
      [globalFilterFields]="['code', 'description','totalAnneeNmoinsUn','totalAnneeN']"
    >
      <ng-template pTemplate="header">
        <tr class="border-b hover:bg-gray-50">
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>
          <th pSortableColumn="code">
            Code
            <p-sortIcon field="code"></p-sortIcon>
          </th>
          <th pSortableColumn="description">
            Description
            <p-sortIcon field="description"></p-sortIcon>
          </th>
          <th  pSortableColumn="totalAnneeNmoinsUn" >
            Total actes {{ anneeNmoinsUn }}
            <p-sortIcon field="totalAnneeNmoinsUn"></p-sortIcon>
          </th>
          <th  pSortableColumn="totalAnneeN" >
            Total actes {{ anneeN }}
            <p-sortIcon field="totalAnneeN"></p-sortIcon>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-acte>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                title="Voir les détails de {{acte.code }} {{acte.description}}"
                (click)="viewActeDetails(acte)"
              ></i>
            </p>
          </td>
          <td>{{acte.code}}</td>
          <td>{{acte.description}}</td>
          <td> {{acte.totalAnneeNmoinsUn}}  </td>
          <td> {{acte.totalAnneeN}}  </td>
        </tr>
      </ng-template>
      <!-- Ligne de total -->
      <ng-template pTemplate="footer">
        <tr  >
          <td colspan="3" class="font-bold text-left">TOTAL</td>
          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeNmoinsUn,'Labo')" severity="info" styleClass="ml-2"></p-badge></td>
          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeN,'Labo')" severity="info" styleClass="ml-3"></p-badge></td>
          <td></td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</section>

<!--DEBUT GRAPHIQUE-->
<section>
  <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">
    <ol class="flex items-center w-full text-sm text-gray-500 font-medium sm:text-base">
      <!-- Step 1: CCAM -->
      <li

        class="flex md:w-full items-center text-indigo-600 sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8"
      >
        <div
          class="flex items-center whitespace-nowrap after:content-['/'] sm:after:hidden after:mx-2 cursor-pointer"
          (click)="showGraphe('CCAMGRAPHE')"
        >
      <span
        [class.bg-indigo-600]="activeGraphe === 'CCAMGRAPHE'"
        [class.text-white]="activeGraphe === 'CCAMGRAPHE'"
        [class.bg-gray-100]="activeGraphe !== 'CCAMGRAPHE'"
        [class.text-gray-600]="activeGraphe !== 'CCAMGRAPHE'"
        class="w-6 h-6  border border-indigo-200 rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10"
      >1</span
      >
          CCAM
        </div>
      </li>

      <!-- Step 2: NGAP -->
      <li
        class="flex md:w-full items-center text-indigo-600 sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8"
      >
        <div
          class="flex items-center whitespace-nowrap after:content-['/'] sm:after:hidden after:mx-2 cursor-pointer"
          (click)="showGraphe('NGAPGRAPHE')"
        >
      <span
        [class.bg-indigo-600]="activeGraphe === 'NGAPGRAPHE'"
        [class.text-white]="activeGraphe === 'NGAPGRAPHE'"
        [class.bg-gray-100]="activeGraphe !== 'NGAPGRAPHE'"
        [class.text-gray-600]="activeGraphe !== 'NGAPGRAPHE'"
        class="w-6 h-6 bg-gray-100 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10"
      >2</span
      >
          NGAP
        </div>
      </li>

      <!-- Step 3: LABO -->
      <li class="flex md:w-full items-center text-indigo-600">
        <div
          class="flex items-center cursor-pointer"
          (click)="showGraphe('LABOGRAPHE')"
        >
      <span
        [class.bg-indigo-600]="activeGraphe === 'LABOGRAPHE'"
        [class.text-white]="activeGraphe === 'LABOGRAPHE'"
        [class.bg-gray-100]="activeGraphe !== 'LABOGRAPHE'"
        [class.text-gray-600]="activeGraphe !== 'LABOGRAPHE'"
        class="w-6 h-6   border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10"
      >3</span
      >
          LABO
        </div>
      </li>
    </ol>
  </div>
  <section  *ngIf="activeGraphe === 'CCAMGRAPHE'"  class="mb-8">

    <!-- Graphiques et tableaux -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Line Chart pour l'évolution mensuelle -->
      <div class="col-span-8 bg-white p-4 shadow rounded-lg relative">
        <h5 class="text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center">
          Évolution mensuelle des actes CCAM
          <button
            class="text-blue-500 hover:underline text-sm flex items-center"
            (click)="showDialog('evolutionMensuelleCCAM')"
          >
            <i class="pi pi-expand mr-2"></i> Agrandir
          </button>
        </h5>
        <p-chart
          type="line"
          [data]="ccamLineChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-64"
        ></p-chart>
      </div>
      <div class="col-span-4 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Données mensuelles</h5>
        <ul>
          <li *ngFor="let item of ccamMonthlyTableData" class="flex justify-between py-2 border-b">
            <span>{{ item.mois }}</span>
            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>
          </li>
        </ul>
      </div>

      <!-- Bar Chart pour la répartition hebdomadaire -->
      <div class="col-span-8 bg-white p-4 shadow rounded-lg relative">
        <h5 class="text-lg font-bold text-gray-700 mb-3 flex justify-between items-center">
          Répartition hebdomadaire
          <button
            class="text-blue-500 hover:underline text-sm flex items-center"
            (click)="showDialog('repartitionHebdomadaireCCAM')"
          >
            <i class="pi pi-expand mr-2"></i> Agrandir
          </button>
        </h5>
        <p-chart
          type="bar"
          [data]="ccamBarChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-64"
        ></p-chart>
      </div>

      <div class="col-span-4 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Données hebdomadaires</h5>
        <ul>
          <li *ngFor="let item of ccamWeeklyTableData" class="flex justify-between py-2 border-b">
            <span>{{ item.semaine }}</span>
            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Dialog pour Évolution mensuelle -->
    <p-dialog
      [(visible)]="dialogState.evolutionMensuelleCCAM"
      [modal]="true"
      [header]="'Évolution mensuelle des actes CCAM'"
      [style]="{ width: '80vw' }"
      [closable]="true"
      [dismissableMask]="true"
     >
      <div class="p-4">
        <p-chart
          type="line"
          [data]="ccamLineChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-96"
        ></p-chart>
      </div>
    </p-dialog>

    <!-- Dialog pour Répartition hebdomadaire -->
    <p-dialog
      [(visible)]="dialogState.repartitionHebdomadaireCCAM"
      [modal]="true"
      [header]="'Répartition hebdomadaire des actes CCAM'"
      [style]="{ width: '80vw' }"
      [closable]="true"
      [dismissableMask]="true"
    >
      <div class="p-4">
        <p-chart
          type="bar"
          [data]="ccamBarChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-96"
        ></p-chart>
      </div>
    </p-dialog>
  </section>
  <section *ngIf="activeGraphe === 'NGAPGRAPHE'" class="mb-8">

    <!-- Graphiques et tableaux -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Line Chart pour l'évolution mensuelle -->
      <div class="col-span-8 bg-white p-4 shadow rounded-lg relative">
        <h5 class="text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center">
          Évolution mensuelle des actes NGAP
          <button
            class="text-blue-500 hover:underline text-sm flex items-center"
            (click)="showDialog('evolutionMensuelle')"
          >
            <i class="pi pi-expand mr-2"></i> Agrandir
          </button>
        </h5>
        <p-chart
          type="line"
          [data]="ngapLineChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-64"
        ></p-chart>
      </div>
      <div class="col-span-4 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Données mensuelles</h5>
        <ul>
          <li *ngFor="let item of ngapMonthlyTableData" class="flex justify-between py-2 border-b">
            <span>{{ item.mois }}</span>
            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>
          </li>
        </ul>
      </div>

      <!-- Bar Chart pour la répartition hebdomadaire -->
      <div class="col-span-8 bg-white p-4 shadow rounded-lg relative">
        <h5 class="text-lg font-bold text-gray-700 mb-3 flex justify-between items-center">
          Répartition hebdomadaire
          <button
            class="text-blue-500 hover:underline text-sm flex items-center"
            (click)="showDialog('repartitionHebdomadaire')"
          >
            <i class="pi pi-expand mr-2"></i> Agrandir
          </button>
        </h5>
        <p-chart
          type="bar"
          [data]="ngapBarChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-64"
        ></p-chart>
      </div>

      <div class="col-span-4 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Données hebdomadaires</h5>
        <ul>
          <li *ngFor="let item of ngapWeeklyTableData" class="flex justify-between py-2 border-b">
            <span>{{ item.semaine }}</span>
            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Dialog pour Évolution mensuelle -->
    <p-dialog
      [(visible)]="dialogState.evolutionMensuelle"
      [modal]="true"
      [header]="'Évolution mensuelle des actes NGAP'"
      [style]="{ width: '80vw' }"
      [closable]="true"
      [dismissableMask]="true"
    >
      <div class="p-4">
        <p-chart
          type="line"
          [data]="ngapLineChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-96"
        ></p-chart>
      </div>
    </p-dialog>

    <!-- Dialog pour Répartition hebdomadaire -->
    <p-dialog
      [(visible)]="dialogState.repartitionHebdomadaire"
      [modal]="true"
      [header]="'Répartition hebdomadaire des actes NGAP'"
      [style]="{ width: '80vw' }"
      [closable]="true"
      [dismissableMask]="true"
    >
      <div class="p-4">
        <p-chart
          type="bar"
          [data]="ngapBarChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-96"
        ></p-chart>
      </div>
    </p-dialog>
  </section>
  <section *ngIf="activeGraphe === 'LABOGRAPHE'" class="mb-8">
    <!-- Graphiques et tableaux -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Line Chart pour l'évolution mensuelle -->
      <div class="col-span-8 bg-white p-4 shadow rounded-lg">

        <h5 class="text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center">
          Évolution mensuelle des actes LABO
          <button
            class="text-blue-500 hover:underline text-sm flex items-center"
            (click)="showDialog('evolutionMensuelleLABO')"
          >
            <i class="pi pi-expand mr-2"></i> Agrandir
          </button>
        </h5>
        <p-chart
          type="line"
          [data]="laboLineChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-64"
        ></p-chart>
      </div>

      <!-- Tableau des données mensuelles -->
      <div class="col-span-4 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Données mensuelles</h5>
        <ul>
          <li *ngFor="let item of laboMonthlyTableData" class="flex justify-between py-2 border-b">
            <span>{{ item.mois }}</span>
            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>
          </li>
        </ul>
      </div>

      <!-- Bar Chart pour la répartition hebdomadaire -->
      <div class="col-span-8 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3 flex justify-between items-center">
          Répartition hebdomadaire
          <button
            class="text-blue-500 hover:underline text-sm flex items-center"
            (click)="showDialog('repartitionHebdomadaireLABO')"
          >
            <i class="pi pi-expand mr-2"></i> Agrandir
          </button>
        </h5>
        <p-chart
          type="bar"
          [data]="laboBarChartData"
          [options]="chartOptions"
          pStyleClass="w-full h-64"
        ></p-chart>
      </div>

      <!-- Tableau des données hebdomadaires -->
      <div class="col-span-4 bg-white p-4 shadow rounded-lg">
        <h5 class="text-lg font-bold text-gray-700 mb-3">Données hebdomadaires</h5>
        <ul>
          <li *ngFor="let item of laboWeeklyTableData" class="flex justify-between py-2 border-b">
            <span>{{ item.semaine }}</span>
            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Dialogs pour agrandir -->
    <p-dialog [(visible)]="dialogState.evolutionMensuelleLABO" header="Évolution mensuelle des actes LABO" [modal]="true" [style]="{ width: '80vw' }">
      <p-chart type="line" [data]="laboLineChartData" [options]="chartOptions" pStyleClass="w-full h-96"></p-chart>
    </p-dialog>

    <p-dialog [(visible)]="dialogState.repartitionHebdomadaireLABO" header="Répartition hebdomadaire des actes LABO" [modal]="true" [style]="{ width: '80vw' }">
      <p-chart type="bar" [data]="laboBarChartData" [options]="chartOptions" pStyleClass="w-full h-96"></p-chart>
    </p-dialog>
  </section>
</section>







