import {Component, Input} from '@angular/core';
import {BreadcrumbItem} from "../../core/models/breadcrumbItem";
import {RouterLink} from "@angular/router";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgIf} from "@angular/common";

@Component({
  selector: 'app-breadcrumb',
  standalone: true,
  imports: [
    RouterLink,
    NgIf,
    NgFor,
    Ng<PERSON>lass
  ],
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss'
})
export class BreadcrumbComponent {

  @Input() items: BreadcrumbItem[] = [];
  @Input() home: BreadcrumbItem = { label: 'CHRU de Nancy', icon: 'pi pi-building', url: '/chru-nancy' };

}
