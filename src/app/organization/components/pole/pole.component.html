<div class="p-4 bg-gray-100 min-h-screen">
  <h1 class="text-2xl font-semibold text-gray-800 mb-4">Liste des Pôles</h1>

  @if (poles.length === 0){
    <div class="text-gray-500 text-center p-4">
      Aucun pôle disponible.
    </div>
  } @else {
    <div *ngFor="let pole of poles" class="bg-white shadow rounded-lg p-4 mb-4">
      <h2 class="text-xl font-bold text-cyan-700">{{ pole.name }}</h2>
      <p class="text-gray-600 mt-1">Départements : {{ pole.departments.length || 0 }}</p>
      <div class="text-gray-500 text-sm mt-2">
        <span class="font-semibold">Code:</span> {{ pole.code }}
      </div>
    </div>
  }
</div>


