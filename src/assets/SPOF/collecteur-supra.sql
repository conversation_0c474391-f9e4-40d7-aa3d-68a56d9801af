--1. Commande principale : Récupération des actes praticiens CCAM
SELECT
    NVL(MATPAIEPRINC, 'A') AS MATRICULE,
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0) AS TYPE_VENUE,
    UFPRINCIPAL,
    SUM(NVL(NB, 0)) AS TOTAL_NB,
    SUM(NVL(ICR_A, 0)) AS TOTAL_ICR
FROM
    homere.EX_MOIS_ACTES2
WHERE
    ANNEE_ACTE >= :anneemin
    AND TYPEACTE = 'CCAM'
    AND CODE_ACTE NOT LIKE 'YYY%'
    AND TYPEVENUE IS NOT NULL
GROUP BY
    NVL(MATPAIEPRINC, 'A'),
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0),
    UFPRINCIPAL;
    
--2. Commande pour les actes NGAP
--Objectif : Récupérer les actes NGAP des praticiens en excluant certains types de codes.

SELECT
    NVL(MATPAIEPRINC, 'A') AS MATRICULE,
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0) AS TYPE_VENUE,
    UFPRINCIPAL,
    SUM(NVL(NB, 0)) AS TOTAL_NB
FROM
    homere.EX_MOIS_ACTES2
WHERE
    ANNEE_ACTE >= :anneemin
    AND TYPEACTE = 'NGAP'
    AND CODE_ACTE NOT LIKE 'M%'
    AND CODE_ACTE NOT LIKE 'N%'
    AND CODE_ACTE NOT IN ('AMI', 'AMY', 'BHN', 'B', 'FCG', 'F', 'ADD')
    AND TYPEVENUE IS NOT NULL
GROUP BY
    NVL(MATPAIEPRINC, 'A'),
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0),
    UFPRINCIPAL;
    
--3. Commande pour les actes de laboratoire (LABO)
--Objectif : Récupérer les actes de laboratoire, en regroupant par praticien, année, mois, type de venue, et UF.
SELECT
    NVL(MATPAIEPRINC, 'A') AS MATRICULE,
    ANNEE_ACTE,
    MOIS_ACTE,
    SUM(NVL(NB, 0)) AS TOTAL_NB,
    NVL(TYPEVENUE, 0) AS TYPE_VENUE,
    UFPRINCIPAL
FROM (
    SELECT
        MATPAIEPRINC,
        ANNEE_ACTE,
        MOIS_ACTE,
        COUNT(COEFFICIENT) * COEFFICIENT AS NB,
        TYPEVENUE,
        UFPRINCIPAL
    FROM
        homere.EX_MOIS_ACTES2
    WHERE
        TYPEACTE = 'LABO'
        AND ANNEE_ACTE >= :anneemin
        AND TYPEVENUE IS NOT NULL
    GROUP BY
        MATPAIEPRINC,
        ANNEE_ACTE,
        MOIS_ACTE,
        COEFFICIENT,
        TYPEVENUE,
        UFPRINCIPAL
)
GROUP BY
    NVL(MATPAIEPRINC, 'A'),
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0),
    UFPRINCIPAL;
    
--4. Commande pour les UF (Unités Fonctionnelles) CCAM
--Objectif : Récupérer les actes CCAM regroupés par UF.
SELECT
    UFPRINCIPAL,
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0) AS TYPE_VENUE,
    SUM(NVL(NB, 0)) AS TOTAL_NB,
    SUM(NVL(ICR_A, 0)) AS TOTAL_ICR
FROM
    homere.EX_MOIS_ACTES2
WHERE
    ANNEE_ACTE >= :anneemin
    AND TYPEACTE = 'CCAM'
    AND CODE_ACTE NOT LIKE 'YYY%'
GROUP BY
    UFPRINCIPAL,
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0);
--5. Commande pour les "Top Actes" CCAM
--Objectif : Identifier les actes CCAM les plus fréquents pour un praticien ou une période donnée.
--Remplacez :anneemin et :mois par les valeurs correspondantes.
SELECT
    NVL(MATPAIEPRINC, 'A') AS MATRICULE,
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0) AS TYPE_VENUE,
    CODE_ACTE,
    LIBELLEL_A AS LIBELLE_ACTE,
    HIERARCHIE8LIB AS LIBELLE_REGROUPEMENT,
    SUM(NVL(NB, 0)) AS TOTAL_NB,
    SUM(NVL(ICR_A, 0)) AS TOTAL_ICR
FROM
    homere.EX_MOIS_ACTES2
WHERE
    ANNEE_ACTE >= :anneemin
    AND MOIS_ACTE = :mois
    AND TYPEACTE = 'CCAM'
    AND TYPEVENUE = 1
    AND CODE_ACTE NOT LIKE 'YYY%'
GROUP BY
    NVL(MATPAIEPRINC, 'A'),
    ANNEE_ACTE,
    MOIS_ACTE,
    CODE_ACTE,
    LIBELLEL_A,
    HIERARCHIE8LIB,
    NVL(TYPEVENUE, 0)
ORDER BY
    TOTAL_NB DESC;
--6. Commande pour récupérer les praticiens depuis HR_AGENT
--Objectif : Récupérer les informations sur les praticiens à partir de leurs identifiants (HR_USER).
--Remplacez :liste_ids par une liste d'IDs séparés par des virgules (par exemple : 'ID1', 'ID2').
SELECT
    HR_USER,
    TRIM(HR_NOM) || ' ' || TRIM(HR_PRENOM) || ' (' || HR_TITRE || ')' AS EXECUTANT
FROM
    homere.HR_AGENT
WHERE
    HR_USER IN (:liste_ids);
--7. Récupérer les données pour les "Top Actes" par UF
--Objectif : Identifier les actes les plus fréquents par UF.
SELECT
    UFPRINCIPAL,
    ANNEE_ACTE,
    MOIS_ACTE,
    NVL(TYPEVENUE, 0) AS TYPE_VENUE,
    CODE_ACTE,
    LIBELLEL_A AS LIBELLE_ACTE,
    HIERARCHIE8LIB AS LIBELLE_REGROUPEMENT,
    SUM(NVL(NB, 0)) AS TOTAL_NB,
    SUM(NVL(ICR_A, 0)) AS TOTAL_ICR
FROM
    homere.EX_MOIS_ACTES2
WHERE
    ANNEE_ACTE >= :anneemin
    AND MOIS_ACTE = :mois
    AND TYPEACTE = 'CCAM'
    AND TYPEVENUE = 1
    AND CODE_ACTE NOT LIKE 'YYY%'
GROUP BY
    UFPRINCIPAL,
    ANNEE_ACTE,
    MOIS_ACTE,
    CODE_ACTE,
    LIBELLEL_A,
    HIERARCHIE8LIB,
    NVL(TYPEVENUE, 0)
ORDER BY
    TOTAL_NB DESC;
