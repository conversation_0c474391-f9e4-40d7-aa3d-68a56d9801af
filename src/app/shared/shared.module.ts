import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {JsonEditorComponent} from "./json-editor/json-editor.component";
import {MaintenanceComponent} from "./maintenance/maintenance.component";
import { AutocompleteSearchComponent } from './components/autocomplete-search/autocomplete-search.component';



@NgModule({
  declarations: [
    AutocompleteSearchComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    JsonEditorComponent,
    MaintenanceComponent
  ],
  exports: [
    JsonEditorComponent,
    MaintenanceComponent,
    AutocompleteSearchComponent // Export pour utilisation dans d'autres modules
  ]
})
export class SharedModule { }
