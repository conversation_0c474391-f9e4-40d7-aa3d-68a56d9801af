<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  AnalyticsDashboardComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/graphical/components/analytics-dashboard/analytics-dashboard.component.ts</code>
</p>






<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-analytics-dashboard</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>ButtonDirective</code>
                            <code>CalendarModule</code>
                            <code>ChartModule</code>
                            <code>FormsModule</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./analytics-dashboard.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./analytics-dashboard.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#chartData" >chartData</a>
                            </li>
                            <li>
                                <a href="#chartOptions" >chartOptions</a>
                            </li>
                            <li>
                                <a href="#dateRange" >dateRange</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#generatePDF" >generatePDF</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>






    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generatePDF"></a>
                    <span class="name">
                        <span ><b>generatePDF</b></span>
                        <a href="#generatePDF"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>generatePDF()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="77"
                                    class="link-to-prism">src/app/graphical/components/analytics-dashboard/analytics-dashboard.component.ts:77</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chartData"></a>
                    <span class="name">
                        <span ><b>chartData</b></span>
                        <a href="#chartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    labels: [&#x27;Jan&#x27;, &#x27;Feb&#x27;, &#x27;Mar&#x27;, &#x27;Apr&#x27;, &#x27;May&#x27;, &#x27;Jun&#x27;, &#x27;Jul&#x27;, &#x27;Aug&#x27;, &#x27;Sep&#x27;, &#x27;Oct&#x27;, &#x27;Nov&#x27;, &#x27;Dec&#x27;],
    datasets: [
      {
        label: &#x27;Personal Wallet&#x27;,
        backgroundColor: &#x27;#42A5F5&#x27;,
        data: [6500, 5900, 8000, 8100, 5600, 5500, 4000, 7000, 8500, 9000, 9500, 10200]
      },
      {
        label: &#x27;Corporate Wallet&#x27;,
        backgroundColor: &#x27;#9CCC65&#x27;,
        data: [3000, 4000, 5000, 4000, 4500, 3800, 3000, 4000, 5000, 4500, 4000, 4200]
      },
      {
        label: &#x27;Investment Wallet&#x27;,
        backgroundColor: &#x27;#FFCA28&#x27;,
        data: [2000, 2500, 3000, 2000, 2200, 1800, 2500, 2700, 3000, 3300, 3500, 4000]
      }
    ]
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="26" class="link-to-prism">src/app/graphical/components/analytics-dashboard/analytics-dashboard.component.ts:26</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chartOptions"></a>
                    <span class="name">
                        <span ><b>chartOptions</b></span>
                        <a href="#chartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 0.8,
    plugins: {
      legend: {
        position: &#x27;top&#x27;,
      },
      tooltip: {
        enabled: true,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          color: &#x27;#ebedef&#x27;,
        },
        ticks: {
          stepSize: 5000,
        },
      },
    },
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="47" class="link-to-prism">src/app/graphical/components/analytics-dashboard/analytics-dashboard.component.ts:47</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="dateRange"></a>
                    <span class="name">
                        <span ><b>dateRange</b></span>
                        <a href="#dateRange"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>Date[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="24" class="link-to-prism">src/app/graphical/components/analytics-dashboard/analytics-dashboard.component.ts:24</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Component } from &#x27;@angular/core&#x27;;
import {ButtonDirective} from &quot;primeng/button&quot;;
import {CalendarModule} from &quot;primeng/calendar&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {FormsModule} from &quot;@angular/forms&quot;;
//
import { jsPDF } from &#x27;jspdf&#x27;;
import html2canvas from &#x27;html2canvas&#x27;;
import {position} from &quot;html2canvas/dist/types/css/property-descriptors/position&quot;;

@Component({
  selector: &#x27;app-analytics-dashboard&#x27;,
  standalone: true,
  imports: [
    ButtonDirective,
    CalendarModule,
    ChartModule,
    FormsModule
  ],
  templateUrl: &#x27;./analytics-dashboard.component.html&#x27;,
  styleUrl: &#x27;./analytics-dashboard.component.scss&#x27;
})
export class AnalyticsDashboardComponent {
  dateRange: Date[] &#x3D; [];

  chartData: any &#x3D; {
    labels: [&#x27;Jan&#x27;, &#x27;Feb&#x27;, &#x27;Mar&#x27;, &#x27;Apr&#x27;, &#x27;May&#x27;, &#x27;Jun&#x27;, &#x27;Jul&#x27;, &#x27;Aug&#x27;, &#x27;Sep&#x27;, &#x27;Oct&#x27;, &#x27;Nov&#x27;, &#x27;Dec&#x27;],
    datasets: [
      {
        label: &#x27;Personal Wallet&#x27;,
        backgroundColor: &#x27;#42A5F5&#x27;,
        data: [6500, 5900, 8000, 8100, 5600, 5500, 4000, 7000, 8500, 9000, 9500, 10200]
      },
      {
        label: &#x27;Corporate Wallet&#x27;,
        backgroundColor: &#x27;#9CCC65&#x27;,
        data: [3000, 4000, 5000, 4000, 4500, 3800, 3000, 4000, 5000, 4500, 4000, 4200]
      },
      {
        label: &#x27;Investment Wallet&#x27;,
        backgroundColor: &#x27;#FFCA28&#x27;,
        data: [2000, 2500, 3000, 2000, 2200, 1800, 2500, 2700, 3000, 3300, 3500, 4000]
      }
    ]
  };

  chartOptions: any &#x3D; {
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio: 0.8,
    plugins: {
      legend: {
        position: &#x27;top&#x27;,
      },
      tooltip: {
        enabled: true,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        grid: {
          color: &#x27;#ebedef&#x27;,
        },
        ticks: {
          stepSize: 5000,
        },
      },
    },
  };


  generatePDF() {
    const element &#x3D; document.getElementById(&#x27;pdfContent&#x27;); // ID de l&#x27;élément HTML que vous souhaitez convertir en PDF
    if (element) {
      html2canvas(element).then((canvas) &#x3D;&gt; {
        const imgData &#x3D; canvas.toDataURL(&#x27;image/png&#x27;); // Convertit l&#x27;élément en image
        const pdf &#x3D; new jsPDF(&#x27;p&#x27;, &#x27;mm&#x27;, &#x27;a4&#x27;); // Configure un PDF au format A4

        // Calcule la largeur et la hauteur en fonction de l&#x27;image et de la taille de la page
        const imgWidth &#x3D; 190; // Largeur d&#x27;une page A4 en mm avec une marge
        const pageHeight &#x3D; 297; // Hauteur d&#x27;une page A4 en mm
        const imgHeight &#x3D; (canvas.height * imgWidth) / canvas.width;

        let position &#x3D; 0; // Position de départ sur la page

        // Ajoute l&#x27;image au PDF
        pdf.addImage(imgData, &#x27;PNG&#x27;, 10, position, imgWidth, imgHeight);

        // Si nécessaire, gère les pages multiples
        if (imgHeight &gt; pageHeight) {
          let heightLeft &#x3D; imgHeight - pageHeight;
          while (heightLeft &gt; 0) {
            position &#x3D; position - pageHeight;
            pdf.addPage();
            pdf.addImage(imgData, &#x27;PNG&#x27;, 10, position, imgWidth, imgHeight);
            heightLeft -&#x3D; pageHeight;
          }
        }

        // Télécharge le PDF
        pdf.save(&#x27;generated.pdf&#x27;);
      });
    } else {
      console.error(&#x27;Element with ID &quot;pdfContent&quot; not found!&#x27;);
    }
  }




}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;div id&#x3D;&quot;pdfContent&quot;  class&#x3D;&quot;p-4 bg-white rounded-lg shadow-md&quot;&gt;
  &lt;div class&#x3D;&quot;flex justify-between items-center&quot;&gt;
    &lt;div&gt;
      &lt;button pButton label&#x3D;&quot;Weekly&quot; class&#x3D;&quot;p-button-text&quot;&gt;&lt;/button&gt;
      &lt;button pButton label&#x3D;&quot;Monthly&quot; class&#x3D;&quot;p-button-text p-button-outlined&quot;&gt;&lt;/button&gt;
      &lt;button pButton label&#x3D;&quot;Yearly&quot; class&#x3D;&quot;p-button-text&quot;&gt;&lt;/button&gt;
    &lt;/div&gt;
    &lt;div class&#x3D;&quot;flex items-center&quot;&gt;
      &lt;button pButton icon&#x3D;&quot;pi pi-download&quot; label&#x3D;&quot;Download&quot; class&#x3D;&quot;mr-2&quot;&gt;&lt;/button&gt;
      &lt;p-calendar [(ngModel)]&#x3D;&quot;dateRange&quot; [showIcon]&#x3D;&quot;true&quot; [rangeSeparator]&#x3D;&quot;&#x27;-&#x27;&quot; selectionMode&#x3D;&quot;range&quot;&gt;&lt;/p-calendar&gt;
    &lt;/div&gt;
  &lt;/div&gt;

  &lt;div class&#x3D;&quot;mt-4&quot;&gt;
    &lt;p-chart type&#x3D;&quot;bar&quot; [data]&#x3D;&quot;chartData&quot; [options]&#x3D;&quot;chartOptions&quot;&gt;&lt;/p-chart&gt;
  &lt;/div&gt;
&lt;/div&gt;

</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><div id="pdfContent"  class="p-4 bg-white rounded-lg shadow-md">  <div class="flex justify-between items-center">    <div>      <button pButton label="Weekly" class="p-button-text"></button>      <button pButton label="Monthly" class="p-button-text p-button-outlined"></button>      <button pButton label="Yearly" class="p-button-text"></button>    </div>    <div class="flex items-center">      <button pButton icon="pi pi-download" label="Download" class="mr-2"></button>      <p-calendar [(ngModel)]="dateRange" [showIcon]="true" [rangeSeparator]="\'-\'" selectionMode="range"></p-calendar>    </div>  </div>  <div class="mt-4">    <p-chart type="bar" [data]="chartData" [options]="chartOptions"></p-chart>  </div></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'AnalyticsDashboardComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'AnalyticsDashboardComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
