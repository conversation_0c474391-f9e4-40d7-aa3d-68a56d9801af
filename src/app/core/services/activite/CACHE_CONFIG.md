# Configuration du Cache Frontend

## Vue d'ensemble

Le système de cache frontend peut être activé ou désactivé via la variable d'environnement `enableFrontendCache`.

## Configuration

### Environnement de développement (`environment.ts`)
```typescript
export const environment = {
  // ...
  enableFrontendCache: false  // Désactivé pour le développement
};
```

### Environnement de production (`environment.prod.ts`)
```typescript
export const environment = {
  // ...
  enableFrontendCache: true   // Activé pour la production
};
```

## Services affectés

### 1. ActesMetadataService
- **Cache activé** : Les métadonnées sont mises en cache pendant 30 minutes
- **Cache désactivé** : Chaque requête va directement au serveur
- **Logs** : Indique l'état du cache dans la console

### 2. ActiviteService
- **Logs** : Indique l'état du cache dans les requêtes avec périodes
- **Héritage** : Utilise le cache du BaseStructureService selon la configuration

## Comportement selon l'état

### Cache Activé (`enableFrontendCache: true`)
- ✅ Mise en cache des réponses avec `shareReplay(1)`
- ✅ TTL de 30 minutes pour les métadonnées
- ✅ Clé de cache basée sur les paramètres de requête
- ✅ Nettoyage automatique après expiration
- 📊 Logs : "Cache HIT/SET" dans la console

### Cache Désactivé (`enableFrontendCache: false`)
- ❌ Aucune mise en cache
- ❌ Chaque appel génère une nouvelle requête HTTP
- ❌ Pas de `shareReplay()` appliqué
- 📊 Logs : "Cache désactivé" dans la console

## Interface utilisateur

### Composant MetadataTestComponent
- **Badge de statut** : Affiche "Cache Activé" ou "Cache Désactivé"
- **Bouton "Vider Cache"** : Désactivé si le cache est désactivé
- **Couleurs** :
  - 🟢 Vert : Cache activé
  - 🟠 Orange : Cache désactivé

## Avantages par environnement

### Développement (Cache désactivé)
- ✅ Données toujours fraîches
- ✅ Facilite le debugging des API
- ✅ Pas de confusion avec des données en cache
- ✅ Tests plus fiables

### Production (Cache activé)
- ✅ Réduction de la charge serveur
- ✅ Amélioration des performances
- ✅ Meilleure expérience utilisateur
- ✅ Économie de bande passante

## Commandes utiles

### Vider le cache manuellement
```typescript
// Dans le composant de test
this.metadataService.clearCache();

// Ou directement dans la console
// (si le service est injecté)
this.actesMetadataService.clearCache();
```

### Vérifier l'état du cache
```typescript
import { environment } from '../environments/environment';

console.log('Cache activé:', environment.enableFrontendCache);
```

## Notes importantes

1. **Cohérence** : Tous les services respectent la même configuration
2. **Logs** : Les messages de console indiquent clairement l'état du cache
3. **Flexibilité** : Peut être changé sans modification du code, juste la configuration
4. **Performance** : En développement, privilégier la fraîcheur des données
5. **Production** : En production, privilégier les performances

## Exemple de logs

### Cache activé
```
📦 Cache HIT pour métadonnées: metadata_2024-01-01_2024-12-31_2023-01-01_2023-12-31_2022-01-01_2022-12-31
📦 Cache SET pour métadonnées: metadata_2024-01-01_2024-12-31_null_null
🗑️ Cache expiré pour métadonnées: metadata_2024-01-01_2024-12-31_null_null
```

### Cache désactivé
```
🌐 Requête API métadonnées (cache: désactivé)
ℹ️ Cache désactivé - aucune action nécessaire
```
