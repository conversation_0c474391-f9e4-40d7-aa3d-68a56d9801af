<!-- Breadcrumb -->
<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

<!-- Loading state -->
<div *ngIf="loading" class="flex justify-center items-center h-64">
  <p-progressSpinner strokeWidth="3" animationDuration="1s"></p-progressSpinner>
</div>

<!-- Error state -->
<div *ngIf="error && !loading" class="p-4">
  <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
    <strong>Erreur:</strong> {{ error }}
  </div>
</div>

<!-- Content when data is loaded -->
<div *ngIf="!loading && !error" class="p-4">
  <!-- Header -->
  <div class="flex justify-between items-center mb-4">
    <div class="flex items-center">
      <i class="pi pi-users text-cyan-700 text-2xl mr-2"></i>
      <h1 class="text-xl font-bold text-gray-700">Liste des Praticiens</h1>
    </div>
  </div>

  <!-- System health indicator -->
  <div class="mb-4 flex justify-between items-center">
    <div class="flex items-center space-x-2">
      <span class="text-sm text-gray-600">Statut système:</span>
      <span
        class="px-2 py-1 rounded text-xs font-medium"
        [ngClass]="{
          'bg-green-100 text-green-800': systemHealthStatus === 'excellent',
          'bg-blue-100 text-blue-800': systemHealthStatus === 'good',
          'bg-yellow-100 text-yellow-800': systemHealthStatus === 'slow',
          'bg-red-100 text-red-800': systemHealthStatus === 'offline'
        }"
      >
        {{ systemHealthMessage }}
      </span>
    </div>

    <div class="text-sm text-gray-600">
      Total: {{ totalItems }} praticien(s)
    </div>
  </div>

  <!-- Subheader with filters - Sticky lors du scroll -->
  <div class="sticky top-0 bg-white shadow-sm" style="z-index: 9999 !important;">
    <app-praticien-subheader (filtersChanged)="onFiltersChanged($event)"></app-praticien-subheader>
  </div>

  <!-- Active filters badges (persistent) -->
  <div *ngIf="activeFilters.length > 0" class="filter-badges-container bg-white border-b border-gray-200 px-4 py-3">
    <div class="flex items-center gap-2 flex-wrap">
      <span class="text-sm font-medium text-gray-600">
        <i class="pi pi-filter text-cyan-600 mr-1"></i>
        Filtres actifs:
      </span>
      <div class="flex flex-wrap gap-2">
        <span
          *ngFor="let filter of activeFilters"
          class="filter-badge inline-flex items-center gap-1 px-3 py-1 bg-cyan-100 text-cyan-800 rounded-full text-sm font-medium"
        >
          <span class="font-semibold">{{ filter.label }}:</span>
          <span>{{ filter.value }}</span>
          <button
            type="button"
            (click)="removeFilter(filter.key)"
            class="remove-filter-btn ml-1 p-0.5 hover:bg-cyan-300 rounded-full transition-colors"
            title="Supprimer ce filtre"
          >
            <i class="pi pi-times text-xs"></i>
          </button>
        </span>
      </div>
      <button
        type="button"
        (click)="clearAllFilters()"
        class="ml-2 px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 border border-red-300 rounded transition-colors"
        title="Effacer tous les filtres"
      >
        <i class="pi pi-times mr-1"></i>
        Tout effacer
      </button>
    </div>
  </div>

  <!-- Data Table -->
  <p-table
    #dt
    [value]="agents"
    [paginator]="true"
    [rows]="itemsPerPage"
    [totalRecords]="totalItems"
    [lazy]="true"
    (onLazyLoad)="onPageChange($event)"
    [loading]="loading"
    styleClass="p-datatable-sm p-datatable-striped"
    [scrollable]="true"
    scrollHeight="600px"
    [tableStyle]="{'min-width': '80rem'}"
  >
    <!-- Table Header -->
    <ng-template pTemplate="header">
      <tr>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold sticky left-0 bg-white z-[5]" style="min-width: 50px; max-width: 60px;">
          <i class="pi pi-eye cursor-pointer ml-2" title="Voir les détails du praticien"></i>
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 200px;">
          Nom complet
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 120px;">
          Matricule
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 100px;">
          Catégorie
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 200px;">
          Email
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 120px;">
          Date d'arrivée
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 120px;">
          Date de départ
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold" style="min-width: 120px;">
          ID RH
        </th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template pTemplate="body" let-agent>
      <tr class="border-b hover:bg-gray-50">
        <!-- Action column -->
        <td class="px-4 py-2 text-center sticky left-0 bg-white z-[5]" style="min-width: 50px; max-width: 60px;">
          <i
            class="pi pi-eye text-lg text-cyan-700 cursor-pointer ml-2"
            [title]="'Voir les détails de ' + getFullNameWithTitle(agent)"
            (click)="viewAgentDetails(agent['@id'])"
          ></i>
        </td>

        <!-- Full name with title -->
        <td class="px-4 py-2" style="min-width: 200px;">
          <div class="flex items-center">
            <div>
              <div class="font-medium text-gray-900">{{ getFullNameWithTitle(agent) }}</div>
              <div class="text-sm text-gray-500" *ngIf="agent.matricule">Matricule: {{ agent.matricule }}</div>
            </div>
          </div>
        </td>

        <!-- Matricule -->
        <td class="px-4 py-2" style="min-width: 120px;">
          <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium">
            {{ agent.matricule || 'N/A' }}
          </span>
        </td>

        <!-- Catégorie -->
        <td class="px-4 py-2" style="min-width: 100px;">
          <span
            class="px-2 py-1 rounded text-sm font-medium"
            [ngClass]="{
              'bg-purple-100 text-purple-800': agent.categorie === 'PH',
              'bg-green-100 text-green-800': agent.categorie === 'IDE',
              'bg-orange-100 text-orange-800': agent.categorie === 'AS',
              'bg-gray-100 text-gray-800': !agent.categorie || (agent.categorie !== 'PH' && agent.categorie !== 'IDE' && agent.categorie !== 'AS')
            }"
          >
            {{ agent.categorie || 'N/A' }}
          </span>
        </td>

        <!-- Email -->
        <td class="px-4 py-2" style="min-width: 200px;">
          <a
            *ngIf="agent.email"
            [href]="'mailto:' + agent.email"
            class="text-cyan-600 hover:text-cyan-800 underline"
          >
            {{ agent.email }}
          </a>
          <span *ngIf="!agent.email" class="text-gray-500">N/A</span>
        </td>

        <!-- Date d'arrivée -->
        <td class="px-4 py-2" style="min-width: 120px;">
          <span class="text-sm">{{ formatDate(agent.dateVenue) }}</span>
        </td>

        <!-- Date de départ -->
        <td class="px-4 py-2" style="min-width: 120px;">
          <span class="text-sm">{{ formatDate(agent.dateDepart) }}</span>
        </td>

        <!-- ID RH -->
        <td class="px-4 py-2" style="min-width: 120px;">
          <span class="px-2 py-1 bg-cyan-100 text-cyan-800 rounded text-sm font-medium">
            {{ agent.hrUser || 'N/A' }}
          </span>
        </td>
      </tr>
    </ng-template>

    <!-- Empty state -->
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="8" class="text-center py-8">
          <div class="flex flex-col items-center">
            <i class="pi pi-users text-4xl text-gray-400 mb-4"></i>
            <p class="text-gray-500 text-lg">Aucun praticien trouvé</p>
            <p class="text-gray-400 text-sm">Essayez de modifier vos critères de recherche</p>
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>
