// Praticien List Component Styles

.praticien-list-container {
  padding: 1rem;
}

// Table customizations
::ng-deep .p-datatable {
  .p-datatable-thead > tr > th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #374151;
    padding: 0.75rem 1rem;
  }

  .p-datatable-tbody > tr {
    &:hover {
      background-color: #f8fafc !important;
    }
  }

  .p-datatable-tbody > tr > td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
  }
}

// Search input styling
.search-container {
  position: relative;

  input {
    transition: all 0.2s ease-in-out;

    &:focus {
      box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
    }
  }
}

// Status badges
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;

  &.active {
    background-color: #dcfce7;
    color: #166534;
  }

  &.inactive {
    background-color: #fee2e2;
    color: #991b1b;
  }
}

// Category badges
.category-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;

  &.ph {
    background-color: #f3e8ff;
    color: #7c3aed;
  }

  &.ide {
    background-color: #dcfce7;
    color: #166534;
  }

  &.as {
    background-color: #fed7aa;
    color: #ea580c;
  }

  &.default {
    background-color: #f3f4f6;
    color: #374151;
  }
}

// System health indicator
.health-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .health-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;

    &.excellent {
      background-color: #dcfce7;
      color: #166534;
    }

    &.good {
      background-color: #dbeafe;
      color: #1d4ed8;
    }

    &.slow {
      background-color: #fef3c7;
      color: #d97706;
    }

    &.offline {
      background-color: #fee2e2;
      color: #991b1b;
    }
  }
}

// Loading spinner container
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16rem;
}

// Error message styling
.error-message {
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  color: #991b1b;
  padding: 1rem;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

// Empty state styling
.empty-state {
  text-align: center;
  padding: 2rem;

  .empty-icon {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
  }

  .empty-title {
    color: #6b7280;
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
  }

  .empty-subtitle {
    color: #9ca3af;
    font-size: 0.875rem;
  }
}

// Active filters badges
.filter-badge {
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: #0891b2 !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .remove-filter-btn {
    transition: all 0.15s ease-in-out;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: scale(1.1);
    }
  }
}

.filter-badges-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Fix z-index for PrimeNG dropdowns to appear above sticky subheader
::ng-deep .p-dropdown-panel,
::ng-deep .p-autocomplete-panel,
::ng-deep .p-multiselect-panel,
::ng-deep .p-calendar-panel,
::ng-deep .p-overlay {
  z-index: 10000 !important;
}

// Ensure PrimeNG overlays appear above sticky subheader
::ng-deep .p-component-overlay {
  z-index: 10000 !important;
}

// Fix for tooltip z-index
::ng-deep .p-tooltip {
  z-index: 10001 !important;
}

// Responsive adjustments
@media (max-width: 768px) {
  .praticien-list-container {
    padding: 0.5rem;
  }

  .search-container input {
    width: 100%;
    max-width: 300px;
  }

  ::ng-deep .p-datatable {
    font-size: 0.875rem;

    .p-datatable-thead > tr > th,
    .p-datatable-tbody > tr > td {
      padding: 0.5rem;
    }
  }

  .filter-badges-container {
    .flex {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
}
