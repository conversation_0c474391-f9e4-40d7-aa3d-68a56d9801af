import { Compo<PERSON>, On<PERSON>nit, <PERSON><PERSON><PERSON>roy, ViewChild, ElementRef, Input, Output, EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { debounceTime, distinctUntilChanged, filter, switchMap, takeUntil, tap } from 'rxjs/operators';
import { Subject, of } from 'rxjs';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { FuseSearchService, SearchEntity } from '../../../core/services/fuse-search.service';
import { ENTITY_TYPE_CONFIG } from '../../../core/models/search-entity.model';

@Component({
  selector: 'app-autocomplete-search',
  templateUrl: './autocomplete-search.component.html',
  styleUrls: ['./autocomplete-search.component.scss'],
  animations: [
    trigger('slideDown', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('200ms cubic-bezier(0.4, 0, 0.2, 1)', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('150ms cubic-bezier(0.4, 0, 0.2, 1)', style({ opacity: 0, transform: 'translateY(-5px)' }))
      ])
    ])
  ]
})
export class AutocompleteSearchComponent implements OnInit, OnDestroy {
  @ViewChild('searchInput', { static: true }) searchInput!: ElementRef<HTMLInputElement>;

  @Input() placeholder: string = 'Rechercher un praticien, pôle, service...';
  @Input() maxResults: number = 8;
  @Input() showCategories: boolean = true;
  @Input() showStats: boolean = true;

  @Output() resultSelected = new EventEmitter<SearchEntity>();
  @Output() searchChanged = new EventEmitter<string>();

  searchControl = new FormControl('');
  searchResults: SearchEntity[] = [];
  groupedResults: { [key: string]: SearchEntity[] } = {};
  isLoading = false;
  isOpen = false;
  highlightedIndex = -1;
  totalResults = 0;
  searchTime = 0;
  currentSearchTerm = '';
  noResultsMessage = 'Aucun résultat trouvé dans le système';

  private destroy$ = new Subject<void>();

  // Configuration des types d'entités
  entityConfig = ENTITY_TYPE_CONFIG;

  constructor(private fuseSearchService: FuseSearchService) {}

  ngOnInit(): void {
    this.initializeSearch();
    this.checkDataLoaded();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeSearch(): void {
    this.searchControl.valueChanges.pipe(
      debounceTime(150), // Délai optimisé pour la réactivité
      distinctUntilChanged(),
      tap(query => {
        this.searchChanged.emit(query || '');
        if (!query || query.length < 2) {
          this.clearResults();
          return;
        }
      }),
      filter(query => query !== null && query.length >= 2),
      tap(() => {
        this.isLoading = true;
        this.highlightedIndex = -1;
      }),
      switchMap(query => {
        const startTime = performance.now();
        this.currentSearchTerm = query || '';
        return this.fuseSearchService.search(query!, this.maxResults * 2).pipe(
          tap(() => {
            this.searchTime = performance.now() - startTime;
          })
        );
      }),
      takeUntil(this.destroy$)
    ).subscribe({
      next: (response) => {
        this.handleSearchResults(response.results, response.total_results);
      },
      error: (error) => {
        console.error('Erreur de recherche:', error);
        this.isLoading = false;
      }
    });
  }

  private handleSearchResults(results: SearchEntity[], total: number): void {
    this.searchResults = results.slice(0, this.maxResults);
    this.totalResults = total;
    this.isLoading = false;

    // Ouvrir le dropdown même s'il n'y a pas de résultats (pour afficher le message)
    this.isOpen = this.currentSearchTerm.length >= 2;

    // Mettre à jour le message selon les résultats
    if (results.length === 0 && this.currentSearchTerm) {
      this.noResultsMessage = `Aucun résultat trouvé pour "${this.currentSearchTerm}" dans le système`;
    } else {
      this.noResultsMessage = 'Aucun résultat trouvé dans le système';
    }

    if (this.showCategories) {
      this.groupResultsByType();
    }
  }

  private groupResultsByType(): void {
    this.groupedResults = {};
    this.searchResults.forEach(result => {
      if (!this.groupedResults[result.type]) {
        this.groupedResults[result.type] = [];
      }
      this.groupedResults[result.type].push(result);
    });
  }

  private clearResults(): void {
    this.searchResults = [];
    this.groupedResults = {};
    this.isOpen = false;
    this.isLoading = false;
    this.totalResults = 0;
    this.highlightedIndex = -1;
  }

  private checkDataLoaded(): void {
    if (!this.fuseSearchService.isDataLoaded()) {
      console.log('Chargement des données de recherche...');
    }
  }

  // Gestion du clavier
  onKeyDown(event: KeyboardEvent): void {
    if (!this.isOpen) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.highlightedIndex = Math.min(this.highlightedIndex + 1, this.searchResults.length - 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.highlightedIndex = Math.max(this.highlightedIndex - 1, -1);
        break;
      case 'Enter':
        event.preventDefault();
        if (this.highlightedIndex >= 0 && this.searchResults[this.highlightedIndex]) {
          this.selectResult(this.searchResults[this.highlightedIndex]);
        }
        break;
      case 'Escape':
        this.closeDropdown();
        break;
    }
  }

  selectResult(result: SearchEntity): void {
    this.searchControl.setValue(result.fullName || result.nom || '');
    this.resultSelected.emit(result);
    this.closeDropdown();
    this.searchInput.nativeElement.blur();
  }

  onFocus(): void {
    if (this.searchResults.length > 0) {
      this.isOpen = true;
    }
  }

  onBlur(): void {
    // Délai pour permettre le clic sur un résultat
    setTimeout(() => {
      this.closeDropdown();
    }, 200);
  }

  private closeDropdown(): void {
    this.isOpen = false;
    this.highlightedIndex = -1;
  }

  // Méthodes utilitaires pour le template
  getEntityIcon(type: string): string {
    return this.entityConfig[type as keyof typeof this.entityConfig]?.icon || 'search';
  }

  getEntityColor(type: string): string {
    return this.entityConfig[type as keyof typeof this.entityConfig]?.color || 'gray';
  }

  getEntityLabel(type: string): string {
    return this.entityConfig[type as keyof typeof this.entityConfig]?.label || type;
  }

  getObjectKeys(obj: any): string[] {
    return Object.keys(obj);
  }

  // Highlighting du texte recherché
  highlightMatch(text: string, query: string): string {
    if (!query || !text) return text;

    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="highlight">$1</mark>');
  }

  clear(): void {
    this.searchControl.setValue('');
    this.clearResults();
    this.searchInput.nativeElement.focus();
  }

  // Optimisation des performances pour ngFor
  trackByFn(index: number, item: SearchEntity): string {
    return item.id;
  }
}
