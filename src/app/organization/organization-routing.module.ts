import {NgModule} from "@angular/core";
import {RouterModule, Routes} from "@angular/router";
import {PoleComponent} from "./components/pole/pole.component";
import {DepartementComponent} from "./components/departement/departement.component";
import {UfComponent} from "./components/uf/uf.component";
import {InformationPanelComponent} from "./components/information-panel/information-panel.component";
import {ServiceHospitalierComponent} from "./components/service-hospitalier/service-hospitalier.component";
import {ServiceListComponent} from "./components/service-list/service-list.component";
import {UfListComponent} from "./components/uf-list/uf-list.component";

const routes: Routes = [
  {path: "pole", component: PoleComponent},
  {path: "departement", component: DepartementComponent},
  {path: "service/:uid", component: ServiceHospitalierComponent},
  {path: "service-list", component: ServiceListComponent},
  {path: "uf-list", component: UfListComponent},
  {path: "uf/:uid", component: UfComponent},
  {path: "details", component: InformationPanelComponent}
]
@NgModule({
  imports :[RouterModule.forChild(routes)],
  exports :[RouterModule]

})

export class OrganizationRoutingModule {}
