-- 1. Requête pour récupérer les actes CCAM
SELECT
    NVL(MATPAIEPRINC, 'A') AS MATRICULE,
    ANNEE_ACTE,
    MOIS_ACTE,
    CODE_ACTE,
    LIBELLEL_A AS LIBELLE_ACTE,
    UFPRINCIPAL,
    NB AS NOMBRE_ACTES,
    TARIF AS TARIF_ACTE,
    ICR_A AS INDICATEUR_R
FROM
    homere.EX_MOIS_ACTES2
WHERE
    TYPEACTE = 'CCAM'
ORDER BY
    ANNEE_ACTE, MOIS_ACTE, CODE_ACTE;


-- 2. Requête pour récupérer les actes NGAP
SELECT
    NVL(MATPAIEPRINC, 'A') AS MATRICULE,
    ANNEE_ACTE,
    MOIS_ACTE,
    CODE_ACTE,
    LIBELLEL_A AS LIBELLE_ACTE,
    UFPRINCIPAL,
    NB AS NOMBRE_ACTES,
    TARIF AS TARIF_ACTE
FROM
    homere.EX_MOIS_ACTES2
WHERE
    TYPEACTE = 'NGAP'
    AND CODE_ACTE NOT LIKE 'M%'  -- Optionnel
    AND CODE_ACTE NOT LIKE 'N%'  -- Optionnel
    AND CODE_ACTE NOT IN ('AMI', 'AMY', 'BHN', 'B', 'FCG', 'F', 'ADD')  -- Optionnel
ORDER BY
    ANNEE_ACTE, MOIS_ACTE, CODE_ACTE;


-- 3. Requête pour récupérer les actes LABO (NABM)
-- Les actes NABM sont identifiés comme type LABO.
SELECT
    NVL(MATPAIEPRINC, 'A') AS MATRICULE,
    ANNEE_ACTE,
    MOIS_ACTE,
    CODE_ACTE,
    LIBELLEL_A AS LIBELLE_ACTE,
    NVL(TYPEVENUE, 0) AS TYPE_VENUE,
    UFPRINCIPAL,
    SUM(NVL(NB, 0)) AS TOTAL_NB
FROM (
    SELECT
        MATPAIEPRINC,
        ANNEE_ACTE,
        MOIS_ACTE,
        CODE_ACTE,
        LIBELLEL_A,
        TYPEVENUE,
        UFPRINCIPAL,
        COUNT(COEFFICIENT) * COEFFICIENT AS NB
    FROM
        homere.EX_MOIS_ACTES2
    WHERE
        TYPEACTE = 'LABO'  -- Filtrer les actes NABM (type LABO)
    GROUP BY
        MATPAIEPRINC,
        ANNEE_ACTE,
        MOIS_ACTE,
        CODE_ACTE,
        LIBELLEL_A,
        TYPEVENUE,
        UFPRINCIPAL,
        COEFFICIENT
)
GROUP BY
    NVL(MATPAIEPRINC, 'A'),
    ANNEE_ACTE,
    MOIS_ACTE,
    CODE_ACTE,
    LIBELLEL_A,
    NVL(TYPEVENUE, 0),
    UFPRINCIPAL
ORDER BY
    ANNEE_ACTE, MOIS_ACTE, TOTAL_NB DESC;
