import { Injectable } from '@angular/core';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';

@Injectable({
  providedIn: 'root'
})
export class PdfGeneratorService {

  constructor() { }

  exportAsPDF(elementId: string, fileName: string): void {
    const element = document.getElementById(elementId);

    if (!element) {
      console.error(`Element with ID "${elementId}" not found!`);
      return;
    }

    html2canvas(element,{ backgroundColor: '#ffffff' }).then((canvas) => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 190; // Largeur de l'image dans le PDF (A4 avec marges)
      const pageHeight = 297; // Hauteur de la page (A4)
      const imgHeight = (canvas.height * imgWidth) / canvas.width; // Calcul de la hauteur proportionnelle
      let heightLeft = imgHeight;
      let position = 10; // Marge supérieure initiale

      // Ajoute la première image
      pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Si le contenu dépasse une page, ajoute des pages supplémentaires
      while (heightLeft > 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

       pdf.save(fileName);
    }).catch((error) => {
      console.error('Error generating PDF:', error);
    });
  }

  exportAsPDFManyOLD(className: string, fileName: string): void {
    const elements = document.querySelectorAll(`.${className}`);

    if (!elements || elements.length === 0) {
      console.error(`No elements found with class "${className}"!`);
      return;
    }

    const pdf = new jsPDF('p', 'mm', 'a4'); // Initialise le PDF
    const imgWidth = 190; // Largeur de l'image dans le PDF
    const pageHeight = 297; // Hauteur de la page
    let currentPosition = 10; // Position initiale sur la première page

    const processElement = (element: HTMLElement, index: number, callback: () => void) => {
      html2canvas(element, { backgroundColor: '#ffffff' }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const imgHeight = (canvas.height * imgWidth) / canvas.width; // Calcul de la hauteur proportionnelle

        if (currentPosition + imgHeight > pageHeight) {
          pdf.addPage(); // Ajoute une nouvelle page si nécessaire
          currentPosition = 10; // Réinitialise la position
        }

        pdf.addImage(imgData, 'PNG', 10, currentPosition, imgWidth, imgHeight);
        currentPosition += imgHeight + 10; // Met à jour la position pour l'élément suivant

        callback(); // Appelle le callback une fois terminé
      }).catch((error) => {
        console.error(`Error processing element ${index + 1}:`, error);
        callback(); // Continue avec les éléments suivants même en cas d'erreur
      });
    };

    const processElementsSequentially = (index: number) => {
      if (index >= elements.length) {
        pdf.save(fileName); // Sauvegarde le fichier une fois tous les éléments traités
        return;
      }

      const element = elements[index] as HTMLElement;
      processElement(element, index, () => processElementsSequentially(index + 1));
    };

    processElementsSequentially(0); // Démarre le traitement des éléments
  }

  exportAsPDFMany(className: string, fileName: string): void {
    const elements = document.querySelectorAll(`.${className}`);

    if (!elements || elements.length === 0) {
      console.error(`No elements found with class "${className}"!`);
      return;
    }

    const pdf = new jsPDF('p', 'mm', 'a4'); // Initialise un seul PDF
    const imgWidth = 190; // Largeur de l'image dans le PDF
    const pageHeight = 297; // Hauteur de la page
    let currentPosition = 10; // Position initiale

    const processElement = (element: HTMLElement, callback: () => void) => {
      html2canvas(element, { backgroundColor: '#ffffff' }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const imgHeight = (canvas.height * imgWidth) / canvas.width; // Hauteur proportionnelle

        // Vérifie si l'élément peut tenir sur la page actuelle
        if (currentPosition + imgHeight > pageHeight - 10) {
          pdf.addPage(); // Ajoute une nouvelle page si nécessaire
          currentPosition = 10; // Réinitialise la position sur la nouvelle page
        }

        // Ajoute l'image dans le PDF
        pdf.addImage(imgData, 'PNG', 10, currentPosition, imgWidth, imgHeight);
        currentPosition += imgHeight + 10; // Ajoute un espacement après l'image

        callback(); // Passe au prochain élément
      }).catch((error) => {
        console.error('Error processing element:', error);
        callback(); // Continue même si une erreur survient
      });
    };

    const processElementsSequentially = (index: number) => {
      if (index >= elements.length) {
        pdf.save(fileName); // Sauvegarde le PDF une fois terminé
        return;
      }

      const element = elements[index] as HTMLElement;
      processElement(element, () => processElementsSequentially(index + 1));
    };

    processElementsSequentially(0); // Commence le traitement
  }


}
