
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import {<PERSON><PERSON><PERSON>} from "../../models/acte/praticien.model";

@Injectable({
  providedIn: 'root'
})
export class PraticienService {
  private praticiensSubject = new BehaviorSubject<Praticien[]>([]);
  praticiens$ = this.praticiensSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadPraticiensData();
  }

  private loadPraticiensData(): void {
    this.http.get<Praticien[]>('/assets/data/praticiens.json').subscribe((data) => {
      this.praticiensSubject.next(data);
    });
  }
}
