import {Component, OnInit, ViewChild} from '@angular/core';
import {MessageService} from "primeng/api";
import {ToastModule} from "primeng/toast";
import {FileUpload, FileUploadModule} from "primeng/fileupload";

// import csvTo<PERSON>son from 'convert-csv-to-json';
import {JsonPipe, NgClass, NgForOf, NgIf} from "@angular/common";
import {PapaParseParser,Papa} from "ngx-papaparse";
import {JsonEditorComponent} from "../../../shared/json-editor/json-editor.component";
import {FormsModule} from "@angular/forms";
import {SidebarModule} from "primeng/sidebar";
import {BreadcrumbComponent} from "../../../pages/breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../../core/models/breadcrumbItem";
import {ImportationFactoryService, UnifiedImportResponse, ImportCategory} from "../../../core/services/importation";
import {ButtonModule} from "primeng/button";
import {DialogModule} from "primeng/dialog";
import {DatePipe} from "@angular/common";
import {ProgressSpinnerModule} from "primeng/progressspinner";

@Component({
  selector: 'app-import',
  standalone: true,
  imports: [
    ToastModule,
    FileUploadModule,
    NgIf,
    JsonPipe,
    JsonEditorComponent,
    FormsModule,
    NgForOf,
    NgClass,
    SidebarModule,
    BreadcrumbComponent,
    ButtonModule,
    DialogModule,
    DatePipe,
    ProgressSpinnerModule
  ],
  templateUrl: './import.component.html',
  styleUrl: './import.component.scss'
})
export class ImportComponent implements OnInit{
  @ViewChild('fileUpload') fileUpload!: FileUpload; // Référence au composant p-fileUpload

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Référent métier', url: '/liberale' },
    { label: 'Import' }
  ];
  //
  totalSize: number = 0;
  totalSizePercent: number = 0;

  //
  convertedJson: any = []; // Stocke les données JSON converties
  errorMessage: string = ''; // Pour gérer les erreurs
  //
  categories = [
    { value: 'sigaps', label: 'SIGAPS', url: 'http://localhost:8000/api/sigaps/batch', urlAfficherTemplate:'https://ej.supra/n/referent/importation-sigaps', fileSample: 'assets/fichier-model/sigaps.xlsx' },
    { value: 'garde-et-astreinte', label: 'Garde et Astreinte', url: 'http://localhost:8000/api/gardes-astreintes/batch', urlAfficherTemplate:'https://ej.supra/n/referent/importation-garde-et-astreinte', fileSample: 'assets/fichier-model/garde-et-astreinte.xlsx' },
    { value: 'liberale', label: 'Libérale', url: 'http://localhost:8000/api/liberal/batch',urlAfficherTemplate:'https://ej.supra/n/referent/importation-liberale', fileSample: 'assets/fichier-model/liberale.xlsx' },
    { value: 'affectation', label: 'Affectation', url: 'http://localhost:8000/api/admin/import/ej0001/affectations',urlAfficherTemplate:'https://ej.supra/n/referent/importation-affectation', fileSample: 'assets/fichier-model/affectation-dam.xlsx' },
    { value: 'actes', label: 'Actes', url: 'http://localhost:8000/api/{ejcode}/actes/batch', urlAfficherTemplate:'https://ej.supra/n/referent/importation-actes', fileSample: 'assets/fichier-model/actes.xlsx' },
  ];


  selectedCategory = ''; // Aucune catégorie sélectionnée par défaut
  currentUrl = ''; // URL vide par défaut
  currentUrlDisplay = ''; // URL d'affichage pour le template
  //
  postRequest: any = {}; // Objet pour afficher la requête POST
  //
  currentFileSample = this.categories[0].fileSample; // Fichier modèle par défaut
  currentCategoryLabel: string = ''; // Libellé de la catégorie sélectionnée
  currentFileName: string = ''; // Nom du fichier modèle

  constructor(
    private messageService: MessageService,
    private papaParse: Papa,
    private importationFactoryService: ImportationFactoryService
    ) {}

  ngOnInit(): void {
    this.resetToDefault();
  }

  onTemplatedUpload(): void {
    this.messageService.add({
      severity: 'info',
      summary: 'Fichier téléchargé',
      detail: 'Les fichiers ont été téléchargés avec succès.',
    });
  }

  onSelectedFiles(event: any): void {
    // Utilisation de `currentFiles` pour accéder aux fichiers sélectionnés
    this.totalSize = event.currentFiles.reduce((total: number, file: any) => total + file.size, 0);
    this.totalSizePercent = (this.totalSize / 1000000) * 100;

    // Conversion CSV en JSON
    this.csvToJson(event.currentFiles[0]); // Passe le premier fichier à la méthode de conversion
  }

  onCategoryChange(): void {
    // this.resetToDefault();
    const selectedCategoryObj = this.categories.find(
      (category) => category.value === this.selectedCategory
    );
    if (selectedCategoryObj) {
      this.currentUrl = selectedCategoryObj.url; // Met à jour l'URL basée sur la catégorie sélectionnée
      this.currentUrlDisplay = (selectedCategoryObj as any).urlAfficherTemplate || selectedCategoryObj.url; // URL d'affichage
      this.currentFileSample = selectedCategoryObj.fileSample; // Met à jour le fichier modèle

      this.currentCategoryLabel = selectedCategoryObj.label;
      this.currentFileSample = selectedCategoryObj.fileSample;
      this.currentFileName = this.currentFileSample.split('/').pop() || 'Nom de fichier non disponible'; // Valeur par défaut
    }
    this.updatePostRequest(); // Met à jour la requête POST avec la nouvelle URL
  }

  updatePostRequest(): void {
    this.postRequest = {
      method: 'POST',
      url: this.currentUrlDisplay || this.currentUrl, // Utilise l'URL d'affichage si disponible
    };
  }

  resetToDefault(): void {
    // Réinitialiser la catégorie sélectionnée (aucune par défaut)
    this.selectedCategory = '';
    this.currentUrl = '';
    this.currentUrlDisplay = '';
    this.currentFileSample = '';
    this.currentCategoryLabel = '';
    this.currentFileName = '';

    // Réinitialiser la taille et le pourcentage
    this.totalSize = 0;
    this.totalSizePercent = 0;

    // Réinitialiser les données JSON
    this.convertedJson = [];

    // Réinitialiser le composant p-fileUpload
    if (this.fileUpload) {
      this.fileUpload.clear();
    }
  }

  csvToJson(file: any): void {
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        const csvData = reader.result as string;
        this.papaParse.parse(csvData, {
          header: true,
          delimiter: ';', // Séparateur de colonnes
          skipEmptyLines: true, // Ignore les lignes vides
          complete: (result) => {
            this.convertedJson = result.data;
            this.updatePostRequest(); // Mettre à jour la requête POST
            console.log('JSON résultant :', this.convertedJson);
          },
          error: (error) => {
            console.error('Erreur de parsing CSV :', error);
            this.errorMessage = 'Erreur lors du traitement du fichier CSV.';
          },
        });
      };
      reader.readAsText(file);
    }
  }


  choose(event: any, callback: Function): void {
    callback(event);
  }



  //******************************* Drawer*******************************************
  isTutorialDrawerVisible: boolean = false;

  showDrawer: boolean = false;

  //******************************* Modal SIGAPS*******************************************
  showSigapsResultsModal: boolean = false;
  sigapsResults: any[] = [];
  readonly SIGAPS_PREVIEW_LIMIT = 10; // Nombre maximum d'éléments à afficher dans l'aperçu

  //******************************* Modal Garde et Astreinte*******************************************
  showGardeAstreinteResultsModal: boolean = false;
  gardeAstreinteResults: any[] = [];
  readonly GARDE_ASTREINTE_PREVIEW_LIMIT = 10;

  //******************************* Modal Libérale*******************************************
  showLiberaleResultsModal: boolean = false;
  liberaleResults: any[] = [];
  readonly LIBERALE_PREVIEW_LIMIT = 10;

  //******************************* Modal Actes*******************************************
  showActesResultsModal: boolean = false;
  actesResults: any[] = [];
  readonly ACTES_PREVIEW_LIMIT = 10;

  //******************************* Loading State*******************************************
  isImporting: boolean = false;

  openTutorialDrawer(): void {
    this.isTutorialDrawerVisible = true;
  }

  closeTutorialDrawer(): void {
    this.isTutorialDrawerVisible = false;
  }

  //******************************* Modal SIGAPS Methods*******************************************

  openSigapsResultsModal(results: any[]): void {
    this.sigapsResults = results;
    this.showSigapsResultsModal = true;
  }

  closeSigapsResultsModal(): void {
    this.showSigapsResultsModal = false;
    this.sigapsResults = [];
  }

  //******************************* Modal Garde et Astreinte Methods*******************************************

  openGardeAstreinteResultsModal(results: any[]): void {
    this.gardeAstreinteResults = results;
    this.showGardeAstreinteResultsModal = true;
  }

  closeGardeAstreinteResultsModal(): void {
    this.showGardeAstreinteResultsModal = false;
    this.gardeAstreinteResults = [];
  }

  getDisplayedGardeAstreinteResults(): any[] {
    return this.gardeAstreinteResults.slice(0, this.GARDE_ASTREINTE_PREVIEW_LIMIT);
  }

  //******************************* Modal Libérale Methods*******************************************

  openLiberaleResultsModal(results: any[]): void {
    this.liberaleResults = results;
    this.showLiberaleResultsModal = true;
  }

  closeLiberaleResultsModal(): void {
    this.showLiberaleResultsModal = false;
    this.liberaleResults = [];
  }

  getDisplayedLiberaleResults(): any[] {
    return this.liberaleResults.slice(0, this.LIBERALE_PREVIEW_LIMIT);
  }

  //******************************* Modal Actes Methods*******************************************

  openActesResultsModal(results: any[]): void {
    this.actesResults = results;
    this.showActesResultsModal = true;
  }

  closeActesResultsModal(): void {
    this.showActesResultsModal = false;
    this.actesResults = [];
  }

  getDisplayedActesResults(): any[] {
    return this.actesResults.slice(0, this.ACTES_PREVIEW_LIMIT);
  }

  formatDate(dateString: string): string {
    if (!dateString || dateString === 'null' || dateString === 'undefined') return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'N/A';
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      return 'N/A';
    }
  }

  formatValue(value: any): string {
    if (value === null || value === undefined || value === 'null' || value === 'undefined' || value === '') {
      return 'N/A';
    }
    return value.toString();
  }

  getDisplayedResults(): any[] {
    // Afficher seulement les premiers résultats pour éviter la surcharge de l'interface
    return this.sigapsResults.slice(0, this.SIGAPS_PREVIEW_LIMIT);
  }

  /**
   * Envoyer les données via le service ImportationService
   */
  sendDataToServer(): void {
    if (!this.convertedJson || this.convertedJson.length === 0) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Aucune donnée',
        detail: 'Veuillez d\'abord charger un fichier CSV.',
      });
      return;
    }

    // Empêcher les envois multiples
    if (this.isImporting) {
      return;
    }

    // Validation des données selon la catégorie
    const validation = this.importationFactoryService.validateDataForCategory(this.selectedCategory as ImportCategory, this.convertedJson);
    if (!validation.isValid) {
      this.messageService.add({
        severity: 'error',
        summary: 'Données invalides',
        detail: validation.errors.join(', '),
      });
      return;
    }

    // Afficher les informations de l'utilisateur connecté
    const userInfo = this.importationFactoryService.getCurrentUserInfo();
    console.log('👤 User info for import:', userInfo);

    // Debug: Afficher les données qui vont être envoyées
    console.log('📤 Data to be sent:', {
      category: this.selectedCategory,
      dataCount: this.convertedJson.length,
      sampleData: this.convertedJson.slice(0, 2),
      fullData: this.convertedJson
    });

    // Debug spécifique pour garde-et-astreinte
    if (this.selectedCategory === 'garde-et-astreinte') {
      console.log('🛡️ Garde et Astreinte specific debug:', {
        expectedFormat: {
          typeGarde: 'string',
          totalGarde: 'string',
          totalAstreinte: 'string',
          dateDebut: 'YYYY-MM-DD',
          dateFin: 'YYYY-MM-DD',
          dateGarde: 'YYYY-MM-DD',
          agentHrU: 'string'
        },
        actualData: this.convertedJson
      });
    }

    // Debug spécifique pour libérale
    if (this.selectedCategory === 'liberale') {
      console.log('💼 Libérale specific debug:', {
        expectedFormat: {
          codeActe: 'string',
          nomActe: 'string',
          typeActe: 'string',
          dateDebut: 'YYYY-MM-DD',
          dateFin: 'YYYY-MM-DD',
          dateRealisation: 'YYYY-MM-DD',
          tarif: 'string',
          agentHrU: 'string'
        },
        actualData: this.convertedJson
      });
    }

    // Démarrer le loader
    this.isImporting = true;

    // Envoyer les données
    this.importationFactoryService.importData(this.selectedCategory as ImportCategory, this.convertedJson).subscribe({
      next: (response) => {
        console.log('✅ Import response:', response);
        this.isImporting = false; // Arrêter le loader
        this.handleImportResponse(response);
      },
      error: (error) => {
        console.error('❌ Import failed:', error);
        console.log('📢 Adding error notification from subscribe:', error.message);
        this.isImporting = false; // Arrêter le loader en cas d'erreur
        this.messageService.add({
          severity: 'error',
          summary: 'Erreur d\'import',
          detail: error.message || 'Une erreur est survenue lors de l\'import.',
          sticky: true
        });
      }
    });
  }

  /**
   * Gérer la réponse d'import selon les différents cas
   */
  handleImportResponse(response: UnifiedImportResponse): void {
    if (!response.success || response.status === 'error') {
      // Cas d'erreur complète
      console.log('📢 Adding error notification:', response.message);
      this.messageService.add({
        severity: 'error',
        summary: 'Erreur d\'import',
        detail: response.message || 'Une erreur est survenue lors de l\'import.',
        sticky: true // L'utilisateur doit fermer manuellement
      });
      return;
    }

    if (response.success && response.status === 'success') {
      const { processed, created, updated, errors } = response;

      // Cas 1: Succès complet (100% OK)
      if (!errors || errors.length === 0) {
        let successMessage = `Import terminé avec succès !\n`;
        successMessage += `• ${processed || 0} enregistrements traités\n`;
        successMessage += `• ${created || 0} créations\n`;
        successMessage += `• ${updated || 0} mises à jour`;

        console.log('📢 Adding success notification:', successMessage);

        // Vérifier si c'est un import SIGAPS avec des résultats à afficher
        if (this.selectedCategory === 'sigaps' && response.results && response.results.length > 0) {
          console.log('🎯 Opening SIGAPS results modal with:', response.results);
          // Ouvrir le modal avec les résultats SIGAPS
          this.openSigapsResultsModal(response.results);
        }
        // Vérifier si c'est un import Garde et Astreinte avec des résultats à afficher
        else if (this.selectedCategory === 'garde-et-astreinte' && response.results && response.results.length > 0) {
          console.log('🛡️ Opening Garde et Astreinte results modal with:', response.results);
          // Ouvrir le modal avec les résultats Garde et Astreinte
          this.openGardeAstreinteResultsModal(response.results);
        }
        // Vérifier si c'est un import Libérale avec des résultats à afficher
        else if (this.selectedCategory === 'liberale' && response.results && response.results.length > 0) {
          console.log('💼 Opening Libérale results modal with:', response.results);
          // Ouvrir le modal avec les résultats Libérale
          this.openLiberaleResultsModal(response.results);
        }
        // Vérifier si c'est un import Actes avec des résultats à afficher
        else if (this.selectedCategory === 'actes' && response.results && response.results.length > 0) {
          console.log('📋 Opening Actes results modal with:', response.results);
          // Ouvrir le modal avec les résultats Actes
          this.openActesResultsModal(response.results);
        } else {
          // Afficher la notification normale pour les autres types
          this.messageService.add({
            severity: 'success',
            summary: 'Import réussi',
            detail: successMessage,
            sticky: true
          });
        }

        // Ne pas réinitialiser automatiquement - laisser l'utilisateur voir le résultat
      }
      // Cas 2: Succès partiel avec erreurs
      else {
        const totalErrors = errors.length;
        const successCount = (created || 0) + (updated || 0);

        // Message principal avec statistiques
        let warningMessage = `Import terminé avec ${totalErrors} erreur(s) :\n`;
        warningMessage += `• ${successCount} enregistrements traités avec succès\n`;
        warningMessage += `• ${totalErrors} enregistrements en erreur\n\n`;
        warningMessage += `Corrigez juste les erreurs ci-dessous et relancez le même fichier.`;

        this.messageService.add({
          severity: 'warn',
          summary: ' Import partiel',
          detail: warningMessage,
          sticky: true // L'utilisateur doit fermer manuellement
        });

        // Afficher chaque erreur individuellement pour que l'utilisateur sache quoi corriger
        this.showDetailedErrors(errors);
      }
    }
  }

  /**
   * Afficher le détail des erreurs métier de manière optimisée
   */
  showDetailedErrors(errors: any[]): void {
    const maxIndividualErrors = 5; // Seuil pour affichage individuel

    if (errors.length <= maxIndividualErrors) {
      // Peu d'erreurs : affichage individuel
      errors.forEach((error, index) => {
        let errorDetail = this.formatErrorDetail(error, index + 1);

        this.messageService.add({
          severity: 'error',
          summary: `❌ Erreur ligne ${index + 1}`,
          detail: errorDetail,
          sticky: true
        });
      });
    } else {
      // Beaucoup d'erreurs : affichage groupé intelligent
      let consolidatedMessage = `📋 Liste des ${errors.length} erreurs à corriger :\n\n`;

      errors.forEach((error, index) => {
        const lineNumber = index + 1;
        const errorSummary = this.formatErrorSummary(error, lineNumber);
        consolidatedMessage += `${lineNumber}. ${errorSummary}\n`;
      });

      consolidatedMessage += `\n💡 Corrigez ces erreurs dans votre fichier CSV et relancez l'import.`;

      this.messageService.add({
        severity: 'error',
        summary: `❌ ${errors.length} erreurs détectées`,
        detail: consolidatedMessage,
        sticky: true
      });
    }

    // Log pour debug technique et copie facile
    console.group('📋 Détail des erreurs d\'import');
    console.table(errors.map((error, index) => ({
      ligne: index + 1,
      uConnexion: error.uConnexion || 'N/A',
      codeUf: error.codeUf || 'N/A',
      erreur: error.error
    })));
    console.groupEnd();
  }

  /**
   * Formater le détail d'une erreur pour affichage individuel
   */
  private formatErrorDetail(error: any, lineNumber: number): string {
    let detail = `Ligne ${lineNumber}\n`;

    if (error.uConnexion && error.codeUf) {
      detail += `uConnexion: ${error.uConnexion} | UF: ${error.codeUf}\n`;
    } else if (error.uConnexion) {
      detail += `uConnexion: ${error.uConnexion}\n`;
    } else if (error.codeUf) {
      detail += `UF: ${error.codeUf}\n`;
    }

    detail += `Erreur: ${error.error}`;
    return detail;
  }

  /**
   * Formater un résumé d'erreur pour affichage groupé
   */
  private formatErrorSummary(error: any, lineNumber: number): string {
    let summary = '';

    if (error.uConnexion && error.codeUf) {
      summary = `${error.uConnexion} (${error.codeUf}) - ${error.error}`;
    } else if (error.uConnexion) {
      summary = `${error.uConnexion} - ${error.error}`;
    } else if (error.codeUf) {
      summary = `UF ${error.codeUf} - ${error.error}`;
    } else {
      summary = error.error;
    }

    return summary;
  }

}
