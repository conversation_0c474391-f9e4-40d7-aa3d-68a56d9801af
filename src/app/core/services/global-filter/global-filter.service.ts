import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AgentModel } from '../../models/activite/ActiviteModel';
import { PoleModel } from '../../models/structure/PoleModel';
import { ServiceModel } from '../../models/structure/ServiceModel';
import { CRModel } from '../../models/structure/CRModel';

/**
 * Interface for a date period
 */
export interface DatePeriod {
  dateDebut: Date | null;
  dateFin: Date | null;
}

/**
 * Interface for the global filter state
 */
export interface GlobalFilterState {
  practitioner: AgentModel | null;
  pole: PoleModel | null;
  service: CRModel | null; // Renommé conceptuellement mais garde le nom 'service' pour compatibilité
  venueType: VenueType | null;
  dateDebut: Date | null;
  dateFin: Date | null;
  // Comparative periods
  p1: DatePeriod | null; // Current period (same as dateDebut/dateFin)
  p2: DatePeriod | null; // Same period, previous year
  p3: DatePeriod | null; // Same period, two years ago
}

/**
 * Enum for venue types
 */
export enum VenueType {
  CONSULTATION = 'CONSULTATION',      // typeVenue = 1
  HOSPITALIZATION = 'HOSPITALIZATION', // typeVenue = 2
  URGENCE = 'URGENCE',                 // typeVenue = 3
  BOTH = 'BOTH'                        // Tous les types
}

/**
 * Service for managing global filters
 * These filters are applied across the entire application
 */
@Injectable({
  providedIn: 'root'
})
export class GlobalFilterService {
  // Storage key for persisting filters
  private readonly STORAGE_KEY = 'global_filters';

  // Default filter state
  private readonly DEFAULT_STATE: GlobalFilterState = {
    practitioner: null,
    pole: null,
    service: null,
    venueType: null,
    dateDebut: null,
    dateFin: null,
    p1: null,
    p2: null,
    p3: null
  };

  // BehaviorSubject to track filter state changes
  private filterState = new BehaviorSubject<GlobalFilterState>(this.DEFAULT_STATE);

  constructor() {
    this.loadFromStorage();
  }

  /**
   * Get the current filter state as an observable
   */
  getFilterState(): Observable<GlobalFilterState> {
    return this.filterState.asObservable();
  }

  /**
   * Get the current filter state value
   */
  getCurrentFilterState(): GlobalFilterState {
    return this.filterState.getValue();
  }

  /**
   * Set the practitioner filter
   * @param practitioner The practitioner to filter by, or null to clear the filter
   */
  setPractitioner(practitioner: AgentModel | null): void {
    const currentState = this.filterState.getValue();
    const newState = { ...currentState, practitioner };
    this.filterState.next(newState);
    this.saveToStorage(newState);
  }

  /**
   * Set the pole filter
   * @param pole The pole to filter by, or null to clear the filter
   */
  setPole(pole: PoleModel | null): void {
    const currentState = this.filterState.getValue();
    const newState = { ...currentState, pole };
    this.filterState.next(newState);
    this.saveToStorage(newState);
  }

  /**
   * Set the CR filter (using service field for compatibility)
   * @param service The CR to filter by, or null to clear the filter
   */
  setService(service: CRModel | null): void {
    const currentState = this.filterState.getValue();
    const newState = { ...currentState, service };
    this.filterState.next(newState);
    this.saveToStorage(newState);
  }

  /**
   * Set the venue type filter
   * @param venueType The venue type to filter by, or null to clear the filter
   */
  setVenueType(venueType: VenueType | null): void {
    const currentState = this.filterState.getValue();
    const newState = { ...currentState, venueType };
    this.filterState.next(newState);
    this.saveToStorage(newState);
  }

  /**
   * Set the date range filter and calculate comparative periods
   * @param dateDebut The start date to filter by, or null to clear the filter
   * @param dateFin The end date to filter by, or null to clear the filter
   */
  setDateRange(dateDebut: Date | null, dateFin: Date | null): void {
    console.log('Setting date range in service:', dateDebut, dateFin);
    const currentState = this.filterState.getValue();

    // Create p1 (current period)
    const p1: DatePeriod | null = dateDebut && dateFin ? {
      dateDebut: new Date(dateDebut),
      dateFin: new Date(dateFin)
    } : null;

    // Create p2 (previous year)
    let p2: DatePeriod | null = null;
    if (p1 && p1.dateDebut && p1.dateFin) {
      const p2DateDebut = new Date(p1.dateDebut);
      p2DateDebut.setFullYear(p2DateDebut.getFullYear() - 1);

      const p2DateFin = new Date(p1.dateFin);
      p2DateFin.setFullYear(p2DateFin.getFullYear() - 1);

      p2 = {
        dateDebut: p2DateDebut,
        dateFin: p2DateFin
      };
    }

    // Create p3 (two years ago)
    let p3: DatePeriod | null = null;
    if (p1 && p1.dateDebut && p1.dateFin) {
      const p3DateDebut = new Date(p1.dateDebut);
      p3DateDebut.setFullYear(p3DateDebut.getFullYear() - 2);

      const p3DateFin = new Date(p1.dateFin);
      p3DateFin.setFullYear(p3DateFin.getFullYear() - 2);

      p3 = {
        dateDebut: p3DateDebut,
        dateFin: p3DateFin
      };
    }

    const newState = { ...currentState, dateDebut, dateFin, p1, p2, p3 };
    console.log('New state with periods:', newState);
    this.filterState.next(newState);
    this.saveToStorage(newState);
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.filterState.next(this.DEFAULT_STATE);
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * Clear only selection filters (practitioner, pole, service, venue type)
   * Keep periods and dates intact
   */
  clearSelectionFilters(): void {
    const currentState = this.filterState.getValue();
    const newState: GlobalFilterState = {
      ...currentState,
      practitioner: null,
      pole: null,
      service: null,
      venueType: null
      // Keep dateDebut, dateFin, p1, p2, p3 unchanged
    };
    this.filterState.next(newState);
    this.saveToStorage(newState);
  }

  /**
   * Load filters from localStorage
   */
  private loadFromStorage(): void {
    try {
      const storedState = localStorage.getItem(this.STORAGE_KEY);
      if (storedState) {
        const parsedState = JSON.parse(storedState);
        console.log('Loaded state from storage:', parsedState);
        console.log('Date strings from storage:', parsedState.dateDebut, parsedState.dateFin);
        console.log('Period strings from storage:', parsedState.p1, parsedState.p2, parsedState.p3);

        // Create a new state object with properly typed properties
        const typedState: GlobalFilterState = {
          practitioner: parsedState.practitioner ? parsedState.practitioner as AgentModel : null,
          pole: parsedState.pole ? parsedState.pole as PoleModel : null,
          service: parsedState.service ? parsedState.service as CRModel : null,
          venueType: parsedState.venueType ? parsedState.venueType as VenueType : null,
          dateDebut: null,
          dateFin: null,
          p1: null,
          p2: null,
          p3: null
        };

        // Helper function to parse a date string
        const parseDate = (dateString: string | null): Date | null => {
          if (!dateString) return null;
          try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
              console.error('Invalid date:', dateString);
              return null;
            }
            return date;
          } catch (e) {
            console.error('Error parsing date:', dateString, e);
            return null;
          }
        };

        // Helper function to parse a period
        const parsePeriod = (periodObj: any): DatePeriod | null => {
          if (!periodObj) return null;
          const dateDebut = parseDate(periodObj.dateDebut);
          const dateFin = parseDate(periodObj.dateFin);
          if (!dateDebut || !dateFin) return null;
          return { dateDebut, dateFin };
        };

        // Handle dateDebut
        if (parsedState.dateDebut) {
          try {
            // Try to parse the date string
            const dateDebut = new Date(parsedState.dateDebut);
            // Check if the date is valid
            if (!isNaN(dateDebut.getTime())) {
              // Ensure it's set to the first day of the month at noon
              dateDebut.setDate(1);
              dateDebut.setHours(12, 0, 0, 0);
              typedState.dateDebut = dateDebut;
            }
          } catch (e) {
            console.error('Error parsing dateDebut:', e);
          }
        }

        // Handle dateFin
        if (parsedState.dateFin) {
          try {
            // Try to parse the date string
            const dateFin = new Date(parsedState.dateFin);
            // Check if the date is valid
            if (!isNaN(dateFin.getTime())) {
              // Get the current date
              const currentDate = new Date();
              const currentYear = currentDate.getFullYear();
              const currentMonth = currentDate.getMonth();
              const currentDay = currentDate.getDate();

              // Get the year and month from the parsed date
              const year = dateFin.getFullYear();
              const month = dateFin.getMonth();

              // If dateFin is the current month and year, use the current day
              // Otherwise, use the last day of the month
              let day;
              if (year === currentYear && month === currentMonth) {
                day = currentDay;
              } else {
                day = new Date(year, month + 1, 0).getDate();
              }

              dateFin.setDate(day);
              dateFin.setHours(12, 0, 0, 0);
              typedState.dateFin = dateFin;
            }
          } catch (e) {
            console.error('Error parsing dateFin:', e);
          }
        }

        // Handle periods
        typedState.p1 = parsePeriod(parsedState.p1);
        typedState.p2 = parsePeriod(parsedState.p2);
        typedState.p3 = parsePeriod(parsedState.p3);

        // If periods are not set but dateDebut and dateFin are, calculate them
        if (typedState.dateDebut && typedState.dateFin && !typedState.p1) {
          // This will recalculate p1, p2, and p3
          this.setDateRange(typedState.dateDebut, typedState.dateFin);
          return; // setDateRange already updates the state
        }

        console.log('Date objects after conversion:', typedState.dateDebut, typedState.dateFin);
        console.log('Period objects after conversion:', typedState.p1, typedState.p2, typedState.p3);
        console.log('Typed state with dates and periods:', typedState);
        this.filterState.next(typedState);
      }
    } catch (error) {
      console.error('Error loading global filters from storage:', error);
      // If there's an error, use the default state
      this.filterState.next(this.DEFAULT_STATE);
    }
  }

  /**
   * Save filters to localStorage
   * Only save the necessary properties to ensure proper reconstruction when loading
   */
  private saveToStorage(state: GlobalFilterState): void {
    try {
      console.log('Saving state to storage with dates:', state.dateDebut, state.dateFin);
      console.log('Saving periods:', state.p1, state.p2, state.p3);

      // Helper function to format a date as YYYY-MM-DD
      const formatDate = (date: Date | null): string | null => {
        if (!date) return null;
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      };

      // Helper function to format a period
      const formatPeriod = (period: DatePeriod | null): { dateDebut: string | null, dateFin: string | null } | null => {
        if (!period) return null;
        return {
          dateDebut: formatDate(period.dateDebut),
          dateFin: formatDate(period.dateFin)
        };
      };

      // Create a simplified state object with only the necessary properties
      const simplifiedState = {
        practitioner: state.practitioner ? {
          '@id': state.practitioner['@id'],
          '@type': state.practitioner['@type'],
          titre: state.practitioner.titre,
          prenom: state.practitioner.prenom,
          nom: state.practitioner.nom,
          fullName: state.practitioner.fullName,
          categorie: state.practitioner.categorie
        } : null,
        pole: state.pole ? {
          '@id': state.pole['@id'],
          '@type': state.pole['@type'],
          libelle: state.pole.libelle,
          polecode: state.pole.polecode
        } : null,
        service: state.service ? {
          '@id': state.service['@id'],
          '@type': state.service['@type'],
          libelle: state.service.libelle,
          crcode: state.service.crcode
        } : null,
        venueType: state.venueType,
        // Store dates as ISO strings but ensure they're in the local timezone
        // by creating a string in the format YYYY-MM-DD
        dateDebut: formatDate(state.dateDebut),
        dateFin: formatDate(state.dateFin),
        // Store periods
        p1: formatPeriod(state.p1),
        p2: formatPeriod(state.p2),
        p3: formatPeriod(state.p3)
      };

      console.log('Simplified state for storage:', simplifiedState);
      const jsonString = JSON.stringify(simplifiedState);
      console.log('JSON string for storage:', jsonString);
      localStorage.setItem(this.STORAGE_KEY, jsonString);
    } catch (error) {
      console.error('Error saving global filters to storage:', error);
    }
  }
}
