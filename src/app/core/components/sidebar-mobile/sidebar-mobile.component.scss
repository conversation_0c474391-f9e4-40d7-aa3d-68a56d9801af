/* ============================================== */
/* STYLES POUR SIDEBAR MOBILE UNIQUEMENT */
/* ============================================== */

/* Assurer que la sidebar mobile s'affiche toujours correctement */
:host {
  position: relative;
  z-index: 50;
}

/* Overlay de fond */
.bg-gray-600 {
  backdrop-filter: blur(2px);
}

/* Animations pour l'ouverture/fermeture */
.sidebar-mobile-enter {
  animation: slideInLeft 0.3s ease-out;
}

.sidebar-mobile-leave {
  animation: slideOutLeft 0.3s ease-in;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* Améliorer les zones de touch sur mobile */
.nav-item {
  min-height: 48px;
  display: flex;
  align-items: center;
}

/* Styles pour le bouton de fermeture */
button:focus {
  outline: 2px solid #67e8f9;
  outline-offset: 2px;
}

/* Responsive - Assurer la visibilité sur tous les écrans mobiles */
@media (max-width: 1023px) {
  :host {
    display: block !important;
    visibility: visible !important;
  }
}
