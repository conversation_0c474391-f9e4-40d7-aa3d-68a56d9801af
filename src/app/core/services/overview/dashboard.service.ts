import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, Subscription, catchError, debounceTime, distinctUntilChanged, map, of, tap } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { DashboardData, RepartitionActesParType, EvolutionMensuelle, EvolutionMensuelleParType, RepartitionActesParServices, PerformancePraticien, ActesInterneExterne, HeatmapData, RepartitionParCategoriePrincipale, RepartitionParUF, RepartitionParProvenance, TauxVariation } from "../../models/overview/dashboard.model";
import { ActiviteModel } from '../../models/activite/ActiviteModel';
import { GlobalFilterService, GlobalFilterState } from '../global-filter/global-filter.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class DashboardService implements OnDestroy {
  private filterSubscription: Subscription;
  private dashboardDataSubject = new BehaviorSubject<DashboardData | null>(null);
  dashboardData$: Observable<DashboardData | null> = this.dashboardDataSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  loading$: Observable<boolean> = this.loadingSubject.asObservable();

  constructor(
    private http: HttpClient,
    private globalFilterService: GlobalFilterService
  ) {
    this.loadDashboardData(); // Charger les données au démarrage

    // Subscribe to filter changes to refresh data when periods change
    // Use debounceTime to avoid multiple refreshes when multiple filter properties change at once
    // Use distinctUntilChanged to avoid refreshes when the filter state hasn't actually changed
    this.filterSubscription = this.globalFilterService.getFilterState()
      .pipe(
        debounceTime(300), // Wait 300ms after the last filter change
        distinctUntilChanged((prev, curr) => {
          // Return true if periods are the same (no refresh needed)
          // Return false if periods have changed (refresh needed)
          const periodsEqual = JSON.stringify(prev.p1) === JSON.stringify(curr.p1) &&
                              JSON.stringify(prev.p2) === JSON.stringify(curr.p2) &&
                              JSON.stringify(prev.p3) === JSON.stringify(curr.p3);
          return periodsEqual;
        })
      )
      .subscribe(() => {
        console.log('Filter state changed, refreshing dashboard data');
        this.loadDashboardData();
      });
  }

  /**
   * Clean up subscriptions when the service is destroyed
   */
  ngOnDestroy(): void {
    if (this.filterSubscription) {
      this.filterSubscription.unsubscribe();
    }
  }

  private loadDashboardData(): void {
    this.loadingSubject.next(true);
    const filterState = this.globalFilterService.getCurrentFilterState();

    if (!filterState.p1 || !filterState.p2 || !filterState.p3) {
      console.error('Périodes non définies dans les filtres globaux');
      // Fallback to mock data if periods are not defined
      this.loadMockData();
      return;
    }

    // Format dates for API call (YYYY-MM-DD)
    const formatDate = (date: Date | null): string => {
      if (!date) return '';
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };

    const p1Start = formatDate(filterState.p1.dateDebut);
    const p1End = formatDate(filterState.p1.dateFin);
    const p2Start = formatDate(filterState.p2.dateDebut);
    const p2End = formatDate(filterState.p2.dateFin);
    const p3Start = formatDate(filterState.p3.dateDebut);
    const p3End = formatDate(filterState.p3.dateFin);

    // Call the real API with periods from global filter
    const apiUrl = `${environment.api_url}/api/actes?p1Start=${p1Start}&p1End=${p1End}&p2Start=${p2Start}&p2End=${p2End}&p3Start=${p3Start}&p3End=${p3End}`;

    this.http.get<ActiviteModel[]>(apiUrl).pipe(
      map(activities => this.transformApiDataToDashboardData(activities, filterState)),
      catchError(error => {
        console.error('Erreur lors du chargement des données du tableau de bord:', error);
        // Fallback to mock data on error
        return this.loadMockData();
      })
    ).subscribe(data => {
      this.dashboardDataSubject.next(data);
      this.loadingSubject.next(false);
    });
  }

  private loadMockData(): Observable<DashboardData> {
    this.loadingSubject.next(true);
    return this.http.get<DashboardData>('/assets/data/overview/dashboard.json').pipe(
      catchError(error => {
        console.error('Erreur lors du chargement des données mock du tableau de bord:', error);
        this.loadingSubject.next(false);
        return of({
          repartitionActesParType: [],
          evolutionMensuelle: [],
          repartitionActesParServices: [],
          performancePraticiens: [],
          actesInterneExterne: []
        });
      }),
      tap(() => this.loadingSubject.next(false))
    );
  }

  private transformApiDataToDashboardData(activities: any, filterState?: GlobalFilterState): DashboardData {
    // Ensure activities is an array
    if (!Array.isArray(activities)) {
      console.error('Expected activities to be an array but got:', typeof activities, activities);

      // Try to extract array from common API response structures
      // First check for the specific structure in the API response
      if (activities?.member && Array.isArray(activities.member)) {
        console.log('Extracted array from activities.member');
        activities = activities.member;
      } else if (activities?.hydra?.member && Array.isArray(activities.hydra.member)) {
        console.log('Extracted array from activities.hydra.member');
        activities = activities.hydra.member;
      } else if (activities?.data && Array.isArray(activities.data)) {
        console.log('Extracted array from activities.data');
        activities = activities.data;
      } else if (activities?.['hydra:member'] && Array.isArray(activities['hydra:member'])) {
        console.log('Extracted array from activities["hydra:member"]');
        activities = activities['hydra:member'];
      } else if (activities?.items && Array.isArray(activities.items)) {
        console.log('Extracted array from activities.items');
        activities = activities.items;
      } else if (activities?.results && Array.isArray(activities.results)) {
        console.log('Extracted array from activities.results');
        activities = activities.results;
      } else {
        console.error('Could not extract array from activities, using empty array instead');
        activities = [];
      }
    }

    // Debug: Check periodeType values
    console.log('Activities length:', activities.length);
    console.log('Sample activity:', activities.length > 0 ? activities[0] : 'No activities');
    console.log('periodeType values:', activities.map((a: ActiviteModel) => a.periodeType).filter((v: string, i: number, a: string[]) => a.indexOf(v) === i));

    // Create arrays for each period based on date ranges
    const p1Activities: ActiviteModel[] = [];
    const p2Activities: ActiviteModel[] = [];
    const p3Activities: ActiviteModel[] = [];

    // Assign activities to periods based on dateRealisation
    activities.forEach((activity: ActiviteModel) => {
      if (!activity.dateRealisation) {
        return; // Skip activities without dateRealisation
      }

      const activityDate = new Date(activity.dateRealisation);

      // Check if the activity date falls within any of the periods
      if (filterState?.p1?.dateDebut && filterState.p1.dateFin &&
          activityDate >= filterState.p1.dateDebut && activityDate <= filterState.p1.dateFin) {
        p1Activities.push(activity);
      } else if (filterState?.p2?.dateDebut && filterState.p2.dateFin &&
          activityDate >= filterState.p2.dateDebut && activityDate <= filterState.p2.dateFin) {
        p2Activities.push(activity);
      } else if (filterState?.p3?.dateDebut && filterState.p3.dateFin &&
          activityDate >= filterState.p3.dateDebut && activityDate <= filterState.p3.dateFin) {
        p3Activities.push(activity);
      }
    });

    console.log('p1Activities length:', p1Activities.length);
    console.log('p2Activities length:', p2Activities.length);
    console.log('p3Activities length:', p3Activities.length);

    // 1. Répartition des actes par type
    const typeMapP1 = new Map<string, number>();
    const typeMapP2 = new Map<string, number>();
    const typeMapP3 = new Map<string, number>();

    p1Activities.forEach((activity: ActiviteModel) => {
      const type = activity.typeActe || 'Non défini';
      typeMapP1.set(type, (typeMapP1.get(type) || 0) + activity.nombreDeRealisation);
    });

    p2Activities.forEach((activity: ActiviteModel) => {
      const type = activity.typeActe || 'Non défini';
      typeMapP2.set(type, (typeMapP2.get(type) || 0) + activity.nombreDeRealisation);
    });

    p3Activities.forEach((activity: ActiviteModel) => {
      const type = activity.typeActe || 'Non défini';
      typeMapP3.set(type, (typeMapP3.get(type) || 0) + activity.nombreDeRealisation);
    });

    // Get all unique types across all periods
    const allTypes = new Set<string>([
      ...Array.from(typeMapP1.keys()),
      ...Array.from(typeMapP2.keys()),
      ...Array.from(typeMapP3.keys())
    ]);

    const repartitionActesParType: RepartitionActesParType[] = Array.from(allTypes)
      .map(type => ({
        type,
        totalP1: typeMapP1.get(type) || 0,
        totalP2: typeMapP2.get(type) || 0,
        totalP3: typeMapP3.get(type) || 0,
        total: (typeMapP1.get(type) || 0) + (typeMapP2.get(type) || 0) + (typeMapP3.get(type) || 0)
      }))
      .sort((a, b) => b.total - a.total);

    // 2. Évolution mensuelle des actes
    const months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    const evolutionMensuelle: EvolutionMensuelle[] = [];
    const evolutionMensuelleParType: EvolutionMensuelleParType[] = [];

    // Create a map for each period to count activities by month
    const p1MonthMap = new Map<number, number>();
    const p2MonthMap = new Map<number, number>();
    const p3MonthMap = new Map<number, number>();

    // Create maps for each period and act type to count activities by month
    const ccamP1MonthMap = new Map<number, number>();
    const ccamP2MonthMap = new Map<number, number>();
    const ccamP3MonthMap = new Map<number, number>();
    const ngapP1MonthMap = new Map<number, number>();
    const ngapP2MonthMap = new Map<number, number>();
    const ngapP3MonthMap = new Map<number, number>();
    const laboP1MonthMap = new Map<number, number>();
    const laboP2MonthMap = new Map<number, number>();
    const laboP3MonthMap = new Map<number, number>();

    p1Activities.forEach((activity: ActiviteModel) => {
      const month = activity.mois;
      if (month >= 1 && month <= 12) {
        p1MonthMap.set(month, (p1MonthMap.get(month) || 0) + activity.nombreDeRealisation);

        // Also count by act type
        const actType = activity.typeActe || 'Non défini';
        if (actType.includes('CCAM')) {
          ccamP1MonthMap.set(month, (ccamP1MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        } else if (actType.includes('NGAP') || actType.toLowerCase().includes('ngap')) {
          ngapP1MonthMap.set(month, (ngapP1MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        } else if (actType.includes('LABO')) {
          laboP1MonthMap.set(month, (laboP1MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        }
      }
    });

    p2Activities.forEach((activity: ActiviteModel) => {
      const month = activity.mois;
      if (month >= 1 && month <= 12) {
        p2MonthMap.set(month, (p2MonthMap.get(month) || 0) + activity.nombreDeRealisation);

        // Also count by act type
        const actType = activity.typeActe || 'Non défini';
        if (actType.includes('CCAM')) {
          ccamP2MonthMap.set(month, (ccamP2MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        } else if (actType.includes('NGAP') || actType.toLowerCase().includes('ngap')) {
          ngapP2MonthMap.set(month, (ngapP2MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        } else if (actType.includes('LABO')) {
          laboP2MonthMap.set(month, (laboP2MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        }
      }
    });

    p3Activities.forEach((activity: ActiviteModel) => {
      const month = activity.mois;
      if (month >= 1 && month <= 12) {
        p3MonthMap.set(month, (p3MonthMap.get(month) || 0) + activity.nombreDeRealisation);

        // Also count by act type
        const actType = activity.typeActe || 'Non défini';
        if (actType.includes('CCAM')) {
          ccamP3MonthMap.set(month, (ccamP3MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        } else if (actType.includes('NGAP') || actType.toLowerCase().includes('ngap')) {
          ngapP3MonthMap.set(month, (ngapP3MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        } else if (actType.includes('LABO')) {
          laboP3MonthMap.set(month, (laboP3MonthMap.get(month) || 0) + activity.nombreDeRealisation);
        }
      }
    });

    // Determine the range of months to display based on the selected periods
    let minMonth = 1;
    let maxMonth = 12;

    if (filterState) {
      // Find the earliest start month and latest end month across all periods
      const getMonth = (date: Date | null): number => date ? date.getMonth() + 1 : 0;

      const p1StartMonth = filterState.p1?.dateDebut ? getMonth(filterState.p1.dateDebut) : 0;
      const p1EndMonth = filterState.p1?.dateFin ? getMonth(filterState.p1.dateFin) : 0;
      const p2StartMonth = filterState.p2?.dateDebut ? getMonth(filterState.p2.dateDebut) : 0;
      const p2EndMonth = filterState.p2?.dateFin ? getMonth(filterState.p2.dateFin) : 0;
      const p3StartMonth = filterState.p3?.dateDebut ? getMonth(filterState.p3.dateDebut) : 0;
      const p3EndMonth = filterState.p3?.dateFin ? getMonth(filterState.p3.dateFin) : 0;

      // Find the minimum start month and maximum end month
      const startMonths = [p1StartMonth, p2StartMonth, p3StartMonth].filter(m => m > 0);
      const endMonths = [p1EndMonth, p2EndMonth, p3EndMonth].filter(m => m > 0);

      if (startMonths.length > 0) {
        minMonth = Math.min(...startMonths);
      }

      if (endMonths.length > 0) {
        maxMonth = Math.max(...endMonths);
      }

      console.log(`Displaying months from ${months[minMonth - 1]} to ${months[maxMonth - 1]}`);
    }

    // Create evolution data only for months within the selected range
    for (let i = minMonth; i <= maxMonth; i++) {
      evolutionMensuelle.push({
        mois: months[i - 1],
        anneeN: p1MonthMap.get(i) || 0,
        anneeNmoinsUn: p2MonthMap.get(i) || 0,
        anneeNmoins2: p3MonthMap.get(i) || 0
      });

      // Create evolution data by act type for months within the selected range
      evolutionMensuelleParType.push({
        mois: months[i - 1],
        ccamP1: ccamP1MonthMap.get(i) || 0,
        ccamP2: ccamP2MonthMap.get(i) || 0,
        ccamP3: ccamP3MonthMap.get(i) || 0,
        ngapP1: ngapP1MonthMap.get(i) || 0,
        ngapP2: ngapP2MonthMap.get(i) || 0,
        ngapP3: ngapP3MonthMap.get(i) || 0,
        laboP1: laboP1MonthMap.get(i) || 0,
        laboP2: laboP2MonthMap.get(i) || 0,
        laboP3: laboP3MonthMap.get(i) || 0
      });
    }

    // 3. Répartition des actes par services (using ufPrincipal)
    const serviceMapP1 = new Map<string, number>();
    const serviceMapP2 = new Map<string, number>();
    const serviceMapP3 = new Map<string, number>();

    p1Activities.forEach((activity: ActiviteModel) => {
      if (activity.ufPrincipal) {
        const service = activity.ufPrincipal.libelle || 'Non défini';
        serviceMapP1.set(service, (serviceMapP1.get(service) || 0) + activity.nombreDeRealisation);
      }
    });

    p2Activities.forEach((activity: ActiviteModel) => {
      if (activity.ufPrincipal) {
        const service = activity.ufPrincipal.libelle || 'Non défini';
        serviceMapP2.set(service, (serviceMapP2.get(service) || 0) + activity.nombreDeRealisation);
      }
    });

    p3Activities.forEach((activity: ActiviteModel) => {
      if (activity.ufPrincipal) {
        const service = activity.ufPrincipal.libelle || 'Non défini';
        serviceMapP3.set(service, (serviceMapP3.get(service) || 0) + activity.nombreDeRealisation);
      }
    });

    // Get all unique services across all periods
    const allServices = new Set<string>([
      ...Array.from(serviceMapP1.keys()),
      ...Array.from(serviceMapP2.keys()),
      ...Array.from(serviceMapP3.keys())
    ]);

    const repartitionActesParServices: RepartitionActesParServices[] = Array.from(allServices)
      .map(service => ({
        service,
        totalP1: serviceMapP1.get(service) || 0,
        totalP2: serviceMapP2.get(service) || 0,
        totalP3: serviceMapP3.get(service) || 0,
        total: (serviceMapP1.get(service) || 0) + (serviceMapP2.get(service) || 0) + (serviceMapP3.get(service) || 0)
      }))
      .sort((a, b) => b.total - a.total);

    // 4. Performance des praticiens
    const praticienMap = new Map<string, { nom: string, prenom: string, total: number }>();
    p1Activities.forEach((activity: ActiviteModel) => {
      if (activity.agent) {
        const key = activity.agent['@id'];
        const existing = praticienMap.get(key) || {
          nom: activity.agent.nom || 'Non défini',
          prenom: activity.agent.prenom || '',
          total: 0
        };
        existing.total += activity.nombreDeRealisation;
        praticienMap.set(key, existing);
      }
    });

    const performancePraticiens: PerformancePraticien[] = Array.from(praticienMap.values())
      .sort((a, b) => b.total - a.total)
      .slice(0, 5); // Top 5 practitioners

    // 5. Actes par type d'admission
    const admissionMapP1 = new Map<string, number>();
    const admissionMapP2 = new Map<string, number>();
    const admissionMapP3 = new Map<string, number>();

    p1Activities.forEach((activity: ActiviteModel) => {
      const type = activity.libTypeVenue || 'Non défini';
      admissionMapP1.set(type, (admissionMapP1.get(type) || 0) + activity.nombreDeRealisation);
    });

    p2Activities.forEach((activity: ActiviteModel) => {
      const type = activity.libTypeVenue || 'Non défini';
      admissionMapP2.set(type, (admissionMapP2.get(type) || 0) + activity.nombreDeRealisation);
    });

    p3Activities.forEach((activity: ActiviteModel) => {
      const type = activity.libTypeVenue || 'Non défini';
      admissionMapP3.set(type, (admissionMapP3.get(type) || 0) + activity.nombreDeRealisation);
    });

    // Get all unique admission types across all periods
    const allAdmissionTypes = new Set<string>([
      ...Array.from(admissionMapP1.keys()),
      ...Array.from(admissionMapP2.keys()),
      ...Array.from(admissionMapP3.keys())
    ]);

    const actesInterneExterne: ActesInterneExterne[] = Array.from(allAdmissionTypes)
      .map(type => ({
        type,
        totalP1: admissionMapP1.get(type) || 0,
        totalP2: admissionMapP2.get(type) || 0,
        totalP3: admissionMapP3.get(type) || 0,
        total: (admissionMapP1.get(type) || 0) + (admissionMapP2.get(type) || 0) + (admissionMapP3.get(type) || 0)
      }))
      .sort((a, b) => b.total - a.total);

    // 6. Répartition par catégorie principale
    const categorieMapP1 = new Map<string, number>();
    const categorieMapP2 = new Map<string, number>();
    const categorieMapP3 = new Map<string, number>();

    p1Activities.forEach((activity: ActiviteModel) => {
      const categorie = activity.category || 'Non défini';
      categorieMapP1.set(categorie, (categorieMapP1.get(categorie) || 0) + activity.nombreDeRealisation);
    });

    p2Activities.forEach((activity: ActiviteModel) => {
      const categorie = activity.category || 'Non défini';
      categorieMapP2.set(categorie, (categorieMapP2.get(categorie) || 0) + activity.nombreDeRealisation);
    });

    p3Activities.forEach((activity: ActiviteModel) => {
      const categorie = activity.category || 'Non défini';
      categorieMapP3.set(categorie, (categorieMapP3.get(categorie) || 0) + activity.nombreDeRealisation);
    });

    // Get all unique categories across all periods
    const allCategories = new Set<string>([
      ...Array.from(categorieMapP1.keys()),
      ...Array.from(categorieMapP2.keys()),
      ...Array.from(categorieMapP3.keys())
    ]);

    const repartitionParCategoriePrincipale = Array.from(allCategories)
      .map(categorie => ({
        categorie,
        totalP1: categorieMapP1.get(categorie) || 0,
        totalP2: categorieMapP2.get(categorie) || 0,
        totalP3: categorieMapP3.get(categorie) || 0,
        total: (categorieMapP1.get(categorie) || 0) + (categorieMapP2.get(categorie) || 0) + (categorieMapP3.get(categorie) || 0)
      }))
      .sort((a, b) => b.total - a.total);

    // 7. Répartition par UF (unité fonctionnelle)
    const ufMapP1 = new Map<string, number>();
    const ufMapP2 = new Map<string, number>();
    const ufMapP3 = new Map<string, number>();

    p1Activities.forEach((activity: ActiviteModel) => {
      // Use ufDemande for this chart as per requirements
      if (activity.ufDemande) {
        const uf = activity.ufDemande.libelle || 'Non défini';
        ufMapP1.set(uf, (ufMapP1.get(uf) || 0) + activity.nombreDeRealisation);
      }
    });

    p2Activities.forEach((activity: ActiviteModel) => {
      // Use ufDemande for this chart as per requirements
      if (activity.ufDemande) {
        const uf = activity.ufDemande.libelle || 'Non défini';
        ufMapP2.set(uf, (ufMapP2.get(uf) || 0) + activity.nombreDeRealisation);
      }
    });

    p3Activities.forEach((activity: ActiviteModel) => {
      // Use ufDemande for this chart as per requirements
      if (activity.ufDemande) {
        const uf = activity.ufDemande.libelle || 'Non défini';
        ufMapP3.set(uf, (ufMapP3.get(uf) || 0) + activity.nombreDeRealisation);
      }
    });

    // Get all unique UFs across all periods
    const allUFs = new Set<string>([
      ...Array.from(ufMapP1.keys()),
      ...Array.from(ufMapP2.keys()),
      ...Array.from(ufMapP3.keys())
    ]);

    const repartitionParUF = Array.from(allUFs)
      .map(uf => ({
        uf,
        totalP1: ufMapP1.get(uf) || 0,
        totalP2: ufMapP2.get(uf) || 0,
        totalP3: ufMapP3.get(uf) || 0,
        total: (ufMapP1.get(uf) || 0) + (ufMapP2.get(uf) || 0) + (ufMapP3.get(uf) || 0)
      }))
      .sort((a, b) => b.total - a.total);

    // 8. Taux de variation entre périodes
    // Calculate totals for each period
    const p1Total = p1Activities.reduce((sum: number, activity: ActiviteModel) => sum + activity.nombreDeRealisation, 0);
    const p2Total = p2Activities.reduce((sum: number, activity: ActiviteModel) => sum + activity.nombreDeRealisation, 0);
    const p3Total = p3Activities.reduce((sum: number, activity: ActiviteModel) => sum + activity.nombreDeRealisation, 0);

    // Calculate variation rates
    const variationP1P2 = p2Total > 0 ? ((p1Total - p2Total) / p2Total) * 100 : 0;
    const variationP2P3 = p3Total > 0 ? ((p2Total - p3Total) / p3Total) * 100 : 0;

    // Calculate totals for each type of act
    const p1TotalCCAM = p1Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && activity.typeActe.includes('CCAM') ? sum + activity.nombreDeRealisation : sum, 0);
    const p2TotalCCAM = p2Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && activity.typeActe.includes('CCAM') ? sum + activity.nombreDeRealisation : sum, 0);
    const p3TotalCCAM = p3Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && activity.typeActe.includes('CCAM') ? sum + activity.nombreDeRealisation : sum, 0);

    const p1TotalNGAP = p1Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && (activity.typeActe.includes('NGAP') || activity.typeActe.toLowerCase().includes('ngap')) ? sum + activity.nombreDeRealisation : sum, 0);
    const p2TotalNGAP = p2Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && (activity.typeActe.includes('NGAP') || activity.typeActe.toLowerCase().includes('ngap')) ? sum + activity.nombreDeRealisation : sum, 0);
    const p3TotalNGAP = p3Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && (activity.typeActe.includes('NGAP') || activity.typeActe.toLowerCase().includes('ngap')) ? sum + activity.nombreDeRealisation : sum, 0);

    const p1TotalLABO = p1Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && activity.typeActe.includes('LABO') ? sum + activity.nombreDeRealisation : sum, 0);
    const p2TotalLABO = p2Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && activity.typeActe.includes('LABO') ? sum + activity.nombreDeRealisation : sum, 0);
    const p3TotalLABO = p3Activities.reduce((sum: number, activity: ActiviteModel) =>
      activity.typeActe && activity.typeActe.includes('LABO') ? sum + activity.nombreDeRealisation : sum, 0);

    // Calculate variation rates for each type
    const variationP1P2CCAM = p2TotalCCAM > 0 ? ((p1TotalCCAM - p2TotalCCAM) / p2TotalCCAM) * 100 : 0;
    const variationP2P3CCAM = p3TotalCCAM > 0 ? ((p2TotalCCAM - p3TotalCCAM) / p3TotalCCAM) * 100 : 0;

    const variationP1P2NGAP = p2TotalNGAP > 0 ? ((p1TotalNGAP - p2TotalNGAP) / p2TotalNGAP) * 100 : 0;
    const variationP2P3NGAP = p3TotalNGAP > 0 ? ((p2TotalNGAP - p3TotalNGAP) / p3TotalNGAP) * 100 : 0;

    const variationP1P2LABO = p2TotalLABO > 0 ? ((p1TotalLABO - p2TotalLABO) / p2TotalLABO) * 100 : 0;
    const variationP2P3LABO = p3TotalLABO > 0 ? ((p2TotalLABO - p3TotalLABO) / p3TotalLABO) * 100 : 0;

    const tauxVariation = [
      {
        label: 'Total des actes',
        p1Total,
        p2Total,
        p3Total,
        variationP1P2,
        variationP2P3
      },
      {
        label: 'CCAM',
        p1Total: p1TotalCCAM,
        p2Total: p2TotalCCAM,
        p3Total: p3TotalCCAM,
        variationP1P2: variationP1P2CCAM,
        variationP2P3: variationP2P3CCAM
      },
      {
        label: 'NGAP',
        p1Total: p1TotalNGAP,
        p2Total: p2TotalNGAP,
        p3Total: p3TotalNGAP,
        variationP1P2: variationP1P2NGAP,
        variationP2P3: variationP2P3NGAP
      },
      {
        label: 'LABO',
        p1Total: p1TotalLABO,
        p2Total: p2TotalLABO,
        p3Total: p3TotalLABO,
        variationP1P2: variationP1P2LABO,
        variationP2P3: variationP2P3LABO
      }
    ];

    // 9. Heatmap des actes par semaine/jour
    // Create maps to store data for each period and act type
    // CCAM
    const heatmapMapP1CCAM = new Map<string, { semaine: number, jour: number, total: number }>();
    const heatmapMapP2CCAM = new Map<string, { semaine: number, jour: number, total: number }>();
    const heatmapMapP3CCAM = new Map<string, { semaine: number, jour: number, total: number }>();
    // NGAP
    const heatmapMapP1NGAP = new Map<string, { semaine: number, jour: number, total: number }>();
    const heatmapMapP2NGAP = new Map<string, { semaine: number, jour: number, total: number }>();
    const heatmapMapP3NGAP = new Map<string, { semaine: number, jour: number, total: number }>();
    // LABO
    const heatmapMapP1LABO = new Map<string, { semaine: number, jour: number, total: number }>();
    const heatmapMapP2LABO = new Map<string, { semaine: number, jour: number, total: number }>();
    const heatmapMapP3LABO = new Map<string, { semaine: number, jour: number, total: number }>();
    // ALL (for backward compatibility)
    const heatmapMapP1 = new Map<string, { semaine: number, jour: number, total: number }>();
    const heatmapMapP2 = new Map<string, { semaine: number, jour: number, total: number }>();
    const heatmapMapP3 = new Map<string, { semaine: number, jour: number, total: number }>();

    // Process activities for period 1
    p1Activities.forEach((activity: ActiviteModel) => {
      if (activity.semaineIso && activity.dateRealisation) {
        const date = new Date(activity.dateRealisation);
        const jour = date.getDay(); // 0-6 for Sunday-Saturday
        const key = `${activity.semaineIso}-${jour}`;
        const actType = activity.typeActe || 'Non défini';

        // Update the general map (for backward compatibility)
        const existing = heatmapMapP1.get(key);
        if (existing) {
          existing.total += activity.nombreDeRealisation;
        } else {
          heatmapMapP1.set(key, {
            semaine: activity.semaineIso,
            jour,
            total: activity.nombreDeRealisation
          });
        }

        // Update the act type specific maps
        if (actType.includes('CCAM')) {
          const existingCCAM = heatmapMapP1CCAM.get(key);
          if (existingCCAM) {
            existingCCAM.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP1CCAM.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        } else if (actType.includes('NGAP') || actType.toLowerCase().includes('ngap')) {
          const existingNGAP = heatmapMapP1NGAP.get(key);
          if (existingNGAP) {
            existingNGAP.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP1NGAP.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        } else if (actType.includes('LABO')) {
          const existingLABO = heatmapMapP1LABO.get(key);
          if (existingLABO) {
            existingLABO.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP1LABO.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        }
      }
    });

    // Process activities for period 2
    p2Activities.forEach((activity: ActiviteModel) => {
      if (activity.semaineIso && activity.dateRealisation) {
        const date = new Date(activity.dateRealisation);
        const jour = date.getDay(); // 0-6 for Sunday-Saturday
        const key = `${activity.semaineIso}-${jour}`;
        const actType = activity.typeActe || 'Non défini';

        // Update the general map (for backward compatibility)
        const existing = heatmapMapP2.get(key);
        if (existing) {
          existing.total += activity.nombreDeRealisation;
        } else {
          heatmapMapP2.set(key, {
            semaine: activity.semaineIso,
            jour,
            total: activity.nombreDeRealisation
          });
        }

        // Update the act type specific maps
        if (actType.includes('CCAM')) {
          const existingCCAM = heatmapMapP2CCAM.get(key);
          if (existingCCAM) {
            existingCCAM.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP2CCAM.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        } else if (actType.includes('NGAP') || actType.toLowerCase().includes('ngap')) {
          const existingNGAP = heatmapMapP2NGAP.get(key);
          if (existingNGAP) {
            existingNGAP.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP2NGAP.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        } else if (actType.includes('LABO')) {
          const existingLABO = heatmapMapP2LABO.get(key);
          if (existingLABO) {
            existingLABO.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP2LABO.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        }
      }
    });

    // Process activities for period 3
    p3Activities.forEach((activity: ActiviteModel) => {
      if (activity.semaineIso && activity.dateRealisation) {
        const date = new Date(activity.dateRealisation);
        const jour = date.getDay(); // 0-6 for Sunday-Saturday
        const key = `${activity.semaineIso}-${jour}`;
        const actType = activity.typeActe || 'Non défini';

        // Update the general map (for backward compatibility)
        const existing = heatmapMapP3.get(key);
        if (existing) {
          existing.total += activity.nombreDeRealisation;
        } else {
          heatmapMapP3.set(key, {
            semaine: activity.semaineIso,
            jour,
            total: activity.nombreDeRealisation
          });
        }

        // Update the act type specific maps
        if (actType.includes('CCAM')) {
          const existingCCAM = heatmapMapP3CCAM.get(key);
          if (existingCCAM) {
            existingCCAM.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP3CCAM.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        } else if (actType.includes('NGAP') || actType.toLowerCase().includes('ngap')) {
          const existingNGAP = heatmapMapP3NGAP.get(key);
          if (existingNGAP) {
            existingNGAP.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP3NGAP.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        } else if (actType.includes('LABO')) {
          const existingLABO = heatmapMapP3LABO.get(key);
          if (existingLABO) {
            existingLABO.total += activity.nombreDeRealisation;
          } else {
            heatmapMapP3LABO.set(key, {
              semaine: activity.semaineIso,
              jour,
              total: activity.nombreDeRealisation
            });
          }
        }
      }
    });

    // Combine data from all periods and act types
    // Get all unique keys
    const allKeys = new Set<string>([
      ...Array.from(heatmapMapP1.keys()),
      ...Array.from(heatmapMapP2.keys()),
      ...Array.from(heatmapMapP3.keys()),
      ...Array.from(heatmapMapP1CCAM.keys()),
      ...Array.from(heatmapMapP2CCAM.keys()),
      ...Array.from(heatmapMapP3CCAM.keys()),
      ...Array.from(heatmapMapP1NGAP.keys()),
      ...Array.from(heatmapMapP2NGAP.keys()),
      ...Array.from(heatmapMapP3NGAP.keys()),
      ...Array.from(heatmapMapP1LABO.keys()),
      ...Array.from(heatmapMapP2LABO.keys()),
      ...Array.from(heatmapMapP3LABO.keys())
    ]);

    // Create an array to hold all heatmap data
    const heatmapData: HeatmapData[] = [];

    // Add data for all act types (for backward compatibility)
    Array.from(allKeys).forEach(key => {
      const [semaine, jour] = key.split('-').map(Number);
      const p1Data = heatmapMapP1.get(key);
      const p2Data = heatmapMapP2.get(key);
      const p3Data = heatmapMapP3.get(key);

      heatmapData.push({
        semaine,
        jour: Number(jour),
        totalP1: p1Data ? p1Data.total : 0,
        totalP2: p2Data ? p2Data.total : 0,
        totalP3: p3Data ? p3Data.total : 0,
        total: (p1Data ? p1Data.total : 0) + (p2Data ? p2Data.total : 0) + (p3Data ? p3Data.total : 0)
      });
    });

    // Add data for CCAM
    Array.from(allKeys).forEach(key => {
      const [semaine, jour] = key.split('-').map(Number);
      const p1Data = heatmapMapP1CCAM.get(key);
      const p2Data = heatmapMapP2CCAM.get(key);
      const p3Data = heatmapMapP3CCAM.get(key);

      // Only add if there's data for at least one period
      if (p1Data || p2Data || p3Data) {
        heatmapData.push({
          semaine,
          jour: Number(jour),
          totalP1: p1Data ? p1Data.total : 0,
          totalP2: p2Data ? p2Data.total : 0,
          totalP3: p3Data ? p3Data.total : 0,
          total: (p1Data ? p1Data.total : 0) + (p2Data ? p2Data.total : 0) + (p3Data ? p3Data.total : 0),
          actType: 'CCAM'
        });
      }
    });

    // Add data for NGAP
    Array.from(allKeys).forEach(key => {
      const [semaine, jour] = key.split('-').map(Number);
      const p1Data = heatmapMapP1NGAP.get(key);
      const p2Data = heatmapMapP2NGAP.get(key);
      const p3Data = heatmapMapP3NGAP.get(key);

      // Only add if there's data for at least one period
      if (p1Data || p2Data || p3Data) {
        heatmapData.push({
          semaine,
          jour: Number(jour),
          totalP1: p1Data ? p1Data.total : 0,
          totalP2: p2Data ? p2Data.total : 0,
          totalP3: p3Data ? p3Data.total : 0,
          total: (p1Data ? p1Data.total : 0) + (p2Data ? p2Data.total : 0) + (p3Data ? p3Data.total : 0),
          actType: 'NGAP'
        });
      }
    });

    // Add data for LABO
    Array.from(allKeys).forEach(key => {
      const [semaine, jour] = key.split('-').map(Number);
      const p1Data = heatmapMapP1LABO.get(key);
      const p2Data = heatmapMapP2LABO.get(key);
      const p3Data = heatmapMapP3LABO.get(key);

      // Only add if there's data for at least one period
      if (p1Data || p2Data || p3Data) {
        heatmapData.push({
          semaine,
          jour: Number(jour),
          totalP1: p1Data ? p1Data.total : 0,
          totalP2: p2Data ? p2Data.total : 0,
          totalP3: p3Data ? p3Data.total : 0,
          total: (p1Data ? p1Data.total : 0) + (p2Data ? p2Data.total : 0) + (p3Data ? p3Data.total : 0),
          actType: 'LABO'
        });
      }
    });

    // 10. Répartition par UF d'intervention
    const ufInterventionMapP1 = new Map<string, number>();
    const ufInterventionMapP2 = new Map<string, number>();
    const ufInterventionMapP3 = new Map<string, number>();

    p1Activities.forEach((activity: ActiviteModel) => {
      if (activity.ufIntervention) {
        const ufIntervention = activity.ufIntervention.libelle || 'Non défini';
        ufInterventionMapP1.set(ufIntervention, (ufInterventionMapP1.get(ufIntervention) || 0) + activity.nombreDeRealisation);
      }
    });

    p2Activities.forEach((activity: ActiviteModel) => {
      if (activity.ufIntervention) {
        const ufIntervention = activity.ufIntervention.libelle || 'Non défini';
        ufInterventionMapP2.set(ufIntervention, (ufInterventionMapP2.get(ufIntervention) || 0) + activity.nombreDeRealisation);
      }
    });

    p3Activities.forEach((activity: ActiviteModel) => {
      if (activity.ufIntervention) {
        const ufIntervention = activity.ufIntervention.libelle || 'Non défini';
        ufInterventionMapP3.set(ufIntervention, (ufInterventionMapP3.get(ufIntervention) || 0) + activity.nombreDeRealisation);
      }
    });

    // Get all unique UF d'intervention across all periods
    const allUFInterventions = new Set<string>([
      ...Array.from(ufInterventionMapP1.keys()),
      ...Array.from(ufInterventionMapP2.keys()),
      ...Array.from(ufInterventionMapP3.keys())
    ]);

    const repartitionParProvenance = Array.from(allUFInterventions)
      .map(ufIntervention => ({
        provenance: ufIntervention, // Keep the property name for backward compatibility
        totalP1: ufInterventionMapP1.get(ufIntervention) || 0,
        totalP2: ufInterventionMapP2.get(ufIntervention) || 0,
        totalP3: ufInterventionMapP3.get(ufIntervention) || 0,
        total: (ufInterventionMapP1.get(ufIntervention) || 0) + (ufInterventionMapP2.get(ufIntervention) || 0) + (ufInterventionMapP3.get(ufIntervention) || 0)
      }))
      .sort((a, b) => b.total - a.total);

    return {
      repartitionActesParType,
      evolutionMensuelle,
      evolutionMensuelleParType,
      repartitionActesParServices,
      performancePraticiens,
      actesInterneExterne,
      repartitionParCategoriePrincipale,
      repartitionParUF,
      tauxVariation,
      heatmapData,
      repartitionParProvenance
    };
  }
}
