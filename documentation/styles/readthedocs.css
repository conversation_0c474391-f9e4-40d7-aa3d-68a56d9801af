.navbar-default {
    background: #2980b9;
    border: none;
}

.navbar-default .navbar-brand {
    color: #fcfcfc;
}

.menu {
    background: #343131;
    color: #fcfcfc;
}

.menu ul.list li a {
    color: #fcfcfc;
}

.menu ul.list li.title {
    background: #2980b9;
    padding-bottom: 5px;
}

.menu ul.list li:nth-child(2) {
    margin-top: 0;
}

.menu ul.list li.chapter a,
.menu ul.list li.chapter .simple {
    color: #555;
    text-transform: uppercase;
    text-decoration: none;
}

.menu ul.list li.chapter ul.links a {
    color: #b3b3b3;
    text-transform: none;
    padding-left: 35px;
}

.menu ul.list li.chapter ul.links a:hover {
    background: #4e4a4a;
}

.menu ul.list li.chapter a.active,
.menu ul.list li.chapter ul.links a.active {
    color: #0099e5;
}

.menu ul.list li.chapter ul.links {
    padding-left: 0;
}

.menu ul.list li.divider {
    background: rgba(255, 255, 255, 0.07);
}

#book-search-input input,
#book-search-input input:focus,
#book-search-input input:hover {
    color: #949494;
}

.copyright {
    color: #b3b3b3;
    background: #272525;
}

.content {
    background: #fcfcfc;
}

.content a {
    color: #2980b9;
}

.content a:hover {
    color: #3091d1;
}

.content a:visited {
    color: #9b59b6;
}

.menu ul.list li:nth-last-child(2) {
    background: none;
}

code {
    white-space: nowrap;
    max-width: 100%;
    background: #fff;
    padding: 2px 5px;
    color: #e74c3c;
    overflow-x: auto;
    border-radius: 0;
}

pre {
    white-space: pre;
    margin: 0;
    padding: 12px 12px;
    font-size: 12px;
    line-height: 1.5;
    display: block;
    overflow: auto;
    color: #404040;
    background: rgba(238, 238, 238, 0.35);
}

.dark .content {
    background: none;
}
.dark code {
    background: none;
    color: #e09393;
}
