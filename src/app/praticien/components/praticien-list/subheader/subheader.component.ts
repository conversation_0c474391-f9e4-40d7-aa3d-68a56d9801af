import { Component, OnInit, OnDestroy, Output, EventEmitter, HostListener } from '@angular/core';
import { Subscription, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

// Services
import { AgentService } from '../../../../core/services/praticien/agent.service';

@Component({
  selector: 'app-praticien-subheader',
  templateUrl: './subheader.component.html',
  styleUrls: ['./subheader.component.scss']
})
export class PraticienSubheaderComponent implements OnInit, OnDestroy {
  @Output() filtersChanged = new EventEmitter<any>();

  // Filter values
  searchFilters = {
    nom: '',
    prenom: '',
    hrUser: '',
    categorie: '',
    email: '',
    // AgentUfs related filters
    'agentUfs.rgt': '',
    'agentUfs.typeGrade': '',
    'agentUfs.libelleGrade': '',
    'agentUfs.etp': '',
    'agentUfs.ufs.ufcode': '',
    'agentUfs.ufs.cr.crcode': ''
  };

  // Plus besoin d'options prédéfinies car tout est en input maintenant

  // UI state
  showAdvancedFilters: boolean = false;
  isSticky: boolean = false;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Debounce subject for search optimization
  private searchSubject = new Subject<any>();

  constructor(
    private agentService: AgentService
  ) {}

  ngOnInit(): void {
    // Note: Pas de recherche automatique, seulement manuelle via Entrée ou bouton
  }

  /**
   * Listen to window scroll events to update sticky state
   */
  @HostListener('window:scroll', ['$event'])
  onWindowScroll(): void {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    this.isSticky = scrollTop > 100; // Devient sticky après 100px de scroll
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Handle filter changes (no automatic search, just update internal state)
   */
  onFilterChange(): void {
    // Ne fait rien automatiquement, attend que l'utilisateur appuie sur Entrée ou clique
    // Les filtres sont mis à jour dans le modèle via ngModel
  }

  /**
   * Handle Enter key press for immediate search
   */
  onEnterKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.triggerImmediateSearch();
    }
  }

  /**
   * Trigger immediate search (for Enter key or search button)
   */
  triggerImmediateSearch(): void {
    const activeFilters = Object.keys(this.searchFilters)
      .filter(key => this.searchFilters[key as keyof typeof this.searchFilters] !== '')
      .reduce((obj, key) => {
        obj[key] = this.searchFilters[key as keyof typeof this.searchFilters];
        return obj;
      }, {} as any);

    this.filtersChanged.emit(activeFilters);
  }

  /**
   * Handle immediate filter changes (for dropdowns)
   */
  onDropdownFilterChange(): void {
    // For dropdowns, emit immediately without debounce
    const activeFilters = Object.keys(this.searchFilters)
      .filter(key => this.searchFilters[key as keyof typeof this.searchFilters] !== '')
      .reduce((obj, key) => {
        obj[key] = this.searchFilters[key as keyof typeof this.searchFilters];
        return obj;
      }, {} as any);

    this.filtersChanged.emit(activeFilters);
  }

  /**
   * Clear all filters
   */
  clearAllFilters(): void {
    this.searchFilters = {
      nom: '',
      prenom: '',
      hrUser: '',
      categorie: '',
      email: '',
      // AgentUfs related filters
      'agentUfs.rgt': '',
      'agentUfs.typeGrade': '',
      'agentUfs.libelleGrade': '',
      'agentUfs.etp': '',
      'agentUfs.ufs.ufcode': '',
      'agentUfs.ufs.cr.crcode': ''
    };
    this.triggerImmediateSearch(); // Immediate clear
  }

  /**
   * Toggle advanced filters visibility
   */
  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  /**
   * Check if any filters are active
   */
  hasActiveFilters(): boolean {
    return Object.values(this.searchFilters).some(value => value !== '');
  }

  /**
   * Get count of active filters
   */
  getActiveFiltersCount(): number {
    return Object.values(this.searchFilters).filter(value => value !== '').length;
  }
}
