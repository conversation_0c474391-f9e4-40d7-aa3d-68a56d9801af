import {Component, OnInit} from '@angular/core';
import {LiberalService} from "../../core/services/liberal.service";
import {LiberalData} from "../../core/models/liberal.model";
import {DatePipe, <PERSON><PERSON>orOf, NgIf} from "@angular/common";
import {ChartModule} from "primeng/chart";
import {StyleClassModule} from "primeng/styleclass";
import {BreadcrumbComponent} from "../breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../core/models/breadcrumbItem";
import {TableModule} from "primeng/table";

@Component({
  selector: 'app-liberale',
  standalone: true,
  imports: [
    NgIf,
    NgForOf,
    ChartModule,
    StyleClassModule,
    BreadcrumbComponent,
    TableModule,
    DatePipe
  ],
  templateUrl: './liberale.component.html',
  styleUrl: './liberale.component.scss'
})
export class LiberaleComponent implements OnInit {
  liberalData: LiberalData | null = null; // Contient les données libérales
  loading: boolean = true; // Indique si les données sont en cours de chargement

  // Données et options pour le graphique
  stackedBarChartData: any;
  stackedBarChartOptions: any;
  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Liberale', url: '/liberale' },
    { label: 'Activité Libérale par Praticien' }
  ];

  constructor(private liberalService: LiberalService) {}

  ngOnInit(): void {
    this.loadLiberals()
  }

  loadLiberals(): void {
    this.liberalService.getLiberals().subscribe({
      next: (data) => {
        if (data) { // Vérifiez que data n'est pas null
          this.liberalData = data;
          this.prepareChartData(data); // Prépare les données si elles existent
        } else {
          console.warn('Données libérales introuvables.');
          this.liberalData = null;
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des données libérales:', err);
        this.liberalData = null;
        this.loading = false;
      },
    });
    // Charge les données si elles ne sont pas déjà chargées
    this.liberalService.loadInitialData();
    //
    this.configureChartOptions();
  }

  // Préparer les données pour le graphique
  private prepareChartData(data: LiberalData): void {
    const labels = data.praticiens.map((p) => p.nom);
    const consultations = data.praticiens.map(
      (p) =>
        (p.consultationsLiberales /
          (p.consultationsLiberales +
            p.interventions +
            p.actesComplementaires)) *
        100
    );
    const interventions = data.praticiens.map(
      (p) =>
        (p.interventions /
          (p.consultationsLiberales +
            p.interventions +
            p.actesComplementaires)) *
        100
    );
    const actesComplementaires = data.praticiens.map(
      (p) =>
        (p.actesComplementaires /
          (p.consultationsLiberales +
            p.interventions +
            p.actesComplementaires)) *
        100
    );

    this.stackedBarChartData = {
      labels: labels,
      datasets: [
        {
          label: 'Consultations',
          backgroundColor: '#42A5F5',
          data: consultations,
          borderWidth: 1,
        },
        {
          label: 'Interventions',
          backgroundColor: '#FFA726',
          data: interventions,
        },
        {
          label: 'Actes Complémentaires',
          backgroundColor: '#66BB6A',
          data: actesComplementaires,
        },
      ],
    };
  }

  // Configurer les options du graphique
  private configureChartOptions(): void {
    this.stackedBarChartOptions = {
      responsive: true,
      maintainAspectRatio: true, // Permet de maintenir le ratio
      aspectRatio: 2, // Ajustez ce ratio pour modifier la hauteur relative (par défaut : 2)
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          callbacks: {
            label: function (context: any) {
              return `${context.dataset.label}: ${context.raw.toFixed(
                2
              )}%`;
            },
          },
        },
      },
      scales: {
        x: {
          stacked: true,
          title: {
            display: true,
            text: 'Praticiens',
          },
        },
        y: {
          stacked: true,
          title: {
            display: true,
            text: 'Pourcentage (%)',
          },
          ticks: {
            callback: (value: number) => `${value}%`,
          },
          max: 100,
        },
      },
    };
  }

  //
  onInput(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }

}
