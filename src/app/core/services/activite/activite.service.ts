import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay, tap } from 'rxjs/operators';
import { BaseStructureService, PaginatedResult } from '../structure/base-structure.service';
import { ActiviteModel } from '../../models/activite/ActiviteModel';
import { DatePeriod } from '../global-filter/global-filter.service';
import { environment } from '../../../../environments/environment';

interface ActiviteResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': ActiviteModel[];
  'view': {
    '@id': string;
    '@type': string;
    'first': string;
    'last': string;
    'next': string;
  };
  'search': any;
}

@Injectable({
  providedIn: 'root'
})
export class ActiviteService extends BaseStructureService<ActiviteModel, ActiviteResponse> {
  // Additional cache for frequently accessed data
  private activitesByMonthCache = new Map<string, Observable<PaginatedResult<ActiviteModel>>>();
  private activitesByAgentCache = new Map<string, Observable<PaginatedResult<ActiviteModel>>>();

  constructor(http: HttpClient) {
    super(http, '/api/actes');
  }

  /**
   * Extract ActiviteModel array and total count from API response
   * @param response API response
   * @returns PaginatedResult containing ActiviteModel array and total count
   */
  protected extractData(response: ActiviteResponse): PaginatedResult<ActiviteModel> {
    return {
      items: response.member,
      totalItems: response.totalItems
    };
  }

  /**
   * Get activities for a specific month and year
   * @param month Month number (1-12)
   * @param year Year (e.g., 2023)
   * @returns Observable of PaginatedResult containing ActiviteModel array and total count
   */
  getActivitiesByMonth(month: number, year: number): Observable<PaginatedResult<ActiviteModel>> {
    const cacheKey = `month_${month}_${year}`;

    if (this.activitesByMonthCache.has(cacheKey)) {
      return this.activitesByMonthCache.get(cacheKey) as Observable<PaginatedResult<ActiviteModel>>;
    }

    const params = new URLSearchParams();
    params.set('mois', month.toString());
    params.set('annee', year.toString());

    const request = this.http.get<ActiviteResponse>(
      `${environment.api_url}${this.apiPath}?${params.toString()}`
    ).pipe(
      map(response => this.extractData(response)),
      shareReplay(1),
      catchError(this.handleError)
    );

    this.activitesByMonthCache.set(cacheKey, request);

    // Clear cache after specified time
    setTimeout(() => {
      this.activitesByMonthCache.delete(cacheKey);
    }, this.cacheTime);

    return request;
  }

  /**
   * Get activities for a specific agent
   * @param agentId Agent ID
   * @returns Observable of PaginatedResult containing ActiviteModel array and total count
   */
  getActivitiesByAgent(agentId: string): Observable<PaginatedResult<ActiviteModel>> {
    const cacheKey = `agent_${agentId}`;

    if (this.activitesByAgentCache.has(cacheKey)) {
      return this.activitesByAgentCache.get(cacheKey) as Observable<PaginatedResult<ActiviteModel>>;
    }

    const params = new URLSearchParams();
    params.set('agent', agentId);

    const request = this.http.get<ActiviteResponse>(
      `${environment.api_url}${this.apiPath}?${params.toString()}`
    ).pipe(
      map(response => this.extractData(response)),
      shareReplay(1),
      catchError(this.handleError)
    );

    this.activitesByAgentCache.set(cacheKey, request);

    // Clear cache after specified time
    setTimeout(() => {
      this.activitesByAgentCache.delete(cacheKey);
    }, this.cacheTime);

    return request;
  }

  /**
   * Récupère les activités avec pagination côté serveur et filtrage par périodes
   *
   * @param page Numéro de page (1-based)
   * @param itemsPerPage Nombre d'éléments par page
   * @param p1 Période P1 (actuelle)
   * @param p2 Période P2 (année précédente)
   * @param p3 Période P3 (il y a deux ans)
   * @param typeActe Type d'acte à filtrer (optionnel)
   * @param search Terme de recherche (optionnel)
   * @returns Observable<PaginatedResult<ActiviteModel>>
   */
  getActivitiesWithPeriods(
    page: number = 1,
    itemsPerPage: number = 10,
    p1: DatePeriod | null = null,
    p2: DatePeriod | null = null,
    p3: DatePeriod | null = null,
    typeActe?: string,
    search?: string
  ): Observable<PaginatedResult<ActiviteModel>> {

    // Construire les paramètres HTTP
    let params = new HttpParams()
      .set('page', page.toString())
      .set('itemsPerPage', itemsPerPage.toString());

    // Ajouter les paramètres de périodes
    if (p1?.dateDebut && p1?.dateFin) {
      params = params.set('p1Start', this.formatDate(p1.dateDebut));
      params = params.set('p1End', this.formatDate(p1.dateFin));
    }

    if (p2?.dateDebut && p2?.dateFin) {
      params = params.set('p2Start', this.formatDate(p2.dateDebut));
      params = params.set('p2End', this.formatDate(p2.dateFin));
    }

    if (p3?.dateDebut && p3?.dateFin) {
      params = params.set('p3Start', this.formatDate(p3.dateDebut));
      params = params.set('p3End', this.formatDate(p3.dateFin));
    }

    // Ajouter le filtre de type si spécifié
    if (typeActe && typeActe !== 'ALL') {
      if (typeActe === 'LABO') {
        // Pour LABO, on inclut LABO et NABM
        params = params.set('typeActe[]', 'LABO');
        params = params.set('typeActe[]', 'NABM');
      } else {
        params = params.set('typeActe', typeActe);
      }
    }

    // Ajouter la recherche si spécifiée
    if (search && search.trim()) {
      params = params.set('search', search.trim());
    }

    console.log('🔄 Requête API avec périodes (cache frontend:', environment.enableFrontendCache ? 'activé' : 'désactivé', '):', {
      page,
      itemsPerPage,
      periods: {
        p1: p1 && p1.dateDebut && p1.dateFin ?
          `${this.formatDate(p1.dateDebut)} → ${this.formatDate(p1.dateFin)}` : null,
        p2: p2 && p2.dateDebut && p2.dateFin ?
          `${this.formatDate(p2.dateDebut)} → ${this.formatDate(p2.dateFin)}` : null,
        p3: p3 && p3.dateDebut && p3.dateFin ?
          `${this.formatDate(p3.dateDebut)} → ${this.formatDate(p3.dateFin)}` : null
      },
      typeActe,
      search,
      url: `${environment.api_url}${this.apiPath}?${params.toString()}`
    });

    return this.http.get<ActiviteResponse>(
      `${environment.api_url}${this.apiPath}`,
      { params }
    ).pipe(
      map(response => this.extractData(response)),
      tap(result => {
        console.log('✅ Réponse API avec périodes:', {
          totalItems: result.totalItems,
          itemsCount: result.items.length,
          page,
          itemsPerPage
        });
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Formate une date au format YYYY-MM-DD pour l'API
   */
  private formatDate(date: Date | null): string {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  }

  /**
   * Override clearCache to also clear specialized caches
   */
  override clearCache(): void {
    super.clearCache();
    this.activitesByMonthCache.clear();
    this.activitesByAgentCache.clear();
  }
}
