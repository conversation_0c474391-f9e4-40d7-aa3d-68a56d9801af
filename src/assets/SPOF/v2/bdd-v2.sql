-- Création de la table `tenant`
CREATE TABLE tenant (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    date_creation DATE DEFAULT CURRENT_DATE
);

-- Création de la table `hopital` liée au tenant
CREATE TABLE hopital (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    adresse TEXT,
    telephone VARCHAR(15),
    email VARCHAR(255),
    date_creation DATE DEFAULT CURRENT_DATE,
    tenant_id UUID NOT NULL,
    CONSTRAINT fk_hopital_tenant FOREIGN KEY (tenant_id) REFERENCES tenant (id) ON DELETE CASCADE
);

-- Table `poles`
CREATE TABLE poles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    hopital_id UUID NOT NULL,
    CONSTRAINT fk_poles_hopital FOREIGN KEY (hopital_id) REFERENCES hopital (id) ON DELETE CASCADE
);

-- Table `services`
CREATE TABLE services (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    pole_id UUID NOT NULL,
    CONSTRAINT fk_services_pole FOREIGN KEY (pole_id) REFERENCES poles (id) ON DELETE CASCADE
);

-- Table `ufs`
CREATE TABLE ufs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    service_id UUID NOT NULL,
    CONSTRAINT fk_ufs_service FOREIGN KEY (service_id) REFERENCES services (id) ON DELETE CASCADE
);

-- Table `praticiens`
CREATE TABLE praticiens (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    specialite VARCHAR(255),
    matricule VARCHAR(50),
    service_id UUID NOT NULL,
    CONSTRAINT fk_praticiens_service FOREIGN KEY (service_id) REFERENCES services (id) ON DELETE CASCADE
);

-- Table `actes`
CREATE TABLE actes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    categorie VARCHAR(100),
    type_acte VARCHAR(50),
    date_realisation DATE NOT NULL,
    uf_id UUID NOT NULL,
    praticien_id UUID NOT NULL,
    CONSTRAINT fk_actes_uf FOREIGN KEY (uf_id) REFERENCES ufs (id) ON DELETE CASCADE,
    CONSTRAINT fk_actes_praticien FOREIGN KEY (praticien_id) REFERENCES praticiens (id) ON DELETE CASCADE
);

-- Table `enseignements`
CREATE TABLE enseignements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    praticien_id UUID NOT NULL,
    annee INT NOT NULL,
    nombre_heures INT NOT NULL,
    type_enseignement VARCHAR(255),
    CONSTRAINT fk_enseignements_praticien FOREIGN KEY (praticien_id) REFERENCES praticiens (id) ON DELETE CASCADE
);

-- Table `gardes_astreintes`
CREATE TABLE gardes_astreintes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    praticien_id UUID NOT NULL,
    date_garde DATE NOT NULL,
    type_garde VARCHAR(50),
    duree_heure INT,
    CONSTRAINT fk_gardes_praticien FOREIGN KEY (praticien_id) REFERENCES praticiens (id) ON DELETE CASCADE
);

-- Table `liberal`
CREATE TABLE liberal (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    praticien_id UUID NOT NULL,
    date_activite DATE NOT NULL,
    montant_honoraires NUMERIC(10, 2),
    type_activite VARCHAR(50),
    CONSTRAINT fk_liberal_praticien FOREIGN KEY (praticien_id) REFERENCES praticiens (id) ON DELETE CASCADE
);

-- Table `sigaps`
CREATE TABLE sigaps (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    praticien_id UUID NOT NULL,
    annee INT NOT NULL,
    categorie VARCHAR(50),
    nombre_publications INT,
    CONSTRAINT fk_sigaps_praticien FOREIGN KEY (praticien_id) REFERENCES praticiens (id) ON DELETE CASCADE
);
