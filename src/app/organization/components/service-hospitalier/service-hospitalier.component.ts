import {Component, OnInit} from '@angular/core';
import {ServiceHospitalier} from "../../../core/models/organization/ServiceHospitalier.model";
import {ServiceHospitalierService} from "../../../core/services/organization/ServiceHospitalierService";
import {Ng<PERSON><PERSON>, <PERSON><PERSON>orOf, Ng<PERSON><PERSON>} from "@angular/common";
import {InformationPanelComponent} from "../information-panel/information-panel.component";
import {ActivatedRoute} from "@angular/router";
import {CardModule} from "primeng/card";
import {BadgeModule} from "primeng/badge";
import {ChartModule} from "primeng/chart";
import {BreadcrumbComponent} from "../../../pages/breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../../core/models/breadcrumbItem";
import {StyleClassModule} from "primeng/styleclass";
import {TableModule} from "primeng/table";
import {PaginatorModule} from "primeng/paginator";
import {ServiceHospitalierOverviewService} from "../../../core/services/overview/service-hospitalier-overview.service";
import {ChartDataModel, UFActeSummary} from "../../../core/models/overview/service-overview.model";
import {SpinnerModule} from "primeng/spinner";
import {ProgressSpinnerModule} from "primeng/progressspinner";
import {PdfGeneratorService} from "../../../core/services/pdf-generator.service";

@Component({
  selector: 'app-service-hospitalier',
  standalone: true,
  imports: [
    NgIf,
    NgForOf,
    InformationPanelComponent,
    CardModule,
    BadgeModule,
    ChartModule,
    BreadcrumbComponent,
    StyleClassModule,
    TableModule,
    PaginatorModule,
    SpinnerModule,
    ProgressSpinnerModule,
    NgClass
  ],
  templateUrl: './service-hospitalier.component.html',
  styleUrl: './service-hospitalier.component.scss'
})
export class ServiceHospitalierComponent implements OnInit {
  currentServiceId: string = '';

  servicesHospitaliers: ServiceHospitalier[] = [];

// fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Pôle Anesthésie-Réanimation', url: '/chru-nancy/pole-anesthesie-reanimation' },
    { label: 'Services', url: '/organisation/service-list' },
    { label: 'Service d’Anesthésie Bloc Opératoire' }
  ];

  // Options pour personnaliser le graphique
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio:0.9,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Mois',
        },
      },
      y: {
        title: {
          display: true,
          text: 'Nombre d\'actes',
        },
      },
    },
  };

  //
  chartData!: ChartDataModel; // Défini avec le type ChartData
  ufActesSummary: UFActeSummary[] = []; // Initialisé avec un tableau vide
  isLoading = true;

  paginatedUfActesSummary: any = []; // Liste paginée
  rowsPerPage = 5; // Nombre d'éléments par page

  //********************************************

  constructor(
            private serviceHospitalierService: ServiceHospitalierService,
            private route: ActivatedRoute,
            private serviceHospitalierOverviewService :ServiceHospitalierOverviewService,
            private  pdfGenerator: PdfGeneratorService
            ) {}

  ngOnInit(): void {
    this.serviceHospitalierService.servicesHospitaliers$.subscribe((data) => {
      this.servicesHospitaliers = data;
    });

    //@todo decommente et utilise cette ligne pour revenir a l'aspect dynamique
    //  this.currentServiceId = this.route.snapshot.paramMap.get('uid') || '';
    //
    this.currentServiceId = "550e8400-e29b-41d4-a716-************";
    //
    this.serviceHospitalierOverviewService.services$.subscribe((data) => {
      // this.servicesHospitaliers = data;
      if (data.length > 0){
        this.loadServiceData(this.currentServiceId);
      }
    });
  }

  loadServiceData(serviceId: string): void {
    console.log('Loading service data for ID:', serviceId);
    this.serviceHospitalierOverviewService.getServiceById(serviceId).subscribe((service) => {
      console.log('Received service data:', service);
      if (service) {
        this.chartData = service.chartData;
        this.ufActesSummary = service.ufActesSummary;

        console.table(this.ufActesSummary)
        console.log(this.ufActesSummary)
        this.paginate(0);
      } else {
        this.isLoading = true; // Arrêter le chargement
        console.error('Service not found for ID:', serviceId);
      }
      this.isLoading = false; // Arrêter le chargement
    });
  }

  // Méthode pour paginer
  paginate(pageIndex: number): void {
    const start = pageIndex * this.rowsPerPage;
    const end = start + this.rowsPerPage;
    this.paginatedUfActesSummary = this.ufActesSummary.slice(start, end);
  }

  // Méthode appelée par le paginator
  onPageChange(event: any): void {
    this.paginate(event.page);
  }

  viewUfDetails(uf: UFActeSummary): void {
    console.log(`Voir les détails pour l'UF : ${uf.nomUF}`, uf);
    alert(`Voir les détails pour l'UF : ${uf.nomUF}`);
  }

  isPdfGenerating = false;

  downloadPDF(elementId:string,filename:string): void {
    this.pdfGenerator.exportAsPDF(elementId, `${filename}.pdf`);
    setTimeout(() => (this.isPdfGenerating = false), 1000); // Réinitialise après la génération

  }
  downloadPDFMany(className:string,filename:string): void {
    this.pdfGenerator.exportAsPDFMany(className, `${filename}.pdf`);
  }



}
