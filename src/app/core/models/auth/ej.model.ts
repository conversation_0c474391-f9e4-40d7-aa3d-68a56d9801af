export interface EjResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  totalItems: number;
  member: Ej[];
}

export interface Ej {
  '@id': string;
  '@type': string;
  nom: string;
  code: string;
  adresse: string;
  telephone: string;
  email: string;
  dateCreation: string;
  tenant: string;
  parametre: {
    addresses: {
      boAgentFilesPath: string;
      reportsPath: string;
    };
    theme: {
      primaryColor: string;
      secondaryColor: string;
      logoUrl: string;
    };
    notifications: {
      adminEmail: string;
    };
    featureFlags: {
      enableNewUi: boolean;
      betaReporting: boolean;
    };
    apiEndpoints: {
      dataIntegratorApi: string;
      backendUrl: string;
    };
    allowedEmailDomains: string[];
  };
  isActif: boolean;
}
