/* Custom styles for the multi-details component */
:host {
  display: block;
  width: 100%;
}

.p-field {
  margin-bottom: 1rem;
}

/* Ensure badges are properly styled */
.rounded-full {
  border-radius: 9999px;
}

/* Ensure the table is responsive */
::ng-deep .p-datatable-wrapper {
  overflow-x: auto;
}

/* Style for the expanded row content */
::ng-deep .p-datatable .p-datatable-tbody > tr.p-row-expanded {
  background-color: #f9fafb;
}

::ng-deep .p-datatable .p-datatable-tbody > tr.p-row-expanded + tr > td {
  padding: 0;
}

::ng-deep .p-datatable .p-datatable-tbody > tr.p-row-expanded + tr > td > div {
  margin: 0.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
