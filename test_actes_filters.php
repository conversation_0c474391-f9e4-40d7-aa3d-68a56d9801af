<?php
// Test script for the /api/actes/filters endpoint
// This script tests the cascade filtering functionality for practitioners, poles, and CRs
// 
// The script tests the following scenarios:
// 1. Basic filters without any parameters
// 2. Filters with periods (January to June for 2025, 2024, 2023)
// 3. Filters with ejcode
// 4. Filters with practitioner selected (cascade test)
// 5. Filters with pole selected (cascade test)
// 6. Filters with CR selected (cascade test)
// 7. Combined filters test

// Function to make a GET request and display the results
function testEndpoint($url, $bearerToken, $testName) {
    echo "=== $testName ===\n";
    echo "Testing endpoint: $url\n";

    // Use cURL to make the request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);

    // Add Authorization header with bearer token
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $bearerToken,
        'Accept: application/json',
        'Content-Type: application/json'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);

    echo "HTTP Status Code: $httpCode\n";
    
    // Display relevant headers
    echo "Response Headers:\n";
    $headerLines = explode("\n", $headers);
    foreach ($headerLines as $line) {
        if (preg_match('/^(Content-Type|Content-Length|X-Debug-Token|X-Debug-Token-Link):/i', $line)) {
            echo "  $line\n";
        }
    }

    echo "Response Body:\n";
    if ($httpCode === 200) {
        $data = json_decode($body, true);
        if ($data) {
            echo "  Total Actes: " . ($data['totalActes'] ?? 'N/A') . "\n";
            echo "  Generation Time: " . ($data['generationTimeMs'] ?? 'N/A') . "ms\n";
            echo "  Practitioners Count: " . count($data['practitioners'] ?? []) . "\n";
            echo "  Poles Count: " . count($data['poles'] ?? []) . "\n";
            echo "  CRs Count: " . count($data['crs'] ?? []) . "\n";
            
            // Show first few practitioners
            if (!empty($data['practitioners'])) {
                echo "  First 3 Practitioners:\n";
                foreach (array_slice($data['practitioners'], 0, 3) as $practitioner) {
                    echo "    - " . ($practitioner['displayName'] ?? 'N/A') . " (" . ($practitioner['@id'] ?? 'N/A') . ")\n";
                }
            }
            
            // Show first few poles
            if (!empty($data['poles'])) {
                echo "  First 3 Poles:\n";
                foreach (array_slice($data['poles'], 0, 3) as $pole) {
                    echo "    - " . ($pole['libelle'] ?? 'N/A') . " (" . ($pole['poleCode'] ?? 'N/A') . ")\n";
                }
            }
            
            // Show applied filters and periods
            if (!empty($data['appliedFilters'])) {
                echo "  Applied Filters: " . json_encode($data['appliedFilters']) . "\n";
            }
            if (!empty($data['appliedPeriods'])) {
                echo "  Applied Periods: " . json_encode($data['appliedPeriods']) . "\n";
            }
        } else {
            echo "  Raw response: $body\n";
        }
    } else {
        echo "  Error response: $body\n";
    }
    
    echo "\n" . str_repeat("-", 80) . "\n\n";
}

// Configuration
$baseUrl = 'http://localhost:8000';
$ejcode = "ej0001";

// Bearer token for authentication (same as test_periods_jan_to_jun.php)
$bearerToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ToYfqp5fsxBUHcC0dXXsr-Js-ylY-U62FEbYSBF2a4g';

// Period parameters (January to June for 2025, 2024, 2023)
$periodParams = "p1Start=2025-01-01&p1End=2025-06-30&p2Start=2024-01-01&p2End=2024-06-30&p3Start=2023-01-01&p3End=2023-06-30";

echo "=== Testing /api/actes/filters endpoint for cascade filtering ===\n\n";

// Test 1: Basic filters without any parameters
testEndpoint("$baseUrl/api/actes/filters", $bearerToken, "Test 1: Basic filters without parameters");

// Test 2: Filters with periods
testEndpoint("$baseUrl/api/actes/filters?$periodParams", $bearerToken, "Test 2: Filters with periods (Jan-Jun 2025,2024,2023)");

// Test 3: Filters with ejcode
testEndpoint("$baseUrl/api/actes/filters?ejcode=$ejcode", $bearerToken, "Test 3: Filters with ejcode");

// Test 4: Filters with periods and ejcode
testEndpoint("$baseUrl/api/actes/filters?$periodParams&ejcode=$ejcode", $bearerToken, "Test 4: Filters with periods and ejcode");

// Test 5: Cascade test - with practitioner selected (should limit poles/CRs)
testEndpoint("$baseUrl/api/actes/filters?$periodParams&ejcode=$ejcode&practitioner=/api/agents/1", $bearerToken, "Test 5: Cascade - Practitioner selected");

// Test 6: Cascade test - with pole selected (should limit practitioners/CRs)
testEndpoint("$baseUrl/api/actes/filters?$periodParams&ejcode=$ejcode&pole=/api/poles/1", $bearerToken, "Test 6: Cascade - Pole selected");

// Test 7: Cascade test - with CR selected (should limit practitioners/poles)
testEndpoint("$baseUrl/api/actes/filters?$periodParams&ejcode=$ejcode&cr=/api/crs/1", $bearerToken, "Test 7: Cascade - CR selected");

// Test 8: Combined filters test
testEndpoint("$baseUrl/api/actes/filters?$periodParams&ejcode=$ejcode&typeVenue=1", $bearerToken, "Test 8: Combined filters with typeVenue");

echo "All tests completed.\n";
echo "This endpoint should provide the data needed for intelligent cascade filtering in the frontend.\n";
?>
