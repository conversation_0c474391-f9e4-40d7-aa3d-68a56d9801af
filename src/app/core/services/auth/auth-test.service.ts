import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';

/**
 * Service for testing authentication functionality
 * This service is used to verify that the auth interceptor is working correctly
 */
@Injectable({
  providedIn: 'root'
})
export class AuthTestService {

  constructor(private http: HttpClient) { }

  /**
   * Makes a test request to the API to verify that the auth token is being sent
   * This method should be used only for testing purposes
   */
  testAuthenticatedRequest(): Observable<any> {
    return this.http.get(`${environment.api_url}/api/test-auth`);
  }

  /**
   * Makes a test request to the API to verify that excluded routes don't send the auth token
   * This method should be used only for testing purposes
   */
  testExcludedRoutes(): Observable<any>[] {
    return [
      this.http.get(`${environment.api_url}/api/ejs`),
      this.http.post(`${environment.api_url}/api/login-ad`, {}),
      this.http.post(`${environment.api_url}/api/auth/email/login`, {}),
      this.http.post(`${environment.api_url}/api/auth/email/verify`, {})
    ];
  }
}
