import {Component, OnInit} from '@angular/core';
import {UF} from "../../../core/models/organization/UF.model";
import {UFService} from "../../../core/services/organization/UFService";
import {<PERSON><PERSON>orOf, NgIf} from "@angular/common";
import {BreadcrumbComponent} from "../../../pages/breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../../core/models/breadcrumbItem";
import {ActivatedRoute} from "@angular/router";
import {InformationPanelComponent} from "../information-panel/information-panel.component";

@Component({
  selector: 'app-uf',
  standalone: true,
  imports: [
    NgIf,
    NgForOf,
    BreadcrumbComponent,
    InformationPanelComponent
  ],
  templateUrl: './uf.component.html',
  styleUrl: './uf.component.scss'
})
export class UfComponent implements OnInit {
  ufs: UF[] = [];
  currentUfId: string = '';
  isLoading = true;
  ufDetails: UF | undefined;

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Pôle Cardiologie', url: '/chru-nancy/pole-anesthesie-reanimation' },
    { label: 'Service de Cardiologie Médicale', url: '/chru-nancy/pole-anesthesie-reanimation/departement-anesthesie' },
    { label: 'UF 2171 - Cardiologie Hypertension' }
  ];

  constructor(private ufService: UFService,private route: ActivatedRoute) {}

  ngOnInit(): void {
    // // Récupération de l'ID de l'UF à partir de la route
    // this.route.paramMap.subscribe((params) => {
    //   const ufId = params.get('id') || '';
    //   this.currentUfId = ufId;
    //   if (ufId) {
    //     this.loadUfData(ufId);
    //   }
    // });
    // this.currentUfId = '4d1e8400-e29b-41d4-a716-************';
    this.currentUfId =  this.route.snapshot.paramMap.get('uid') || '';
    this.ufService.ufs$.subscribe((data) => {
      this.ufs = data;
      if (this.currentUfId) {
        this.loadUfData(this.currentUfId);
      }
    });
  }

  /*********************************************************************/

  // Charger les données de l'UF
  loadUfData(ufId: string): void {
    this.isLoading = true;
    this.ufService.getUFById(ufId).subscribe((uf) => {
      if (uf) {
        this.ufDetails = uf;
        this.breadcrumbItems[this.breadcrumbItems.length - 1] = {
          label: uf.name,
        };
      } else {
        console.error('UF introuvable pour l’ID:', ufId);
      }
      this.isLoading = false;
    });
  }


}
