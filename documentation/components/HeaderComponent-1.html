<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  HeaderComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/core/components/header/header.component.ts</code>
</p>


    <p class="comment">
        <h3>Description</h3>
    </p>
    <p class="comment">
        <p>some composoc class documentation</p>

    </p>


    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-header</code></td>
            </tr>






            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./header.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./header.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#isDropdownOpen" >isDropdownOpen</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#toggleDropdown" >toggleDropdown</a>
                            </li>
                            <li>
                                <a href="#toggleSidebar" >toggleSidebar</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(sidebarService: SidebarService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="14" class="link-to-prism">src/app/core/components/header/header.component.ts:14</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>sidebarService</td>

                                                        <td>
                                                                    <code>SidebarService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="18"
                                    class="link-to-prism">src/app/core/components/header/header.component.ts:18</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="toggleDropdown"></a>
                    <span class="name">
                        <span ><b>toggleDropdown</b></span>
                        <a href="#toggleDropdown"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>toggleDropdown()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="28"
                                    class="link-to-prism">src/app/core/components/header/header.component.ts:28</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="toggleSidebar"></a>
                    <span class="name">
                        <span ><b>toggleSidebar</b></span>
                        <a href="#toggleSidebar"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>toggleSidebar()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="24"
                                    class="link-to-prism">src/app/core/components/header/header.component.ts:24</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p> cette methode dois se montrer dsna compodoc</p>
</div>

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isDropdownOpen"></a>
                    <span class="name">
                        <span ><b>isDropdownOpen</b></span>
                        <a href="#isDropdownOpen"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>false</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="14" class="link-to-prism">src/app/core/components/header/header.component.ts:14</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import {SidebarService} from &quot;../../services/sidebar.service&quot;;

/**
 * some composoc class documentation
 */
@Component({
  selector: &#x27;app-header&#x27;,
  standalone: false,
  templateUrl: &#x27;./header.component.html&#x27;,
  styleUrl: &#x27;./header.component.scss&#x27;
})
export class HeaderComponent implements OnInit{
  isDropdownOpen &#x3D; false;

  constructor(private sidebarService: SidebarService) {}

  ngOnInit(): void {
  }

  /**
   *  cette methode dois se montrer dsna compodoc
   */
  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  toggleDropdown() {
    this.isDropdownOpen &#x3D; !this.isDropdownOpen;
  }
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">
&lt;div class&#x3D;&quot;relative z-10 flex-shrink-0 flex h-16 bg-white border-b border-gray-200 lg:border-none&quot;&gt;
  &lt;button
    (click)&#x3D;&quot;toggleSidebar()&quot;
    type&#x3D;&quot;button&quot;
    class&#x3D;&quot;px-4 border-r border-gray-200 text-gray-400 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden&quot;
  &gt;
    &lt;span class&#x3D;&quot;sr-only&quot;&gt;Open sidebar&lt;/span&gt;
    &lt;svg
      class&#x3D;&quot;h-6 w-6&quot;
      xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;
      fill&#x3D;&quot;none&quot;
      viewBox&#x3D;&quot;0 0 24 24&quot;
      stroke&#x3D;&quot;currentColor&quot;
      aria-hidden&#x3D;&quot;true&quot;
    &gt;
      &lt;path
        stroke-linecap&#x3D;&quot;round&quot;
        stroke-linejoin&#x3D;&quot;round&quot;
        stroke-width&#x3D;&quot;2&quot;
        d&#x3D;&quot;M4 6h16M4 12h8m-8 6h16&quot;
      /&gt;
    &lt;/svg&gt;
  &lt;/button&gt;
  &lt;!-- Search bar --&gt;
  &lt;div class&#x3D;&quot;flex-1 px-4 flex justify-between sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8&quot;&gt;
    &lt;div class&#x3D;&quot;flex-1 flex&quot;&gt;
      &lt;form class&#x3D;&quot;w-full flex md:ml-0&quot; action&#x3D;&quot;#&quot; method&#x3D;&quot;GET&quot;&gt;
        &lt;label for&#x3D;&quot;search-field&quot; class&#x3D;&quot;sr-only&quot;&gt;Search&lt;/label&gt;
        &lt;div class&#x3D;&quot;relative w-full text-gray-400 focus-within:text-gray-600&quot;&gt;
          &lt;div
            class&#x3D;&quot;absolute inset-y-0 left-0 flex items-center pointer-events-none&quot;
            aria-hidden&#x3D;&quot;true&quot;
          &gt;
            &lt;svg
              class&#x3D;&quot;h-5 w-5&quot;
              xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;
              viewBox&#x3D;&quot;0 0 20 20&quot;
              fill&#x3D;&quot;currentColor&quot;
              aria-hidden&#x3D;&quot;true&quot;
            &gt;
              &lt;path
                fill-rule&#x3D;&quot;evenodd&quot;
                d&#x3D;&quot;M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z&quot;
                clip-rule&#x3D;&quot;evenodd&quot;
              /&gt;
            &lt;/svg&gt;
          &lt;/div&gt;
          &lt;input
            id&#x3D;&quot;search-field&quot;
            name&#x3D;&quot;search-field&quot;
            class&#x3D;&quot;block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-transparent sm:text-sm&quot;
            placeholder&#x3D;&quot;Rechercher un praticien ou un service&quot;
            type&#x3D;&quot;search&quot;
          /&gt;
        &lt;/div&gt;
      &lt;/form&gt;
    &lt;/div&gt;
    &lt;div class&#x3D;&quot;ml-4 flex items-center md:ml-6&quot;&gt;
      &lt;button
        type&#x3D;&quot;button&quot;
        class&#x3D;&quot;bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500&quot;
      &gt;
        &lt;span class&#x3D;&quot;sr-only&quot;&gt;View notifications&lt;/span&gt;
        &lt;svg
          class&#x3D;&quot;h-6 w-6&quot;
          xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;
          fill&#x3D;&quot;none&quot;
          viewBox&#x3D;&quot;0 0 24 24&quot;
          stroke&#x3D;&quot;currentColor&quot;
          aria-hidden&#x3D;&quot;true&quot;
        &gt;
          &lt;path
            stroke-linecap&#x3D;&quot;round&quot;
            stroke-linejoin&#x3D;&quot;round&quot;
            stroke-width&#x3D;&quot;2&quot;
            d&#x3D;&quot;M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9&quot;
          /&gt;
        &lt;/svg&gt;
      &lt;/button&gt;

      &lt;!-- Profile dropdown --&gt;
      &lt;div class&#x3D;&quot;ml-3 relative&quot;&gt;
        &lt;div&gt;
          &lt;button
            (click)&#x3D;&quot;toggleDropdown()&quot;
            type&#x3D;&quot;button&quot;
            class&#x3D;&quot;max-w-xs bg-white rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 lg:p-2 lg:rounded-md lg:hover:bg-gray-50&quot;
            aria-expanded&#x3D;&quot;false&quot;
            aria-haspopup&#x3D;&quot;true&quot;
          &gt;
            &lt;!-- Replace image with ProfilePicture component --&gt;
            &lt;div class&#x3D;&quot;h-7 w-8 mr-1 &quot;&gt;
              &lt;img class&#x3D;&quot;h-8 w-8 rounded-full&quot;
                   src&#x3D;&quot;./assets/profils/cme-president.jpeg&quot;
                   alt&#x3D;&quot;Pt Marc Debouverie&quot;&gt;
&lt;!--              &lt;app-profile-picture--&gt;
&lt;!--                [nom]&#x3D;&quot;&#x27;Debouverie&#x27;&quot;--&gt;
&lt;!--                [prenom]&#x3D;&quot;&#x27;Marc&#x27;&quot;--&gt;
&lt;!--                [status]&#x3D;&quot;&#x27;online&#x27;&quot;--&gt;
&lt;!--              &gt;&lt;/app-profile-picture&gt;--&gt;
            &lt;/div&gt;

            &lt;span
              class&#x3D;&quot;hidden ml-3 text-gray-700 text-sm font-medium lg:block&quot;
            &gt;
              &lt;span class&#x3D;&quot;sr-only&quot;&gt;Open user menu for &lt;/span&gt;Pt Marc
              Debouverie
            &lt;/span&gt;
            &lt;svg
              class&#x3D;&quot;hidden flex-shrink-0 ml-1 h-5 w-5 text-gray-400 lg:block&quot;
              xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;
              viewBox&#x3D;&quot;0 0 20 20&quot;
              fill&#x3D;&quot;currentColor&quot;
              aria-hidden&#x3D;&quot;true&quot;
            &gt;
              &lt;path
                fill-rule&#x3D;&quot;evenodd&quot;
                d&#x3D;&quot;M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z&quot;
                clip-rule&#x3D;&quot;evenodd&quot;
              /&gt;
            &lt;/svg&gt;
          &lt;/button&gt;
        &lt;/div&gt;
        &lt;!-- Dropdown menu --&gt;
        @if (isDropdownOpen)
        {
          &lt;div
            class&#x3D;&quot;origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none&quot;
            role&#x3D;&quot;menu&quot;
            aria-orientation&#x3D;&quot;vertical&quot;
            aria-labelledby&#x3D;&quot;user-menu-button&quot;
            tabindex&#x3D;&quot;-1&quot;
          &gt;
            &lt;a
              href&#x3D;&quot;#&quot;
              class&#x3D;&quot;block px-4 py-2 text-sm text-gray-700&quot;
              role&#x3D;&quot;menuitem&quot;
              tabindex&#x3D;&quot;-1&quot;
              id&#x3D;&quot;user-menu-item-0&quot;
            &gt;
              Votre profil
            &lt;/a&gt;
            &lt;a
              href&#x3D;&quot;#&quot;
              class&#x3D;&quot;block px-4 py-2 text-sm text-gray-700&quot;
              role&#x3D;&quot;menuitem&quot;
              tabindex&#x3D;&quot;-1&quot;
              id&#x3D;&quot;user-menu-item-1&quot;
            &gt;
              Paramètres
            &lt;/a&gt;
            &lt;a
              href&#x3D;&quot;#&quot;
              class&#x3D;&quot;block px-4 py-2 text-sm text-gray-700&quot;
              role&#x3D;&quot;menuitem&quot;
              tabindex&#x3D;&quot;-1&quot;
              id&#x3D;&quot;user-menu-item-2&quot;
            &gt;
              Déconnexion
            &lt;/a&gt;
          &lt;/div&gt;
        }
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;



&lt;!--&lt;img class&#x3D;&quot;h-8 w-8 rounded-full&quot;--&gt;
&lt;!--     src&#x3D;&quot;./assets/profils/cme-president.jpeg&quot;--&gt;
&lt;!--     alt&#x3D;&quot;Pt Marc Debouverie&quot;&gt;--&gt;
</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><div class="relative z-10 flex-shrink-0 flex h-16 bg-white border-b border-gray-200 lg:border-none">  <button    (click)="toggleSidebar()"    type="button"    class="px-4 border-r border-gray-200 text-gray-400 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden"  >    <span class="sr-only">Open sidebar</span>    <svg      class="h-6 w-6"      xmlns="http://www.w3.org/2000/svg"      fill="none"      viewBox="0 0 24 24"      stroke="currentColor"      aria-hidden="true"    >      <path        stroke-linecap="round"        stroke-linejoin="round"        stroke-width="2"        d="M4 6h16M4 12h8m-8 6h16"      />    </svg>  </button>  <!-- Search bar -->  <div class="flex-1 px-4 flex justify-between sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">    <div class="flex-1 flex">      <form class="w-full flex md:ml-0" action="#" method="GET">        <label for="search-field" class="sr-only">Search</label>        <div class="relative w-full text-gray-400 focus-within:text-gray-600">          <div            class="absolute inset-y-0 left-0 flex items-center pointer-events-none"            aria-hidden="true"          >            <svg              class="h-5 w-5"              xmlns="http://www.w3.org/2000/svg"              viewBox="0 0 20 20"              fill="currentColor"              aria-hidden="true"            >              <path                fill-rule="evenodd"                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"                clip-rule="evenodd"              />            </svg>          </div>          <input            id="search-field"            name="search-field"            class="block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-0 focus:border-transparent sm:text-sm"            placeholder="Rechercher un praticien ou un service"            type="search"          />        </div>      </form>    </div>    <div class="ml-4 flex items-center md:ml-6">      <button        type="button"        class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"      >        <span class="sr-only">View notifications</span>        <svg          class="h-6 w-6"          xmlns="http://www.w3.org/2000/svg"          fill="none"          viewBox="0 0 24 24"          stroke="currentColor"          aria-hidden="true"        >          <path            stroke-linecap="round"            stroke-linejoin="round"            stroke-width="2"            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"          />        </svg>      </button>      <!-- Profile dropdown -->      <div class="ml-3 relative">        <div>          <button            (click)="toggleDropdown()"            type="button"            class="max-w-xs bg-white rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 lg:p-2 lg:rounded-md lg:hover:bg-gray-50"            aria-expanded="false"            aria-haspopup="true"          >            <!-- Replace image with ProfilePicture component -->            <div class="h-7 w-8 mr-1 ">              <img class="h-8 w-8 rounded-full"                   src="./assets/profils/cme-president.jpeg"                   alt="Pt Marc Debouverie"><!--              <app-profile-picture--><!--                [nom]="\'Debouverie\'"--><!--                [prenom]="\'Marc\'"--><!--                [status]="\'online\'"--><!--              ></app-profile-picture>-->            </div>            <span              class="hidden ml-3 text-gray-700 text-sm font-medium lg:block"            >              <span class="sr-only">Open user menu for </span>Pt Marc              Debouverie            </span>            <svg              class="hidden flex-shrink-0 ml-1 h-5 w-5 text-gray-400 lg:block"              xmlns="http://www.w3.org/2000/svg"              viewBox="0 0 20 20"              fill="currentColor"              aria-hidden="true"            >              <path                fill-rule="evenodd"                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"                clip-rule="evenodd"              />            </svg>          </button>        </div>        <!-- Dropdown menu -->        @if (isDropdownOpen)        {          <div            class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"            role="menu"            aria-orientation="vertical"            aria-labelledby="user-menu-button"            tabindex="-1"          >            <a              href="#"              class="block px-4 py-2 text-sm text-gray-700"              role="menuitem"              tabindex="-1"              id="user-menu-item-0"            >              Votre profil            </a>            <a              href="#"              class="block px-4 py-2 text-sm text-gray-700"              role="menuitem"              tabindex="-1"              id="user-menu-item-1"            >              Paramètres            </a>            <a              href="#"              class="block px-4 py-2 text-sm text-gray-700"              role="menuitem"              tabindex="-1"              id="user-menu-item-2"            >              Déconnexion            </a>          </div>        }      </div>    </div>  </div></div><!--<img class="h-8 w-8 rounded-full"--><!--     src="./assets/profils/cme-president.jpeg"--><!--     alt="Pt Marc Debouverie">--></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'HeaderComponent-1'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'HeaderComponent-1.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
