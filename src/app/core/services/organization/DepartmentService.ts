import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {BehaviorSubject, map, Observable} from 'rxjs';
import {Department} from "../../models/organization/Department.model";

@Injectable({
  providedIn: 'root'
})
export class DepartementService {
  private departementsSubject = new BehaviorSubject<Department[]>([]);
  departements$: Observable<Department[]> = this.departementsSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadDepartements();
  }

  private loadDepartements() {
    this.http.get<Department[]>('/assets/data/departments.json').subscribe((data) => {
      this.departementsSubject.next(data);
    });
  }

  getDepartementById(id: string): Observable<Department | undefined> {
    return this.departements$.pipe(
      map((departements) => departements.find((departement) => departement.id === id))
    );
  }
}
