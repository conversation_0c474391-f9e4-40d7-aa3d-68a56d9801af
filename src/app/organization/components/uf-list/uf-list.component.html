<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

<section>
  <div class="p-4 bg-gray-50 rounded-lg shadow mb-4">
    <div class="flex justify-between items-center">
      <!-- Titre principal avec icône -->
      <div class="flex items-center space-x-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-8 h-8 text-cyan-700"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"
          />
        </svg>
        <h1 class="text-xl font-bold text-gray-700">Liste des Unités Fonctionnelles</h1>
      </div>

      <!-- Statistiques globales -->
      <div class="flex items-center space-x-8">
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalUFs }}</p>
          <p class="text-sm text-gray-500">Unités Fonctionnelles</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalPractitioners }}</p>
          <p class="text-sm text-gray-500">Praticiens</p>
        </div>
      </div>

      <!-- Icône décorative -->
      <div class="hidden lg:block text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-24 h-24 text-cyan-700 mx-auto"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"
          />
        </svg>
        <p class="mt-2 text-sm font-medium text-gray-500">CHRU Nancy</p>
      </div>
    </div>
  </div>
</section>


<section>
  <!-- Tableau des UFs -->
  <div class="p-card">
    <p-table
      #ufTable
      [value]="ufs"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [responsiveLayout]="'scroll'"
      [globalFilterFields]="['name', 'code']"
      class="min-w-full bg-white border rounded-lg shadow"
    >
      <!-- Caption -->
      <ng-template pTemplate="caption">
        <div class="relative">
          <!-- Icône de recherche -->
          <span class="absolute inset-y-0 left-0 flex items-center pl-3">
          <i class="pi pi-search text-gray-400"></i>
        </span>
          <!-- Input de recherche -->
          <input
            type="text"
            (input)="ufTable.filterGlobal(onInput($event), 'contains')"
            class="block w-full pl-10 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
             ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600
             sm:text-sm"
            placeholder="Filtrer par nom ou code"
          />
        </div>
      </ng-template>

      <!-- Header -->
      <ng-template pTemplate="header">
        <tr>
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>

          <th pSortableColumn="code" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Code
            <p-sortIcon field="code"></p-sortIcon>
          </th>
          <th pSortableColumn="name" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Nom
            <p-sortIcon field="name"></p-sortIcon>
          </th>
          <th pSortableColumn="practitioners" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Nombre de Praticiens
            <p-sortIcon field="practitioners"></p-sortIcon>
          </th>
        </tr>
      </ng-template>

      <!-- Body -->
      <ng-template pTemplate="body" let-uf>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-lg text-cyan-700 cursor-pointer ml-2"
                title="Voir les détails du sevice {{uf.name }} {{uf.code}}"
                (click)="viewUfDetails(uf.id)"
              ></i>
            </p>
          </td>
          <td class="px-4 py-2"  (click)="viewUfDetails(uf.id)">
            <a  class="text-lg text-cyan-700 hover:underline cursor-pointer font-semibold"
                (click)="viewUfDetails(uf.id)">
              {{ uf.name }}
            </a>
          </td>

          <td class="px-4 py-2">{{ uf.code }}</td>
          <td class="px-4 py-2">{{ uf.practitioners }}</td>
        </tr>
      </ng-template>

      <!-- Message vide -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="3" class="text-center py-4">Aucune unité fonctionnelle trouvée</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</section>
