import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, <PERSON>For<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgOptimizedImage } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { Table, TableModule } from "primeng/table";
import { Router } from "@angular/router";
import { BreadcrumbItem } from "../../../core/models/breadcrumbItem";
import { BreadcrumbComponent } from "../../../pages/breadcrumb/breadcrumb.component";
import { Button, ButtonDirective } from "primeng/button";
import { ServiceModel } from "../../../core/models/structure";
import { ServiceStructureService } from "../../../core/services/structure/service-structure.service";
import { LazyLoadEvent } from 'primeng/api';
import { SkeletonModule } from 'primeng/skeleton';
import { TooltipModule } from 'primeng/tooltip';
import { CheckboxModule } from 'primeng/checkbox';

@Component({
  selector: 'app-service-list',
  standalone: true,
  imports: [
    NgForOf,
    NgIf,
    NgClass,
    AsyncPipe,
    FormsModule,
    TableModule,
    BreadcrumbComponent,
    ButtonDirective,
    Button,
    NgOptimizedImage,
    SkeletonModule,
    TooltipModule,
    CheckboxModule
  ],
  templateUrl: './service-list.component.html',
  styleUrl: './service-list.component.scss'
})
export class ServiceListComponent implements OnInit {
  services: ServiceModel[] = []; // Tableau local pour stocker les données
  loading: boolean = false; // Variable pour suivre l'état de chargement
  allServices: ServiceModel[] = []; // Tableau pour stocker toutes les données chargées
  filteredServices: ServiceModel[] = []; // Tableau pour stocker les données filtrées
  selectedServices: ServiceModel[] = []; // Tableau pour stocker les services sélectionnés

  // Pagination variables
  currentPage: number = 1;
  pageSize: number = 10;
  first: number = 0;
  rowsPerPageOptions: number[] = [10, 25, 50];

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Structure', url: '/structure' },
    { label: 'Liste des services' }
  ];

  // Statistiques globales
  totalServices: number = 0;
  totalActiveServices: number = 0;

  constructor(
    private serviceStructureService: ServiceStructureService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadAllServices(); // Charger toutes les données
  }

  // Variables for sorting
  sortField: string = '';
  sortOrder: number = 1;

  // Fonction pour charger toutes les données
  loadAllServices(): void {
    this.loading = true; // Définir l'état de chargement à true avant l'appel API

    // Charger toutes les données avec tri côté serveur
    this.serviceStructureService.getAll(undefined, undefined, this.sortField, this.sortOrder).subscribe(
      (paginatedResult) => {
        if (paginatedResult) {
          // Stocker toutes les données chargées
          this.allServices = paginatedResult.items;
          this.filteredServices = [...this.allServices];
          this.services = this.filteredServices;
          // Utilise le nombre total d'items retourné par l'API
          this.totalServices = paginatedResult.totalItems;
          this.totalActiveServices = paginatedResult.items.filter(service => service.isActif).length;
          // Update rowsPerPageOptions based on total items
          this.updateRowsPerPageOptions();
        }
        this.loading = false; // Définir l'état de chargement à false après réception des données
      },
      (error) => {
        console.error('Erreur lors du chargement des services :', error);
        this.services = []; // Gère les erreurs en initialisant un tableau vide
        this.allServices = [];
        this.filteredServices = [];
        this.totalServices = 0;
        this.totalActiveServices = 0;
        this.loading = false; // Définir l'état de chargement à false en cas d'erreur
      }
    );
  }

  // Fonction pour charger les données
  loadServices(page: number = this.currentPage, pageSize: number = this.pageSize, sortField: string = this.sortField, sortOrder: number = this.sortOrder): void {
    this.loading = true; // Définir l'état de chargement à true avant l'appel API

    // Charger les données depuis l'API avec tri côté serveur
    this.serviceStructureService.getAll(page, pageSize, sortField, sortOrder).subscribe(
      (paginatedResult) => {
        if (paginatedResult) {
          // Stocker les données chargées
          this.services = paginatedResult.items;
          this.allServices = paginatedResult.items;
          this.filteredServices = [...this.allServices];
          // Utilise le nombre total d'items retourné par l'API
          this.totalServices = paginatedResult.totalItems;
          this.totalActiveServices = paginatedResult.items.filter(service => service.isActif).length;
          // Update rowsPerPageOptions based on total items
          this.updateRowsPerPageOptions();
        }
        this.loading = false; // Définir l'état de chargement à false après réception des données
      },
      (error) => {
        console.error('Erreur lors du chargement des services :', error);
        this.services = []; // Gère les erreurs en initialisant un tableau vide
        this.allServices = [];
        this.filteredServices = [];
        this.totalServices = 0;
        this.totalActiveServices = 0;
        this.loading = false; // Définir l'état de chargement à false en cas d'erreur
      }
    );
  }


  onInput(event: Event): string {
    const filterValue = (event.target as HTMLInputElement).value;

    // Si nous avons déjà chargé toutes les données, nous pouvons les filtrer côté client
    if (this.allServices.length > 0) {
      this.applyClientSideFilter(filterValue);
    }

    return filterValue;
  }

  // Méthode pour appliquer le filtre côté client
  applyClientSideFilter(filterValue: string): void {
    if (!filterValue || filterValue.trim() === '') {
      // Si le filtre est vide, utiliser toutes les données
      this.filteredServices = [...this.allServices];
    } else {
      // Sinon, filtrer les données
      const filterValueLower = filterValue.toLowerCase();
      this.filteredServices = this.allServices.filter(service => {
        return (
          (service.libelle && service.libelle.toLowerCase().includes(filterValueLower)) ||
          (service.secode && service.secode.toLowerCase().includes(filterValueLower)) ||
          (service.etab && service.etab.toLowerCase().includes(filterValueLower))
        );
      });
    }

    // Mettre à jour le nombre total de services filtrés
    this.totalServices = this.filteredServices.length;

    // Appliquer la pagination
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.services = this.filteredServices.slice(startIndex, endIndex);
  }

  viewServiceDetails(serviceId: string): void {
    // Extract the ID from the full URI (e.g., "/api/services/123" -> "123")
    const id = serviceId.split('/').pop();
    this.router.navigate(['/structure/service', id]);
  }

  // Helper method to format dates
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  }

  // Handle pagination and sorting events from p-table
  onPageChange(event: any) {
    // Calculate the page number (1-based) from the first row index (0-based)
    const page = event.first !== undefined ? Math.floor(event.first / (event.rows || this.pageSize)) + 1 : 1;
    const pageSize = event.rows || this.pageSize;

    // Extract sort field and order if present
    let sortField = this.sortField;
    let sortOrder = this.sortOrder;

    if (event.sortField) {
      sortField = event.sortField;
      sortOrder = event.sortOrder === 1 ? 1 : -1; // Convert PrimeNG's sortOrder to our format

      // Update component state for sorting
      this.sortField = sortField;
      this.sortOrder = sortOrder;
    }

    // Update component state for pagination
    this.currentPage = page;
    this.pageSize = pageSize;
    this.first = event.first || 0;

    // Charger les données depuis l'API avec tri côté serveur
    this.loadServices(page, pageSize, sortField, sortOrder);
  }

  // Update rowsPerPageOptions based on total items
  updateRowsPerPageOptions() {
    // Default options for small datasets
    let options = [10, 25, 50];

    // For larger datasets, add more options
    if (this.totalServices > 50) {
      options.push(100);
    }

    if (this.totalServices > 100) {
      options.push(200);
    }

    if (this.totalServices > 500) {
      options.push(500);
    }

    // Ensure we don't have options larger than the total items
    this.rowsPerPageOptions = options.filter(option => option <= this.totalServices || option === 10);
  }

  // Check if all items on the current page are selected
  isAllSelected(): boolean {
    return this.services.length > 0 && this.selectedServices.length === this.services.length;
  }

  // Select or deselect all items on the current page
  toggleSelectAll(event: any): void {
    if (event.checked) {
      // Select all items on the current page
      this.selectedServices = [...this.services];
    } else {
      // Deselect all items
      this.selectedServices = [];
    }
  }

  // Check if a specific service is selected
  isServiceSelected(service: ServiceModel): boolean {
    return this.selectedServices.some(s => s['@id'] === service['@id']);
  }

  // Toggle selection of a specific service
  toggleServiceSelection(service: ServiceModel): void {
    if (this.isServiceSelected(service)) {
      // Remove from selection
      this.selectedServices = this.selectedServices.filter(s => s['@id'] !== service['@id']);
    } else {
      // Add to selection
      this.selectedServices = [...this.selectedServices, service];
    }
  }

  // Export selected services to CSV
  exportSelectedServices(): void {
    if (this.selectedServices.length === 0) {
      alert('Veuillez sélectionner au moins un service à exporter');
      return;
    }

    // Create CSV content
    const headers = ['Nom', 'Code', 'Établissement', 'Date de début', 'Statut'];
    const csvContent = [
      headers.join(','),
      ...this.selectedServices.map(service => [
        `"${service.libelle || ''}"`,
        `"${service.secode || ''}"`,
        `"${service.etab || ''}"`,
        `"${this.formatDate(service.datdeb) || ''}"`,
        `"${service.isActif ? 'Actif' : 'Inactif'}"`
      ].join(','))
    ].join('\n');

    // Create a Blob with UTF-8 BOM for Excel compatibility
    const BOM = new Uint8Array([0xEF, 0xBB, 0xBF]);
    const blob = new Blob([BOM, csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `export-services-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
