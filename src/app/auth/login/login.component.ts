import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { FormsModule } from "@angular/forms";
import { DropdownModule } from "primeng/dropdown";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { <PERSON><PERSON><PERSON>Of, NgIf } from "@angular/common";
import { Router } from "@angular/router";
import { AuthService } from "../../core/services/auth/auth.service";
import { Ej } from "../../core/models/auth/ej.model";
import { finalize } from "rxjs/operators";
import { ParticlesComponent } from "../../shared/particles/particles.component";
import { TabViewModule } from 'primeng/tabview';
import { DialogModule } from 'primeng/dialog';
import { interval, Subscription } from 'rxjs';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    FormsModule,
    DropdownModule,
    InputTextModule,
    ButtonModule,
    NgForOf,
    NgIf,
    ParticlesComponent,
    TabViewModule,
    DialogModule,
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent implements OnInit {

  // Liste des entités juridiques (hôpitaux)
  ejOptions: { label: string, value: string }[] = [];

  // Champs du formulaire - Login classique
  selectedHopitalCode: string = '';
  username: string = '';
  password: string = '';

  // Champs du formulaire - Login par email
  email: string = '';
  verificationCode: string = '';

  // État du formulaire
  loading: boolean = false;
  errorMessage: string = '';
  successMessage: string = '';

  // État de l'interface
  activeTabIndex: number = 0;
  showVerificationModal: boolean = false;

  // Timer pour le code de vérification (5 minutes)
  remainingTime: number = 300; // 5 minutes en secondes
  timerSubscription?: Subscription;

  // Domaines autorisés pour l'email
  allowedDomains: string[] = environment.allowedDomains;

  constructor(private auth: AuthService, private router: Router) {}

  ngOnInit(): void {
    this.loadHospitals();
  }

  ngOnDestroy(): void {
    this.stopTimer();
  }

  // Charger la liste des hôpitaux depuis l'API
  loadHospitals(): void {
    this.loading = true;
    this.auth.getEjs()
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (ejs: Ej[]) => {
          this.ejOptions = ejs.map(ej => ({
            label: ej.nom,
            value: ej.code
          }));

          // Si des options sont disponibles, sélectionner la première par défaut
          if (this.ejOptions.length > 0) {
            this.selectedHopitalCode = this.ejOptions[0].value;
          }
        },
        error: (error) => {
          console.error('Failed to load hospitals:', error);
          this.errorMessage = 'Impossible de charger la liste des hôpitaux. Veuillez réessayer plus tard.';
        }
      });
  }

  // Méthode appelée lors de la soumission du formulaire classique
  onSubmit(): void {
    if (!this.username || !this.password || !this.selectedHopitalCode) {
      this.errorMessage = 'Veuillez remplir tous les champs.';
      return;
    }

    this.errorMessage = '';
    this.loading = true;

    this.auth.login(this.username, this.password, this.selectedHopitalCode)
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            console.log('Authentification réussie');
            // Afficher un message de succès au lieu de rediriger
            this.successMessage = 'Authentification réussie! Bienvenue sur Supra.';
            this.errorMessage = '';
            // Stocker le token mais ne pas rediriger (pour les tests)
            // const redirectUrl = this.auth.redirectUrl || '/graphic/dashboard';
            // this.router.navigateByUrl(redirectUrl);
          } else {
            this.errorMessage = response.message || 'Échec de l\'authentification.';
            this.successMessage = '';
          }
        },
        error: (error) => {
          console.error('Login error:', error);
          this.errorMessage = 'Échec de l\'authentification. Veuillez vérifier vos identifiants.';
          this.successMessage = '';
        }
      });
  }

  // Méthode pour changer d'onglet
  onTabChange(event: any): void {
    this.activeTabIndex = event.index;
    this.errorMessage = '';
    this.successMessage = '';
  }

  // Vérifier si l'email appartient à un domaine autorisé
  isValidDomain(email: string): boolean {
    if (!email) return false;
    return this.allowedDomains.some(domain => email.endsWith('@' + domain));
  }

  // Méthode pour initier la connexion par email
  onEmailSubmit(): void {
    if (!this.email) {
      this.errorMessage = 'Veuillez saisir votre adresse email.';
      return;
    }

    if (!this.isValidDomain(this.email)) {
      this.errorMessage = 'Seules les adresses email des domaines suivants sont autorisées: ' + this.allowedDomains.join(', ');
      return;
    }

    this.errorMessage = '';
    this.loading = true;

    this.auth.loginWithEmail(this.email)
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.successMessage = response.message;
            this.errorMessage = '';
            this.showVerificationModal = true;
            this.startTimer();
          } else {
            this.errorMessage = response.message || 'Échec de l\'envoi du code de vérification.';
            this.successMessage = '';
          }
        },
        error: (error) => {
          console.error('Email login error:', error);
          this.errorMessage = 'Échec de l\'envoi du code de vérification. Veuillez réessayer.';
          this.successMessage = '';
        }
      });
  }

  // Méthode pour vérifier le code
  onVerifyCode(): void {
    if (!this.verificationCode) {
      this.errorMessage = 'Veuillez saisir le code de vérification.';
      return;
    }

    this.errorMessage = '';
    this.loading = true;

    this.auth.verifyEmailCode(this.email, this.verificationCode)
      .pipe(finalize(() => this.loading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.successMessage = 'Authentification réussie! Bienvenue sur Supra.';
            this.errorMessage = '';
            this.showVerificationModal = false;
            this.stopTimer();
            // Stocker le token mais ne pas rediriger (pour les tests)
            // const redirectUrl = this.auth.redirectUrl || '/graphic/dashboard';
            // this.router.navigateByUrl(redirectUrl);
          } else {
            this.errorMessage = response.message || 'Code de vérification invalide.';
            this.successMessage = '';
          }
        },
        error: (error) => {
          console.error('Verification error:', error);
          this.errorMessage = 'Échec de la vérification du code. Veuillez réessayer.';
          this.successMessage = '';
        }
      });
  }

  // Démarrer le timer de 5 minutes
  startTimer(): void {
    this.remainingTime = 300; // 5 minutes en secondes
    this.stopTimer(); // Arrêter un éventuel timer en cours

    this.timerSubscription = interval(1000).subscribe(() => {
      this.remainingTime--;

      if (this.remainingTime <= 0) {
        this.stopTimer();
        this.showVerificationModal = false;
        this.errorMessage = 'Le délai de 5 minutes est écoulé. Veuillez demander un nouveau code.';
      }
    });
  }

  // Arrêter le timer
  stopTimer(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
      this.timerSubscription = undefined;
    }
  }

  // Formater le temps restant en minutes:secondes
  formatTime(): string {
    const minutes = Math.floor(this.remainingTime / 60);
    const seconds = this.remainingTime % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  }

  // Fermer le modal de vérification
  closeVerificationModal(): void {
    this.showVerificationModal = false;
    this.stopTimer();
  }

  logout(): void {
    this.auth.logout();
  }
}
