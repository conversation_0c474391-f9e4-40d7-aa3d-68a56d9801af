<!-- Notice informative - only shown when agent data is loaded and hospital email exists -->
<div *ngIf="agent && !loading && agent.hopital?.email" class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4 max-w-5xl mx-auto">
  <p class="text-sm text-blue-700">Me<PERSON><PERSON> de signaler toute information incorrecte via {{ agent.hopital.email }}</p>
</div>

<div class="bg-white shadow overflow-hidden sm:rounded-lg max-w-5xl mx-auto mt-8">
  <!-- Loading state -->
  <div *ngIf="loading" class="p-8 text-center">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-700 mx-auto"></div>
    <p class="mt-4 text-gray-600">Chargement des informations...</p>
  </div>
  <!-- Error state -->
  <div *ngIf="error && !loading" class="p-8 text-center">
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <p>{{ error }}</p>
      <button (click)="loadAgentDetails()" class="mt-4 bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
        Reessayer
      </button>
    </div>
  </div>
  <!-- Agent details -->
  <div *ngIf="agent && !loading">
    <div class="px-4 py-5 sm:px-6 bg-cyan-700 text-white">
      <h3 class="text-lg leading-6 font-medium">
        Profil de l'agent
      </h3>
      <p class="mt-1 max-w-2xl text-sm">
        Informations personnelles et affectations
      </p>
    </div>
    <!-- Personal information section -->
    <div class="border-t border-gray-200">
      <dl>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Nom complet</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
            {{ agent.titre || '' }} {{ agent.nom || '' }} {{ agent.prenom || '' }}
          </dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Email</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ agent.email || 'Non specifie' }}</dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Matricule</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
            {{ agent.matricule || 'Pas connu de supra' }}
          </dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">HR User</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ agent.hrUser || 'Non specifie' }}</dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Date d'arrivee</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ formatDate(agent.dateVenue) }}</dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Date de depart</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ formatDate(agent.dateDepart) }}</dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Etablissement</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ agent.etablissement || 'Non specifie' }}</dd>
        </div>
        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Grade</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ agent.categorie || 'Non specifie' }}</dd>
        </div>
        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
          <dt class="text-sm font-medium text-gray-500">Hopital</dt>
          <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
            {{ agent.hopital?.nom || 'Non specifie' }}
            <span *ngIf="agent.hopital?.code" class="text-xs text-gray-500 ml-2">({{ agent.hopital.email || 'Email non specifie' }})</span>
          </dd>
        </div>
      </dl>
    </div>
    <!-- Affectations section -->
    <div *ngIf="agent.affectations && agent.affectations.length > 0" class="border-t border-gray-200">
      <div class="px-4 py-5 sm:px-6 bg-cyan-600 text-white">
        <h3 class="text-lg leading-6 font-medium">Affectations</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Periode</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ETP</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Principale</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absences</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UF</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr *ngFor="let affectation of agent.affectations">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(affectation.dateDebut) }} - {{ formatDate(affectation.dateFin) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ affectation.etp || 'N/A' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ affectation.affectationPrincipale === 'O' ? 'Oui' : 'Non' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ affectation.absences || 0 }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div *ngIf="affectation.ufs" class="flex items-center">
                  <ng-container *ngIf="getUfDetails(affectation.ufs) as uf">
                    <span class="font-medium">{{ uf.code }}</span>
                    <span class="ml-2 text-gray-500">{{ uf.libelle }}</span>
                  </ng-container>
                  <ng-container *ngIf="!getUfDetails(affectation.ufs)">
                    <span class="font-medium">{{ affectation.ufs }}</span>
                    <span *ngIf="loadingUfs" class="ml-2 text-xs text-gray-500">(chargement...)</span>
                  </ng-container>
                </div>
                <div *ngIf="!affectation.ufs" class="text-gray-500">
                  Non specifie
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div *ngIf="!agent.affectations || agent.affectations.length === 0" class="border-t border-gray-200 p-6 text-center text-gray-500">
      Aucune affectation trouvee pour cet agent.
    </div>
  </div>
</div>
