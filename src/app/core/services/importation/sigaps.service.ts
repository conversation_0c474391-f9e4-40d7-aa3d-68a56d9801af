import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../auth/auth.service';
import { BaseImportService, ImportValidation } from './base-import.service';

export interface SigapsImportResponse {
  success: boolean;
  processed: number;
  results: SigapsResult[];
}

export interface SigapsResult {
  dateDebut: string;
  dateFin: string;
  score: string;
  repartitionParCategorie: {
    'A+'?: number;
    'A'?: number;
    'B'?: number;
    'C'?: number;
    'D'?: number;
  };
  categorie: string;
  nombrePublication: number;
  agentHrU: string | null;
  validFrom: string;
  validTo: string | null;
  periodeType: string;
  source: string;
  isActif: boolean;
  dateCreation: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class SigapsService extends BaseImportService {

  constructor(
    protected override http: HttpClient,
    protected override authService: AuthService
  ) {
    super(http, authService);
  }

  /**
   * Construire l'URL d'import pour SIGAPS
   */
  protected buildImportUrl(): string {
    return `${environment.api_url}/api/sigaps/batch`;
  }

  /**
   * Valider les données SIGAPS
   */
  protected validateData(data: any[]): ImportValidation {
    const errors: string[] = [];

    if (!data || data.length === 0) {
      errors.push('Aucune donnée à importer');
      return { isValid: false, errors };
    }

    // Validation basique des champs requis pour SIGAPS
    data.forEach((item, index) => {
      if (!item.score && item.score !== 0) {
        errors.push(`Ligne ${index + 1}: score requis`);
      }
      // if (!item.categorie) {
      //   errors.push(`Ligne ${index + 1}: categorie requise`);
      // }
      if (!item.nombrePublication && item.nombrePublication !== 0) {
        errors.push(`Ligne ${index + 1}: nombrePublication requis`);
      }
      if (!item.dateDebut) {
        errors.push(`Ligne ${index + 1}: dateDebut requise`);
      }
      if (!item.dateFin) {
        errors.push(`Ligne ${index + 1}: dateFin requise`);
      }
      if (!item.agentHrU) {
        errors.push(`Ligne ${index + 1}: identifiant agent requis (  agentHrU)`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
