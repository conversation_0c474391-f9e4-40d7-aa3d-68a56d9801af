// Test script to verify date handling
// This is just a conceptual test, not meant to be run directly

// Simulate selecting January 2025 as dateDebut and December 2025 as dateFin
const dateDebut = new Date(2025, 0, 1); // January 2025
const dateFin = new Date(2025, 11, 1); // December 2025

console.log('Original dates:');
console.log('dateDebut:', dateDebut);
console.log('dateFin:', dateFin);

// Simulate the onDateRangeChange method in sidebar.component.ts
const dateDebutYear = dateDebut.getFullYear();
const dateDebutMonth = dateDebut.getMonth();
const adjustedDateDebut = new Date(dateDebutYear, dateDebutMonth, 1, 12, 0, 0);

const dateFinYear = dateFin.getFullYear();
const dateFinMonth = dateFin.getMonth();
const lastDay = new Date(dateFinYear, dateFinMonth + 1, 0).getDate();
const adjustedDateFin = new Date(dateFinYear, dateFinMonth, lastDay, 12, 0, 0);

console.log('\nAdjusted dates:');
console.log('adjustedDateDebut:', adjustedDateDebut);
console.log('adjustedDateFin:', adjustedDateFin);

// Simulate the saveToStorage method in GlobalFilterService
const dateDebutString = `${adjustedDateDebut.getFullYear()}-${String(adjustedDateDebut.getMonth() + 1).padStart(2, '0')}-${String(adjustedDateDebut.getDate()).padStart(2, '0')}`;
const dateFinString = `${adjustedDateFin.getFullYear()}-${String(adjustedDateFin.getMonth() + 1).padStart(2, '0')}-${String(adjustedDateFin.getDate()).padStart(2, '0')}`;

console.log('\nStored date strings:');
console.log('dateDebutString:', dateDebutString);
console.log('dateFinString:', dateFinString);

// Simulate the loadFromStorage method in GlobalFilterService
const parsedDateDebut = new Date(dateDebutString);
parsedDateDebut.setDate(1);
parsedDateDebut.setHours(12, 0, 0, 0);

const parsedDateFin = new Date(dateFinString);
const parsedDateFinYear = parsedDateFin.getFullYear();
const parsedDateFinMonth = parsedDateFin.getMonth();
const parsedLastDay = new Date(parsedDateFinYear, parsedDateFinMonth + 1, 0).getDate();
parsedDateFin.setDate(parsedLastDay);
parsedDateFin.setHours(12, 0, 0, 0);

console.log('\nParsed dates:');
console.log('parsedDateDebut:', parsedDateDebut);
console.log('parsedDateFin:', parsedDateFin);

// Expected results:
// dateDebutString should be "2025-01-01"
// dateFinString should be "2025-12-31"
// parsedDateDebut should be January 1, 2025 at 12:00:00
// parsedDateFin should be December 31, 2025 at 12:00:00
