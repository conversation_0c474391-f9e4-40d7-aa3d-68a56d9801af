export interface AgentModel {
  '@id': string;
  '@type': string;
  
  // CollecteurTrait fields
  email: string | null;
  nom: string | null;
  prenom: string | null;
  matricule: string | null;
  hrUser: string | null;
  titre: string | null;
  categorie: string | null;
  etablissement: string | null;
  dateDepart: string | null;
  dateVenue: string | null;
  dateMaj: string | null;
  createurFiche: string | null;
  isAdmin: boolean;
  isAnesthesiste: boolean;
  
  // NativeFieldTrait fields
  fullName: string | null;
  imgUrl: string | null;
  badge: string | null;
  roles: string[];
  
  // Relations
  hopital: EntiteJuridiqueModel | null;
  affectations: AffectationModel[] | null;
  
  // BaseDto fields
  isActif: boolean;
  dateCreation: string;
}

export interface EntiteJuridiqueModel {
  '@id': string;
  '@type': string;
  nom: string;
  code: string;
  adresse: string | null;
  telephone: string | null;
  email: string | null;
  dateCreation: string;
  tenant: string | null;
  isActif: boolean;
}

export interface AffectationModel {
  '@id': string;
  '@type': string;
  dateDebut: string | null;
  dateFin: string | null;
  rgt: string | null;
  etpStatutaire: number | null;
  tauxAffectation: number | null;
  etp: number | null;
  affectationPrincipale: string | null;
  typeGrade: string | null;
  libelleGrade: string | null;
  absences: number | null;
  validFrom: string;
  validTo: string | null;
  periodeType: string;
  source: string | null;
  hrUser: string | null;
  matricule: string | null;
  email: string | null;
  codeUf: string | null;
  agent: string | null;
  ufs: UFModel | null;
  isActif: boolean;
  dateCreation: string;
}

export interface UFModel {
  '@id': string;
  '@type': string;
  ufcode: string;
  libelle: string;
  cr: CRModel | null;
  isActif: boolean;
  dateCreation: string;
}

export interface CRModel {
  '@id': string;
  '@type': string;
  crcode: string;
  libelle: string;
  isActif: boolean;
  dateCreation: string;
}
