
<nav class="flex items-center space-x-3 p-3 bg-gray-50 rounded-md shadow-sm border border-gray-200 mb-4">
  <!-- Lien Accueil -->
  <a [routerLink]="home.url" class="flex items-center space-x-2 text-cyan-600 hover:text-cyan-800 font-semibold">
    <i class="pi {{home.icon}} text-xl"></i>
    <span>{{ home.label }}</span>
  </a>

  <!-- Breadcrumb dynamique -->
  <ng-container *ngFor="let item of items; let last = last">
    <i class="pi pi-angle-right text-gray-400 text-lg"></i>

    <!-- Si ce n'est pas le dernier élément, afficher un lien -->
    <a *ngIf="!last" [routerLink]="item.url" class="text-cyan-600 hover:text-cyan-800 font-semibold transition duration-150 ease-in-out">
      {{ item.label }}
    </a>

    <!-- Si c'est le dernier élément, afficher du texte non cliquable -->
    <span *ngIf="last" class="text-gray-700 font-semibold">
      {{ item.label }}
    </span>
  </ng-container>
</nav>





