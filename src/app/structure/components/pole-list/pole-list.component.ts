import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>or<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgOptimizedImage } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { Table, TableModule } from "primeng/table";
import { Router } from "@angular/router";
import { BreadcrumbItem } from "../../../core/models/breadcrumbItem";
import { BreadcrumbComponent } from "../../../pages/breadcrumb/breadcrumb.component";
import { Button, ButtonDirective } from "primeng/button";
import { PoleModel } from "../../../core/models/structure";
import { PoleService } from "../../../core/services/structure";
import { LazyLoadEvent } from 'primeng/api';
import { SkeletonModule } from 'primeng/skeleton';
import { TooltipModule } from 'primeng/tooltip';
import { CheckboxModule } from 'primeng/checkbox';

@Component({
  selector: 'app-pole-list',
  standalone: true,
  imports: [
    NgForO<PERSON>,
    NgI<PERSON>,
    Ng<PERSON>lass,
    AsyncPipe,
    FormsModule,
    TableModule,
    BreadcrumbComponent,
    ButtonDirective,
    Button,
    NgOptimizedImage,
    SkeletonModule,
    TooltipModule,
    CheckboxModule
  ],
  templateUrl: './pole-list.component.html',
  styleUrl: './pole-list.component.scss'
})
export class PoleListComponent implements OnInit {
  poles: PoleModel[] = []; // Tableau local pour stocker les données
  loading: boolean = false; // Variable pour suivre l'état de chargement
  allPoles: PoleModel[] = []; // Tableau pour stocker toutes les données chargées
  filteredPoles: PoleModel[] = []; // Tableau pour stocker les données filtrées
  selectedPoles: PoleModel[] = []; // Tableau pour stocker les pôles sélectionnés

  // Pagination variables
  currentPage: number = 1;
  pageSize: number = 10;
  first: number = 0;
  rowsPerPageOptions: number[] = [10, 25, 50];

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Structure', url: '/structure' },
    { label: 'Liste des pôles' }
  ];

  // Statistiques globales
  totalPoles: number = 0;
  totalActivePoles: number = 0;

  constructor(
    private poleService: PoleService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadAllPoles(); // Charger toutes les données
  }

  // Variables for sorting
  sortField: string = '';
  sortOrder: number = 1;

  // Fonction pour charger toutes les données
  loadAllPoles(): void {
    this.loading = true; // Définir l'état de chargement à true avant l'appel API

    // Charger toutes les données avec tri côté serveur
    this.poleService.getAll(undefined, undefined, this.sortField, this.sortOrder).subscribe(
      (paginatedResult) => {
        if (paginatedResult) {
          // Stocker toutes les données chargées
          this.allPoles = paginatedResult.items;
          this.filteredPoles = [...this.allPoles];
          this.poles = this.filteredPoles;
          // Utilise le nombre total d'items retourné par l'API
          this.totalPoles = paginatedResult.totalItems;
          this.totalActivePoles = paginatedResult.items.filter(pole => pole.isActif).length;
          // Update rowsPerPageOptions based on total items
          this.updateRowsPerPageOptions();
        }
        this.loading = false; // Définir l'état de chargement à false après réception des données
      },
      (error) => {
        console.error('Erreur lors du chargement des pôles :', error);
        this.poles = []; // Gère les erreurs en initialisant un tableau vide
        this.allPoles = [];
        this.filteredPoles = [];
        this.totalPoles = 0;
        this.totalActivePoles = 0;
        this.loading = false; // Définir l'état de chargement à false en cas d'erreur
      }
    );
  }

  // Fonction pour charger les données
  loadPoles(page: number = this.currentPage, pageSize: number = this.pageSize, sortField: string = this.sortField, sortOrder: number = this.sortOrder): void {
    this.loading = true; // Définir l'état de chargement à true avant l'appel API

    // Charger les données depuis l'API avec tri côté serveur
    this.poleService.getAll(page, pageSize, sortField, sortOrder).subscribe(
      (paginatedResult) => {
        if (paginatedResult) {
          // Stocker les données chargées
          this.poles = paginatedResult.items;
          this.allPoles = paginatedResult.items;
          this.filteredPoles = [...this.allPoles];
          // Utilise le nombre total d'items retourné par l'API
          this.totalPoles = paginatedResult.totalItems;
          this.totalActivePoles = paginatedResult.items.filter(pole => pole.isActif).length;
          // Update rowsPerPageOptions based on total items
          this.updateRowsPerPageOptions();
        }
        this.loading = false; // Définir l'état de chargement à false après réception des données
      },
      (error) => {
        console.error('Erreur lors du chargement des pôles :', error);
        this.poles = []; // Gère les erreurs en initialisant un tableau vide
        this.allPoles = [];
        this.filteredPoles = [];
        this.totalPoles = 0;
        this.totalActivePoles = 0;
        this.loading = false; // Définir l'état de chargement à false en cas d'erreur
      }
    );
  }


  onInput(event: Event): string {
    const filterValue = (event.target as HTMLInputElement).value;

    // Si nous avons déjà chargé toutes les données, nous pouvons les filtrer côté client
    if (this.allPoles.length > 0) {
      this.applyClientSideFilter(filterValue);
    }

    return filterValue;
  }

  // Méthode pour appliquer le filtre côté client
  applyClientSideFilter(filterValue: string): void {
    if (!filterValue || filterValue.trim() === '') {
      // Si le filtre est vide, utiliser toutes les données
      this.filteredPoles = [...this.allPoles];
    } else {
      // Sinon, filtrer les données
      const filterValueLower = filterValue.toLowerCase();
      this.filteredPoles = this.allPoles.filter(pole => {
        return (
          (pole.libelle && pole.libelle.toLowerCase().includes(filterValueLower)) ||
          (pole.polecode && pole.polecode.toLowerCase().includes(filterValueLower)) ||
          (pole.etab && pole.etab.toLowerCase().includes(filterValueLower))
        );
      });
    }

    // Mettre à jour le nombre total de pôles filtrés
    this.totalPoles = this.filteredPoles.length;

    // Appliquer la pagination
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.poles = this.filteredPoles.slice(startIndex, endIndex);
  }

  viewPoleDetails(poleId: string): void {
    // Extract the ID from the full URI (e.g., "/api/poles/123" -> "123")
    const id = poleId.split('/').pop();
    this.router.navigate(['/structure/pole', id]);
  }

  // Helper method to format dates
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  }

  // Handle pagination and sorting events from p-table
  onPageChange(event: any) {
    // Calculate the page number (1-based) from the first row index (0-based)
    const page = event.first !== undefined ? Math.floor(event.first / (event.rows || this.pageSize)) + 1 : 1;
    const pageSize = event.rows || this.pageSize;

    // Extract sort field and order if present
    let sortField = this.sortField;
    let sortOrder = this.sortOrder;

    if (event.sortField) {
      sortField = event.sortField;
      sortOrder = event.sortOrder === 1 ? 1 : -1; // Convert PrimeNG's sortOrder to our format

      // Update component state for sorting
      this.sortField = sortField;
      this.sortOrder = sortOrder;
    }

    // Update component state for pagination
    this.currentPage = page;
    this.pageSize = pageSize;
    this.first = event.first || 0;

    // Charger les données depuis l'API avec tri côté serveur
    this.loadPoles(page, pageSize, sortField, sortOrder);
  }

  // Update rowsPerPageOptions based on total items
  updateRowsPerPageOptions() {
    // Default options for small datasets
    let options = [10, 25, 50];

    // For larger datasets, add more options
    if (this.totalPoles > 50) {
      options.push(100);
    }

    if (this.totalPoles > 100) {
      options.push(200);
    }

    if (this.totalPoles > 500) {
      options.push(500);
    }

    // Ensure we don't have options larger than the total items
    this.rowsPerPageOptions = options.filter(option => option <= this.totalPoles || option === 10);
  }

  // Check if all items on the current page are selected
  isAllSelected(): boolean {
    return this.poles.length > 0 && this.selectedPoles.length === this.poles.length;
  }

  // Select or deselect all items on the current page
  toggleSelectAll(event: any): void {
    if (event.checked) {
      // Select all items on the current page
      this.selectedPoles = [...this.poles];
    } else {
      // Deselect all items
      this.selectedPoles = [];
    }
  }

  // Check if a specific pole is selected
  isPoleSelected(pole: PoleModel): boolean {
    return this.selectedPoles.some(p => p['@id'] === pole['@id']);
  }

  // Toggle selection of a specific pole
  togglePoleSelection(pole: PoleModel): void {
    if (this.isPoleSelected(pole)) {
      // Remove from selection
      this.selectedPoles = this.selectedPoles.filter(p => p['@id'] !== pole['@id']);
    } else {
      // Add to selection
      this.selectedPoles = [...this.selectedPoles, pole];
    }
  }

  // Export selected poles to CSV
  exportSelectedPoles(): void {
    if (this.selectedPoles.length === 0) {
      alert('Veuillez sélectionner au moins un pôle à exporter');
      return;
    }

    // Create CSV content
    const headers = ['Nom', 'Code', 'Établissement', 'Date de début', 'Statut'];
    const csvContent = [
      headers.join(','),
      ...this.selectedPoles.map(pole => [
        `"${pole.libelle || ''}"`,
        `"${pole.polecode || ''}"`,
        `"${pole.etab || ''}"`,
        `"${this.formatDate(pole.datdeb) || ''}"`,
        `"${pole.isActif ? 'Actif' : 'Inactif'}"`
      ].join(','))
    ].join('\n');

    // Create a Blob with UTF-8 BOM for Excel compatibility
    const BOM = new Uint8Array([0xEF, 0xBB, 0xBF]);
    const blob = new Blob([BOM, csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `export-poles-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
