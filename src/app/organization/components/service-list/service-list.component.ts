import {Component, OnInit} from '@angular/core';
import {Observable} from "rxjs";
import {ServiceHospitalier} from "../../../core/models/organization/ServiceHospitalier.model";
import {ServiceHospitalierService} from "../../../core/services/organization/ServiceHospitalierService";
import {AsyncPipe, NgForOf, NgOptimizedImage} from "@angular/common";
import {Table, TableModule} from "primeng/table";
import {Router} from "@angular/router";
import {BreadcrumbItem} from "../../../core/models/breadcrumbItem";
import {BreadcrumbComponent} from "../../../pages/breadcrumb/breadcrumb.component";
import {Button, ButtonDirective} from "primeng/button";

@Component({
  selector: 'app-service-list',
  standalone: true,
  imports: [
    NgForOf,
    AsyncPipe,
    TableModule,
    BreadcrumbComponent,
    ButtonDirective,
    Button,
    NgOptimizedImage
  ],
  templateUrl: './service-list.component.html',
  styleUrl: './service-list.component.scss'
})
export class ServiceListComponent implements OnInit {
  servicesHospitaliers: ServiceHospitalier[] = []; // Tableau local pour stocker les données

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Services', url: '/organisation/service-list' },
    { label: 'Liste des services hospitaliers' }
  ];

  // Statistiques globales
  totalServices: number = 0;
  totalPractitioners: number = 0;
  totalUFs: number = 0;

  constructor(
    private serviceHospitalierService: ServiceHospitalierService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadServicesHospitaliers(); // Appel de la fonction pour charger les données
  }

  // Fonction pour charger les données
  loadServicesHospitaliers(): void {
    this.serviceHospitalierService.servicesHospitaliers$.subscribe(
      (data) => {
        if (data) {
          this.servicesHospitaliers = data; // Assigne les données au tableau local
          // Calcul des statistiques globales
          this.totalServices = data.length;
          this.totalPractitioners = data.reduce((sum, service) => sum + service.practitioners, 0);
          this.totalUFs = data.reduce((sum, service) => sum + service.ufs.length, 0);
        }
      },
      (error) => {
        console.error('Erreur lors du chargement des services hospitaliers :', error);
        this.servicesHospitaliers = []; // Gère les erreurs en initialisant un tableau vide
        this.totalServices = 0;
        this.totalPractitioners = 0;
        this.totalUFs = 0;
      }
    );
  }
  onInput(event: Event): string {
    console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }

  viewServiceDetails(serviceId: string): void {
    this.router.navigate(['/organisation/service', serviceId]);
  }
}
