import { Injectable } from '@angular/core';
import {
  <PERSON>ttp<PERSON>e<PERSON>,
  <PERSON>ttpH<PERSON><PERSON>,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth/auth.service';
import { environment } from '../../../environments/environment';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(private authService: AuthService) {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    // Get the auth token from the service
    const token = this.authService.getToken();

    // Check if the request URL is for a route that doesn't need authentication
    const url = request.url;
    const apiUrl = environment.api_url;

    // Routes that don't need authentication:
    // - /api/ejs (hospital list)
    // - Login routes (/api/login-ad, /api/auth/email/login, /api/auth/email/verify)
    const excludedRoutes = [
      `${apiUrl}/api/ejs`,
      `${apiUrl}/api/login-ad`,
      `${apiUrl}/api/auth/email/login`,
      `${apiUrl}/api/auth/email/verify`
    ];

    // If the request URL is in the excluded routes, don't add the token
    if (excludedRoutes.some(route => url.startsWith(route))) {
      return next.handle(request);
    }

    // If token exists and the route is not excluded, clone the request and add the authorization header
    if (token) {
      const authReq = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        }
      });
      return next.handle(authReq);
    }

    // If no token, proceed with the original request
    return next.handle(request);
  }
}
