<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  InformationPanelComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/organization/components/information-panel/information-panel.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-information-panel</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>FilterByPraticienPipe</code>
                            <code>NgForOf</code>
                            <code>TitleCasePipe</code>
                            <code>NgIf</code>
                            <code>TableModule</code>
                            <code>InputTextModule</code>
                            <code>IconFieldModule</code>
                            <code>InputIconModule</code>
                            <code>BadgeModule</code>
                            <code>CardModule</code>
                            <code>ChartModule</code>
                                <code><a href="../components/BreadcrumbComponent.html" target="_self" >BreadcrumbComponent</a></code>
                            <code>Button</code>
                            <code>OverlayPanelModule</code>
                            <code>DatePipe</code>
                            <code>DropdownModule</code>
                            <code>FormsModule</code>
                            <code>FloatLabelModule</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./information-panel.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./information-panel.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#actesCCAM" >actesCCAM</a>
                            </li>
                            <li>
                                <a href="#actesLABO" >actesLABO</a>
                            </li>
                            <li>
                                <a href="#actesNGAP" >actesNGAP</a>
                            </li>
                            <li>
                                <a href="#ccamData" >ccamData</a>
                            </li>
                            <li>
                                <a href="#chartOptions" >chartOptions</a>
                            </li>
                            <li>
                                <a href="#endYear" >endYear</a>
                            </li>
                            <li>
                                <a href="#externalPractitionerSummary" >externalPractitionerSummary</a>
                            </li>
                            <li>
                                <a href="#filteredActesCCAM" >filteredActesCCAM</a>
                            </li>
                            <li>
                                <a href="#filteredActesCCAMSummary" >filteredActesCCAMSummary</a>
                            </li>
                            <li>
                                <a href="#filteredActesLABOSummary" >filteredActesLABOSummary</a>
                            </li>
                            <li>
                                <a href="#filteredActesNGAP" >filteredActesNGAP</a>
                            </li>
                            <li>
                                <a href="#filteredActesNGAPSummary" >filteredActesNGAPSummary</a>
                            </li>
                            <li>
                                <a href="#filterePractitionerActeSummary" >filterePractitionerActeSummary</a>
                            </li>
                            <li>
                                <a href="#grades" >grades</a>
                            </li>
                            <li>
                                <a href="#laboData" >laboData</a>
                            </li>
                            <li>
                                <a href="#ngapData" >ngapData</a>
                            </li>
                            <li>
                                <a href="#nonAffectesDAMSummary" >nonAffectesDAMSummary</a>
                            </li>
                            <li>
                                <a href="#praticiens" >praticiens</a>
                            </li>
                            <li>
                                <a href="#praticiensListFilter" >praticiensListFilter</a>
                            </li>
                            <li>
                                <a href="#realisations" >realisations</a>
                            </li>
                            <li>
                                <a href="#selectedStatut" >selectedStatut</a>
                            </li>
                            <li>
                                <a href="#startYear" >startYear</a>
                            </li>
                            <li>
                                <a href="#statutOptions" >statutOptions</a>
                            </li>
                            <li>
                                <a href="#totalCCAM" >totalCCAM</a>
                            </li>
                            <li>
                                <a href="#totalCCAMExternal" >totalCCAMExternal</a>
                            </li>
                            <li>
                                <a href="#totalCCAMNonAffectes" >totalCCAMNonAffectes</a>
                            </li>
                            <li>
                                <a href="#totalEtp" >totalEtp</a>
                            </li>
                            <li>
                                <a href="#totalEtpExternal" >totalEtpExternal</a>
                            </li>
                            <li>
                                <a href="#totalLABO" >totalLABO</a>
                            </li>
                            <li>
                                <a href="#totalNGAP" >totalNGAP</a>
                            </li>
                            <li>
                                <a href="#totalNGAPExternal" >totalNGAPExternal</a>
                            </li>
                            <li>
                                <a href="#totalNGAPNonAffectes" >totalNGAPNonAffectes</a>
                            </li>
                            <li>
                                <a href="#totalPartCCAM" >totalPartCCAM</a>
                            </li>
                            <li>
                                <a href="#totalPartCCAMExternal" >totalPartCCAMExternal</a>
                            </li>
                            <li>
                                <a href="#totalPartCCAMNonAffectes" >totalPartCCAMNonAffectes</a>
                            </li>
                            <li>
                                <a href="#totalPartNGAP" >totalPartNGAP</a>
                            </li>
                            <li>
                                <a href="#totalPartNGAPExternal" >totalPartNGAPExternal</a>
                            </li>
                            <li>
                                <a href="#totalPartNGAPNonAffectes" >totalPartNGAPNonAffectes</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#applyStatutFilter" >applyStatutFilter</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#filterActs" >filterActs</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#filterRealisationsByElement" >filterRealisationsByElement</a>
                            </li>
                            <li>
                                <a href="#generateActeSummary" >generateActeSummary</a>
                            </li>
                            <li>
                                <a href="#generateExternalPractitionerSummary" >generateExternalPractitionerSummary</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generateExternalPractitionerSummaryForService" >generateExternalPractitionerSummaryForService</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generateExternalPractitionerSummaryForUf" >generateExternalPractitionerSummaryForUf</a>
                            </li>
                            <li>
                                <a href="#generateExternalPractitionerSummaryNEW" >generateExternalPractitionerSummaryNEW</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#generatePractitionerActeSummary" >generatePractitionerActeSummary</a>
                            </li>
                            <li>
                                <a href="#generateUnassignedPractitionersSummary" >generateUnassignedPractitionersSummary</a>
                            </li>
                            <li>
                                <a href="#getCount" >getCount</a>
                            </li>
                            <li>
                                <a href="#getPraticiensParActe" >getPraticiensParActe</a>
                            </li>
                            <li>
                                <a href="#getTotalActeCount" >getTotalActeCount</a>
                            </li>
                            <li>
                                <a href="#getTotalForYearByTypeActe" >getTotalForYearByTypeActe</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#handleExternalPractitionerSummary" >handleExternalPractitionerSummary</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#onInput" >onInput</a>
                            </li>
                            <li>
                                <a href="#onStatutChange" >onStatutChange</a>
                            </li>
                            <li>
                                <a href="#quickDisplayOverview" >quickDisplayOverview</a>
                            </li>
                            <li>
                                <a href="#viewActeDetails" >viewActeDetails</a>
                            </li>
                            <li>
                                <a href="#viewPratitionnerDetails" >viewPratitionnerDetails</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Inputs</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#elementId" >elementId</a>
                            </li>
                            <li>
                                <a href="#elementType" >elementType</a>
                            </li>
                        </ul>
                    </td>
                </tr>




        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(acteService: ActeService, realisationService: ActeService, praticienService: PraticienService, serviceHospitalierService: ServiceHospitalierService, ufService: UFService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="140" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:140</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>acteService</td>

                                                        <td>
                                                                    <code>ActeService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>realisationService</td>

                                                        <td>
                                                                    <code>ActeService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>praticienService</td>

                                                        <td>
                                                                    <code>PraticienService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>serviceHospitalierService</td>

                                                        <td>
                                                                    <code>ServiceHospitalierService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>ufService</td>

                                                        <td>
                                                                    <code>UFService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>

    <section data-compodoc="block-inputs">
    <h3 id="inputs">Inputs</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="elementId"></a>
                        <b>elementId</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="77" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:77</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="elementType"></a>
                        <b>elementType</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>&quot;pole&quot; | &quot;department&quot; | &quot;service&quot; | &quot;uf&quot;</code>

                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="78" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:78</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>



    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="applyStatutFilter"></a>
                    <span class="name">
                        <span ><b>applyStatutFilter</b></span>
                        <a href="#applyStatutFilter"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>applyStatutFilter()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="371"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:371</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterActs"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>filterActs</b></span>
                        <a href="#filterActs"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>filterActs()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="234"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:234</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterRealisationsByElement"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>filterRealisationsByElement</b></span>
                        <a href="#filterRealisationsByElement"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>filterRealisationsByElement(realisations: RealisationActe[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="251"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:251</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>realisations</td>
                                            <td>
                                                        <code>RealisationActe[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>RealisationActe[]</code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateActeSummary"></a>
                    <span class="name">
                        <span ><b>generateActeSummary</b></span>
                        <a href="#generateActeSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>generateActeSummary(realisations: RealisationActe[], startYear: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, endYear: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, typeActe: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="268"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:268</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>realisations</td>
                                            <td>
                                                        <code>RealisationActe[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>startYear</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>endYear</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>typeActe</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="../interfaces/ActeSummary.html" target="_self" >ActeSummary[]</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateExternalPractitionerSummary"></a>
                    <span class="name">
                        <span ><b>generateExternalPractitionerSummary</b></span>
                        <a href="#generateExternalPractitionerSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>generateExternalPractitionerSummary(serviceId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="426"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:426</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>serviceId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateExternalPractitionerSummaryForService"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generateExternalPractitionerSummaryForService</b></span>
                        <a href="#generateExternalPractitionerSummaryForService"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateExternalPractitionerSummaryForService(serviceId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="794"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:794</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>serviceId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateExternalPractitionerSummaryForUf"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generateExternalPractitionerSummaryForUf</b></span>
                        <a href="#generateExternalPractitionerSummaryForUf"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generateExternalPractitionerSummaryForUf(ufId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="812"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:812</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ufId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateExternalPractitionerSummaryNEW"></a>
                    <span class="name">
                        <span ><b>generateExternalPractitionerSummaryNEW</b></span>
                        <a href="#generateExternalPractitionerSummaryNEW"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>generateExternalPractitionerSummaryNEW(elementId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="781"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:781</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>DANS LE CAS OU LE elementType EST UNE UF</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>elementId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generatePractitionerActeSummary"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>generatePractitionerActeSummary</b></span>
                        <a href="#generatePractitionerActeSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>generatePractitionerActeSummary(realisations: RealisationActe[], praticiens: Praticien[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="315"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:315</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>realisations</td>
                                            <td>
                                                        <code>RealisationActe[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>praticiens</td>
                                            <td>
                                                        <code>Praticien[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>PracticienActeSummary[]</code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateUnassignedPractitionersSummary"></a>
                    <span class="name">
                        <span ><b>generateUnassignedPractitionersSummary</b></span>
                        <a href="#generateUnassignedPractitionersSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>generateUnassignedPractitionersSummary(serviceId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="552"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:552</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>serviceId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getCount"></a>
                    <span class="name">
                        <span ><b>getCount</b></span>
                        <a href="#getCount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getCount(acteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, praticienId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, typeActe: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="691"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:691</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>acteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>praticienId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>typeActe</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getPraticiensParActe"></a>
                    <span class="name">
                        <span ><b>getPraticiensParActe</b></span>
                        <a href="#getPraticiensParActe"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getPraticiensParActe(acteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, typeActe: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="684"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:684</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>acteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>typeActe</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>    <code>Praticien[]</code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTotalActeCount"></a>
                    <span class="name">
                        <span ><b>getTotalActeCount</b></span>
                        <a href="#getTotalActeCount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTotalActeCount(acteId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="698"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:698</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>acteId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTotalForYearByTypeActe"></a>
                    <span class="name">
                        <span ><b>getTotalForYearByTypeActe</b></span>
                        <a href="#getTotalForYearByTypeActe"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTotalForYearByTypeActe(year: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>, typeActe: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="672"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:672</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>year</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>typeActe</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="handleExternalPractitionerSummary"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>handleExternalPractitionerSummary</b></span>
                        <a href="#handleExternalPractitionerSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>handleExternalPractitionerSummary(elementId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, ufsIds: string[], isService: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank">boolean</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="827"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:827</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>elementId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>ufsIds</td>
                                            <td>
                                                        <code>string[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>isService</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="154"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:154</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onInput"></a>
                    <span class="name">
                        <span ><b>onInput</b></span>
                        <a href="#onInput"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onInput(event: Event)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="704"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:704</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                        <code>Event</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onStatutChange"></a>
                    <span class="name">
                        <span ><b>onStatutChange</b></span>
                        <a href="#onStatutChange"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onStatutChange(event: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="409"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:409</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="quickDisplayOverview"></a>
                    <span class="name">
                        <span ><b>quickDisplayOverview</b></span>
                        <a href="#quickDisplayOverview"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>quickDisplayOverview()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="709"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:709</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="viewActeDetails"></a>
                    <span class="name">
                        <span ><b>viewActeDetails</b></span>
                        <a href="#viewActeDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>viewActeDetails(acte: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="774"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:774</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>acte</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="viewPratitionnerDetails"></a>
                    <span class="name">
                        <span ><b>viewPratitionnerDetails</b></span>
                        <a href="#viewPratitionnerDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>viewPratitionnerDetails(acte: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="770"
                                    class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:770</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>acte</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="actesCCAM"></a>
                    <span class="name">
                        <span ><b>actesCCAM</b></span>
                        <a href="#actesCCAM"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>ActeCCAM[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="81" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:81</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="actesLABO"></a>
                    <span class="name">
                        <span ><b>actesLABO</b></span>
                        <a href="#actesLABO"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>ActeLABO[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="82" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:82</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="actesNGAP"></a>
                    <span class="name">
                        <span ><b>actesNGAP</b></span>
                        <a href="#actesNGAP"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>ActeNGAP[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="83" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:83</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamData"></a>
                    <span class="name">
                        <span ><b>ccamData</b></span>
                        <a href="#ccamData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="116" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:116</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chartOptions"></a>
                    <span class="name">
                        <span ><b>chartOptions</b></span>
                        <a href="#chartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="119" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:119</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="endYear"></a>
                    <span class="name">
                        <span ><b>endYear</b></span>
                        <a href="#endYear"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>2024</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="111" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:111</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="externalPractitionerSummary"></a>
                    <span class="name">
                        <span ><b>externalPractitionerSummary</b></span>
                        <a href="#externalPractitionerSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>PracticienActeSummary[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="102" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:102</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filteredActesCCAM"></a>
                    <span class="name">
                        <span ><b>filteredActesCCAM</b></span>
                        <a href="#filteredActesCCAM"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>ActeCCAM[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="94" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:94</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filteredActesCCAMSummary"></a>
                    <span class="name">
                        <span ><b>filteredActesCCAMSummary</b></span>
                        <a href="#filteredActesCCAMSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../interfaces/ActeSummary.html" target="_self" >ActeSummary[]</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="97" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:97</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filteredActesLABOSummary"></a>
                    <span class="name">
                        <span ><b>filteredActesLABOSummary</b></span>
                        <a href="#filteredActesLABOSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../interfaces/ActeSummary.html" target="_self" >ActeSummary[]</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="99" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:99</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filteredActesNGAP"></a>
                    <span class="name">
                        <span ><b>filteredActesNGAP</b></span>
                        <a href="#filteredActesNGAP"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>ActeNGAP[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="95" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:95</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filteredActesNGAPSummary"></a>
                    <span class="name">
                        <span ><b>filteredActesNGAPSummary</b></span>
                        <a href="#filteredActesNGAPSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../interfaces/ActeSummary.html" target="_self" >ActeSummary[]</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="98" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:98</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterePractitionerActeSummary"></a>
                    <span class="name">
                        <span ><b>filterePractitionerActeSummary</b></span>
                        <a href="#filterePractitionerActeSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>PracticienActeSummary[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="101" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:101</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="grades"></a>
                    <span class="name">
                        <span ><b>grades</b></span>
                        <a href="#grades"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>GradeETP[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
    {
      specialite: &#x27;14,883&#x27;,
      medecine: &#x27;4,250&#x27;,
      juniorMedecine: &#x27;3,641&#x27;,
      specMedecineGen: &#x27;2,395&#x27;,
      ffiMed: &#x27;1,000&#x27;
    }
  ]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="123" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:123</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="laboData"></a>
                    <span class="name">
                        <span ><b>laboData</b></span>
                        <a href="#laboData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="118" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:118</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapData"></a>
                    <span class="name">
                        <span ><b>ngapData</b></span>
                        <a href="#ngapData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="117" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:117</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="nonAffectesDAMSummary"></a>
                    <span class="name">
                        <span ><b>nonAffectesDAMSummary</b></span>
                        <a href="#nonAffectesDAMSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="546" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:546</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="praticiens"></a>
                    <span class="name">
                        <span ><b>praticiens</b></span>
                        <a href="#praticiens"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>Praticien[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="85" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:85</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="praticiensListFilter"></a>
                    <span class="name">
                        <span ><b>praticiensListFilter</b></span>
                        <a href="#praticiensListFilter"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="113" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:113</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="realisations"></a>
                    <span class="name">
                        <span ><b>realisations</b></span>
                        <a href="#realisations"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>RealisationActe[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="84" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:84</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="selectedStatut"></a>
                    <span class="name">
                        <span ><b>selectedStatut</b></span>
                        <a href="#selectedStatut"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>string | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>null</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="140" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:140</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="startYear"></a>
                    <span class="name">
                        <span ><b>startYear</b></span>
                        <a href="#startYear"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>2023</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="110" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:110</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="statutOptions"></a>
                    <span class="name">
                        <span ><b>statutOptions</b></span>
                        <a href="#statutOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
    { label: &#x27;Tout&#x27;, value: null },
    { label: &#x27;Permanent&#x27;, value: &#x27;Permanent&#x27; },
    { label: &#x27;Temporaire&#x27;, value: &#x27;Temporaire&#x27; },
    { label: &#x27;Permanent + Temporaire&#x27;, value: &#x27;PermanentTemporaire&#x27;},
    { label: &#x27;Junior&#x27;, value: &#x27;Junior&#x27; }
  ]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="133" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:133</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalCCAM"></a>
                    <span class="name">
                        <span ><b>totalCCAM</b></span>
                        <a href="#totalCCAM"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="90" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:90</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalCCAMExternal"></a>
                    <span class="name">
                        <span ><b>totalCCAMExternal</b></span>
                        <a href="#totalCCAMExternal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="421" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:421</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalCCAMNonAffectes"></a>
                    <span class="name">
                        <span ><b>totalCCAMNonAffectes</b></span>
                        <a href="#totalCCAMNonAffectes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="547" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:547</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalEtp"></a>
                    <span class="name">
                        <span ><b>totalEtp</b></span>
                        <a href="#totalEtp"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="105" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:105</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalEtpExternal"></a>
                    <span class="name">
                        <span ><b>totalEtpExternal</b></span>
                        <a href="#totalEtpExternal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="420" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:420</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalLABO"></a>
                    <span class="name">
                        <span ><b>totalLABO</b></span>
                        <a href="#totalLABO"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="92" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:92</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalNGAP"></a>
                    <span class="name">
                        <span ><b>totalNGAP</b></span>
                        <a href="#totalNGAP"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="91" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:91</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalNGAPExternal"></a>
                    <span class="name">
                        <span ><b>totalNGAPExternal</b></span>
                        <a href="#totalNGAPExternal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="422" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:422</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalNGAPNonAffectes"></a>
                    <span class="name">
                        <span ><b>totalNGAPNonAffectes</b></span>
                        <a href="#totalNGAPNonAffectes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="548" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:548</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartCCAM"></a>
                    <span class="name">
                        <span ><b>totalPartCCAM</b></span>
                        <a href="#totalPartCCAM"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;0%&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="106" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:106</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartCCAMExternal"></a>
                    <span class="name">
                        <span ><b>totalPartCCAMExternal</b></span>
                        <a href="#totalPartCCAMExternal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="423" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:423</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartCCAMNonAffectes"></a>
                    <span class="name">
                        <span ><b>totalPartCCAMNonAffectes</b></span>
                        <a href="#totalPartCCAMNonAffectes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;0%&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="549" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:549</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartNGAP"></a>
                    <span class="name">
                        <span ><b>totalPartNGAP</b></span>
                        <a href="#totalPartNGAP"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;0%&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="107" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:107</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartNGAPExternal"></a>
                    <span class="name">
                        <span ><b>totalPartNGAPExternal</b></span>
                        <a href="#totalPartNGAPExternal"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="424" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:424</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartNGAPNonAffectes"></a>
                    <span class="name">
                        <span ><b>totalPartNGAPNonAffectes</b></span>
                        <a href="#totalPartNGAPNonAffectes"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;0%&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="550" class="link-to-prism">src/app/organization/components/information-panel/information-panel.component.ts:550</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, Input, OnInit} from &#x27;@angular/core&#x27;;
import {ActeCCAM} from &quot;../../../core/models/acte/acte-ccam.model&quot;;
import {ActeNGAP} from &quot;../../../core/models/acte/acte-ngap.model&quot;;
import {RealisationActe} from &quot;../../../core/models/acte/realisation-acte.model&quot;;
import {Praticien} from &quot;../../../core/models/acte/praticien.model&quot;;
import {PraticienService} from &quot;../../../core/services/auth/praticien.service&quot;;
import {ActeService} from &quot;../../../core/services/acte/acte.service&quot;;
import {FilterByPraticienPipe} from &quot;../../../core/pipes/filter-by-praticien.pipe&quot;;
import {DatePipe, NgForOf, NgIf, TitleCasePipe} from &quot;@angular/common&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {InputTextModule} from &quot;primeng/inputtext&quot;;
import {IconFieldModule} from &quot;primeng/iconfield&quot;;
import {InputIconModule} from &quot;primeng/inputicon&quot;;
import {BadgeModule} from &quot;primeng/badge&quot;;
import {CardModule} from &quot;primeng/card&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {BreadcrumbComponent} from &quot;../../../pages/breadcrumb/breadcrumb.component&quot;;
import {GradeETP} from &quot;../../../core/models/GradeETP&quot;;
import {Button} from &quot;primeng/button&quot;;
import {OverlayPanelModule} from &quot;primeng/overlaypanel&quot;;
import {ActeLABO} from &quot;../../../core/models/acte/acte-labo.model&quot;;
import {combineLatest} from &quot;rxjs&quot;;
import {DropdownModule} from &quot;primeng/dropdown&quot;;
import {FormsModule} from &quot;@angular/forms&quot;;
import {FloatLabelModule} from &quot;primeng/floatlabel&quot;;
import {ServiceHospitalier} from &quot;../../../core/models/organization/ServiceHospitalier.model&quot;;
import {ServiceHospitalierService} from &quot;../../../core/services/organization/ServiceHospitalierService&quot;;
import {UFService} from &quot;../../../core/services/organization/UFService&quot;;


interface ActeSummary {
  code: string;
  description: string;
  totalAnneeNMoins1: number;
  totalAnneeN: number;
}


interface PracticienActeSummary {
  nomUsuel: string;
  nomPatronymique: string;
  prenom: string;
  etp: string;
  nbActesCCAM: number;
  partPraticienCCAM: string;
  nbActesNGAP: number;
  partPraticienNGAP: string;
}

@Component({
  selector: &#x27;app-information-panel&#x27;,
  standalone: true,
  imports: [
    FilterByPraticienPipe,
    NgForOf,
    TitleCasePipe,
    NgIf,
    TableModule,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
    BadgeModule,
    CardModule,
    ChartModule,
    BreadcrumbComponent,
    Button,
    OverlayPanelModule,
    DatePipe,
    DropdownModule,
    FormsModule,
    FloatLabelModule
  ],
  templateUrl: &#x27;./information-panel.component.html&#x27;,
  styleUrl: &#x27;./information-panel.component.scss&#x27;
})
export class InformationPanelComponent implements OnInit {
  @Input() elementId!: string; // ID de l&#x27;élément à afficher (poleId, departmentId, serviceId, ou ufId)
  @Input() elementType!: &#x27;pole&#x27; | &#x27;department&#x27; | &#x27;service&#x27; | &#x27;uf&#x27;; // Type de l&#x27;élément


  actesCCAM: ActeCCAM[] &#x3D; [];
  actesLABO: ActeLABO[] &#x3D; [];
  actesNGAP: ActeNGAP[] &#x3D; [];
  realisations: RealisationActe[] &#x3D; [];
  praticiens: Praticien[] &#x3D; [];




  totalCCAM: number &#x3D; 0;
  totalNGAP: number &#x3D; 0;
  totalLABO: number &#x3D; 0;

  filteredActesCCAM:  ActeCCAM[] &#x3D; [];
  filteredActesNGAP: ActeNGAP[] &#x3D; [];

  filteredActesCCAMSummary: ActeSummary[] &#x3D; [];  //
  filteredActesNGAPSummary: ActeSummary[] &#x3D; [];  //
  filteredActesLABOSummary: ActeSummary[] &#x3D; [];  //
  //
  filterePractitionerActeSummary: PracticienActeSummary[] &#x3D; [];
  externalPractitionerSummary: PracticienActeSummary[] &#x3D; [];


  totalEtp: number &#x3D; 0;
  totalPartCCAM: string &#x3D; &#x27;0%&#x27;;
  totalPartNGAP: string &#x3D; &#x27;0%&#x27;;

  // Simulating a chosen period for now
    startYear &#x3D; 2023; // Replace with dynamically chosen start year
    endYear &#x3D; 2024;   // Replace with dynamically chosen end year

  praticiensListFilter!: any;

  // pour overview
  ccamData: any;
  ngapData: any;
  laboData: any;
  chartOptions: any;
  //

  //
  grades: GradeETP[] &#x3D; [
    {
      specialite: &#x27;14,883&#x27;,
      medecine: &#x27;4,250&#x27;,
      juniorMedecine: &#x27;3,641&#x27;,
      specMedecineGen: &#x27;2,395&#x27;,
      ffiMed: &#x27;1,000&#x27;
    }
  ];
  //********************************************************************************
  statutOptions &#x3D; [
    { label: &#x27;Tout&#x27;, value: null },
    { label: &#x27;Permanent&#x27;, value: &#x27;Permanent&#x27; },
    { label: &#x27;Temporaire&#x27;, value: &#x27;Temporaire&#x27; },
    { label: &#x27;Permanent + Temporaire&#x27;, value: &#x27;PermanentTemporaire&#x27;},
    { label: &#x27;Junior&#x27;, value: &#x27;Junior&#x27; }
  ];
  selectedStatut: string | null &#x3D; null;

  //*****************************************************************************************



  constructor(
    private acteService: ActeService,
    private realisationService: ActeService,
    private praticienService: PraticienService,
    private serviceHospitalierService: ServiceHospitalierService,
    private  ufService: UFService
  ) {}

  ngOnInit(): void {
    this.acteService.actesCCAM$.subscribe((data) &#x3D;&gt; {
      this.actesCCAM &#x3D; data;
      this.filterActs();
    });


    this.acteService.actesNGAP$.subscribe((data) &#x3D;&gt; {
      this.actesNGAP &#x3D; data;
      this.filterActs();
    });

    this.acteService.actesLABO$.subscribe((data) &#x3D;&gt; {
      this.actesLABO &#x3D; data;
      this.filterActs();
    });

    this.realisationService.realisations$.subscribe((data) &#x3D;&gt; {
      this.realisations &#x3D; this.filterRealisationsByElement(data);
      //
      // this.filteredActesCCAMSummary &#x3D; this.generateCCAMSummary(data,this.startYear,this.endYear);
      this.filteredActesCCAMSummary &#x3D; this.generateActeSummary(this.realisations,this.startYear,this.endYear,&#x27;CCAM&#x27;);
      this.filteredActesNGAPSummary &#x3D; this.generateActeSummary(this.realisations,this.startYear,this.endYear,&#x27;NGAP&#x27;);
      this.filteredActesLABOSummary &#x3D; this.generateActeSummary(this.realisations,this.startYear,this.endYear,&#x27;LABO&#x27;);
      //

      console.log(&#x27;le tableau du template&#x27;+this.filterePractitionerActeSummary)
      console.table(this.filterePractitionerActeSummary)
      // Sum the counts for total CCAM and total NGAP
      this.totalCCAM &#x3D; this.realisations
        .filter((r) &#x3D;&gt; r.typeActe.toUpperCase() &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
        .reduce((total, r) &#x3D;&gt; total + (r.count || 1), 0);

      this.totalNGAP &#x3D; this.realisations
        .filter((r) &#x3D;&gt; r.typeActe.toUpperCase() &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
        .reduce((total, r) &#x3D;&gt; total + (r.count || 1), 0);

      this.totalLABO &#x3D; this.realisations
        .filter((r) &#x3D;&gt; r.typeActe.toUpperCase() &#x3D;&#x3D;&#x3D; &#x27;LABO&#x27;)
        .reduce((total, r) &#x3D;&gt; total + (r.count || 1), 0);

      this.filterActs(); // repartition des actes [CCAM,NGAP] par pratiicien
    });

    this.quickDisplayOverview();
    //


    combineLatest([
      this.realisationService.realisations$,
      this.praticienService.praticiens$,
      this.serviceHospitalierService.servicesHospitaliers$,
    ]).subscribe(
      ([realisations, praticiens]) &#x3D;&gt; {
        this.realisations &#x3D; this.filterRealisationsByElement(realisations);
        this.praticiens &#x3D; praticiens;

        // Debug pour vérifier les données avant application du filtre
        console.log(&#x27;Praticiens chargés:&#x27;, this.praticiens);
        console.log(&#x27;Réalisations chargées:&#x27;, this.realisations);

        // Appliquer le filtre de statut
        this.applyStatutFilter();

        ///****************************************
        // Générer le tableau des praticiens externes
        this.generateExternalPractitionerSummary(this.elementId);
        ///****************************************
        this.generateUnassignedPractitionersSummary(this.elementId);


      },
      error &#x3D;&gt; {
        console.error(&quot;Erreur lors du chargement des praticiens ou des réalisations :&quot;, error);
      }
    );


  }

  private filterActs(): void {
    const realizedCCAMIds &#x3D; this.realisations
      .filter((r) &#x3D;&gt; r.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
      .map((r) &#x3D;&gt; r.acteId);
    const realizedNGAPIds &#x3D; this.realisations
      .filter((r) &#x3D;&gt; r.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
      .map((r) &#x3D;&gt; r.acteId);

    this.filteredActesCCAM &#x3D; this.actesCCAM.filter((acte) &#x3D;&gt;
      realizedCCAMIds.includes(acte.id)
    );
    this.filteredActesNGAP &#x3D; this.actesNGAP.filter((acte) &#x3D;&gt;
      realizedNGAPIds.includes(acte.id)
    );
  }

  // Filtrer les réalisations en fonction de l&#x27;élément et de son type
  private filterRealisationsByElement(realisations: RealisationActe[]): RealisationActe[] {
    switch (this.elementType) {
      case &#x27;pole&#x27;:
        return realisations.filter(r &#x3D;&gt; r.poleId &#x3D;&#x3D;&#x3D; this.elementId);
      case &#x27;department&#x27;:
        return realisations.filter(r &#x3D;&gt; r.departmentId &#x3D;&#x3D;&#x3D; this.elementId);
      case &#x27;service&#x27;:
        return realisations.filter(r &#x3D;&gt; r.serviceId &#x3D;&#x3D;&#x3D; this.elementId);
      case &#x27;uf&#x27;:
        return realisations.filter(r &#x3D;&gt; r.ufId &#x3D;&#x3D;&#x3D; this.elementId);
      default:
        return [];
    }
  }


  // Generate a summary for specified acte type data filtered by the selected years
   generateActeSummary(realisations: RealisationActe[], startYear: number, endYear: number, typeActe: string): ActeSummary[] {
    const summaryMap &#x3D; new Map&lt;string, ActeSummary&gt;();

    // Filter realizations by specified acte type and map them to acte data
    realisations
      .filter((realisation) &#x3D;&gt; realisation.typeActe &#x3D;&#x3D;&#x3D; typeActe)
      .forEach((realisation) &#x3D;&gt; {
        const year &#x3D; new Date(realisation.dateRealisation).getFullYear();
        // const acte &#x3D; (typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27; ? this.actesCCAM : typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27; ? this.actesNGAP : this.actesLABO).find((a) &#x3D;&gt; a.id &#x3D;&#x3D;&#x3D; realisation.acteId);

        const acte &#x3D; (
          typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27; ? this.actesCCAM :
            typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27; ? this.actesNGAP :
              typeActe &#x3D;&#x3D;&#x3D; &#x27;LABO&#x27; ? this.actesLABO :
                []
        ).find((a) &#x3D;&gt; a.id &#x3D;&#x3D;&#x3D; realisation.acteId);


        if (acte) {
          if (!summaryMap.has(acte.id)) {
            summaryMap.set(acte.id, {
              code: acte.code,
              description: acte.description,
              totalAnneeNMoins1: 0,
              totalAnneeN: 0,
            });
          }

          const summaryEntry &#x3D; summaryMap.get(acte.id)!;
          if (year &#x3D;&#x3D;&#x3D; startYear) {
            summaryEntry.totalAnneeNMoins1 +&#x3D; realisation.count || 1;
          } else if (year &#x3D;&#x3D;&#x3D; endYear) {
            summaryEntry.totalAnneeN +&#x3D; realisation.count || 1;
          }
        } else {
          console.error(&#x60;No matching acte found for acteId: ${realisation.acteId} in ${typeActe}&#x60;);
        }
      });

    // Log the summary map to verify the result
    console.log(&#x60;Generated Summary Map for ${typeActe}:&#x60;, Array.from(summaryMap.values()));

    return Array.from(summaryMap.values());
  }


  //**************************************************** Tableau Repartition des actes par Praticien***********************************************************************
  private generatePractitionerActeSummary(
    realisations: RealisationActe[],
    praticiens: Praticien[]
  ): PracticienActeSummary[] {
    console.log(&quot;Generating Practitioner Acte Summary...&quot;);

    // / Réinitialisation des totaux en fonction des filter par status
    this.totalPartCCAM &#x3D; &quot;0&quot;;
    this.totalPartNGAP &#x3D; &quot;0&quot;;

    // Réinitialisation des totaux
    let totalPartCCAMTemp &#x3D; 0;
    let totalPartNGAPTemp &#x3D; 0;

    //
    this.totalEtp &#x3D; praticiens.reduce((sum, praticien) &#x3D;&gt; sum + parseFloat(praticien.etp), 0);

    return praticiens.map((praticien) &#x3D;&gt; {
      const actsByPractitioner &#x3D; realisations.filter((act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id);
      let nbActesCCAM &#x3D; actsByPractitioner
        .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
        .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);
      let nbActesNGAP &#x3D; actsByPractitioner
        .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
        .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

      // Calculate part of practitioner in percentage terms
      const partPraticienCCAM &#x3D; this.totalCCAM &gt; 0 ? (nbActesCCAM / this.totalCCAM) * 100 : 0;
      const partPraticienNGAP &#x3D; this.totalNGAP &gt; 0 ? (nbActesNGAP / this.totalNGAP) * 100 : 0;


      // Mise à jour des totaux cumulés
      totalPartCCAMTemp +&#x3D; partPraticienCCAM;
      totalPartNGAPTemp +&#x3D; partPraticienNGAP;

      this.totalPartCCAM &#x3D; Math.min(100, totalPartCCAMTemp).toFixed(2) + &#x27;%&#x27;;
      this.totalPartNGAP &#x3D; Math.min(100, totalPartNGAPTemp).toFixed(2) + &#x27;%&#x27;;


      return {
        nomUsuel: praticien.nom,
        nomPatronymique: praticien.nomPatronymique,
        prenom: praticien.prenom,
        praticienDateDepart: praticien.dateDepart,
        praticienDateArrivee: praticien.dateArrivee,
        etp: praticien.etp,
        nbActesCCAM,
        partPraticienCCAM:  Math.min(100, partPraticienCCAM).toFixed(2) + &#x27;%&#x27;,
        nbActesNGAP,
        partPraticienNGAP:  Math.min(100, partPraticienNGAP).toFixed(2) + &#x27;%&#x27;
      };
    });
  }


  // Méthode pour appliquer le filtre de statut et générer le résumé
  applyStatutFilter() {
    let filteredPraticiens;


    if (this.selectedStatut &#x3D;&#x3D;&#x3D; &#x27;PermanentTemporaire&#x27;) {
      // Filtrer par Permanent et Temporaire
      filteredPraticiens &#x3D; this.praticiens.filter(
        praticien &#x3D;&gt; praticien.statut &#x3D;&#x3D;&#x3D; &#x27;Permanent&#x27; || praticien.statut &#x3D;&#x3D;&#x3D; &#x27;Temporaire&#x27;
      );
    } else if (this.selectedStatut) {
      // Filtrer par le statut sélectionné
      filteredPraticiens &#x3D; this.praticiens.filter(
        praticien &#x3D;&gt; praticien.statut &#x3D;&#x3D;&#x3D; this.selectedStatut
      );

    } else {
      // Pas de filtre, afficher tous les praticiens
      filteredPraticiens &#x3D; this.praticiens;
    }

    // Filtrer les réalisations correspondantes
    const filteredRealisations &#x3D; this.realisations.filter(realisation &#x3D;&gt;
      filteredPraticiens.some(praticien &#x3D;&gt; praticien.id &#x3D;&#x3D;&#x3D; realisation.praticienId)
    );

    this.totalCCAM &#x3D; filteredRealisations
      .filter(act &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
      .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

    this.totalNGAP &#x3D; filteredRealisations
      .filter(act &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
      .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);


    this.filterePractitionerActeSummary &#x3D; this.generatePractitionerActeSummary(filteredRealisations, filteredPraticiens);
  }

  // Méthode appelée lors du changement de statut dans le filtre
  onStatutChange(event: any) {
    console.log(&#x27;le event value &#x27;+event.value)
    this.selectedStatut &#x3D; event.value; // Mise à jour du statut sélectionné avec la valeur de l&#x27;événement
    this.applyStatutFilter(); // Applique le filtre avec le nouveau statut
  }

  //*********************************************************************************************************************************


  //****************************** Tableau (Personnes qui ont fait des actes dans ce service sans y être affectées)  *******************************************************************************************

   totalEtpExternal : any ;
   totalCCAMExternal : any;
   totalNGAPExternal : any;
   totalPartCCAMExternal : any;
   totalPartNGAPExternal :any;

  generateExternalPractitionerSummary(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) &#x3D;&gt; {
        if (!service) {
          console.error(&#x60;Service avec l&#x27;ID ${serviceId} introuvable&#x60;);
          return;
        }

        const ufsIdsInService &#x3D; service.ufs.map((uf) &#x3D;&gt; uf.id);

        // Filtrer les réalisations associées au service
        const realisationsInService &#x3D; this.realisations.filter(
          (realisation) &#x3D;&gt;
            realisation.serviceId &#x3D;&#x3D;&#x3D; serviceId &amp;&amp;
            !ufsIdsInService.includes(realisation.ufId)
        );

        console.log(&quot;Réalisations dans le service :&quot;, realisationsInService);

        // Trouver les praticiens ayant une &#x60;ufId&#x60; mais qui ne font pas partie du service
        const externalPractitionersWithRealisations &#x3D; this.praticiens.filter(
          (praticien) &#x3D;&gt;
            praticien.ufId &amp;&amp; // Inclure seulement ceux qui ont une ufId
            !ufsIdsInService.includes(praticien.ufId) &amp;&amp; // Exclure ceux qui sont dans le service
            realisationsInService.some(
              (realisation) &#x3D;&gt; realisation.praticienId &#x3D;&#x3D;&#x3D; praticien.id
            )
        );

        console.log(
          &quot;Praticiens externes ayant des réalisations :&quot;,
          externalPractitionersWithRealisations
        );

        // Calculer les totaux globaux à partir des réalisations
        const totalCCAM &#x3D; realisationsInService
          .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;CCAM&quot;)
          .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

        const totalNGAP &#x3D; realisationsInService
          .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;NGAP&quot;)
          .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

        const totalEtp &#x3D; externalPractitionersWithRealisations.reduce(
          (sum, praticien) &#x3D;&gt; sum + parseFloat(praticien.etp || &quot;0&quot;),
          0
        );

        console.log(&#x60;Total CCAM: ${totalCCAM}, Total NGAP: ${totalNGAP}, Total ETP: ${totalEtp}&#x60;);

        // Générer le tableau des praticiens externes
        this.externalPractitionerSummary &#x3D; externalPractitionersWithRealisations.map(
          (praticien) &#x3D;&gt; {
            const actsByPractitioner &#x3D; realisationsInService.filter(
              (act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id
            );

            const nbActesCCAM &#x3D; actsByPractitioner
              .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;CCAM&quot;)
              .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

            const nbActesNGAP &#x3D; actsByPractitioner
              .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;NGAP&quot;)
              .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

            // Calculer les parts pour CCAM et NGAP
            const partPraticienCCAM &#x3D;
              totalCCAM &gt; 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;

            const partPraticienNGAP &#x3D;
              totalNGAP &gt; 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

            return {
              nomUsuel: praticien.nom,
              nomPatronymique: praticien.nomPatronymique,
              prenom: praticien.prenom,
              praticienDateDepart: praticien.dateDepart,
              praticienDateArrivee: praticien.dateArrivee,
              etp: praticien.etp,
              nbActesCCAM,
              partPraticienCCAM: partPraticienCCAM.toFixed(2) + &quot;%&quot;,
              nbActesNGAP,
              partPraticienNGAP: partPraticienNGAP.toFixed(2) + &quot;%&quot;,
            };
          }
        );

        // Calculer les totaux cumulés pour CCAM et NGAP
        const recalculatedTotalCCAM &#x3D; this.externalPractitionerSummary.reduce(
          (sum, summary) &#x3D;&gt; sum + summary.nbActesCCAM,
          0
        );

        const recalculatedTotalNGAP &#x3D; this.externalPractitionerSummary.reduce(
          (sum, summary) &#x3D;&gt; sum + summary.nbActesNGAP,
          0
        );

        // Corriger les pourcentages pour totalPartCCAMExternal et totalPartNGAPExternal
        const totalPartCCAM &#x3D; totalCCAM &gt; 0 ? 100 : 0;
        const totalPartNGAP &#x3D; totalNGAP &gt; 0 ? 100 : 0;

        // Mettre à jour les totaux pour affichage
        this.totalEtpExternal &#x3D; totalEtp.toFixed(1);
        this.totalCCAMExternal &#x3D; recalculatedTotalCCAM;
        this.totalNGAPExternal &#x3D; recalculatedTotalNGAP;
        this.totalPartCCAMExternal &#x3D; totalPartCCAM.toFixed(2) + &quot;%&quot;;
        this.totalPartNGAPExternal &#x3D; totalPartNGAP.toFixed(2) + &quot;%&quot;;

        console.log(&quot;Tableau des praticiens externes :&quot;, this.externalPractitionerSummary);
      },
      (error) &#x3D;&gt; {
        console.error(&quot;Erreur lors de la récupération du service :&quot;, error);
      }
    );
  }


//*************************** Personnes qui ont fait des actes dans ce service mais non affectées par la DAM **********************************************************************************************

  nonAffectesDAMSummary: any[] &#x3D; [];
  totalCCAMNonAffectes: number &#x3D; 0;
  totalNGAPNonAffectes: number &#x3D; 0;
  totalPartCCAMNonAffectes: string &#x3D; &quot;0%&quot;;
  totalPartNGAPNonAffectes: string &#x3D; &quot;0%&quot;;

  generateUnassignedPractitionersSummary(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) &#x3D;&gt; {
        if (!service) {
          console.error(&#x60;Service avec l&#x27;ID ${serviceId} introuvable&#x60;);
          return;
        }

        const ufsIdsInService &#x3D; service.ufs.map((uf) &#x3D;&gt; uf.id);

        // Filtrer les réalisations associées au service
        const realisationsInService &#x3D; this.realisations.filter(
          (realisation) &#x3D;&gt;
            realisation.serviceId &#x3D;&#x3D;&#x3D; serviceId
        );

        console.log(&quot;Réalisations dans le service :&quot;, realisationsInService);

        // Trouver les praticiens non affectés par la DAM ayant des réalisations
        const unassignedPractitionersWithRealisations &#x3D; this.praticiens.filter(
          (praticien) &#x3D;&gt;
            !praticien.ufId &amp;&amp;
            realisationsInService.some(
              (realisation) &#x3D;&gt; realisation.praticienId &#x3D;&#x3D;&#x3D; praticien.id
            )
        );

        console.log(
          &quot;Personnes non affectées par la DAM ayant réalisé des actes :&quot;,
          unassignedPractitionersWithRealisations
        );

        // Calculer uniquement les totaux pour les praticiens non affectés
        let totalCCAM &#x3D; 0;
        let totalNGAP &#x3D; 0;

        unassignedPractitionersWithRealisations.forEach((praticien) &#x3D;&gt; {
          const actsByPractitioner &#x3D; realisationsInService.filter(
            (act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id
          );

          totalCCAM +&#x3D; actsByPractitioner
            .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;CCAM&quot;)
            .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

          totalNGAP +&#x3D; actsByPractitioner
            .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;NGAP&quot;)
            .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);
        });

        console.log(&#x60;DAM : Total CCAM pour les praticiens non affectés : ${totalCCAM}&#x60;);
        console.log(&#x60;DAM : Total NGAP pour les praticiens non affectés : ${totalNGAP}&#x60;);

        // Calculer les parts cumulées
        let cumulativePartCCAM &#x3D; 0;
        let cumulativePartNGAP &#x3D; 0;

        // Générer le tableau des praticiens non affectés
        this.nonAffectesDAMSummary &#x3D; unassignedPractitionersWithRealisations.map(
          (praticien) &#x3D;&gt; {
            const actsByPractitioner &#x3D; realisationsInService.filter(
              (act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id
            );

            const nbActesCCAM &#x3D; actsByPractitioner
              .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;CCAM&quot;)
              .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

            const nbActesNGAP &#x3D; actsByPractitioner
              .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;NGAP&quot;)
              .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

            // Calculer la part du praticien pour chaque type d&#x27;acte
            const partPraticienCCAM &#x3D; totalCCAM &gt; 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;
            const partPraticienNGAP &#x3D; totalNGAP &gt; 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

            // Ajouter aux parts cumulées
            cumulativePartCCAM +&#x3D; partPraticienCCAM;
            cumulativePartNGAP +&#x3D; partPraticienNGAP;

            return {
              nomUsuel: praticien.nom,
              nomPatronymique: praticien.nomPatronymique,
              prenom: praticien.prenom,
              specialite: praticien.specialite,
              nbActesCCAM,
              partPraticienCCAM: partPraticienCCAM.toFixed(2) + &quot;%&quot;,
              nbActesNGAP,
              partPraticienNGAP: partPraticienNGAP.toFixed(2) + &quot;%&quot;,
            };
          }
        );

        // Mettre à jour les totaux globaux
        this.totalCCAMNonAffectes &#x3D; totalCCAM;
        this.totalNGAPNonAffectes &#x3D; totalNGAP;
        this.totalPartCCAMNonAffectes &#x3D; Math.min(cumulativePartCCAM, 100).toFixed(2) + &quot;%&quot;;
        this.totalPartNGAPNonAffectes &#x3D; Math.min(cumulativePartNGAP, 100).toFixed(2) + &quot;%&quot;;

        console.log(
          &quot;Tableau des praticiens non affectés par la DAM (corrigé) :&quot;,
          this.nonAffectesDAMSummary
        );

        console.log(
          &#x60;Totaux corrigés : CCAM &#x3D; ${this.totalCCAMNonAffectes}, NGAP &#x3D; ${this.totalNGAPNonAffectes}, Part CCAM &#x3D; ${this.totalPartCCAMNonAffectes}, Part NGAP &#x3D; ${this.totalPartNGAPNonAffectes}&#x60;
        );
      },
      (error) &#x3D;&gt; {
        console.error(&quot;Erreur lors de la récupération du service :&quot;, error);
      }
    );
  }



  //*********************************************************************************************************************************


  // Calculate the total for a specific year and type of acte
  getTotalForYearByTypeActe(year: number, typeActe: string): number {
    const summary &#x3D; typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;
      ? this.filteredActesCCAMSummary
      : typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;
        ? this.filteredActesNGAPSummary
        : this.filteredActesLABOSummary;

    return summary.reduce((sum, acte) &#x3D;&gt; {
      return sum + (year &#x3D;&#x3D;&#x3D; 2023 ? acte.totalAnneeNMoins1 : acte.totalAnneeN);
    }, 0);
  }

  getPraticiensParActe(acteId: string, typeActe: string): Praticien[] {
    const realisationsFiltrees &#x3D; this.realisations.filter(
      r &#x3D;&gt; r.acteId &#x3D;&#x3D;&#x3D; acteId &amp;&amp; r.typeActe &#x3D;&#x3D;&#x3D; typeActe
    );
    return realisationsFiltrees.map(r &#x3D;&gt; this.praticiens.find(p &#x3D;&gt; p.id &#x3D;&#x3D;&#x3D; r.praticienId)!).filter(Boolean);
  }

  getCount(acteId: string, praticienId: string, typeActe: string): number {
    const realizations &#x3D; this.realisations.filter(
      (r) &#x3D;&gt; r.acteId &#x3D;&#x3D;&#x3D; acteId &amp;&amp; r.praticienId &#x3D;&#x3D;&#x3D; praticienId &amp;&amp; r.typeActe &#x3D;&#x3D;&#x3D; typeActe
    );
    return realizations.reduce((sum, r) &#x3D;&gt; sum + (r.count || 1), 0);
  }

  getTotalActeCount(acteId: string): number {
    const realizationsForActe &#x3D; this.realisations.filter(r &#x3D;&gt; r.acteId &#x3D;&#x3D;&#x3D; acteId);
    return realizationsForActe.reduce((total, realization) &#x3D;&gt; total + (realization.count || 1), 0);
  }


  onInput(event: Event): string {
    console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }
//
  quickDisplayOverview(): void {
    // Example data for each chart
    this.ccamData &#x3D; {
      labels: [&#x27;Jan&#x27;, &#x27;Feb&#x27;, &#x27;Mar&#x27;, &#x27;Apr&#x27;, &#x27;May&#x27;],
      datasets: [
        {
          label: &#x27;CCAM&#x27;,
          data: [0.8, 0.7, 0.6, 0.9, 0.8],
          fill: false,
          borderColor: &#x27;#ff5252&#x27;,
          tension: 0.4,
          borderWidth: 2,      // Augmente l&#x27;épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    this.ngapData &#x3D; {
      labels: [&#x27;Jan&#x27;, &#x27;Feb&#x27;, &#x27;Mar&#x27;, &#x27;Apr&#x27;, &#x27;May&#x27;],
      datasets: [
        {
          label: &#x27;NGAP&#x27;,
          data: [300, 320, 310, 330, 306],
          fill: false,
          borderColor: &#x27;#4caf50&#x27;,
          tension: 0.4,
          borderWidth: 2,      // Augmente l&#x27;épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    this.laboData &#x3D; {
      labels: [&#x27;Jan&#x27;, &#x27;Feb&#x27;, &#x27;Mar&#x27;, &#x27;Apr&#x27;, &#x27;May&#x27;],
      datasets: [
        {
          label: &#x27;LABO&#x27;,
          data: [1600, 1620, 1580, 1650, 1600],
          fill: false,
          borderColor: &#x27;#00bcd4&#x27;,
          tension: 0.4,
          borderWidth: 2,      // Augmente l&#x27;épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    // Options for the chart
    this.chartOptions &#x3D; {
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: { display: false },
        y: { display: false }
      }
    };
  }

  viewPratitionnerDetails(acte: any) {
    alert(&#x60;Voir les détails pour l&#x27;UF : ${acte.nomUsuel}&#x60;);
  }

  viewActeDetails(acte: any) {
    alert(&#x60;Voir les détails de l&#x27;acte : ${acte.code}&#x60;);
  }

  /****************************************************** DANS LE CAS OU LE elementType EST UNE UF **************/

  // REFACTORE  //@TODO  FAIRE EN SORTE QUE LA FONCTION SADAPTE AU ELEMENT TYPE
  generateExternalPractitionerSummaryNEW(elementId: string): void {
    switch (this.elementType) {
      case &#x27;service&#x27;:
        this.generateExternalPractitionerSummaryForService(this.elementId);
        break;
      case &#x27;uf&#x27;:
        this.generateExternalPractitionerSummaryForUf(this.elementId);
        break;
      default:
        console.error(&#x60;Type d&#x27;élément non supporté : ${this.elementType}&#x60;);
    }
  }

  private generateExternalPractitionerSummaryForService(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) &#x3D;&gt; {
        if (!service) {
          console.error(&#x60;Service avec l&#x27;ID ${serviceId} introuvable&#x60;);
          return;
        }

        const ufsIdsInService &#x3D; service.ufs.map((uf) &#x3D;&gt; uf.id);

        this.handleExternalPractitionerSummary(serviceId, ufsIdsInService, true);
      },
      (error) &#x3D;&gt; {
        console.error(&quot;Erreur lors de la récupération du service :&quot;, error);
      }
    );
  }

  private generateExternalPractitionerSummaryForUf(ufId: string): void {
    this.ufService.getUFById(ufId).subscribe(
      (uf) &#x3D;&gt; {
        if (!uf) {
          console.error(&#x60;UF avec l&#x27;ID ${ufId} introuvable&#x60;);
          return;
        }

        this.handleExternalPractitionerSummary(ufId, [ufId], false);
      },
      (error) &#x3D;&gt; {
        console.error(&quot;Erreur lors de la récupération de l&#x27;UF :&quot;, error);
      }
    );
  }
  private handleExternalPractitionerSummary(
    elementId: string,
    ufsIds: string[],
    isService: boolean
  ): void {
    // Filtrer les réalisations associées à l&#x27;élément
    const realisationsInElement &#x3D; this.realisations.filter((realisation) &#x3D;&gt;
      isService
        ? realisation.serviceId &#x3D;&#x3D;&#x3D; elementId &amp;&amp; !ufsIds.includes(realisation.ufId)
        : realisation.ufId &#x3D;&#x3D;&#x3D; elementId
    );

    console.log(
      &#x60;Réalisations dans le ${isService ? &#x27;service&#x27; : &#x27;UF&#x27;} :&#x60;,
      realisationsInElement
    );

    // Trouver les praticiens externes
    const externalPractitionersWithRealisations &#x3D; this.praticiens.filter(
      (praticien) &#x3D;&gt;
        praticien.ufId &amp;&amp;
        !ufsIds.includes(praticien.ufId) &amp;&amp;
        realisationsInElement.some(
          (realisation) &#x3D;&gt; realisation.praticienId &#x3D;&#x3D;&#x3D; praticien.id
        )
    );

    console.log(
      &#x60;Praticiens externes ayant des réalisations :&#x60;,
      externalPractitionersWithRealisations
    );

    // Calculer les totaux globaux
    const totalCCAM &#x3D; realisationsInElement
      .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27; &amp;&amp; act.count &gt; 0)
      .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

    const totalNGAP &#x3D; realisationsInElement
      .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
      .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

    const totalEtp &#x3D; externalPractitionersWithRealisations.reduce(
      (sum, praticien) &#x3D;&gt; sum + parseFloat(praticien.etp || &#x27;0&#x27;),
      0
    );

    console.log(&#x60;Total CCAM: ${totalCCAM}, Total NGAP: ${totalNGAP}, Total ETP: ${totalEtp}&#x60;);

    // Générer le tableau des praticiens externes
    this.externalPractitionerSummary &#x3D; externalPractitionersWithRealisations.map(
      (praticien) &#x3D;&gt; {
        const actsByPractitioner &#x3D; realisationsInElement.filter(
          (act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id
        );

        const nbActesCCAM &#x3D; actsByPractitioner
          .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
          .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

        const nbActesNGAP &#x3D; actsByPractitioner
          .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
          .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

        const partPraticienCCAM &#x3D;
          totalCCAM &gt; 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;

        const partPraticienNGAP &#x3D;
          totalNGAP &gt; 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

        return {
          nomUsuel: praticien.nom,
          nomPatronymique: praticien.nomPatronymique,
          prenom: praticien.prenom,
          praticienDateDepart: praticien.dateDepart,
          praticienDateArrivee: praticien.dateArrivee,
          etp: praticien.etp,
          nbActesCCAM,
          partPraticienCCAM: partPraticienCCAM.toFixed(2) + &#x27;%&#x27;,
          nbActesNGAP,
          partPraticienNGAP: partPraticienNGAP.toFixed(2) + &#x27;%&#x27;,
        };
      }
    );

    // Mettre à jour les totaux
    this.totalEtpExternal &#x3D; totalEtp.toFixed(1);
    this.totalCCAMExternal &#x3D; totalCCAM;
    this.totalNGAPExternal &#x3D; totalNGAP;
    this.totalPartCCAMExternal &#x3D; (totalCCAM &gt; 0 ? 100 : 0).toFixed(2) + &#x27;%&#x27;;
    this.totalPartNGAPExternal &#x3D; (totalNGAP &gt; 0 ? 100 : 0).toFixed(2) + &#x27;%&#x27;;

    console.log(
      &#x60;Tableau des praticiens externes pour ${
        isService ? &#x27;service&#x27; : &#x27;UF&#x27;
      } :&#x60;,
      this.externalPractitionerSummary
    );
  }



  /****************************************************** **************************************** **************/
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">

&lt;h2 class&#x3D;&quot;text-xl font-bold text-cyan-700 mb-4&quot;&gt;Informations sur le {{ elementType | titlecase }}&lt;/h2&gt;

&lt;!-- Début Overview --&gt;
&lt;div class&#x3D;&quot;flex space-x-4 p-4 bg-gray-800 rounded-lg&quot;&gt;
  &lt;!-- CCAM Card --&gt;
  &lt;p-card class&#x3D;&quot;bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative&quot;&gt;
    &lt;div class&#x3D;&quot;absolute top-2 right-2 flex items-center space-x-1&quot;&gt;
      &lt;p-button (onClick)&#x3D;&quot;ccamOverlay.toggle($event)&quot; icon&#x3D;&quot;pi pi-eye&quot;
                class&#x3D;&quot;p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg&quot;&gt;
      &lt;/p-button&gt;
      &lt;p-overlayPanel #ccamOverlay class&#x3D;&quot;custom-overlay&quot;&gt;
        &lt;div class&#x3D;&quot;p-4&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold mb-3&quot;&gt;
             Analyse Comparative des {{totalCCAM}} Actes CCAM ({{startYear}}-{{ endYear }})
          &lt;/h5&gt;
          &lt;div&gt;
            &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
              &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
              &lt;/div&gt;
              &lt;input
                type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
                (input)&#x3D;&quot;ccamTableParActeParAnnee.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
                class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
                placeholder&#x3D;&quot;  Filtrer par ccam&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;p-table
            #ccamTableParActeParAnnee
            [value]&#x3D;&quot;filteredActesCCAMSummary&quot;
            [scrollable]&#x3D;&quot;true&quot;
            scrollHeight&#x3D;&quot;400px&quot;
            [tableStyle]&#x3D;&quot;{&#x27;min-width&#x27;: &#x27;50rem&#x27;}&quot;
            [globalFilterFields]&#x3D;&quot;[&#x27;code&#x27;, &#x27;description&#x27;,&#x27;totalAnneeNMoins1&#x27;,&#x27;totalAnneeN&#x27;]&quot;
          &gt;
            &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
              &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
                &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
                  &lt;i
                    class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
                    title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
                  &gt;&lt;/i&gt;
                &lt;/th&gt;
                &lt;th pSortableColumn&#x3D;&quot;code&quot;&gt;
                  Code
                  &lt;p-sortIcon field&#x3D;&quot;code&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th pSortableColumn&#x3D;&quot;description&quot;&gt;
                  Description
                  &lt;p-sortIcon field&#x3D;&quot;description&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th  pSortableColumn&#x3D;&quot;totalAnneeNMoins1&quot; &gt;
                  Total actes {{ startYear }}
                  &lt;p-sortIcon field&#x3D;&quot;totalAnneeNMoins1&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th  pSortableColumn&#x3D;&quot;totalAnneeN&quot; &gt;
                  Total actes {{ endYear }}
                  &lt;p-sortIcon field&#x3D;&quot;totalAnneeN&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
              &lt;/tr&gt;
            &lt;/ng-template&gt;
            &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
              &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
                &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
                  &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
                    &lt;i
                      class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                      title&#x3D;&quot;Voir les détails de {{acte.code }} {{acte.description}}&quot;
                      (click)&#x3D;&quot;viewActeDetails(acte)&quot;
                    &gt;&lt;/i&gt;
                  &lt;/p&gt;
                &lt;/td&gt;
                &lt;td&gt;{{acte.code}}&lt;/td&gt;
                &lt;td&gt;{{acte.description}}&lt;/td&gt;
                &lt;td&gt; {{acte.totalAnneeNMoins1}}  &lt;/td&gt;
                &lt;td&gt; {{acte.totalAnneeN}}  &lt;/td&gt;
              &lt;/tr&gt;
            &lt;/ng-template&gt;
            &lt;!-- Ligne de total --&gt;
            &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
              &lt;tr  &gt;
                &lt;td colspan&#x3D;&quot;3&quot; class&#x3D;&quot;font-bold text-left&quot;&gt;TOTAL&lt;/td&gt;
                &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(startYear,&#x27;CCAM&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
                &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(endYear,&#x27;CCAM&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-3&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
                &lt;td&gt;&lt;/td&gt;
              &lt;/tr&gt;
            &lt;/ng-template&gt;
          &lt;/p-table&gt;
        &lt;/div&gt;
      &lt;/p-overlayPanel&gt;
    &lt;/div&gt;
    &lt;div class&#x3D;&quot;flex items-center justify-between p-4&quot;&gt;
      &lt;div class&#x3D;&quot;w-1/2&quot;&gt;
        &lt;p class&#x3D;&quot;text-sm uppercase font-semibold text-gray-400&quot;&gt;Total CCAM&lt;/p&gt;
        &lt;div class&#x3D;&quot;flex items-center space-x-2 mt-2&quot;&gt;
          &lt;p-badge [value]&#x3D;&quot;&#x27;↘ 0.6%&#x27;&quot; severity&#x3D;&quot;danger&quot; class&#x3D;&quot;mr-2&quot;&gt;&lt;/p-badge&gt;
          &lt;span class&#x3D;&quot;text-2xl font-bold&quot;&gt;{{ totalCCAM }}&lt;/span&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;w-1/2 flex justify-end&quot;&gt;
        &lt;p-chart type&#x3D;&quot;line&quot; [data]&#x3D;&quot;ccamData&quot; [options]&#x3D;&quot;chartOptions&quot; class&#x3D;&quot;w-full h-16&quot;&gt;&lt;/p-chart&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/p-card&gt;

  &lt;!-- NGAP Card --&gt;
  &lt;p-card class&#x3D;&quot;bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative&quot;&gt;
    &lt;div class&#x3D;&quot;absolute top-2 right-2 flex items-center space-x-1&quot;&gt;
      &lt;p-button (onClick)&#x3D;&quot;ngapOverlay.toggle($event)&quot; icon&#x3D;&quot;pi pi-eye&quot;
                class&#x3D;&quot;p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg&quot;&gt;
      &lt;/p-button&gt;
      &lt;p-overlayPanel #ngapOverlay class&#x3D;&quot;custom-overlay&quot;&gt;
        &lt;div class&#x3D;&quot;p-4&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold mb-3&quot;&gt;
            Analyse Comparative des {{totalNGAP}} Actes NGAP ({{startYear}}-{{ endYear }})
          &lt;/h5&gt;
          &lt;div&gt;
            &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
              &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
              &lt;/div&gt;
              &lt;input
                type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
                (input)&#x3D;&quot;ngapTableParActeParAnnee.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
                class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
                placeholder&#x3D;&quot;  Filtrer par ccam&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;p-table
            #ngapTableParActeParAnnee
            [value]&#x3D;&quot;filteredActesNGAPSummary&quot;
            [scrollable]&#x3D;&quot;true&quot;
            scrollHeight&#x3D;&quot;400px&quot;
            [tableStyle]&#x3D;&quot;{&#x27;min-width&#x27;: &#x27;50rem&#x27;}&quot;
            [globalFilterFields]&#x3D;&quot;[&#x27;code&#x27;, &#x27;description&#x27;,&#x27;totalAnneeNMoins1&#x27;,&#x27;totalAnneeN&#x27;]&quot;
          &gt;
            &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
              &lt;tr  class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
                &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
                  &lt;i
                    class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
                    title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
                  &gt;&lt;/i&gt;
                &lt;/th&gt;
                &lt;th pSortableColumn&#x3D;&quot;code&quot;&gt;
                  Code
                  &lt;p-sortIcon field&#x3D;&quot;code&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th pSortableColumn&#x3D;&quot;description&quot;&gt;
                  Description
                  &lt;p-sortIcon field&#x3D;&quot;description&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th  pSortableColumn&#x3D;&quot;totalAnneeNMoins1&quot; &gt;
                  Total actes {{ startYear }}
                  &lt;p-sortIcon field&#x3D;&quot;totalAnneeNMoins1&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th  pSortableColumn&#x3D;&quot;totalAnneeN&quot; &gt;
                  Total actes {{ endYear }}
                  &lt;p-sortIcon field&#x3D;&quot;totalAnneeN&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
              &lt;/tr&gt;
            &lt;/ng-template&gt;
            &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
              &lt;tr  class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
                &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
                  &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
                    &lt;i
                      class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                      title&#x3D;&quot;Voir les détails de {{acte.code }} {{acte.description}}&quot;
                      (click)&#x3D;&quot;viewActeDetails(acte)&quot;
                    &gt;&lt;/i&gt;
                  &lt;/p&gt;
                &lt;/td&gt;
                &lt;td&gt;{{acte.code}}&lt;/td&gt;
                &lt;td&gt;{{acte.description}}&lt;/td&gt;
                &lt;td&gt; {{acte.totalAnneeNMoins1}}  &lt;/td&gt;
                &lt;td&gt; {{acte.totalAnneeN}}  &lt;/td&gt;

              &lt;/tr&gt;
            &lt;/ng-template&gt;
            &lt;!-- Ligne de total --&gt;
            &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
              &lt;tr&gt;
                &lt;td colspan&#x3D;&quot;3&quot; class&#x3D;&quot;font-bold text-left&quot;&gt;TOTAL&lt;/td&gt;
                &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(startYear,&#x27;NGAP&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
                &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(endYear,&#x27;NGAP&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-3&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
                &lt;td&gt;&lt;/td&gt;
               &lt;/tr&gt;
            &lt;/ng-template&gt;
          &lt;/p-table&gt;
        &lt;/div&gt;
      &lt;/p-overlayPanel&gt;
    &lt;/div&gt;
    &lt;div class&#x3D;&quot;flex items-center justify-between p-4&quot;&gt;
      &lt;div class&#x3D;&quot;w-1/2&quot;&gt;
        &lt;p class&#x3D;&quot;text-sm uppercase font-semibold text-gray-400&quot;&gt;Total NGAP&lt;/p&gt;
        &lt;div class&#x3D;&quot;flex items-center space-x-2 mt-2&quot;&gt;
          &lt;p-badge [value]&#x3D;&quot;&#x27;↑ 4.2%&#x27;&quot; severity&#x3D;&quot;success&quot; class&#x3D;&quot;mr-2&quot;&gt;&lt;/p-badge&gt;
          &lt;span class&#x3D;&quot;text-2xl font-bold&quot;&gt;{{ totalNGAP }}&lt;/span&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;w-1/2 flex justify-end&quot;&gt;
        &lt;p-chart type&#x3D;&quot;line&quot; [data]&#x3D;&quot;ngapData&quot; [options]&#x3D;&quot;chartOptions&quot; class&#x3D;&quot;w-full h-16&quot;&gt;&lt;/p-chart&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/p-card&gt;

  &lt;!-- LABO Card --&gt;
  &lt;p-card class&#x3D;&quot;bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative&quot;&gt;
    &lt;div class&#x3D;&quot;absolute top-2 right-2 flex items-center space-x-1&quot;&gt;
      &lt;p-button (onClick)&#x3D;&quot;laboOverlay.toggle($event)&quot; icon&#x3D;&quot;pi pi-eye&quot;
                class&#x3D;&quot;p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg&quot;&gt;
      &lt;/p-button&gt;
      &lt;p-overlayPanel #laboOverlay class&#x3D;&quot;custom-overlay&quot;&gt;
        &lt;div class&#x3D;&quot;p-4&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold mb-3&quot;&gt;
            Analyse Comparative des {{totalLABO}} Actes CCAM ({{startYear}}-{{ endYear }})
          &lt;/h5&gt;
          &lt;div&gt;
            &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
              &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
              &lt;/div&gt;
              &lt;input
                type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
                (input)&#x3D;&quot;laboTableParActeParAnnee.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
                class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
                placeholder&#x3D;&quot;  Filtrer par acte labo&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;p-table
            #laboTableParActeParAnnee
            [value]&#x3D;&quot;filteredActesLABOSummary&quot;
            [scrollable]&#x3D;&quot;true&quot;
            scrollHeight&#x3D;&quot;400px&quot;
            [tableStyle]&#x3D;&quot;{&#x27;min-width&#x27;: &#x27;50rem&#x27;}&quot;
            [globalFilterFields]&#x3D;&quot;[&#x27;code&#x27;, &#x27;description&#x27;,&#x27;totalAnneeNMoins1&#x27;,&#x27;totalAnneeN&#x27;]&quot;
          &gt;
            &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
              &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
                &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
                  &lt;i
                    class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
                    title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
                  &gt;&lt;/i&gt;
                &lt;/th&gt;
                &lt;th pSortableColumn&#x3D;&quot;code&quot;&gt;
                  Code
                  &lt;p-sortIcon field&#x3D;&quot;code&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th pSortableColumn&#x3D;&quot;description&quot;&gt;
                  Description
                  &lt;p-sortIcon field&#x3D;&quot;description&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th  pSortableColumn&#x3D;&quot;totalAnneeNMoins1&quot; &gt;
                  Total actes {{ startYear }}
                  &lt;p-sortIcon field&#x3D;&quot;totalAnneeNMoins1&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
                &lt;th  pSortableColumn&#x3D;&quot;totalAnneeN&quot; &gt;
                  Total actes {{ endYear }}
                  &lt;p-sortIcon field&#x3D;&quot;totalAnneeN&quot;&gt;&lt;/p-sortIcon&gt;
                &lt;/th&gt;
              &lt;/tr&gt;
            &lt;/ng-template&gt;
            &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
              &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
                &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
                  &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
                    &lt;i
                      class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                      title&#x3D;&quot;Voir les détails de {{acte.code }} {{acte.description}}&quot;
                      (click)&#x3D;&quot;viewActeDetails(acte)&quot;
                    &gt;&lt;/i&gt;
                  &lt;/p&gt;
                &lt;/td&gt;
                &lt;td&gt;{{acte.code}}&lt;/td&gt;
                &lt;td&gt;{{acte.description}}&lt;/td&gt;
                &lt;td&gt; {{acte.totalAnneeNMoins1}}  &lt;/td&gt;
                &lt;td&gt; {{acte.totalAnneeN}}  &lt;/td&gt;
              &lt;/tr&gt;
            &lt;/ng-template&gt;
            &lt;!-- Ligne de total --&gt;
            &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
              &lt;tr&gt;
                &lt;td colspan&#x3D;&quot;3&quot; class&#x3D;&quot;font-bold text-right&quot;&gt;TOTAL&lt;/td&gt;
                &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(startYear,&#x27;LABO&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
                &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(endYear,&#x27;LABO&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-3&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
                &lt;td&gt;&lt;/td&gt;
              &lt;/tr&gt;
            &lt;/ng-template&gt;
          &lt;/p-table&gt;
        &lt;/div&gt;
      &lt;/p-overlayPanel&gt;
    &lt;/div&gt;
    &lt;div class&#x3D;&quot;flex items-center justify-between p-4&quot;&gt;
      &lt;div class&#x3D;&quot;w-1/2&quot;&gt;
        &lt;p class&#x3D;&quot;text-sm uppercase font-semibold text-gray-400&quot;&gt;Total LABO&lt;/p&gt;
        &lt;div class&#x3D;&quot;flex items-center space-x-2 mt-2&quot;&gt;
          &lt;p-badge [value]&#x3D;&quot;&#x27;→ 2.1%&#x27;&quot; severity&#x3D;&quot;info&quot; class&#x3D;&quot;mr-2&quot;&gt;&lt;/p-badge&gt;
          &lt;span class&#x3D;&quot;text-2xl font-bold&quot;&gt;{{ totalLABO }}&lt;/span&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;w-1/2 flex justify-end&quot;&gt;
        &lt;p-chart type&#x3D;&quot;line&quot; [data]&#x3D;&quot;laboData&quot; [options]&#x3D;&quot;chartOptions&quot; class&#x3D;&quot;w-full h-16&quot;&gt;&lt;/p-chart&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/p-card&gt;
&lt;/div&gt;



&lt;!-- TABLEAU D&#x27;ETP PAR GRADE--&gt;
&lt;p-card class&#x3D;&quot;mb-4&quot;&gt;
  &lt;h3 class&#x3D;&quot;text-lg font-semibold text-cyan-700 mb-2&quot;&gt;Nombre d&#x27;ETP par Grade&lt;/h3&gt;
  &lt;p-table [value]&#x3D;&quot;grades&quot;
           class&#x3D;&quot;p-datatable-gridlines p-datatable-striped custom-table rounded-lg overflow-hidden shadow&quot;
  &gt;
    &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
      &lt;tr&gt;
        &lt;th&gt;Grade&lt;/th&gt;
        &lt;th&gt;INTERNE DE SPECIALITE&lt;/th&gt;
        &lt;th&gt;INT MEDECINE&lt;/th&gt;
        &lt;th&gt;Docteur junior médecine&lt;/th&gt;
        &lt;th&gt;Interne Spéc. Médecine Gen&lt;/th&gt;
        &lt;th&gt;FFI MED&lt;/th&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;
    &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-row&gt;
      &lt;tr&gt;
        &lt;td class&#x3D;&quot;font-semibold&quot;&gt;Nombre d&#x27;ETP&lt;/td&gt;
        &lt;td&gt;{{ row.specialite }}&lt;/td&gt;
        &lt;td&gt;{{ row.medecine }}&lt;/td&gt;
        &lt;td&gt;{{ row.juniorMedecine }}&lt;/td&gt;
        &lt;td&gt;{{ row.specMedecineGen }}&lt;/td&gt;
        &lt;td&gt;{{ row.ffiMed }}&lt;/td&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;
  &lt;/p-table&gt;
&lt;/p-card&gt;

&lt;!--TABLEAU REPARTITION DES ACTE PAR PRATICIEN--&gt;
&lt;section  class&#x3D;&quot;mb-8 mt-8&quot;&gt;
  &lt;h2 class&#x3D;&quot;text-2xl font-bold text-cyan-700 mb-4&quot;&gt;
    Répartition des Actes par Praticien
  &lt;/h2&gt;

  &lt;div class&#x3D;&quot;p-card&quot;&gt;
    &lt;p-table
      #repartitionDesActeParPraticien
      [value]&#x3D;&quot;filterePractitionerActeSummary&quot;
      [paginator]&#x3D;&quot;true&quot;
      [rows]&#x3D;&quot;10&quot;
      [rowsPerPageOptions]&#x3D;&quot;[10, 25, 50]&quot;
      [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;nomUsuel&#x27;, &#x27;nomPatronymique&#x27;,&#x27;prenom&#x27;,&#x27;etp&#x27;]&quot;
      class&#x3D;&quot;min-w-full bg-white border rounded-lg shadow&quot;
    &gt;
      &lt;!-- Caption with Title, Total, and Search Input --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;caption&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between items-center p-3 rounded-t-lg&quot;&gt;
          &lt;div class&#x3D;&quot;text-lg font-bold text-cyan-700 flex items-center&quot;&gt;

              &lt;p-dropdown
                [options]&#x3D;&quot;statutOptions&quot;
                [(ngModel)]&#x3D;&quot;selectedStatut&quot;
                (onChange)&#x3D;&quot;onStatutChange($event)&quot;
                placeholder&#x3D;&quot;Filtrer par statut&quot;
              /&gt;


          &lt;/div&gt;
          &lt;div&gt;
            &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
              &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
          &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
            &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
          &lt;/span&gt;
              &lt;/div&gt;
              &lt;input
                type&#x3D;&quot;text&quot;
                name&#x3D;&quot;search&quot;
                (input)&#x3D;&quot;repartitionDesActeParPraticien.filterGlobal( onInput($event), &#x27;contains&#x27;)&quot;
                class&#x3D;&quot;block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm&quot;
                placeholder&#x3D;&quot;Filtrer par nom, prénom ou etp&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/ng-template&gt;


      &lt;!-- Column Headers --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr&gt;
          &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            &lt;i
              class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
              title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
            &gt;&lt;/i&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nomUsuel&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            Nom usuel
            &lt;p-sortIcon field&#x3D;&quot;nomUsuel&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nomPatronymique&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            Nom patronymique
            &lt;p-sortIcon field&#x3D;&quot;nomPatronymique&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;prenom&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            Prénom
            &lt;p-sortIcon field&#x3D;&quot;prenom&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;etp&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            (ETP praticien)/(ETP total)
            &lt;p-sortIcon field&#x3D;&quot;etp&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesCCAM&quot; class&#x3D;&quot;px-4 py-2 border text-center text-gray-700 font-semibold&quot;&gt;
            CCAM
            &lt;p-sortIcon field&#x3D;&quot;nbActesCCAM&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
            &lt;span&gt;
              NB actes
            &lt;/span&gt;
              &lt;span&gt;Part du praticien&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesNGAP&quot; class&#x3D;&quot;px-4 py-2 border text-center text-gray-700 font-semibold&quot;&gt;
            NGAP
            &lt;p-sortIcon field&#x3D;&quot;nbActesNGAP&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;NB actes&lt;/span&gt;
              &lt;span&gt;Part du praticien&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;!-- Table Body with Centered Values --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                title&#x3D;&quot;Voir les détails de {{acte.nomUsuel }} {{acte.prenom}}&quot;
                (click)&#x3D;&quot;viewPratitionnerDetails(acte)&quot;
              &gt;&lt;/i&gt;
            &lt;/p&gt;
          &lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;{{ acte.nomUsuel || &#x27;nom&#x27; }}&lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;{{ acte.nomPatronymique || &#x27;nom&#x27; }}&lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;{{ acte.prenom || &#x27;prenom&#x27; }}&lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            {{ acte.etp +&#x27;%&#x27; || &#x27;0&#x27; }}
            &lt;div *ngIf&#x3D;&quot;acte.praticienDateDepart&quot;&gt;Arrivé(e) le {{ acte.praticienDateDepart | date: &#x27;dd/MM/yyyy&#x27; }}&lt;/div&gt;
            &lt;div *ngIf&#x3D;&quot;acte.praticienDateArrivee&quot;&gt;Parti(e) le {{ acte.praticienDateArrivee | date: &#x27;dd/MM/yyyy&#x27; }}&lt;/div&gt;
          &lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: center;&quot; class&#x3D;&quot;border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8 &quot;&gt;
              &lt;span&gt;{{ acte.nbActesCCAM || &#x27;0&#x27; }}&lt;/span&gt;
              &lt;span&gt;{{ acte.partPraticienCCAM || &#x27;0&#x27; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: center;&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ acte.nbActesNGAP || &#x27;0&#x27; }}&lt;/span&gt;
              &lt;span&gt;{{ acte.partPraticienNGAP || &#x27;0&#x27; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;!-- Footer for Totals --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
        &lt;tr class&#x3D;&quot;font-semibold bg-gray-100&quot;&gt;
          &lt;td colspan&#x3D;&quot;4&quot; class&#x3D;&quot;px-4 py-2 text-left&quot;&gt;TOTAL&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-left&quot;&gt;{{ totalEtp + &#x27;%&#x27; || &#x27;0&#x27; }}&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalCCAM || &#x27;0&#x27; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartCCAM || &#x27;0&#x27; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalNGAP || &#x27;0&#x27; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartNGAP || &#x27;0&#x27; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;!-- Empty Message --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;emptymessage&quot;&gt;
        &lt;tr&gt;
          &lt;td colspan&#x3D;&quot;6&quot; class&#x3D;&quot;text-center p-4&quot;&gt;Aucun praticien &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;
  &lt;/div&gt;
&lt;/section&gt;


&lt;!-- PERSONNES NON AFFECTER AU SERVICE AYANT POSER AU MOINS UN ACTES--&gt;
&lt;section  class&#x3D;&quot;mb-8 mt-8&quot;&gt;
  &lt;h2 class&#x3D;&quot;text-2xl font-bold text-cyan-700 mb-4&quot;&gt;
    Personnes qui ont fait des actes dans ce service sans y être affectées
  &lt;/h2&gt;

  &lt;div class&#x3D;&quot;p-card&quot;&gt;
    &lt;p-table
      #repartitionDesActeParPraticienNonAffectes
      [value]&#x3D;&quot;externalPractitionerSummary&quot;
      [paginator]&#x3D;&quot;true&quot;
      [rows]&#x3D;&quot;10&quot;
      [rowsPerPageOptions]&#x3D;&quot;[10, 25, 50]&quot;
      [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;nomUsuel&#x27;, &#x27;nomPatronymique&#x27;,&#x27;prenom&#x27;,&#x27;etp&#x27;]&quot;
      class&#x3D;&quot;min-w-full bg-white border rounded-lg shadow&quot;
    &gt;
      &lt;!-- Caption with Title, Total, and Search Input --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;caption&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between items-center p-3 rounded-t-lg&quot;&gt;
          &lt;div class&#x3D;&quot;text-lg font-bold text-cyan-700 flex items-center&quot;&gt;
&lt;!--             drop down hear if needed--&gt;
          &lt;/div&gt;
          &lt;div&gt;
            &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
              &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
          &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
            &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
          &lt;/span&gt;
              &lt;/div&gt;
              &lt;input
                type&#x3D;&quot;text&quot;
                name&#x3D;&quot;search&quot;
                (input)&#x3D;&quot;repartitionDesActeParPraticienNonAffectes.filterGlobal( onInput($event), &#x27;contains&#x27;)&quot;
                class&#x3D;&quot;block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm&quot;
                placeholder&#x3D;&quot;Filtrer par nom, prénom ou etp&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/ng-template&gt;


      &lt;!-- Column Headers --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr&gt;
          &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            &lt;i
              class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
              title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
            &gt;&lt;/i&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nomUsuel&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            Nom usuel
            &lt;p-sortIcon field&#x3D;&quot;nomUsuel&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nomPatronymique&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            Nom patronymique
            &lt;p-sortIcon field&#x3D;&quot;nomPatronymique&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;prenom&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            Prénom
            &lt;p-sortIcon field&#x3D;&quot;prenom&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;etp&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            (ETP praticien)/(ETP total)
            &lt;p-sortIcon field&#x3D;&quot;etp&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesCCAM&quot; class&#x3D;&quot;px-4 py-2 border text-center text-gray-700 font-semibold&quot;&gt;
            CCAM
            &lt;p-sortIcon field&#x3D;&quot;nbActesCCAM&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
            &lt;span&gt;
              NB actes
            &lt;/span&gt;
              &lt;span&gt;Part du praticien&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesNGAP&quot; class&#x3D;&quot;px-4 py-2 border text-center text-gray-700 font-semibold&quot;&gt;
            NGAP
            &lt;p-sortIcon field&#x3D;&quot;nbActesNGAP&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;NB actes&lt;/span&gt;
              &lt;span&gt;Part du praticien&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;!-- Table Body with Centered Values --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                title&#x3D;&quot;Voir les détails de {{acte.nomUsuel }} {{acte.prenom}}&quot;
                (click)&#x3D;&quot;viewPratitionnerDetails(acte)&quot;
              &gt;&lt;/i&gt;
            &lt;/p&gt;
          &lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;{{ acte.nomUsuel || &#x27;nom&#x27; }}&lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;{{ acte.nomPatronymique || &#x27;nom&#x27; }}&lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;{{ acte.prenom || &#x27;prenom&#x27; }}&lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            {{ acte.etp +&#x27;%&#x27; || &#x27;0&#x27; }}
            &lt;div *ngIf&#x3D;&quot;acte.praticienDateDepart&quot;&gt;Arrivé(e) le {{ acte.praticienDateDepart | date: &#x27;dd/MM/yyyy&#x27; }}&lt;/div&gt;
            &lt;div *ngIf&#x3D;&quot;acte.praticienDateArrivee&quot;&gt;Parti(e) le {{ acte.praticienDateArrivee | date: &#x27;dd/MM/yyyy&#x27; }}&lt;/div&gt;

          &lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: center;&quot; class&#x3D;&quot;border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8 &quot;&gt;
              &lt;span&gt;{{ acte.nbActesCCAM || &#x27;0&#x27; }}&lt;/span&gt;
              &lt;span&gt;{{ acte.partPraticienCCAM || &#x27;0&#x27; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: center;&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ acte.nbActesNGAP || &#x27;0&#x27; }}&lt;/span&gt;
              &lt;span&gt;{{ acte.partPraticienNGAP || &#x27;0&#x27; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;!-- Footer for Totals --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
        &lt;tr class&#x3D;&quot;font-semibold bg-gray-100&quot;&gt;
          &lt;td colspan&#x3D;&quot;4&quot; class&#x3D;&quot;px-4 py-2 text-left&quot;&gt;TOTAL&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-left&quot;&gt;{{ totalEtpExternal  + &#x27;%&#x27; || &#x27;0&#x27; }}&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalCCAMExternal  || &#x27;0&#x27; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartCCAMExternal  || &#x27;0&#x27; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalNGAPExternal  || &#x27;0&#x27; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartNGAPExternal  || &#x27;0&#x27; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;!-- Empty Message --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;emptymessage&quot;&gt;
        &lt;tr&gt;
          &lt;td colspan&#x3D;&quot;6&quot; class&#x3D;&quot;text-center p-4&quot;&gt;Aucun praticien externe avec des actes&lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;
  &lt;/div&gt;
&lt;/section&gt;

&lt;!-- PERSONNES NON AFFECTEES PAR LA DAM--&gt;
&lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
  &lt;h2 class&#x3D;&quot;text-2xl font-bold text-cyan-700 mb-4&quot;&gt;
    Personnes non affectées par la DAM
  &lt;/h2&gt;

  &lt;div class&#x3D;&quot;p-card&quot;&gt;
    &lt;p-table
      #nonAffectesDAMTable
      [value]&#x3D;&quot;nonAffectesDAMSummary&quot;
      [paginator]&#x3D;&quot;true&quot;
      [rows]&#x3D;&quot;10&quot;
      [rowsPerPageOptions]&#x3D;&quot;[10, 25, 50]&quot;
      [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;nomUsuel&#x27;, &#x27;nomPatronymique&#x27;,&#x27;prenom&#x27;]&quot;
      class&#x3D;&quot;min-w-full bg-white border rounded-lg shadow&quot;
    &gt;
      &lt;!-- Caption with Title, Total, and Search Input --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;caption&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between items-center p-3 rounded-t-lg&quot;&gt;
          &lt;div class&#x3D;&quot;text-lg font-bold text-cyan-700 flex items-center&quot;&gt;
            &lt;!--             drop down hear if needed--&gt;
          &lt;/div&gt;
          &lt;div&gt;
            &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
              &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
          &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
            &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
          &lt;/span&gt;
              &lt;/div&gt;
              &lt;input
                type&#x3D;&quot;text&quot;
                name&#x3D;&quot;search&quot;
                (input)&#x3D;&quot;nonAffectesDAMTable.filterGlobal( onInput($event), &#x27;contains&#x27;)&quot;
                class&#x3D;&quot;block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm&quot;
                placeholder&#x3D;&quot;Filtrer par nom, prénom ou etp&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/ng-template&gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr&gt;
          &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            &lt;i
              class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
              title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
            &gt;&lt;/i&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nomUsuel&quot; class&#x3D;&quot;px-4 py-2 border-b text-left font-semibold&quot;&gt;
            Nom usuel
            &lt;p-sortIcon field&#x3D;&quot;nomUsuel&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nomPatronymique&quot; class&#x3D;&quot;px-4 py-2 border-b text-left font-semibold&quot;&gt;
            Nom patronymique
            &lt;p-sortIcon field&#x3D;&quot;nomPatronymique&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;prenom&quot; class&#x3D;&quot;px-4 py-2 border-b text-left font-semibold&quot;&gt;
            Prénom
            &lt;p-sortIcon field&#x3D;&quot;prenom&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesCCAM&quot; class&#x3D;&quot;px-4 py-2 border text-center font-semibold&quot;&gt;
            CCAM
            &lt;p-sortIcon field&#x3D;&quot;nbActesCCAM&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;NB actes&lt;/span&gt;
              &lt;span&gt;Part du praticien&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesNGAP&quot; class&#x3D;&quot;px-4 py-2 border text-center font-semibold&quot;&gt;
            NGAP
            &lt;p-sortIcon field&#x3D;&quot;nbActesNGAP&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;NB actes&lt;/span&gt;
              &lt;span&gt;Part du praticien&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-praticien&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                title&#x3D;&quot;Voir les détails de {{praticien.nomUsuel }} {{praticien.prenom}}&quot;
                (click)&#x3D;&quot;viewPratitionnerDetails(praticien)&quot;
              &gt;&lt;/i&gt;
            &lt;/p&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2  &quot;&gt;{{ praticien.nomUsuel }}&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2  &quot;&gt;{{ praticien.nomPatronymique }}&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2  &quot;&gt;{{ praticien.prenom }}&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 border text-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ praticien.nbActesCCAM || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ praticien.partPraticienCCAM || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 border text-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ praticien.nbActesNGAP || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ praticien.partPraticienNGAP || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
        &lt;tr class&#x3D;&quot;font-semibold bg-gray-100&quot;&gt;
          &lt;td colspan&#x3D;&quot;4&quot; class&#x3D;&quot;px-4 py-2 text-left&quot;&gt;TOTAL&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalCCAMNonAffectes || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartCCAMNonAffectes || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalNGAPNonAffectes || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartNGAPNonAffectes || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;
  &lt;/div&gt;
&lt;/section&gt;

&lt;!--DEBUT DES TABLEAU DE DISTRIBUTION AVEC CCAM | NGAP--&gt;
&lt;div class&#x3D;&quot;p-card&quot;&gt;
  &lt;p-table
    #ccamTable
    [value]&#x3D;&quot;filteredActesCCAM&quot;
    [paginator]&#x3D;&quot;true&quot;
    [rows]&#x3D;&quot;10&quot;
    [rowsPerPageOptions]&#x3D;&quot;[10, 25, 50]&quot;
    [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot;
    [globalFilterFields]&#x3D;&quot;[&#x27;code&#x27;, &#x27;description&#x27;]&quot;
    class&#x3D;&quot;min-w-full bg-white border rounded-lg shadow&quot;
  &gt;
    &lt;!-- Caption with Title, Total, and Search Input --&gt;
    &lt;ng-template pTemplate&#x3D;&quot;caption&quot;&gt;
      &lt;div class&#x3D;&quot;flex justify-between items-center  p-3 rounded-t-lg&quot;&gt;
        &lt;div class&#x3D;&quot;text-lg font-bold text-cyan-700 flex items-center&quot;&gt;
          &lt;span&gt;Répartition des Actes CCAM par Praticien&lt;/span&gt;
          &lt;span class&#x3D;&quot;ml-2 text-gray-500 text-base font-semibold bg-gray-100 px-2 py-1 rounded-md&quot;&gt;
            Nombre total : {{ totalCCAM }}
          &lt;/span&gt;
        &lt;/div&gt;
        &lt;div&gt;
          &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
            &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
            &lt;/div&gt;
            &lt;input
              type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
              (input)&#x3D;&quot;ccamTable.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
              class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
              placeholder&#x3D;&quot;  Filtrer par ccam&quot;
            /&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/ng-template&gt;

    &lt;!-- Column Headers --&gt;
    &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
      &lt;tr &gt;
        &lt;th pSortableColumn&#x3D;&quot;code&quot; style&#x3D;&quot;width: 15%&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
          Code
          &lt;p-sortIcon field&#x3D;&quot;code&quot;&gt;&lt;/p-sortIcon&gt;

        &lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;description&quot; style&#x3D;&quot;width: 45%&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
          Description
          &lt;p-sortIcon field&#x3D;&quot;description&quot;&gt;&lt;/p-sortIcon&gt;
        &lt;/th&gt;
        &lt;th  style&#x3D;&quot;width: 40%&quot; class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
          Praticiens  &lt;p-badge  [value]&#x3D;&quot;&#x27;Fréquence&#x27;&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot; /&gt;
        &lt;/th&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;

    &lt;!-- Table Body --&gt;
    &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
      &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
        &lt;td style&#x3D;&quot;padding: 0.75rem;&quot;&gt;{{ acte.code }}&lt;/td&gt;
        &lt;td style&#x3D;&quot;padding: 0.75rem;&quot;&gt;{{ acte.description }}&lt;/td&gt;
        &lt;td style&#x3D;&quot;padding: 0.75rem;&quot;&gt;
          &lt;ul class&#x3D;&quot;list-disc list-inside space-y-1&quot;&gt;
            &lt;li *ngFor&#x3D;&quot;let praticien of getPraticiensParActe(acte.id, &#x27;CCAM&#x27;)&quot;&gt;
              {{ praticien.nom }} - {{ praticien.prenom }}
              &lt;p-badge  [value]&#x3D;&quot;getCount(acte.id, praticien.id, &#x27;CCAM&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot; /&gt;
              &lt;!--              ({{ getCount(acte.id, praticien.id, &#x27;CCAM&#x27;) }} fois)--&gt;
            &lt;/li&gt;
          &lt;/ul&gt;
        &lt;/td&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;

    &lt;ng-template pTemplate&#x3D;&quot;emptymessage&quot;&gt;
      &lt;tr&gt;
        &lt;td colspan&#x3D;&quot;3&quot;&gt;Aucun acte CCAM réalisé&lt;/td&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;
  &lt;/p-table&gt;
&lt;/div&gt;

&lt;div class&#x3D;&quot;card mb-4&quot;&gt;
  &lt;p-table
    #ngapTable
    [value]&#x3D;&quot;filteredActesNGAP&quot;
    [paginator]&#x3D;&quot;true&quot;
    [rows]&#x3D;&quot;10&quot;
    [rowsPerPageOptions]&#x3D;&quot;[10, 25, 50]&quot;
    [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot;
    [globalFilterFields]&#x3D;&quot;[&#x27;code&#x27;, &#x27;description&#x27;]&quot;
    class&#x3D;&quot; p-card  min-w-full bg-white border border-gray-200 rounded-lg shadow&quot;
  &gt;
    &lt;!-- Caption with a Global Search Input --&gt;
    &lt;ng-template pTemplate&#x3D;&quot;caption&quot;&gt;
      &lt;div class&#x3D;&quot;flex justify-between items-center p-3 rounded-t-lg&quot;&gt;
        &lt;div class&#x3D;&quot;text-lg  font-bold text-cyan-700 flex items-center&quot;&gt;
          &lt;span&gt;Répartition des Actes NGAP par Praticien&lt;/span&gt;
          &lt;span class&#x3D;&quot;ml-2 text-gray-500 text-base font-semibold bg-gray-100 px-2 py-1 rounded-md&quot;&gt;
          Nombre total : {{ totalNGAP }}
         &lt;/span&gt;
        &lt;/div&gt;
        &lt;div&gt;
          &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
            &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;  &gt;&lt;/i&gt;
              &lt;/span&gt;
            &lt;/div&gt;
            &lt;input
              type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
              (input)&#x3D;&quot;ngapTable.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
              class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2   rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
              placeholder&#x3D;&quot;  Filtrer par ngap&quot;
            /&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/ng-template&gt;

    &lt;!-- Column Headers --&gt;
    &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
      &lt;tr&gt;
        &lt;th pSortableColumn&#x3D;&quot;code&quot; style&#x3D;&quot;width: 15%&quot;&gt;
          Code
          &lt;p-sortIcon field&#x3D;&quot;code&quot;&gt;&lt;/p-sortIcon&gt;

        &lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;description&quot; style&#x3D;&quot;width: 45%&quot;&gt;
          Description
          &lt;p-sortIcon field&#x3D;&quot;description&quot;&gt;&lt;/p-sortIcon&gt;
        &lt;/th&gt;
        &lt;th  style&#x3D;&quot;width: 40%&quot;&gt;
          Praticiens  &lt;p-badge  [value]&#x3D;&quot;&#x27;Fréquence&#x27;&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot; /&gt;
        &lt;/th&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;

    &lt;!-- Table Body --&gt;
    &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
      &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
        &lt;td style&#x3D;&quot;padding: 0.75rem;&quot;&gt;{{ acte.code }}&lt;/td&gt;
        &lt;td style&#x3D;&quot;padding: 0.75rem;&quot;&gt;{{ acte.description }}&lt;/td&gt;
        &lt;td style&#x3D;&quot;padding: 0.75rem;&quot;&gt;
          &lt;ul class&#x3D;&quot;list-disc list-inside space-y-1&quot;&gt;
            &lt;li *ngFor&#x3D;&quot;let praticien of getPraticiensParActe(acte.id, &#x27;NGAP&#x27;)&quot;&gt;
              {{ praticien.nom }} - {{ praticien.prenom }}
              &lt;p-badge  [value]&#x3D;&quot;getCount(acte.id, praticien.id, &#x27;NGAP&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot; /&gt;
            &lt;/li&gt;
          &lt;/ul&gt;
        &lt;/td&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;

    &lt;ng-template pTemplate&#x3D;&quot;emptymessage&quot;&gt;
      &lt;tr&gt;
        &lt;td colspan&#x3D;&quot;3&quot;&gt;Aucun acte NGPA réalisé&lt;/td&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;
  &lt;/p-table&gt;
&lt;/div&gt;







</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><h2 class="text-xl font-bold text-cyan-700 mb-4">Informations sur le {{ elementType | titlecase }}</h2><!-- Début Overview --><div class="flex space-x-4 p-4 bg-gray-800 rounded-lg">  <!-- CCAM Card -->  <p-card class="bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative">    <div class="absolute top-2 right-2 flex items-center space-x-1">      <p-button (onClick)="ccamOverlay.toggle($event)" icon="pi pi-eye"                class="p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg">      </p-button>      <p-overlayPanel #ccamOverlay class="custom-overlay">        <div class="p-4">          <h5 class="text-lg font-bold mb-3">             Analyse Comparative des {{totalCCAM}} Actes CCAM ({{startYear}}-{{ endYear }})          </h5>          <div>            <div class="relative mt-2 rounded-md shadow-sm">              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>              </div>              <input                type="text" name="price"                (input)="ccamTableParActeParAnnee.filterGlobal(onInput($event), \'contains\')"                class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "                placeholder="  Filtrer par ccam"              />            </div>          </div>          <p-table            #ccamTableParActeParAnnee            [value]="filteredActesCCAMSummary"            [scrollable]="true"            scrollHeight="400px"            [tableStyle]="{\'min-width\': \'50rem\'}"            [globalFilterFields]="[\'code\', \'description\',\'totalAnneeNMoins1\',\'totalAnneeN\']"          >            <ng-template pTemplate="header">              <tr class="border-b hover:bg-gray-50">                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">                  <i                    class="pi pi-hashtag cursor-pointer ml-2"                    title="Voir les détails de l\'acte"                  ></i>                </th>                <th pSortableColumn="code">                  Code                  <p-sortIcon field="code"></p-sortIcon>                </th>                <th pSortableColumn="description">                  Description                  <p-sortIcon field="description"></p-sortIcon>                </th>                <th  pSortableColumn="totalAnneeNMoins1" >                  Total actes {{ startYear }}                  <p-sortIcon field="totalAnneeNMoins1"></p-sortIcon>                </th>                <th  pSortableColumn="totalAnneeN" >                  Total actes {{ endYear }}                  <p-sortIcon field="totalAnneeN"></p-sortIcon>                </th>              </tr>            </ng-template>            <ng-template pTemplate="body" let-acte>              <tr class="border-b hover:bg-gray-50">                <td style="padding: 0.75rem; text-align: left;">                  <p class="text-sm font-bold text-gray-600 flex items-center">                    <i                      class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                      title="Voir les détails de {{acte.code }} {{acte.description}}"                      (click)="viewActeDetails(acte)"                    ></i>                  </p>                </td>                <td>{{acte.code}}</td>                <td>{{acte.description}}</td>                <td> {{acte.totalAnneeNMoins1}}  </td>                <td> {{acte.totalAnneeN}}  </td>              </tr>            </ng-template>            <!-- Ligne de total -->            <ng-template pTemplate="footer">              <tr  >                <td colspan="3" class="font-bold text-left">TOTAL</td>                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(startYear,\'CCAM\')" severity="info" styleClass="ml-2"></p-badge></td>                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(endYear,\'CCAM\')" severity="info" styleClass="ml-3"></p-badge></td>                <td></td>              </tr>            </ng-template>          </p-table>        </div>      </p-overlayPanel>    </div>    <div class="flex items-center justify-between p-4">      <div class="w-1/2">        <p class="text-sm uppercase font-semibold text-gray-400">Total CCAM</p>        <div class="flex items-center space-x-2 mt-2">          <p-badge [value]="\'↘ 0.6%\'" severity="danger" class="mr-2"></p-badge>          <span class="text-2xl font-bold">{{ totalCCAM }}</span>        </div>      </div>      <div class="w-1/2 flex justify-end">        <p-chart type="line" [data]="ccamData" [options]="chartOptions" class="w-full h-16"></p-chart>      </div>    </div>  </p-card>  <!-- NGAP Card -->  <p-card class="bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative">    <div class="absolute top-2 right-2 flex items-center space-x-1">      <p-button (onClick)="ngapOverlay.toggle($event)" icon="pi pi-eye"                class="p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg">      </p-button>      <p-overlayPanel #ngapOverlay class="custom-overlay">        <div class="p-4">          <h5 class="text-lg font-bold mb-3">            Analyse Comparative des {{totalNGAP}} Actes NGAP ({{startYear}}-{{ endYear }})          </h5>          <div>            <div class="relative mt-2 rounded-md shadow-sm">              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>              </div>              <input                type="text" name="price"                (input)="ngapTableParActeParAnnee.filterGlobal(onInput($event), \'contains\')"                class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "                placeholder="  Filtrer par ccam"              />            </div>          </div>          <p-table            #ngapTableParActeParAnnee            [value]="filteredActesNGAPSummary"            [scrollable]="true"            scrollHeight="400px"            [tableStyle]="{\'min-width\': \'50rem\'}"            [globalFilterFields]="[\'code\', \'description\',\'totalAnneeNMoins1\',\'totalAnneeN\']"          >            <ng-template pTemplate="header">              <tr  class="border-b hover:bg-gray-50">                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">                  <i                    class="pi pi-hashtag cursor-pointer ml-2"                    title="Voir les détails de l\'acte"                  ></i>                </th>                <th pSortableColumn="code">                  Code                  <p-sortIcon field="code"></p-sortIcon>                </th>                <th pSortableColumn="description">                  Description                  <p-sortIcon field="description"></p-sortIcon>                </th>                <th  pSortableColumn="totalAnneeNMoins1" >                  Total actes {{ startYear }}                  <p-sortIcon field="totalAnneeNMoins1"></p-sortIcon>                </th>                <th  pSortableColumn="totalAnneeN" >                  Total actes {{ endYear }}                  <p-sortIcon field="totalAnneeN"></p-sortIcon>                </th>              </tr>            </ng-template>            <ng-template pTemplate="body" let-acte>              <tr  class="border-b hover:bg-gray-50">                <td style="padding: 0.75rem; text-align: left;">                  <p class="text-sm font-bold text-gray-600 flex items-center">                    <i                      class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                      title="Voir les détails de {{acte.code }} {{acte.description}}"                      (click)="viewActeDetails(acte)"                    ></i>                  </p>                </td>                <td>{{acte.code}}</td>                <td>{{acte.description}}</td>                <td> {{acte.totalAnneeNMoins1}}  </td>                <td> {{acte.totalAnneeN}}  </td>              </tr>            </ng-template>            <!-- Ligne de total -->            <ng-template pTemplate="footer">              <tr>                <td colspan="3" class="font-bold text-left">TOTAL</td>                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(startYear,\'NGAP\')" severity="info" styleClass="ml-2"></p-badge></td>                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(endYear,\'NGAP\')" severity="info" styleClass="ml-3"></p-badge></td>                <td></td>               </tr>            </ng-template>          </p-table>        </div>      </p-overlayPanel>    </div>    <div class="flex items-center justify-between p-4">      <div class="w-1/2">        <p class="text-sm uppercase font-semibold text-gray-400">Total NGAP</p>        <div class="flex items-center space-x-2 mt-2">          <p-badge [value]="\'↑ 4.2%\'" severity="success" class="mr-2"></p-badge>          <span class="text-2xl font-bold">{{ totalNGAP }}</span>        </div>      </div>      <div class="w-1/2 flex justify-end">        <p-chart type="line" [data]="ngapData" [options]="chartOptions" class="w-full h-16"></p-chart>      </div>    </div>  </p-card>  <!-- LABO Card -->  <p-card class="bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative">    <div class="absolute top-2 right-2 flex items-center space-x-1">      <p-button (onClick)="laboOverlay.toggle($event)" icon="pi pi-eye"                class="p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg">      </p-button>      <p-overlayPanel #laboOverlay class="custom-overlay">        <div class="p-4">          <h5 class="text-lg font-bold mb-3">            Analyse Comparative des {{totalLABO}} Actes CCAM ({{startYear}}-{{ endYear }})          </h5>          <div>            <div class="relative mt-2 rounded-md shadow-sm">              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>              </div>              <input                type="text" name="price"                (input)="laboTableParActeParAnnee.filterGlobal(onInput($event), \'contains\')"                class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "                placeholder="  Filtrer par acte labo"              />            </div>          </div>          <p-table            #laboTableParActeParAnnee            [value]="filteredActesLABOSummary"            [scrollable]="true"            scrollHeight="400px"            [tableStyle]="{\'min-width\': \'50rem\'}"            [globalFilterFields]="[\'code\', \'description\',\'totalAnneeNMoins1\',\'totalAnneeN\']"          >            <ng-template pTemplate="header">              <tr class="border-b hover:bg-gray-50">                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">                  <i                    class="pi pi-hashtag cursor-pointer ml-2"                    title="Voir les détails de l\'acte"                  ></i>                </th>                <th pSortableColumn="code">                  Code                  <p-sortIcon field="code"></p-sortIcon>                </th>                <th pSortableColumn="description">                  Description                  <p-sortIcon field="description"></p-sortIcon>                </th>                <th  pSortableColumn="totalAnneeNMoins1" >                  Total actes {{ startYear }}                  <p-sortIcon field="totalAnneeNMoins1"></p-sortIcon>                </th>                <th  pSortableColumn="totalAnneeN" >                  Total actes {{ endYear }}                  <p-sortIcon field="totalAnneeN"></p-sortIcon>                </th>              </tr>            </ng-template>            <ng-template pTemplate="body" let-acte>              <tr class="border-b hover:bg-gray-50">                <td style="padding: 0.75rem; text-align: left;">                  <p class="text-sm font-bold text-gray-600 flex items-center">                    <i                      class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                      title="Voir les détails de {{acte.code }} {{acte.description}}"                      (click)="viewActeDetails(acte)"                    ></i>                  </p>                </td>                <td>{{acte.code}}</td>                <td>{{acte.description}}</td>                <td> {{acte.totalAnneeNMoins1}}  </td>                <td> {{acte.totalAnneeN}}  </td>              </tr>            </ng-template>            <!-- Ligne de total -->            <ng-template pTemplate="footer">              <tr>                <td colspan="3" class="font-bold text-right">TOTAL</td>                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(startYear,\'LABO\')" severity="info" styleClass="ml-2"></p-badge></td>                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(endYear,\'LABO\')" severity="info" styleClass="ml-3"></p-badge></td>                <td></td>              </tr>            </ng-template>          </p-table>        </div>      </p-overlayPanel>    </div>    <div class="flex items-center justify-between p-4">      <div class="w-1/2">        <p class="text-sm uppercase font-semibold text-gray-400">Total LABO</p>        <div class="flex items-center space-x-2 mt-2">          <p-badge [value]="\'→ 2.1%\'" severity="info" class="mr-2"></p-badge>          <span class="text-2xl font-bold">{{ totalLABO }}</span>        </div>      </div>      <div class="w-1/2 flex justify-end">        <p-chart type="line" [data]="laboData" [options]="chartOptions" class="w-full h-16"></p-chart>      </div>    </div>  </p-card></div><!-- TABLEAU D\'ETP PAR GRADE--><p-card class="mb-4">  <h3 class="text-lg font-semibold text-cyan-700 mb-2">Nombre d\'ETP par Grade</h3>  <p-table [value]="grades"           class="p-datatable-gridlines p-datatable-striped custom-table rounded-lg overflow-hidden shadow"  >    <ng-template pTemplate="header">      <tr>        <th>Grade</th>        <th>INTERNE DE SPECIALITE</th>        <th>INT MEDECINE</th>        <th>Docteur junior médecine</th>        <th>Interne Spéc. Médecine Gen</th>        <th>FFI MED</th>      </tr>    </ng-template>    <ng-template pTemplate="body" let-row>      <tr>        <td class="font-semibold">Nombre d\'ETP</td>        <td>{{ row.specialite }}</td>        <td>{{ row.medecine }}</td>        <td>{{ row.juniorMedecine }}</td>        <td>{{ row.specMedecineGen }}</td>        <td>{{ row.ffiMed }}</td>      </tr>    </ng-template>  </p-table></p-card><!--TABLEAU REPARTITION DES ACTE PAR PRATICIEN--><section  class="mb-8 mt-8">  <h2 class="text-2xl font-bold text-cyan-700 mb-4">    Répartition des Actes par Praticien  </h2>  <div class="p-card">    <p-table      #repartitionDesActeParPraticien      [value]="filterePractitionerActeSummary"      [paginator]="true"      [rows]="10"      [rowsPerPageOptions]="[10, 25, 50]"      [responsiveLayout]="\'scroll\'"      [globalFilterFields]="[\'nomUsuel\', \'nomPatronymique\',\'prenom\',\'etp\']"      class="min-w-full bg-white border rounded-lg shadow"    >      <!-- Caption with Title, Total, and Search Input -->      <ng-template pTemplate="caption">        <div class="flex justify-between items-center p-3 rounded-t-lg">          <div class="text-lg font-bold text-cyan-700 flex items-center">              <p-dropdown                [options]="statutOptions"                [(ngModel)]="selectedStatut"                (onChange)="onStatutChange($event)"                placeholder="Filtrer par statut"              />          </div>          <div>            <div class="relative mt-2 rounded-md shadow-sm">              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">          <span class="text-gray-500 sm:text-sm mr-3">            <i class="pi pi-search"></i>          </span>              </div>              <input                type="text"                name="search"                (input)="repartitionDesActeParPraticien.filterGlobal( onInput($event), \'contains\')"                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm"                placeholder="Filtrer par nom, prénom ou etp"              />            </div>          </div>        </div>      </ng-template>      <!-- Column Headers -->      <ng-template pTemplate="header">        <tr>          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            <i              class="pi pi-hashtag cursor-pointer ml-2"              title="Voir les détails de l\'acte"            ></i>          </th>          <th pSortableColumn="nomUsuel" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            Nom usuel            <p-sortIcon field="nomUsuel"></p-sortIcon>          </th>          <th pSortableColumn="nomPatronymique" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            Nom patronymique            <p-sortIcon field="nomPatronymique"></p-sortIcon>          </th>          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            Prénom            <p-sortIcon field="prenom"></p-sortIcon>          </th>          <th pSortableColumn="etp" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            (ETP praticien)/(ETP total)            <p-sortIcon field="etp"></p-sortIcon>          </th>          <th pSortableColumn="nbActesCCAM" class="px-4 py-2 border text-center text-gray-700 font-semibold">            CCAM            <p-sortIcon field="nbActesCCAM"></p-sortIcon>            <div class="flex justify-center space-x-8">            <span>              NB actes            </span>              <span>Part du praticien</span>            </div>          </th>          <th pSortableColumn="nbActesNGAP" class="px-4 py-2 border text-center text-gray-700 font-semibold">            NGAP            <p-sortIcon field="nbActesNGAP"></p-sortIcon>            <div class="flex justify-center space-x-8">              <span>NB actes</span>              <span>Part du praticien</span>            </div>          </th>        </tr>      </ng-template>      <!-- Table Body with Centered Values -->      <ng-template pTemplate="body" let-acte>        <tr class="border-b hover:bg-gray-50">          <td style="padding: 0.75rem; text-align: left;">            <p class="text-sm font-bold text-gray-600 flex items-center">              <i                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                title="Voir les détails de {{acte.nomUsuel }} {{acte.prenom}}"                (click)="viewPratitionnerDetails(acte)"              ></i>            </p>          </td>          <td style="padding: 0.75rem; text-align: left;">{{ acte.nomUsuel || \'nom\' }}</td>          <td style="padding: 0.75rem; text-align: left;">{{ acte.nomPatronymique || \'nom\' }}</td>          <td style="padding: 0.75rem; text-align: left;">{{ acte.prenom || \'prenom\' }}</td>          <td style="padding: 0.75rem; text-align: left;">            {{ acte.etp +\'%\' || \'0\' }}            <div *ngIf="acte.praticienDateDepart">Arrivé(e) le {{ acte.praticienDateDepart | date: \'dd/MM/yyyy\' }}</div>            <div *ngIf="acte.praticienDateArrivee">Parti(e) le {{ acte.praticienDateArrivee | date: \'dd/MM/yyyy\' }}</div>          </td>          <td style="padding: 0.75rem; text-align: center;" class="border">            <div class="flex justify-center space-x-8 ">              <span>{{ acte.nbActesCCAM || \'0\' }}</span>              <span>{{ acte.partPraticienCCAM || \'0\' }}</span>            </div>          </td>          <td style="padding: 0.75rem; text-align: center;">            <div class="flex justify-center space-x-8">              <span>{{ acte.nbActesNGAP || \'0\' }}</span>              <span>{{ acte.partPraticienNGAP || \'0\' }}</span>            </div>          </td>        </tr>      </ng-template>      <!-- Footer for Totals -->      <ng-template pTemplate="footer">        <tr class="font-semibold bg-gray-100">          <td colspan="4" class="px-4 py-2 text-left">TOTAL</td>          <td class="px-4 py-2 text-left">{{ totalEtp + \'%\' || \'0\' }}</td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalCCAM || \'0\' }}</span>              <span>{{ totalPartCCAM || \'0\' }}</span>            </div>          </td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalNGAP || \'0\' }}</span>              <span>{{ totalPartNGAP || \'0\' }}</span>            </div>          </td>        </tr>      </ng-template>      <!-- Empty Message -->      <ng-template pTemplate="emptymessage">        <tr>          <td colspan="6" class="text-center p-4">Aucun praticien </td>        </tr>      </ng-template>    </p-table>  </div></section><!-- PERSONNES NON AFFECTER AU SERVICE AYANT POSER AU MOINS UN ACTES--><section  class="mb-8 mt-8">  <h2 class="text-2xl font-bold text-cyan-700 mb-4">    Personnes qui ont fait des actes dans ce service sans y être affectées  </h2>  <div class="p-card">    <p-table      #repartitionDesActeParPraticienNonAffectes      [value]="externalPractitionerSummary"      [paginator]="true"      [rows]="10"      [rowsPerPageOptions]="[10, 25, 50]"      [responsiveLayout]="\'scroll\'"      [globalFilterFields]="[\'nomUsuel\', \'nomPatronymique\',\'prenom\',\'etp\']"      class="min-w-full bg-white border rounded-lg shadow"    >      <!-- Caption with Title, Total, and Search Input -->      <ng-template pTemplate="caption">        <div class="flex justify-between items-center p-3 rounded-t-lg">          <div class="text-lg font-bold text-cyan-700 flex items-center"><!--             drop down hear if needed-->          </div>          <div>            <div class="relative mt-2 rounded-md shadow-sm">              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">          <span class="text-gray-500 sm:text-sm mr-3">            <i class="pi pi-search"></i>          </span>              </div>              <input                type="text"                name="search"                (input)="repartitionDesActeParPraticienNonAffectes.filterGlobal( onInput($event), \'contains\')"                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm"                placeholder="Filtrer par nom, prénom ou etp"              />            </div>          </div>        </div>      </ng-template>      <!-- Column Headers -->      <ng-template pTemplate="header">        <tr>          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            <i              class="pi pi-hashtag cursor-pointer ml-2"              title="Voir les détails de l\'acte"            ></i>          </th>          <th pSortableColumn="nomUsuel" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            Nom usuel            <p-sortIcon field="nomUsuel"></p-sortIcon>          </th>          <th pSortableColumn="nomPatronymique" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            Nom patronymique            <p-sortIcon field="nomPatronymique"></p-sortIcon>          </th>          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            Prénom            <p-sortIcon field="prenom"></p-sortIcon>          </th>          <th pSortableColumn="etp" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            (ETP praticien)/(ETP total)            <p-sortIcon field="etp"></p-sortIcon>          </th>          <th pSortableColumn="nbActesCCAM" class="px-4 py-2 border text-center text-gray-700 font-semibold">            CCAM            <p-sortIcon field="nbActesCCAM"></p-sortIcon>            <div class="flex justify-center space-x-8">            <span>              NB actes            </span>              <span>Part du praticien</span>            </div>          </th>          <th pSortableColumn="nbActesNGAP" class="px-4 py-2 border text-center text-gray-700 font-semibold">            NGAP            <p-sortIcon field="nbActesNGAP"></p-sortIcon>            <div class="flex justify-center space-x-8">              <span>NB actes</span>              <span>Part du praticien</span>            </div>          </th>        </tr>      </ng-template>      <!-- Table Body with Centered Values -->      <ng-template pTemplate="body" let-acte>        <tr class="border-b hover:bg-gray-50">          <td style="padding: 0.75rem; text-align: left;">            <p class="text-sm font-bold text-gray-600 flex items-center">              <i                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                title="Voir les détails de {{acte.nomUsuel }} {{acte.prenom}}"                (click)="viewPratitionnerDetails(acte)"              ></i>            </p>          </td>          <td style="padding: 0.75rem; text-align: left;">{{ acte.nomUsuel || \'nom\' }}</td>          <td style="padding: 0.75rem; text-align: left;">{{ acte.nomPatronymique || \'nom\' }}</td>          <td style="padding: 0.75rem; text-align: left;">{{ acte.prenom || \'prenom\' }}</td>          <td style="padding: 0.75rem; text-align: left;">            {{ acte.etp +\'%\' || \'0\' }}            <div *ngIf="acte.praticienDateDepart">Arrivé(e) le {{ acte.praticienDateDepart | date: \'dd/MM/yyyy\' }}</div>            <div *ngIf="acte.praticienDateArrivee">Parti(e) le {{ acte.praticienDateArrivee | date: \'dd/MM/yyyy\' }}</div>          </td>          <td style="padding: 0.75rem; text-align: center;" class="border">            <div class="flex justify-center space-x-8 ">              <span>{{ acte.nbActesCCAM || \'0\' }}</span>              <span>{{ acte.partPraticienCCAM || \'0\' }}</span>            </div>          </td>          <td style="padding: 0.75rem; text-align: center;">            <div class="flex justify-center space-x-8">              <span>{{ acte.nbActesNGAP || \'0\' }}</span>              <span>{{ acte.partPraticienNGAP || \'0\' }}</span>            </div>          </td>        </tr>      </ng-template>      <!-- Footer for Totals -->      <ng-template pTemplate="footer">        <tr class="font-semibold bg-gray-100">          <td colspan="4" class="px-4 py-2 text-left">TOTAL</td>          <td class="px-4 py-2 text-left">{{ totalEtpExternal  + \'%\' || \'0\' }}</td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalCCAMExternal  || \'0\' }}</span>              <span>{{ totalPartCCAMExternal  || \'0\' }}</span>            </div>          </td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalNGAPExternal  || \'0\' }}</span>              <span>{{ totalPartNGAPExternal  || \'0\' }}</span>            </div>          </td>        </tr>      </ng-template>      <!-- Empty Message -->      <ng-template pTemplate="emptymessage">        <tr>          <td colspan="6" class="text-center p-4">Aucun praticien externe avec des actes</td>        </tr>      </ng-template>    </p-table>  </div></section><!-- PERSONNES NON AFFECTEES PAR LA DAM--><section class="mb-8 mt-8">  <h2 class="text-2xl font-bold text-cyan-700 mb-4">    Personnes non affectées par la DAM  </h2>  <div class="p-card">    <p-table      #nonAffectesDAMTable      [value]="nonAffectesDAMSummary"      [paginator]="true"      [rows]="10"      [rowsPerPageOptions]="[10, 25, 50]"      [responsiveLayout]="\'scroll\'"      [globalFilterFields]="[\'nomUsuel\', \'nomPatronymique\',\'prenom\']"      class="min-w-full bg-white border rounded-lg shadow"    >      <!-- Caption with Title, Total, and Search Input -->      <ng-template pTemplate="caption">        <div class="flex justify-between items-center p-3 rounded-t-lg">          <div class="text-lg font-bold text-cyan-700 flex items-center">            <!--             drop down hear if needed-->          </div>          <div>            <div class="relative mt-2 rounded-md shadow-sm">              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">          <span class="text-gray-500 sm:text-sm mr-3">            <i class="pi pi-search"></i>          </span>              </div>              <input                type="text"                name="search"                (input)="nonAffectesDAMTable.filterGlobal( onInput($event), \'contains\')"                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm"                placeholder="Filtrer par nom, prénom ou etp"              />            </div>          </div>        </div>      </ng-template>      <ng-template pTemplate="header">        <tr>          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            <i              class="pi pi-hashtag cursor-pointer ml-2"              title="Voir les détails de l\'acte"            ></i>          </th>          <th pSortableColumn="nomUsuel" class="px-4 py-2 border-b text-left font-semibold">            Nom usuel            <p-sortIcon field="nomUsuel"></p-sortIcon>          </th>          <th pSortableColumn="nomPatronymique" class="px-4 py-2 border-b text-left font-semibold">            Nom patronymique            <p-sortIcon field="nomPatronymique"></p-sortIcon>          </th>          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left font-semibold">            Prénom            <p-sortIcon field="prenom"></p-sortIcon>          </th>          <th pSortableColumn="nbActesCCAM" class="px-4 py-2 border text-center font-semibold">            CCAM            <p-sortIcon field="nbActesCCAM"></p-sortIcon>            <div class="flex justify-center space-x-8">              <span>NB actes</span>              <span>Part du praticien</span>            </div>          </th>          <th pSortableColumn="nbActesNGAP" class="px-4 py-2 border text-center font-semibold">            NGAP            <p-sortIcon field="nbActesNGAP"></p-sortIcon>            <div class="flex justify-center space-x-8">              <span>NB actes</span>              <span>Part du praticien</span>            </div>          </th>        </tr>      </ng-template>      <ng-template pTemplate="body" let-praticien>        <tr class="border-b hover:bg-gray-50">          <td style="padding: 0.75rem; text-align: left;">            <p class="text-sm font-bold text-gray-600 flex items-center">              <i                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                title="Voir les détails de {{praticien.nomUsuel }} {{praticien.prenom}}"                (click)="viewPratitionnerDetails(praticien)"              ></i>            </p>          </td>          <td class="px-4 py-2  ">{{ praticien.nomUsuel }}</td>          <td class="px-4 py-2  ">{{ praticien.nomPatronymique }}</td>          <td class="px-4 py-2  ">{{ praticien.prenom }}</td>          <td class="px-4 py-2 border text-center">            <div class="flex justify-center space-x-8">              <span>{{ praticien.nbActesCCAM || "0" }}</span>              <span>{{ praticien.partPraticienCCAM || "0%" }}</span>            </div>          </td>          <td class="px-4 py-2 border text-center">            <div class="flex justify-center space-x-8">              <span>{{ praticien.nbActesNGAP || "0" }}</span>              <span>{{ praticien.partPraticienNGAP || "0%" }}</span>            </div>          </td>        </tr>      </ng-template>      <ng-template pTemplate="footer">        <tr class="font-semibold bg-gray-100">          <td colspan="4" class="px-4 py-2 text-left">TOTAL</td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalCCAMNonAffectes || "0" }}</span>              <span>{{ totalPartCCAMNonAffectes || "0%" }}</span>            </div>          </td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalNGAPNonAffectes || "0" }}</span>              <span>{{ totalPartNGAPNonAffectes || "0%" }}</span>            </div>          </td>        </tr>      </ng-template>    </p-table>  </div></section><!--DEBUT DES TABLEAU DE DISTRIBUTION AVEC CCAM | NGAP--><div class="p-card">  <p-table    #ccamTable    [value]="filteredActesCCAM"    [paginator]="true"    [rows]="10"    [rowsPerPageOptions]="[10, 25, 50]"    [responsiveLayout]="\'scroll\'"    [globalFilterFields]="[\'code\', \'description\']"    class="min-w-full bg-white border rounded-lg shadow"  >    <!-- Caption with Title, Total, and Search Input -->    <ng-template pTemplate="caption">      <div class="flex justify-between items-center  p-3 rounded-t-lg">        <div class="text-lg font-bold text-cyan-700 flex items-center">          <span>Répartition des Actes CCAM par Praticien</span>          <span class="ml-2 text-gray-500 text-base font-semibold bg-gray-100 px-2 py-1 rounded-md">            Nombre total : {{ totalCCAM }}          </span>        </div>        <div>          <div class="relative mt-2 rounded-md shadow-sm">            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>            </div>            <input              type="text" name="price"              (input)="ccamTable.filterGlobal(onInput($event), \'contains\')"              class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "              placeholder="  Filtrer par ccam"            />          </div>        </div>      </div>    </ng-template>    <!-- Column Headers -->    <ng-template pTemplate="header">      <tr >        <th pSortableColumn="code" style="width: 15%" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">          Code          <p-sortIcon field="code"></p-sortIcon>        </th>        <th pSortableColumn="description" style="width: 45%" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">          Description          <p-sortIcon field="description"></p-sortIcon>        </th>        <th  style="width: 40%" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">          Praticiens  <p-badge  [value]="\'Fréquence\'" severity="info" styleClass="ml-2" />        </th>      </tr>    </ng-template>    <!-- Table Body -->    <ng-template pTemplate="body" let-acte>      <tr class="border-b hover:bg-gray-50">        <td style="padding: 0.75rem;">{{ acte.code }}</td>        <td style="padding: 0.75rem;">{{ acte.description }}</td>        <td style="padding: 0.75rem;">          <ul class="list-disc list-inside space-y-1">            <li *ngFor="let praticien of getPraticiensParActe(acte.id, \'CCAM\')">              {{ praticien.nom }} - {{ praticien.prenom }}              <p-badge  [value]="getCount(acte.id, praticien.id, \'CCAM\')" severity="info" styleClass="ml-2" />              <!--              ({{ getCount(acte.id, praticien.id, \'CCAM\') }} fois)-->            </li>          </ul>        </td>      </tr>    </ng-template>    <ng-template pTemplate="emptymessage">      <tr>        <td colspan="3">Aucun acte CCAM réalisé</td>      </tr>    </ng-template>  </p-table></div><div class="card mb-4">  <p-table    #ngapTable    [value]="filteredActesNGAP"    [paginator]="true"    [rows]="10"    [rowsPerPageOptions]="[10, 25, 50]"    [responsiveLayout]="\'scroll\'"    [globalFilterFields]="[\'code\', \'description\']"    class=" p-card  min-w-full bg-white border border-gray-200 rounded-lg shadow"  >    <!-- Caption with a Global Search Input -->    <ng-template pTemplate="caption">      <div class="flex justify-between items-center p-3 rounded-t-lg">        <div class="text-lg  font-bold text-cyan-700 flex items-center">          <span>Répartition des Actes NGAP par Praticien</span>          <span class="ml-2 text-gray-500 text-base font-semibold bg-gray-100 px-2 py-1 rounded-md">          Nombre total : {{ totalNGAP }}         </span>        </div>        <div>          <div class="relative mt-2 rounded-md shadow-sm">            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"  ></i>              </span>            </div>            <input              type="text" name="price"              (input)="ngapTable.filterGlobal(onInput($event), \'contains\')"              class="block w-full h-full pl-8 pr-5 py-2   rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "              placeholder="  Filtrer par ngap"            />          </div>        </div>      </div>    </ng-template>    <!-- Column Headers -->    <ng-template pTemplate="header">      <tr>        <th pSortableColumn="code" style="width: 15%">          Code          <p-sortIcon field="code"></p-sortIcon>        </th>        <th pSortableColumn="description" style="width: 45%">          Description          <p-sortIcon field="description"></p-sortIcon>        </th>        <th  style="width: 40%">          Praticiens  <p-badge  [value]="\'Fréquence\'" severity="info" styleClass="ml-2" />        </th>      </tr>    </ng-template>    <!-- Table Body -->    <ng-template pTemplate="body" let-acte>      <tr class="border-b hover:bg-gray-50">        <td style="padding: 0.75rem;">{{ acte.code }}</td>        <td style="padding: 0.75rem;">{{ acte.description }}</td>        <td style="padding: 0.75rem;">          <ul class="list-disc list-inside space-y-1">            <li *ngFor="let praticien of getPraticiensParActe(acte.id, \'NGAP\')">              {{ praticien.nom }} - {{ praticien.prenom }}              <p-badge  [value]="getCount(acte.id, praticien.id, \'NGAP\')" severity="info" styleClass="ml-2" />            </li>          </ul>        </td>      </tr>    </ng-template>    <ng-template pTemplate="emptymessage">      <tr>        <td colspan="3">Aucun acte NGPA réalisé</td>      </tr>    </ng-template>  </p-table></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'InformationPanelComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'InformationPanelComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
