<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
<section>
  <div class="p-4 bg-gray-50 rounded-lg shadow mb-4">
    <div class="flex justify-between items-center">
      <!-- Titre principal avec icône -->
      <div class="flex items-center space-x-3">
        <i class="pi pi-briefcase text-cyan-700 text-2xl"></i>
        <h1 class="text-xl font-bold text-gray-700">Liste des Services Hospitaliers</h1>
      </div>

      <!-- Statistiques globales -->
      <div class="flex items-center space-x-8">
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalServices }}</p>
          <p class="text-sm text-gray-500">Services Hospitaliers</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalPractitioners }}</p>
          <p class="text-sm text-gray-500">Praticiens</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalUFs }}</p>
          <p class="text-sm text-gray-500">Unités Fonctionnelles</p>
        </div>
      </div>

      <!-- Icône SVG décorative -->

      <div class="hidden lg:block text-center">
        <!-- Icône SVG -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-24 h-24 text-cyan-700 mx-auto"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"
          />
        </svg>

        <!-- Texte sous le SVG -->
        <p class="mt-2 text-sm font-medium text-gray-500">CHRU Nancy</p>
      </div>


    </div>
  </div>
</section>


<div class="p-card">
  <p-table
    #serviceTable
    [value]="servicesHospitaliers"
    [paginator]="true"
    [rows]="10"
    [rowsPerPageOptions]="[10, 25, 50]"
    [responsiveLayout]="'scroll'"
    [globalFilterFields]="['name', 'code', 'poles']"
    [exportHeader]="'customExportHeader'"
    class="min-w-full bg-white border rounded-lg shadow"
  >
    <!-- Caption with Title and Search Input -->
    <ng-template pTemplate="caption">
      <div class="relative">
        <!-- Icône de recherche -->
        <span class="absolute inset-y-0 left-0 flex items-center pl-3">
          <i class="pi pi-search text-gray-400"></i>
        </span>
        <!-- Input de recherche -->
        <input
          type="text"
          (input)="serviceTable.filterGlobal(onInput($event), 'contains')"
          class="block w-full pl-10 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
             ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600
             sm:text-sm"
          placeholder="Filtrer par nom, code ou pôle"
        />
      </div>
    </ng-template>




    <!-- Column Headers -->
    <ng-template pTemplate="header">
      <tr>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          <i
            class="pi pi-hashtag cursor-pointer ml-2"
            title="Voir les détails du service"
          ></i>
        </th>
        <th pSortableColumn="name" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Nom du Service
          <p-sortIcon field="name"></p-sortIcon>
        </th>
        <th pSortableColumn="code" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Code
          <p-sortIcon field="code"></p-sortIcon>
        </th>
        <th pSortableColumn="poles" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Pôles
          <p-sortIcon field="poles"></p-sortIcon>
        </th>
        <th pSortableColumn="ufs.length" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Nombre d'UF
          <p-sortIcon field="ufs.length"></p-sortIcon>
        </th>
        <th pSortableColumn="practitioners" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Nombre de Praticiens
          <p-sortIcon field="practitioners"></p-sortIcon>
        </th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template pTemplate="body" let-service>
      <tr class="border-b hover:bg-gray-50">
        <td style="padding: 0.75rem; text-align: left;">
          <p class="text-sm font-bold text-gray-600 flex items-center">
            <i
              class="pi pi-eye text-lg text-cyan-700 cursor-pointer ml-2"
              title="Voir les détails du sevice {{service.name }} {{service.code}}"
              (click)="viewServiceDetails(service.id)"
            ></i>
          </p>
        </td>
        <td class="px-4 py-2"  (click)="viewServiceDetails(service.id)">
          <a  class="text-lg text-cyan-700 hover:underline cursor-pointer font-semibold"
              (click)="viewServiceDetails(service.id)">
            {{ service.name }}
          </a>
        </td>
        <td class="px-4 py-2">{{ service.code }}</td>
        <td class="px-4 py-2">
          <ul class="list-disc list-inside space-y-1">
            <li *ngFor="let pole of service.poles">{{ pole }}</li>
          </ul>
        </td>
        <td class="px-4 py-2">{{ service.ufs.length }}</td>
        <td class="px-4 py-2">{{ service.practitioners }}</td>
      </tr>
    </ng-template>

    <!-- Empty Message -->
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="5" class="text-center py-4">Aucun service hospitalier trouvé</td>
      </tr>
    </ng-template>
  </p-table>
</div>
