import { Component } from '@angular/core';
import {NgIf} from "@angular/common";

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [
    NgIf
  ],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.scss'
})
export class FooterComponent {

  showRgpdModal = false;
  isFooterExpanded = true; // Footer ouvert par défaut

  openRgpdModal() {
    this.showRgpdModal = true;
  }

  closeRgpdModal() {
    this.showRgpdModal = false;
  }

  toggleFooter() {
    this.isFooterExpanded = !this.isFooterExpanded;
  }

}
