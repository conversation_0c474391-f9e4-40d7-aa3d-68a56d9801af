<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  DashboardComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/graphical/components/dashboard/dashboard.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-dashboard</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>ChartModule</code>
                            <code>StyleClassModule</code>
                            <code>NgForOf</code>
                            <code>DialogModule</code>
                            <code>ButtonDirective</code>
                            <code>NgIf</code>
                                <code><a href="../components/BreadcrumbComponent.html" target="_self" >BreadcrumbComponent</a></code>
                            <code>TableModule</code>
                                <code><a href="../components/AnalyticsDashboardComponent.html" target="_self" >AnalyticsDashboardComponent</a></code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./dashboard.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./dashboard.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#admissionTableData" >admissionTableData</a>
                            </li>
                            <li>
                                <a href="#breadcrumbItems" >breadcrumbItems</a>
                            </li>
                            <li>
                                <a href="#chartOptions" >chartOptions</a>
                            </li>
                            <li>
                                <a href="#dialogStates" >dialogStates</a>
                            </li>
                            <li>
                                <a href="#evolutionTableData" >evolutionTableData</a>
                            </li>
                            <li>
                                <a href="#horizontalBarChartData" >horizontalBarChartData</a>
                            </li>
                            <li>
                                <a href="#lineChartData" >lineChartData</a>
                            </li>
                            <li>
                                <a href="#performanceTableData" >performanceTableData</a>
                            </li>
                            <li>
                                <a href="#pieChartData" >pieChartData</a>
                            </li>
                            <li>
                                <a href="#polarChartData" >polarChartData</a>
                            </li>
                            <li>
                                <a href="#polarTableData" >polarTableData</a>
                            </li>
                            <li>
                                <a href="#servicesTableData" >servicesTableData</a>
                            </li>
                            <li>
                                <a href="#showInfoMessage" >showInfoMessage</a>
                            </li>
                            <li>
                                <a href="#verticalBarChartData" >verticalBarChartData</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#hideInfoMessage" >hideInfoMessage</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#initHorizontalBarChart" >initHorizontalBarChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#initLineChart" >initLineChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#initPieChart" >initPieChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#initPolarChart" >initPolarChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#initVerticalBarChart" >initVerticalBarChart</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#showDialog" >showDialog</a>
                            </li>
                            <li>
                                <a href="#showInfoMessageAgain" >showInfoMessageAgain</a>
                            </li>
                            <li>
                                <a href="#viewPraticienDetails" >viewPraticienDetails</a>
                            </li>
                            <li>
                                <a href="#viewServiceDetails" >viewServiceDetails</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(dashboardService: DashboardService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="70" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:70</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>dashboardService</td>

                                                        <td>
                                                                    <code>DashboardService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="hideInfoMessage"></a>
                    <span class="name">
                        <span ><b>hideInfoMessage</b></span>
                        <a href="#hideInfoMessage"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>hideInfoMessage()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="104"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:104</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initHorizontalBarChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>initHorizontalBarChart</b></span>
                        <a href="#initHorizontalBarChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initHorizontalBarChart(data: any[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="165"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:165</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initLineChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>initLineChart</b></span>
                        <a href="#initLineChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initLineChart(data: any[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="139"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:139</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initPieChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>initPieChart</b></span>
                        <a href="#initPieChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initPieChart(data: any[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="197"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:197</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initPolarChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>initPolarChart</b></span>
                        <a href="#initPolarChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initPolarChart(data: any[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="120"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:120</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initVerticalBarChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>initVerticalBarChart</b></span>
                        <a href="#initVerticalBarChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initVerticalBarChart(data: any[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="181"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:181</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>data</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="74"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:74</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="showDialog"></a>
                    <span class="name">
                        <span ><b>showDialog</b></span>
                        <a href="#showDialog"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>showDialog(section: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="112"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:112</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>section</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="showInfoMessageAgain"></a>
                    <span class="name">
                        <span ><b>showInfoMessageAgain</b></span>
                        <a href="#showInfoMessageAgain"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>showInfoMessageAgain()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="108"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:108</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="viewPraticienDetails"></a>
                    <span class="name">
                        <span ><b>viewPraticienDetails</b></span>
                        <a href="#viewPraticienDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>viewPraticienDetails(praticien: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="215"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:215</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>praticien</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="viewServiceDetails"></a>
                    <span class="name">
                        <span ><b>viewServiceDetails</b></span>
                        <a href="#viewServiceDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>viewServiceDetails(service: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="211"
                                    class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:211</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>service</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="admissionTableData"></a>
                    <span class="name">
                        <span ><b>admissionTableData</b></span>
                        <a href="#admissionTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="51" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:51</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="breadcrumbItems"></a>
                    <span class="name">
                        <span ><b>breadcrumbItems</b></span>
                        <a href="#breadcrumbItems"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>BreadcrumbItem[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
    { label: &#x27;Tableau de Bord&#x27;, url: &#x27;/graphic/dashboard&#x27; },
    { label: &#x27;Pondération des 6 derniers mois de l\&#x27;année en cours.&#x27; }
  ]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:67</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chartOptions"></a>
                    <span class="name">
                        <span ><b>chartOptions</b></span>
                        <a href="#chartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="53" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:53</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="dialogStates"></a>
                    <span class="name">
                        <span ><b>dialogStates</b></span>
                        <a href="#dialogStates"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>literal type</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    repartition: false, // Section 1
    evolution: false, // Section 2
    services: false, // Section 3
    praticien: false, // Section 4
    admission: false, // Section 5
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="56" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:56</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="evolutionTableData"></a>
                    <span class="name">
                        <span ><b>evolutionTableData</b></span>
                        <a href="#evolutionTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:39</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="horizontalBarChartData"></a>
                    <span class="name">
                        <span ><b>horizontalBarChartData</b></span>
                        <a href="#horizontalBarChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="42" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:42</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="lineChartData"></a>
                    <span class="name">
                        <span ><b>lineChartData</b></span>
                        <a href="#lineChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:38</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="performanceTableData"></a>
                    <span class="name">
                        <span ><b>performanceTableData</b></span>
                        <a href="#performanceTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="47" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:47</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="pieChartData"></a>
                    <span class="name">
                        <span ><b>pieChartData</b></span>
                        <a href="#pieChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="50" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:50</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="polarChartData"></a>
                    <span class="name">
                        <span ><b>polarChartData</b></span>
                        <a href="#polarChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="34" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:34</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="polarTableData"></a>
                    <span class="name">
                        <span ><b>polarTableData</b></span>
                        <a href="#polarTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:35</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="servicesTableData"></a>
                    <span class="name">
                        <span ><b>servicesTableData</b></span>
                        <a href="#servicesTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="43" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:43</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="showInfoMessage"></a>
                    <span class="name">
                        <span ><b>showInfoMessage</b></span>
                        <a href="#showInfoMessage"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/boolean" target="_blank" >boolean</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>true</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="64" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:64</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="verticalBarChartData"></a>
                    <span class="name">
                        <span ><b>verticalBarChartData</b></span>
                        <a href="#verticalBarChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="46" class="link-to-prism">src/app/graphical/components/dashboard/dashboard.component.ts:46</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import {DashboardService} from &quot;../../../core/services/overview/dashboard.service&quot;;
import {DashboardData} from &quot;../../../core/models/overview/dashboard.model&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {StyleClassModule} from &quot;primeng/styleclass&quot;;
import {NgForOf, NgIf} from &quot;@angular/common&quot;;
import {DialogModule} from &quot;primeng/dialog&quot;;
import {ButtonDirective} from &quot;primeng/button&quot;;
import {BreadcrumbComponent} from &quot;../../../pages/breadcrumb/breadcrumb.component&quot;;
import {BreadcrumbItem} from &quot;../../../core/models/breadcrumbItem&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {AnalyticsDashboardComponent} from &quot;../analytics-dashboard/analytics-dashboard.component&quot;;

@Component({
  selector: &#x27;app-dashboard&#x27;,
  standalone: true,
  imports: [
    ChartModule,
    StyleClassModule,
    NgForOf,
    DialogModule,
    ButtonDirective,
    NgIf,
    BreadcrumbComponent,
    TableModule,
    AnalyticsDashboardComponent
  ],
  templateUrl: &#x27;./dashboard.component.html&#x27;,
  styleUrl: &#x27;./dashboard.component.scss&#x27;
})
export class DashboardComponent implements OnInit {

  // Graphique 1: Répartition des actes par type
  polarChartData: any;
  polarTableData: any[] &#x3D; [];

  // Graphique 2: Évolution mensuelle des actes
  lineChartData: any;
  evolutionTableData: any[] &#x3D; [];

  // Graphique 3: Répartition des actes par services
  horizontalBarChartData: any;
  servicesTableData: any[] &#x3D; [];

  // Graphique 4: Performance des praticiens
  verticalBarChartData: any;
  performanceTableData: any[] &#x3D; [];

  // Graphique 5: Actes par type d&#x27;admission
  pieChartData: any;
  admissionTableData: any[] &#x3D; [];

  chartOptions: any;

  //
  dialogStates: { [key: string]: boolean } &#x3D; {
    repartition: false, // Section 1
    evolution: false, // Section 2
    services: false, // Section 3
    praticien: false, // Section 4
    admission: false, // Section 5
  };
  //
  showInfoMessage: boolean &#x3D; true;
  //
  // fil d&#x27;ariane
  breadcrumbItems: BreadcrumbItem[] &#x3D; [
    { label: &#x27;Tableau de Bord&#x27;, url: &#x27;/graphic/dashboard&#x27; },
    { label: &#x27;Pondération des 6 derniers mois de l\&#x27;année en cours.&#x27; }
  ];

  constructor(private dashboardService: DashboardService) {}

  ngOnInit(): void {
    // Configurations globales des graphiques
    this.chartOptions &#x3D; {
      responsive: true,
      maintainAspectRatio: false,
      aspectRatio: 0.8,
      plugins: {
        legend: {
          position: &#x27;top&#x27;
        }
      }
    };


    // Charger les données depuis le service
    this.dashboardService.dashboardData$.subscribe((data) &#x3D;&gt; {
      if (data) {
        this.initPolarChart(data.repartitionActesParType);
        this.initLineChart(data.evolutionMensuelle);
        this.initHorizontalBarChart(data.repartitionActesParServices);
        this.initVerticalBarChart(data.performancePraticiens);
        this.initPieChart(data.actesInterneExterne);
      }
    });
    //
    setTimeout(() &#x3D;&gt; {
      this.showInfoMessage &#x3D; false;
    }, 10000);
  }

  hideInfoMessage(): void {
    this.showInfoMessage &#x3D; false;
  }

  showInfoMessageAgain(): void {
    this.showInfoMessage &#x3D; true;
  }

  showDialog(section: string): void {
    if (this.dialogStates.hasOwnProperty(section)) {
      this.dialogStates[section] &#x3D; true;
    }
  }


  // Section 1: Répartition des actes par type
  private initPolarChart(data: any[]): void {
    this.polarChartData &#x3D; {
      labels: data.map((item) &#x3D;&gt; item.type),
      datasets: [
        {
          data: data.map((item) &#x3D;&gt; item.total),
          backgroundColor: [&#x27;#42A5F5&#x27;, &#x27;#FFA726&#x27;, &#x27;#66BB6A&#x27;]
        }
      ]
    };

    this.polarTableData &#x3D; data.map((item) &#x3D;&gt; ({
      type: item.type,
      total: item.total,
      percent: ((item.total / data.reduce((sum, item) &#x3D;&gt; sum + item.total, 0)) * 100).toFixed(2)
    }));
  }

  // Section 2: Évolution mensuelle des actes
  private initLineChart(data: any[]): void {
    this.lineChartData &#x3D; {
      labels: data.map((item) &#x3D;&gt; item.mois),
      datasets: [
        {
          label: &#x27;2024&#x27;,
          data: data.map((item) &#x3D;&gt; item.anneeNmoinsUn),
          borderColor: &#x27;#42A5F5&#x27;,
          fill: false
        },
        {
          label: &#x27;2023&#x27;,
          data: data.map((item) &#x3D;&gt; item.anneeN),
          borderColor: &#x27;#FFA726&#x27;,
          fill: false
        }
      ]
    };

    this.evolutionTableData &#x3D; data.map((item) &#x3D;&gt; ({
      mois: item.mois,
      total: item.anneeN + item.anneeNmoinsUn
    }));
  }

  // Section 3: Répartition des actes par services
  private initHorizontalBarChart(data: any[]): void {
    this.horizontalBarChartData &#x3D; {
      labels: data.map((item) &#x3D;&gt; item.service),
      datasets: [
        {
          label: &#x27;Actes réalisés&#x27;,
          data: data.map((item) &#x3D;&gt; item.total),
          backgroundColor: &#x27;#42A5F5&#x27;
        }
      ]
    };

    this.servicesTableData &#x3D; data;
  }

  // Section 4: Performance des praticiens
  private initVerticalBarChart(data: any[]): void {
    this.verticalBarChartData &#x3D; {
      labels: data.map((item) &#x3D;&gt; &#x60;${item.nom} ${item.prenom}&#x60;),
      datasets: [
        {
          label: &#x27;Actes réalisés&#x27;,
          data: data.map((item) &#x3D;&gt; item.total),
          backgroundColor: &#x27;#FFA726&#x27;
        }
      ]
    };

    this.performanceTableData &#x3D; data;
  }

  // Section 5: Actes par type d&#x27;admission
  private initPieChart(data: any[]): void {
    this.pieChartData &#x3D; {
      labels: data.map((item) &#x3D;&gt; item.type),
      datasets: [
        {
          data: data.map((item) &#x3D;&gt; item.total),
          backgroundColor: [&#x27;#42A5F5&#x27;, &#x27;#FFA726&#x27;]
        }
      ]
    };

    this.admissionTableData &#x3D; data;
  }

  viewServiceDetails(service: any) {
    alert(&#x60;${service.name} de ${service.type} de ${service.total}&#x60;);
  }

  viewPraticienDetails(praticien: any) {
    alert(&#x60;${praticien.nom} &#x3D;&gt; ${praticien.total}&#x60;);
  }
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;app-breadcrumb [items]&#x3D;&quot;breadcrumbItems&quot;&gt;&lt;/app-breadcrumb&gt;
&lt;!--&lt;app-analytics-dashboard&gt;&lt;/app-analytics-dashboard&gt;--&gt;
&lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
  &lt;!-- Section 1: Répartition des actes par type --&gt;
  &lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
    &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg&quot;&gt;
      &lt;!-- Graphique Polar Area --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Répartition par type d&#x27;acte
          &lt;/h5&gt;
          &lt;!-- Bouton pour agrandir --&gt;
          &lt;button
            pButton
            icon&#x3D;&quot;pi pi-external-link&quot;
            class&#x3D;&quot;p-button-rounded p-button-text&quot;
            (click)&#x3D;&quot;showDialog(&#x27;repartition&#x27;)&quot;
            title&#x3D;&quot;Agrandir&quot;
          &gt;&lt;/button&gt;
        &lt;/div&gt;
        &lt;p-chart
          type&#x3D;&quot;polarArea&quot;
          [data]&#x3D;&quot;polarChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;

      &lt;!-- Tableau des données --&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
          Détails des types d&#x27;actes
        &lt;/h5&gt;
        &lt;ul&gt;
          &lt;li
            *ngFor&#x3D;&quot;let item of polarTableData&quot;
            class&#x3D;&quot;flex justify-between py-2 border-b&quot;
          &gt;
            &lt;span&gt;{{ item.type }}&lt;/span&gt;
            &lt;span&gt;{{ item.total }} ({{ item.percent }}%)&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Dialog pour agrandir le graphique --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogStates[&#x27;repartition&#x27;]&quot;
      [style]&#x3D;&quot;{ width: &#x27;70vw&#x27;, height: &#x27;auto&#x27; }&quot;
      [modal]&#x3D;&quot;true&quot;
      [closable]&#x3D;&quot;true&quot;
      [draggable]&#x3D;&quot;false&quot;
      [resizable]&#x3D;&quot;false&quot;
      header&#x3D;&quot;Répartition des actes par type&quot;
    &gt;
      &lt;p-chart
        type&#x3D;&quot;polarArea&quot;
        [data]&#x3D;&quot;polarChartData&quot;
        [options]&#x3D;&quot;chartOptions&quot;
        pStyleClass&#x3D;&quot;w-full h-96&quot;
      &gt;&lt;/p-chart&gt;
    &lt;/p-dialog&gt;
  &lt;/section&gt;

  &lt;!-- Section 2: Évolution mensuelle des actes réalisés --&gt;
  &lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
    &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg&quot;&gt;
      &lt;!-- Graphique Line Chart --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Évolution mensuelle des actes réalisés
          &lt;/h5&gt;
          &lt;!-- Bouton pour agrandir --&gt;
          &lt;button
            pButton
            icon&#x3D;&quot;pi pi-external-link&quot;
            class&#x3D;&quot;p-button-rounded p-button-text&quot;
            (click)&#x3D;&quot;showDialog(&#x27;evolution&#x27;)&quot;
            title&#x3D;&quot;Agrandir&quot;
          &gt;&lt;/button&gt;
        &lt;/div&gt;
        &lt;p-chart
          type&#x3D;&quot;line&quot;
          [data]&#x3D;&quot;lineChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;

      &lt;!-- Tableau des données --&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
          Détails de l&#x27;évolution
        &lt;/h5&gt;
        &lt;ul&gt;
          &lt;li
            *ngFor&#x3D;&quot;let item of evolutionTableData&quot;
            class&#x3D;&quot;flex justify-between py-2 border-b&quot;
          &gt;
            &lt;span&gt;{{ item.mois }}&lt;/span&gt;
            &lt;span&gt;{{ item.total }} actes&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Dialog pour agrandir le graphique --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogStates[&#x27;evolution&#x27;]&quot;
      [style]&#x3D;&quot;{ width: &#x27;70vw&#x27;, height: &#x27;auto&#x27; }&quot;
      [modal]&#x3D;&quot;true&quot;
      [closable]&#x3D;&quot;true&quot;
      [draggable]&#x3D;&quot;false&quot;
      [resizable]&#x3D;&quot;false&quot;
      header&#x3D;&quot;Évolution mensuelle des actes réalisés&quot;
    &gt;
      &lt;p-chart
        type&#x3D;&quot;line&quot;
        [data]&#x3D;&quot;lineChartData&quot;
        [options]&#x3D;&quot;chartOptions&quot;
        pStyleClass&#x3D;&quot;w-full h-96&quot;
      &gt;&lt;/p-chart&gt;
    &lt;/p-dialog&gt;
  &lt;/section&gt;


  &lt;!-- Section 3: Répartition des actes par services --&gt;
  &lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
    &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg&quot;&gt;
      &lt;!-- Graphique Horizontal Bar Chart --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Répartition des actes par services
          &lt;/h5&gt;
          &lt;!-- Bouton pour agrandir --&gt;
          &lt;button
            pButton
            icon&#x3D;&quot;pi pi-external-link&quot;
            class&#x3D;&quot;p-button-rounded p-button-text&quot;
            (click)&#x3D;&quot;showDialog(&#x27;services&#x27;)&quot;
            title&#x3D;&quot;Agrandir&quot;
          &gt;&lt;/button&gt;
        &lt;/div&gt;
        &lt;p-chart
          type&#x3D;&quot;bar&quot;
          [data]&#x3D;&quot;horizontalBarChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-72&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;
      &lt;!-- Tableau des données --&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;Détails des services&lt;/h5&gt;
        &lt;p-table
          [value]&#x3D;&quot;servicesTableData&quot;
          [paginator]&#x3D;&quot;true&quot;
          [rows]&#x3D;&quot;3&quot;
          [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot;
          class&#x3D;&quot;min-w-full bg-white border rounded-lg shadow&quot;
        &gt;
          &lt;!-- En-tête des colonnes --&gt;
          &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
            &lt;tr&gt;
              &lt;th class&#x3D;&quot;text-left px-4 py-2&quot;&gt;Service&lt;/th&gt;
              &lt;th class&#x3D;&quot;text-right px-4 py-2&quot;&gt;Actes réalisés&lt;/th&gt;
            &lt;/tr&gt;
          &lt;/ng-template&gt;

          &lt;!-- Corps des données --&gt;
          &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-service&gt;
            &lt;tr&gt;
              &lt;td class&#x3D;&quot;px-4 py-2 text-left flex items-center&quot;&gt;
                &lt;i
                  class&#x3D;&quot;pi pi-eye ml-2 mr-3 text-blue-600 cursor-pointer&quot;
                  title&#x3D;&quot;Voir les détails&quot;
                  (click)&#x3D;&quot;viewServiceDetails(service)&quot;
                &gt;&lt;/i&gt;
                {{ service.service }}
              &lt;/td&gt;
              &lt;td class&#x3D;&quot;px-4 py-2 text-right&quot;&gt;{{ service.total }}&lt;/td&gt;
            &lt;/tr&gt;
          &lt;/ng-template&gt;

          &lt;!-- Message lorsque la table est vide --&gt;
          &lt;ng-template pTemplate&#x3D;&quot;emptymessage&quot;&gt;
            &lt;tr&gt;
              &lt;td colspan&#x3D;&quot;2&quot; class&#x3D;&quot;text-center p-4&quot;&gt;Aucun service trouvé&lt;/td&gt;
            &lt;/tr&gt;
          &lt;/ng-template&gt;
        &lt;/p-table&gt;
      &lt;/div&gt;



    &lt;/div&gt;

    &lt;!-- Dialog pour agrandir le graphique --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogStates[&#x27;services&#x27;]&quot;
      [style]&#x3D;&quot;{ width: &#x27;70vw&#x27;, height: &#x27;auto&#x27; }&quot;
      [modal]&#x3D;&quot;true&quot;
      [closable]&#x3D;&quot;true&quot;
      [draggable]&#x3D;&quot;false&quot;
      [resizable]&#x3D;&quot;false&quot;
      header&#x3D;&quot;Répartition des actes par services&quot;
    &gt;
      &lt;p-chart
        type&#x3D;&quot;bar&quot;
        [data]&#x3D;&quot;horizontalBarChartData&quot;
        [options]&#x3D;&quot;chartOptions&quot;
        pStyleClass&#x3D;&quot;w-full h-96&quot;
      &gt;&lt;/p-chart&gt;
    &lt;/p-dialog&gt;
  &lt;/section&gt;


  &lt;!-- Section 4: Performance des praticiens (Top 5) --&gt;
  &lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
    &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg&quot;&gt;
      &lt;!-- Graphique Vertical Bar Chart --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Performance des praticiens (Top 5)
          &lt;/h5&gt;
          &lt;!-- Bouton pour agrandir --&gt;
          &lt;button
            pButton
            icon&#x3D;&quot;pi pi-external-link&quot;
            class&#x3D;&quot;p-button-rounded p-button-text&quot;
            (click)&#x3D;&quot;showDialog(&#x27;praticien&#x27;)&quot;
            title&#x3D;&quot;Agrandir&quot;
          &gt;&lt;/button&gt;
        &lt;/div&gt;
        &lt;p-chart
          type&#x3D;&quot;bar&quot;
          [data]&#x3D;&quot;verticalBarChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;

      &lt;!-- Tableau des données --&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
          Top praticiens
        &lt;/h5&gt;
        &lt;ul&gt;
          &lt;li *ngFor&#x3D;&quot;let praticien of performanceTableData&quot; class&#x3D;&quot;flex justify-between py-2 border-b&quot;&gt;
            &lt;span&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye ml-2 mr-3 text-blue-600 cursor-pointer&quot;
                title&#x3D;&quot;Voir les détails&quot;
                (click)&#x3D;&quot;viewPraticienDetails(praticien)&quot;
              &gt;&lt;/i&gt;
              {{ praticien.nom }} {{ praticien.prenom }}
            &lt;/span&gt;
            &lt;span&gt;{{ praticien.total }} actes&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Dialog pour agrandir le graphique --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogStates[&#x27;praticien&#x27;]&quot;
      [style]&#x3D;&quot;{ width: &#x27;70vw&#x27;, height: &#x27;auto&#x27; }&quot;
      [modal]&#x3D;&quot;true&quot;
      [closable]&#x3D;&quot;true&quot;
      [draggable]&#x3D;&quot;false&quot;
      [resizable]&#x3D;&quot;false&quot;
      header&#x3D;&quot;Performance des praticiens (Top 5)&quot;
    &gt;
      &lt;p-chart
        type&#x3D;&quot;bar&quot;
        [data]&#x3D;&quot;verticalBarChartData&quot;
        [options]&#x3D;&quot;chartOptions&quot;
        pStyleClass&#x3D;&quot;w-full h-96&quot;
      &gt;&lt;/p-chart&gt;
    &lt;/p-dialog&gt;
  &lt;/section&gt;


  &lt;!-- Section 5: Actes par type d&#x27;admission (Interne/Externe) --&gt;
  &lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
    &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg&quot;&gt;
      &lt;!-- Graphique Pie Chart --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Actes par type d&#x27;admission
          &lt;/h5&gt;
          &lt;!-- Bouton pour agrandir --&gt;
          &lt;button
            pButton
            icon&#x3D;&quot;pi pi-external-link&quot;
            class&#x3D;&quot;p-button-rounded p-button-text&quot;
            (click)&#x3D;&quot;showDialog(&#x27;admission&#x27;)&quot;
            title&#x3D;&quot;Agrandir&quot;
          &gt;&lt;/button&gt;
        &lt;/div&gt;
        &lt;p-chart
          type&#x3D;&quot;pie&quot;
          [data]&#x3D;&quot;pieChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;

      &lt;!-- Tableau des données --&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
          Détails admission
        &lt;/h5&gt;
        &lt;ul&gt;
          &lt;li
            *ngFor&#x3D;&quot;let item of admissionTableData&quot;
            class&#x3D;&quot;flex justify-between py-2 border-b&quot;
          &gt;
            &lt;span&gt;{{ item.type }}&lt;/span&gt;
            &lt;span&gt;{{ item.total }} actes&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Dialog pour agrandir le graphique --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogStates[&#x27;admission&#x27;]&quot;
      [style]&#x3D;&quot;{ width: &#x27;70vw&#x27;, height: &#x27;auto&#x27; }&quot;
      [modal]&#x3D;&quot;true&quot;
      [closable]&#x3D;&quot;true&quot;
      [draggable]&#x3D;&quot;false&quot;
      [resizable]&#x3D;&quot;false&quot;
      header&#x3D;&quot;Actes par type d&#x27;admission&quot;
    &gt;
      &lt;p-chart
        type&#x3D;&quot;pie&quot;
        [data]&#x3D;&quot;pieChartData&quot;
        [options]&#x3D;&quot;chartOptions&quot;
        pStyleClass&#x3D;&quot;w-full h-96&quot;
      &gt;&lt;/p-chart&gt;
    &lt;/p-dialog&gt;
  &lt;/section&gt;

&lt;/section&gt;
</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb><!--<app-analytics-dashboard></app-analytics-dashboard>--><section class="mb-8 mt-8">  <!-- Section 1: Répartition des actes par type -->  <section class="mb-8 mt-8">    <div class="grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg">      <!-- Graphique Polar Area -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg">        <div class="flex justify-between">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Répartition par type d\'acte          </h5>          <!-- Bouton pour agrandir -->          <button            pButton            icon="pi pi-external-link"            class="p-button-rounded p-button-text"            (click)="showDialog(\'repartition\')"            title="Agrandir"          ></button>        </div>        <p-chart          type="polarArea"          [data]="polarChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <!-- Tableau des données -->      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">          Détails des types d\'actes        </h5>        <ul>          <li            *ngFor="let item of polarTableData"            class="flex justify-between py-2 border-b"          >            <span>{{ item.type }}</span>            <span>{{ item.total }} ({{ item.percent }}%)</span>          </li>        </ul>      </div>    </div>    <!-- Dialog pour agrandir le graphique -->    <p-dialog      [(visible)]="dialogStates[\'repartition\']"      [style]="{ width: \'70vw\', height: \'auto\' }"      [modal]="true"      [closable]="true"      [draggable]="false"      [resizable]="false"      header="Répartition des actes par type"    >      <p-chart        type="polarArea"        [data]="polarChartData"        [options]="chartOptions"        pStyleClass="w-full h-96"      ></p-chart>    </p-dialog>  </section>  <!-- Section 2: Évolution mensuelle des actes réalisés -->  <section class="mb-8 mt-8">    <div class="grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg">      <!-- Graphique Line Chart -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg">        <div class="flex justify-between">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Évolution mensuelle des actes réalisés          </h5>          <!-- Bouton pour agrandir -->          <button            pButton            icon="pi pi-external-link"            class="p-button-rounded p-button-text"            (click)="showDialog(\'evolution\')"            title="Agrandir"          ></button>        </div>        <p-chart          type="line"          [data]="lineChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <!-- Tableau des données -->      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">          Détails de l\'évolution        </h5>        <ul>          <li            *ngFor="let item of evolutionTableData"            class="flex justify-between py-2 border-b"          >            <span>{{ item.mois }}</span>            <span>{{ item.total }} actes</span>          </li>        </ul>      </div>    </div>    <!-- Dialog pour agrandir le graphique -->    <p-dialog      [(visible)]="dialogStates[\'evolution\']"      [style]="{ width: \'70vw\', height: \'auto\' }"      [modal]="true"      [closable]="true"      [draggable]="false"      [resizable]="false"      header="Évolution mensuelle des actes réalisés"    >      <p-chart        type="line"        [data]="lineChartData"        [options]="chartOptions"        pStyleClass="w-full h-96"      ></p-chart>    </p-dialog>  </section>  <!-- Section 3: Répartition des actes par services -->  <section class="mb-8 mt-8">    <div class="grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg">      <!-- Graphique Horizontal Bar Chart -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg">        <div class="flex justify-between">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Répartition des actes par services          </h5>          <!-- Bouton pour agrandir -->          <button            pButton            icon="pi pi-external-link"            class="p-button-rounded p-button-text"            (click)="showDialog(\'services\')"            title="Agrandir"          ></button>        </div>        <p-chart          type="bar"          [data]="horizontalBarChartData"          [options]="chartOptions"          pStyleClass="w-full h-72"        ></p-chart>      </div>      <!-- Tableau des données -->      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">Détails des services</h5>        <p-table          [value]="servicesTableData"          [paginator]="true"          [rows]="3"          [responsiveLayout]="\'scroll\'"          class="min-w-full bg-white border rounded-lg shadow"        >          <!-- En-tête des colonnes -->          <ng-template pTemplate="header">            <tr>              <th class="text-left px-4 py-2">Service</th>              <th class="text-right px-4 py-2">Actes réalisés</th>            </tr>          </ng-template>          <!-- Corps des données -->          <ng-template pTemplate="body" let-service>            <tr>              <td class="px-4 py-2 text-left flex items-center">                <i                  class="pi pi-eye ml-2 mr-3 text-blue-600 cursor-pointer"                  title="Voir les détails"                  (click)="viewServiceDetails(service)"                ></i>                {{ service.service }}              </td>              <td class="px-4 py-2 text-right">{{ service.total }}</td>            </tr>          </ng-template>          <!-- Message lorsque la table est vide -->          <ng-template pTemplate="emptymessage">            <tr>              <td colspan="2" class="text-center p-4">Aucun service trouvé</td>            </tr>          </ng-template>        </p-table>      </div>    </div>    <!-- Dialog pour agrandir le graphique -->    <p-dialog      [(visible)]="dialogStates[\'services\']"      [style]="{ width: \'70vw\', height: \'auto\' }"      [modal]="true"      [closable]="true"      [draggable]="false"      [resizable]="false"      header="Répartition des actes par services"    >      <p-chart        type="bar"        [data]="horizontalBarChartData"        [options]="chartOptions"        pStyleClass="w-full h-96"      ></p-chart>    </p-dialog>  </section>  <!-- Section 4: Performance des praticiens (Top 5) -->  <section class="mb-8 mt-8">    <div class="grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg">      <!-- Graphique Vertical Bar Chart -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg">        <div class="flex justify-between">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Performance des praticiens (Top 5)          </h5>          <!-- Bouton pour agrandir -->          <button            pButton            icon="pi pi-external-link"            class="p-button-rounded p-button-text"            (click)="showDialog(\'praticien\')"            title="Agrandir"          ></button>        </div>        <p-chart          type="bar"          [data]="verticalBarChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <!-- Tableau des données -->      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">          Top praticiens        </h5>        <ul>          <li *ngFor="let praticien of performanceTableData" class="flex justify-between py-2 border-b">            <span>              <i                class="pi pi-eye ml-2 mr-3 text-blue-600 cursor-pointer"                title="Voir les détails"                (click)="viewPraticienDetails(praticien)"              ></i>              {{ praticien.nom }} {{ praticien.prenom }}            </span>            <span>{{ praticien.total }} actes</span>          </li>        </ul>      </div>    </div>    <!-- Dialog pour agrandir le graphique -->    <p-dialog      [(visible)]="dialogStates[\'praticien\']"      [style]="{ width: \'70vw\', height: \'auto\' }"      [modal]="true"      [closable]="true"      [draggable]="false"      [resizable]="false"      header="Performance des praticiens (Top 5)"    >      <p-chart        type="bar"        [data]="verticalBarChartData"        [options]="chartOptions"        pStyleClass="w-full h-96"      ></p-chart>    </p-dialog>  </section>  <!-- Section 5: Actes par type d\'admission (Interne/Externe) -->  <section class="mb-8 mt-8">    <div class="grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg">      <!-- Graphique Pie Chart -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg">        <div class="flex justify-between">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Actes par type d\'admission          </h5>          <!-- Bouton pour agrandir -->          <button            pButton            icon="pi pi-external-link"            class="p-button-rounded p-button-text"            (click)="showDialog(\'admission\')"            title="Agrandir"          ></button>        </div>        <p-chart          type="pie"          [data]="pieChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <!-- Tableau des données -->      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">          Détails admission        </h5>        <ul>          <li            *ngFor="let item of admissionTableData"            class="flex justify-between py-2 border-b"          >            <span>{{ item.type }}</span>            <span>{{ item.total }} actes</span>          </li>        </ul>      </div>    </div>    <!-- Dialog pour agrandir le graphique -->    <p-dialog      [(visible)]="dialogStates[\'admission\']"      [style]="{ width: \'70vw\', height: \'auto\' }"      [modal]="true"      [closable]="true"      [draggable]="false"      [resizable]="false"      header="Actes par type d\'admission"    >      <p-chart        type="pie"        [data]="pieChartData"        [options]="chartOptions"        pStyleClass="w-full h-96"      ></p-chart>    </p-dialog>  </section></section></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'DashboardComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'DashboardComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
