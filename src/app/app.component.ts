import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { take, Subject, takeUntil } from 'rxjs';
import { OlympicService } from './core/services/olympic.service';
import { SidebarService } from './core/services/sidebar.service';
import { SidebarMobileComponent } from './core/components/sidebar-mobile/sidebar-mobile.component';
import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {
  showToaster = false;
  isInMaintenance = environment.maintenance;

  // État de la sidebar pour adapter le layout
  isSidebarCollapsed = false;
  private destroy$ = new Subject<void>();

  constructor(
    private olympicService: OlympicService,
    private sidebarService: SidebarService
  ) {}

  ngOnInit(): void {
    // this.olympicService.loadInitialData().pipe(take(1)).subscribe();
    this.showToaster = true;

    // Le toaster disparaît automatiquement après 6 secondes
    setTimeout(() => {
      this.showToaster = false;
    }, 9000);

    // Écouter les changements d'état de la sidebar
    this.sidebarService.sidebarCollapsed$
      .pipe(takeUntil(this.destroy$))
      .subscribe(collapsed => {
        this.isSidebarCollapsed = collapsed;
        console.log('App component - Sidebar collapsed:', collapsed);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  closeToaster() {
    this.showToaster = false;
  }
}
