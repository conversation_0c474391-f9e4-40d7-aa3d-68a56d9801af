// Test simple pour vérifier les imports
console.log('Testing imports...');

try {
  // Test si le service existe
  const fs = require('fs');
  const path = require('path');
  
  const servicePath = path.join(__dirname, 'src/app/activites/services/actes-filters.service.ts');
  const indexPath = path.join(__dirname, 'src/app/activites/services/index.ts');
  
  console.log('Service file exists:', fs.existsSync(servicePath));
  console.log('Index file exists:', fs.existsSync(indexPath));
  
  if (fs.existsSync(servicePath)) {
    const serviceContent = fs.readFileSync(servicePath, 'utf8');
    console.log('Service has @Injectable:', serviceContent.includes('@Injectable'));
    console.log('Service has export class:', serviceContent.includes('export class ActesFiltersService'));
  }
  
  if (fs.existsSync(indexPath)) {
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    console.log('Index exports service:', indexContent.includes('actes-filters.service'));
  }
  
  console.log('✅ All checks passed');
} catch (error) {
  console.error('❌ Error:', error.message);
}
