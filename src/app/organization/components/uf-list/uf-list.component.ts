import {Component, OnInit} from '@angular/core';
import {UF} from "../../../core/models/organization/UF.model";
import {UFService} from "../../../core/services/organization/UFService";
import {TableModule} from "primeng/table";
import {BreadcrumbComponent} from "../../../pages/breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../../core/models/breadcrumbItem";
import {Router} from "@angular/router";

@Component({
  selector: 'app-uf-list',
  standalone: true,
  imports: [
    TableModule,
    BreadcrumbComponent
  ],
  templateUrl: './uf-list.component.html',
  styleUrl: './uf-list.component.scss'
})
export class UfListComponent implements OnInit {

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Ufs', url: '/organisation/uf-list' },
    { label: 'Liste des ufs' }
  ];

  ufs: UF[] = []; // Tableau local pour stocker les données
  totalUFs: number = 0; // Nombre total d'UF
  totalPractitioners: number = 0; // Nombre total de praticiens

  constructor(
    private ufService: UFService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUFs();
  }

  loadUFs(): void {
    this.ufService.ufs$.subscribe(
      (data) => {
        if (data) {
          this.ufs = data;

          // Calcul des statistiques globales
          this.totalUFs = data.length;
          this.totalPractitioners = data.reduce((sum, uf) => sum + uf.practitioners, 0);
        }
      },
      (error) => {
        console.error('Erreur lors du chargement des UFs :', error);
        this.ufs = [];
        this.totalUFs = 0;
        this.totalPractitioners = 0;
      }
    );
  }
  onInput(event: Event): string {
    console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }

  viewUfDetails(ufId:string) {
    this.router.navigate(['/organisation/uf', ufId]);
  }
}
