import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../auth/auth.service';

/**
 * Interface for UF (Unité Fonctionnelle) model
 */
export interface UfModel {
  '@id': string;
  '@type': string;
  'id': string;
  'code': string;
  'libelle': string;
  'isActif': boolean;
  'dateCreation': string;
  // Add other properties as needed
}

/**
 * Service for fetching UF (Unité Fonctionnelle) data
 */
@Injectable({
  providedIn: 'root'
})
export class UfService {
  private readonly API_URL = `${environment.api_url}/api/ufs`;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Get a UF by ID
   * @param id The UF ID
   * @returns Observable of the UF
   */
  getUfById(id: string): Observable<UfModel> {
    // Extract the ID from the full path if needed
    const ufId = id.includes('/api/ufs/') ? id.split('/api/ufs/')[1] : id;

    // Get the authentication token
    const token = this.authService.getToken();

    // Prepare headers with the authentication token
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    // Fetch from API with authentication
    return this.http.get<UfModel>(`${this.API_URL}/${ufId}`, { headers }).pipe(
      catchError(error => {
        console.error(`Error fetching UF with ID ${ufId}:`, error);
        return throwError(() => new Error(`Failed to fetch UF details: ${error.message}`));
      })
    );
  }
}
