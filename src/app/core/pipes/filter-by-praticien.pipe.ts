
import { Pipe, PipeTransform } from '@angular/core';
import {RealisationActe} from "../models/acte/realisation-acte.model";

@Pipe({
  standalone: true,
  name: 'filterByPraticien'
})
export class FilterByPraticienPipe implements PipeTransform {
  transform(realisations: RealisationActe[], praticienId: string): RealisationActe[] {
    return realisations.filter(r => r.praticienId === praticienId);
  }
}
