<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  CcamGraphComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/graphical/components/ccam-graph/ccam-graph.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-ccam-graph</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                                <code><a href="../components/OverviewComponent.html" target="_self" >OverviewComponent</a></code>
                                <code><a href="../components/ResumeProfilsComponent.html" target="_self" >ResumeProfilsComponent</a></code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./ccam-graph.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./ccam-graph.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#ccamBarChartByDay" >ccamBarChartByDay</a>
                            </li>
                            <li>
                                <a href="#ccamCombinedChart" >ccamCombinedChart</a>
                            </li>
                            <li>
                                <a href="#ccamLineChartByMonth" >ccamLineChartByMonth</a>
                            </li>
                            <li>
                                <a href="#chart" >chart</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createCCAMBarChartByDay" >createCCAMBarChartByDay</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createCCAMLineChartByMonth" >createCCAMLineChartByMonth</a>
                            </li>
                            <li>
                                <a href="#createChart" >createChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createCombinedChartCCAM" >createCombinedChartCCAM</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor()</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="20" class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:20</a></div>
                            </td>
                        </tr>

            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCCAMBarChartByDay"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createCCAMBarChartByDay</b></span>
                        <a href="#createCCAMBarChartByDay"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCCAMBarChartByDay()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="31"
                                    class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:31</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCCAMLineChartByMonth"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createCCAMLineChartByMonth</b></span>
                        <a href="#createCCAMLineChartByMonth"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCCAMLineChartByMonth()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="74"
                                    class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:74</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createChart"></a>
                    <span class="name">
                        <span ><b>createChart</b></span>
                        <a href="#createChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>createChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="121"
                                    class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:121</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCombinedChartCCAM"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createCombinedChartCCAM</b></span>
                        <a href="#createCombinedChartCCAM"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCombinedChartCCAM()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="166"
                                    class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:166</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="25"
                                    class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:25</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamBarChartByDay"></a>
                    <span class="name">
                        <span ><b>ccamBarChartByDay</b></span>
                        <a href="#ccamBarChartByDay"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="20" class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:20</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamCombinedChart"></a>
                    <span class="name">
                        <span ><b>ccamCombinedChart</b></span>
                        <a href="#ccamCombinedChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:18</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamLineChartByMonth"></a>
                    <span class="name">
                        <span ><b>ccamLineChartByMonth</b></span>
                        <a href="#ccamLineChartByMonth"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="19" class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:19</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chart"></a>
                    <span class="name">
                        <span ><b>chart</b></span>
                        <a href="#chart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="17" class="link-to-prism">src/app/graphical/components/ccam-graph/ccam-graph.component.ts:17</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import { Chart } from &#x27;chart.js/auto&#x27;;
import {OverviewComponent} from &quot;../../../pages/overview/overview.component&quot;;
import {ResumeProfilsComponent} from &quot;../../../pages/resume-profils/resume-profils.component&quot;;

@Component({
  selector: &#x27;app-ccam-graph&#x27;,
  standalone: true,
    imports: [
        OverviewComponent,
        ResumeProfilsComponent
    ],
  templateUrl: &#x27;./ccam-graph.component.html&#x27;,
  styleUrl: &#x27;./ccam-graph.component.scss&#x27;
})
export class CcamGraphComponent implements OnInit{
  chart : any;
  ccamCombinedChart!: any;
  ccamLineChartByMonth!: any;
  ccamBarChartByDay!: any;

  constructor() {
  }

  ngOnInit():void {
    this.createChart();
    this.createCombinedChartCCAM();
    this.createCCAMLineChartByMonth();
    this.createCCAMBarChartByDay();
  }
  private createCCAMBarChartByDay(): void {
    this.ccamBarChartByDay &#x3D; new Chart(&#x27;ccamBarChartByDay&#x27;, {
      type: &#x27;bar&#x27;,
      data: {
        labels: [&#x27;Lundi&#x27;, &#x27;Mardi&#x27;, &#x27;Mercredi&#x27;, &#x27;Jeudi&#x27;, &#x27;Vendredi&#x27;, &#x27;Samedi&#x27;, &#x27;Dimanche&#x27;], // Jours de la semaine
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes CCAM 2023&#x27;,
            data: [10, 15, 12, 14, 18, 20, 5], // Données fictives pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.7)&#x27;, // Bleu
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;,
            borderWidth: 1,
          },
          {
            label: &#x27;Nombre d\&#x27;actes CCAM 2024&#x27;,
            data: [12, 14, 13, 16, 19, 18, 6], // Données fictives pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.7)&#x27;, // Rouge
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;,
            borderWidth: 1,
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes CCAM&#x27;
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: &#x27;Comparaison des actes CCAM par jours de la semaine (2023 vs 2024)&#x27;
          }
        }
      }
    });
  }

  private createCCAMLineChartByMonth(): void {
    this.ccamLineChartByMonth &#x3D; new Chart(&#x27;ccamLineChartByMonth&#x27;, {
      type: &#x27;line&#x27;,
      data: {
        labels: [&#x27;Janvier&#x27;, &#x27;Février&#x27;, &#x27;Mars&#x27;, &#x27;Avril&#x27;, &#x27;Mai&#x27;, &#x27;Juin&#x27;, &#x27;Juillet&#x27;, &#x27;Août&#x27;, &#x27;Septembre&#x27;, &#x27;Octobre&#x27;, &#x27;Novembre&#x27;, &#x27;Décembre&#x27;],
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes CCAM 2023&#x27;,
            data: [100, 120, 130, 140, 150, 160, 170, 130, 140, 150, 160, 170], // Données fictives pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.2)&#x27;, // Bleu
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;,
            borderWidth: 2,
            fill: true,
            tension: 0.3, // Ajoute une légère courbe aux lignes
          },
          {
            label: &#x27;Nombre d\&#x27;actes CCAM 2024&#x27;,
            data: [90, 110, 140, 130, 160, 150, 180, 120, 130, 160, 170, 180], // Données fictives pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.2)&#x27;, // Rouge
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;,
            borderWidth: 2,
            fill: true,
            tension: 0.3,
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes CCAM&#x27;
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: &#x27;Comparaison des actes CCAM par mois (2023 vs 2024)&#x27;
          }
        }
      }
    });
  }

  createChart(): void {
    this.chart &#x3D; new Chart(&#x27;ccamChart&#x27;, {
      type: &#x27;bar&#x27;, // Graphique en barres pour représenter le nombre d&#x27;actes médicaux
      data: {
        labels: [&#x27;Janvier&#x27;, &#x27;Février&#x27;, &#x27;Mars&#x27;, &#x27;Avril&#x27;, &#x27;Mai&#x27;, &#x27;Juin&#x27;], // Mois
        datasets: [
          {
            label: &#x27;Actes Chirurgicaux&#x27;,
            data: [120, 150, 180, 170, 200, 210], // Données fictives pour les actes chirurgicaux
            backgroundColor: &#x27;rgba(75, 192, 192, 0.6)&#x27;,
            borderColor: &#x27;rgba(75, 192, 192, 1)&#x27;,
            borderWidth: 1
          },
          {
            label: &#x27;Actes Diagnostiques&#x27;,
            data: [90, 100, 140, 130, 160, 180], // Données fictives pour les actes diagnostiques
            backgroundColor: &#x27;rgba(255, 99, 132, 0.6)&#x27;,
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;,
            borderWidth: 1
          },
          {
            label: &#x27;Actes Thérapeutiques&#x27;,
            data: [60, 70, 80, 90, 100, 120], // Données fictives pour les actes thérapeutiques
            backgroundColor: &#x27;rgba(153, 102, 255, 0.6)&#x27;,
            borderColor: &#x27;rgba(153, 102, 255, 1)&#x27;,
            borderWidth: 1
          }
        ]
      },
      options: {
        scales: {
          y: {
            beginAtZero: true
          }
        },
        plugins: {
          title: {
            display: true,
            text: &#x27;Actes Médicaux CCAM par Mois&#x27;
          }
        }
      }
    });
  }

  private createCombinedChartCCAM(): void {
    this.ccamCombinedChart &#x3D; new Chart(&#x27;ccamCombinedChart&#x27;, {
      type: &#x27;bar&#x27;, // Commence avec un graphique en barres
      data: {
        labels: [
          &#x27;Acte chirurgical&#x27;, &#x27;Acte diagnostique&#x27;, &#x27;Acte thérapeutique&#x27;,
          &#x27;Acte endoscopique&#x27;, &#x27;Acte radiologique&#x27;, &#x27;Acte anesthésique&#x27;
        ], // Types d&#x27;actes CCAM
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes CCAM mensuels 2023&#x27;,
            data: [120, 110, 130, 150, 140, 160], // Données fictives mensuelles 2023
            type: &#x27;line&#x27;, // Ce dataset est en courbe
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;, // Bleu pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.2)&#x27;, // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            tension: 0.3,
            yAxisID: &#x27;y&#x27;, // Utilise le premier axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes CCAM mensuels 2024&#x27;,
            data: [110, 120, 125, 135, 145, 155], // Données fictives mensuelles 2024
            type: &#x27;line&#x27;, // Ce dataset est en courbe
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;, // Rouge pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.2)&#x27;, // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            tension: 0.3,
            yAxisID: &#x27;y&#x27;, // Utilise le premier axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes CCAM par type 2023&#x27;,
            data: [150, 130, 140, 110, 120, 100], // Données fictives par type 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.7)&#x27;, // Bleu pour les barres 2023
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;,
            borderWidth: 1,
            yAxisID: &#x27;y1&#x27;, // Utilise le deuxième axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes CCAM par type 2024&#x27;,
            data: [140, 125, 135, 105, 115, 95], // Données fictives par type 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.7)&#x27;, // Rouge pour les barres 2024
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;,
            borderWidth: 1,
            yAxisID: &#x27;y1&#x27;, // Utilise le deuxième axe Y
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            position: &#x27;left&#x27;,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes CCAM mensuels&#x27;
            }
          },
          y1: {
            beginAtZero: true,
            position: &#x27;right&#x27;,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes CCAM par type&#x27;
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                let label &#x3D; context.dataset.label || &#x27;&#x27;;

                if (label) {
                  label +&#x3D; &#x27;: &#x27;;
                }
                if (context.parsed.y !&#x3D;&#x3D; null) {
                  label +&#x3D; &#x60;${context.parsed.y} actes&#x60;;
                }
                return label;
              },
              afterLabel: function(context) {
                // Ajout de détails supplémentaires sur les types d&#x27;actes CCAM
                let typeActe &#x3D; &#x27;&#x27;;
                switch (context.label) {
                  case &#x27;Acte chirurgical&#x27;:
                    typeActe &#x3D; &#x27;Actes liés aux interventions chirurgicales&#x27;;
                    break;
                  case &#x27;Acte diagnostique&#x27;:
                    typeActe &#x3D; &#x27;Actes de diagnostic réalisés&#x27;;
                    break;
                  case &#x27;Acte thérapeutique&#x27;:
                    typeActe &#x3D; &#x27;Actes thérapeutiques&#x27;;
                    break;
                  case &#x27;Acte endoscopique&#x27;:
                    typeActe &#x3D; &#x27;Actes d\&#x27;endoscopie&#x27;;
                    break;
                  case &#x27;Acte radiologique&#x27;:
                    typeActe &#x3D; &#x27;Actes de radiologie&#x27;;
                    break;
                  case &#x27;Acte anesthésique&#x27;:
                    typeActe &#x3D; &#x27;Actes d\&#x27;anesthésie&#x27;;
                    break;
                  default:
                    typeActe &#x3D; &#x27;Actes divers&#x27;;
                    break;
                }
                return typeActe;
              }
            }
          },
          title: {
            display: true,
            text: &#x27;Comparaison des actes CCAM par type pour Dr Morel Olivier (2023 vs 2024)&#x27;
          }
        }
      }
    });
  }

}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">
&lt;div class&#x3D;&quot;bg-white shadow&quot;&gt;
  &lt;div class&#x3D;&quot;px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8&quot;&gt;
    &lt;app-resume-profils&gt;&lt;/app-resume-profils&gt;
  &lt;/div&gt;
&lt;/div&gt;
&lt;app-overview&gt;&lt;/app-overview&gt;

&lt;p class&#x3D;&quot;text-xl font-semibold text-center text-gray-800 mb-6&quot;&gt;Visualisation CCAM - Dr Morel Olivier&lt;/p&gt;
&lt;div class&#x3D;&quot;flex flex-col space-y-6 p-6 bg-gray-50&quot;&gt;
  &lt;!-- Graphique CCAM en courbe (par mois) --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-gray-100 rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ccamLineChartByMonth&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;

  &lt;!-- Histogramme CCAM (par jours de la semaine) --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ccamBarChartByDay&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;
&lt;/div&gt;


&lt;div class&#x3D;&quot;flex flex-col space-y-6 p-6 bg-gray-50&quot;&gt;
  &lt;!-- Premier graphique CCAM (Bar chart ou Line chart) --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-gray-100 rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ccamChart&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;

  &lt;!-- Deuxième graphique CCAM (Combined chart) --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ccamCombinedChart&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;
&lt;/div&gt;



</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><div class="bg-white shadow">  <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">    <app-resume-profils></app-resume-profils>  </div></div><app-overview></app-overview><p class="text-xl font-semibold text-center text-gray-800 mb-6">Visualisation CCAM - Dr Morel Olivier</p><div class="flex flex-col space-y-6 p-6 bg-gray-50">  <!-- Graphique CCAM en courbe (par mois) -->  <div class="w-full h-96 flex justify-center items-center bg-gray-100 rounded-lg shadow-lg">    <canvas id="ccamLineChartByMonth"></canvas>  </div>  <!-- Histogramme CCAM (par jours de la semaine) -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="ccamBarChartByDay"></canvas>  </div></div><div class="flex flex-col space-y-6 p-6 bg-gray-50">  <!-- Premier graphique CCAM (Bar chart ou Line chart) -->  <div class="w-full h-96 flex justify-center items-center bg-gray-100 rounded-lg shadow-lg">    <canvas id="ccamChart"></canvas>  </div>  <!-- Deuxième graphique CCAM (Combined chart) -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="ccamCombinedChart"></canvas>  </div></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'CcamGraphComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'CcamGraphComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
