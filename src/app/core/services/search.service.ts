// src/app/services/search.service.ts
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, catchError, debounceTime, distinctUntilChanged, switchMap } from 'rxjs';
import { SearchResponse } from '../models/search-result.model';

@Injectable({
  providedIn: 'root'
})
export class SearchService {
  private apiUrl = 'http://localhost:8000/api/search'; // Remplace par l'URL de ton API Symfony

  constructor(private http: HttpClient) {}

  /**
   * Recherche avec une query, limite et page.
   * @param query Le terme de recherche
   * @param limit Nombre de résultats par page
   * @param page Numéro de la page
   */
  search(query: string, limit: number = 10, page: number = 1): Observable<SearchResponse> {
    let params = new HttpParams()
      .set('q', query)
      .set('limit', limit.toString())
      .set('page', page.toString());

    return this.http.get<SearchResponse>(this.apiUrl, { params }).pipe(
      catchError((error) => {
        console.error('Erreur lors de la recherche', error);
        throw error;
      })
    );
}
}
