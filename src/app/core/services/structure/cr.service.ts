import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseStructureService, PaginatedResult } from './base-structure.service';
import { CRModel } from '../../models/structure/CRModel';

interface CRResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': CRModel[];
}

@Injectable({
  providedIn: 'root'
})
export class CRService extends BaseStructureService<CRModel, CRResponse> {
  constructor(http: HttpClient) {
    super(http, '/api/crs');
  }

  /**
   * Extract CRModel array and total count from API response
   * @param response API response
   * @returns PaginatedResult containing CRModel array and total count
   */
  protected extractData(response: CRResponse): PaginatedResult<CRModel> {
    return {
      items: response.member,
      totalItems: response.totalItems
    };
  }
}
