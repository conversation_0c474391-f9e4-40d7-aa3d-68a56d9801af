<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  GardeAstreinteComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/pages/garde-astreinte/garde-astreinte.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-garde-astreinte</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>TableModule</code>
                            <code>DatePipe</code>
                            <code>ChartModule</code>
                            <code>AutoCompleteModule</code>
                            <code>FormsModule</code>
                            <code>NgIf</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./garde-astreinte.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./garde-astreinte.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#barChartData" >barChartData</a>
                            </li>
                            <li>
                                <a href="#barChartOptions" >barChartOptions</a>
                            </li>
                            <li>
                                <a href="#comparisonChartData" >comparisonChartData</a>
                            </li>
                            <li>
                                <a href="#comparisonChartOptions" >comparisonChartOptions</a>
                            </li>
                            <li>
                                <a href="#filteredPractitioners" >filteredPractitioners</a>
                            </li>
                            <li>
                                <a href="#gardeAstreinteData" >gardeAstreinteData</a>
                            </li>
                            <li>
                                <a href="#hasError" >hasError</a>
                            </li>
                            <li>
                                <a href="#isLoading" >isLoading</a>
                            </li>
                            <li>
                                <a href="#selectedPractitioner" >selectedPractitioner</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#filterPractitioners" >filterPractitioners</a>
                            </li>
                            <li>
                                <a href="#getTotalOnCalls" >getTotalOnCalls</a>
                            </li>
                            <li>
                                <a href="#getTotalStandbys" >getTotalStandbys</a>
                            </li>
                            <li>
                                <a href="#initializeBarChart" >initializeBarChart</a>
                            </li>
                            <li>
                                <a href="#initializeBarChartStacked" >initializeBarChartStacked</a>
                            </li>
                            <li>
                                <a href="#initializeComparisonChart" >initializeComparisonChart</a>
                            </li>
                            <li>
                                <a href="#loadGardeAstreinteData" >loadGardeAstreinteData</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#onInput" >onInput</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(gardeAstreinteService: GardeAstreinteService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:31</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>gardeAstreinteService</td>

                                                        <td>
                                                                    <code>GardeAstreinteService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterPractitioners"></a>
                    <span class="name">
                        <span ><b>filterPractitioners</b></span>
                        <a href="#filterPractitioners"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>filterPractitioners(event: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="185"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:185</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTotalOnCalls"></a>
                    <span class="name">
                        <span ><b>getTotalOnCalls</b></span>
                        <a href="#getTotalOnCalls"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTotalOnCalls(practitioner: GardeAstreintePractitioner)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="167"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:167</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>practitioner</td>
                                            <td>
                                                        <code>GardeAstreintePractitioner</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTotalStandbys"></a>
                    <span class="name">
                        <span ><b>getTotalStandbys</b></span>
                        <a href="#getTotalStandbys"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTotalStandbys(practitioner: GardeAstreintePractitioner)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="171"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:171</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>practitioner</td>
                                            <td>
                                                        <code>GardeAstreintePractitioner</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeBarChart"></a>
                    <span class="name">
                        <span ><b>initializeBarChart</b></span>
                        <a href="#initializeBarChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeBarChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="65"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:65</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeBarChartStacked"></a>
                    <span class="name">
                        <span ><b>initializeBarChartStacked</b></span>
                        <a href="#initializeBarChartStacked"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeBarChartStacked()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="115"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:115</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeComparisonChart"></a>
                    <span class="name">
                        <span ><b>initializeComparisonChart</b></span>
                        <a href="#initializeComparisonChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeComparisonChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="194"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:194</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadGardeAstreinteData"></a>
                    <span class="name">
                        <span ><b>loadGardeAstreinteData</b></span>
                        <a href="#loadGardeAstreinteData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadGardeAstreinteData()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="39"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:39</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="35"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:35</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onInput"></a>
                    <span class="name">
                        <span ><b>onInput</b></span>
                        <a href="#onInput"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onInput(event: Event)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="174"
                                    class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:174</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                        <code>Event</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="barChartData"></a>
                    <span class="name">
                        <span ><b>barChartData</b></span>
                        <a href="#barChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="30" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:30</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="barChartOptions"></a>
                    <span class="name">
                        <span ><b>barChartOptions</b></span>
                        <a href="#barChartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:31</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="comparisonChartData"></a>
                    <span class="name">
                        <span ><b>comparisonChartData</b></span>
                        <a href="#comparisonChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="180" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:180</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="comparisonChartOptions"></a>
                    <span class="name">
                        <span ><b>comparisonChartOptions</b></span>
                        <a href="#comparisonChartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="181" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:181</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filteredPractitioners"></a>
                    <span class="name">
                        <span ><b>filteredPractitioners</b></span>
                        <a href="#filteredPractitioners"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>GardeAstreintePractitioner[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="183" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:183</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="gardeAstreinteData"></a>
                    <span class="name">
                        <span ><b>gardeAstreinteData</b></span>
                        <a href="#gardeAstreinteData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>GardeAstreinteData | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>null</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="26" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:26</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="hasError"></a>
                    <span class="name">
                        <span ><b>hasError</b></span>
                        <a href="#hasError"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>false</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="28" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:28</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isLoading"></a>
                    <span class="name">
                        <span ><b>isLoading</b></span>
                        <a href="#isLoading"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>true</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="27" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:27</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="selectedPractitioner"></a>
                    <span class="name">
                        <span ><b>selectedPractitioner</b></span>
                        <a href="#selectedPractitioner"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>GardeAstreintePractitioner | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>null</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="179" class="link-to-prism">src/app/pages/garde-astreinte/garde-astreinte.component.ts:179</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import {GardeAstreinteData, GardeAstreintePractitioner} from &quot;../../core/models/garde-astreinte.model&quot;;
import {GardeAstreinteService} from &quot;../../core/services/garde-astreinte.service&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {DatePipe, NgIf} from &quot;@angular/common&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {AutoCompleteModule} from &quot;primeng/autocomplete&quot;;
import {FormsModule} from &quot;@angular/forms&quot;;

@Component({
  selector: &#x27;app-garde-astreinte&#x27;,
  standalone: true,
  imports: [
    TableModule,
    DatePipe,
    ChartModule,
    AutoCompleteModule,
    FormsModule,
    NgIf
  ],
  templateUrl: &#x27;./garde-astreinte.component.html&#x27;,
  styleUrl: &#x27;./garde-astreinte.component.scss&#x27;
})
export class GardeAstreinteComponent implements OnInit{

  gardeAstreinteData: GardeAstreinteData | null &#x3D; null; // Données de garde et astreinte
  isLoading &#x3D; true; // Indique si les données sont en cours de chargement
  hasError &#x3D; false; // Indique s&#x27;il y a eu une erreur lors du chargement

  barChartData: any; // Données pour le graphique en barres
  barChartOptions: any; // Options pour le graphique en barres

  constructor(private gardeAstreinteService: GardeAstreinteService) {}

  ngOnInit(): void {
    this.loadGardeAstreinteData();
  }

  loadGardeAstreinteData(): void {
    this.gardeAstreinteService.getGardeAstreinteData().subscribe({
      next: (data) &#x3D;&gt; {
        if (data) {
          this.gardeAstreinteData &#x3D; data;
          this.initializeBarChart(); // Initialise le graphique
          this.initializeBarChartStacked();
          this.hasError &#x3D; false;
        } else {
          this.hasError &#x3D; true;
        }
        this.isLoading &#x3D; false;
      },
      error: () &#x3D;&gt; {
        this.hasError &#x3D; true;
        this.isLoading &#x3D; false;
      },
    });

    // Charger les données initiales si elles ne sont pas déjà chargées
    this.gardeAstreinteService.loadInitialData().subscribe({
      next: () &#x3D;&gt; console.log(&#x27;Données de garde et astreinte chargées avec succès.&#x27;),
      error: () &#x3D;&gt; console.error(&#x27;Erreur lors du chargement initial des données.&#x27;),
    });
  }

  initializeBarChart(): void {
    if (!this.gardeAstreinteData?.data) return;

    const labels &#x3D; this.gardeAstreinteData.data.map((practitioner) &#x3D;&gt; practitioner.praticienName);
    const datasets &#x3D; [
      {
        label: &#x27;Gardes&#x27;,
        backgroundColor: &#x27;#42a5f5&#x27;,
        data: this.gardeAstreinteData.data.map((practitioner) &#x3D;&gt; {
          return practitioner.yearlyBreakdown.reduce((sum, year) &#x3D;&gt; sum + year.onCalls, 0);
        }),
      },
      {
        label: &#x27;Astreintes&#x27;,
        backgroundColor: &#x27;#66bb6a&#x27;,
        data: this.gardeAstreinteData.data.map((practitioner) &#x3D;&gt; {
          return practitioner.yearlyBreakdown.reduce((sum, year) &#x3D;&gt; sum + year.standbys, 0);
        }),
      },
    ];

    this.barChartData &#x3D; {
      labels,
      datasets,
    };

    this.barChartOptions &#x3D; {
      plugins: {
        legend: {
          display: true,
          position: &#x27;top&#x27;,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: &#x27;Praticiens&#x27;,
          },
        },
        y: {
          title: {
            display: true,
            text: &#x27;Nombre de Gardes et Astreintes&#x27;,
          },
        },
      },
    };
  }

  initializeBarChartStacked(): void {
    if (!this.gardeAstreinteData?.data) return;

    const labels &#x3D; this.gardeAstreinteData.data.map((practitioner) &#x3D;&gt; practitioner.praticienName);
    const datasets &#x3D; [
      {
        label: &#x27;Gardes&#x27;,
        backgroundColor: &#x27;#42a5f5&#x27;,
        data: this.gardeAstreinteData.data.map((practitioner) &#x3D;&gt; {
          return practitioner.yearlyBreakdown.reduce((sum, year) &#x3D;&gt; sum + year.onCalls, 0);
        }),
      },
      {
        label: &#x27;Astreintes&#x27;,
        backgroundColor: &#x27;#66bb6a&#x27;,
        data: this.gardeAstreinteData.data.map((practitioner) &#x3D;&gt; {
          return practitioner.yearlyBreakdown.reduce((sum, year) &#x3D;&gt; sum + year.standbys, 0);
        }),
      },
    ];

    this.barChartData &#x3D; {
      labels,
      datasets,
    };

    this.barChartOptions &#x3D; {
      plugins: {
        legend: {
          display: true,
          position: &#x27;top&#x27;,
        },
      },
      scales: {
        x: {
          stacked: true, // Activer l&#x27;empilement sur l&#x27;axe X
          title: {
            display: true,
            text: &#x27;Praticiens&#x27;,
          },
        },
        y: {
          stacked: true, // Activer l&#x27;empilement sur l&#x27;axe Y
          title: {
            display: true,
            text: &#x27;Nombre de Gardes et Astreintes&#x27;,
          },
        },
      },
    };
  }

  getTotalOnCalls(practitioner: GardeAstreintePractitioner): number {
    return practitioner.yearlyBreakdown.reduce((sum, year) &#x3D;&gt; sum + year.onCalls, 0);
  }

  getTotalStandbys(practitioner: GardeAstreintePractitioner): number {
    return practitioner.yearlyBreakdown.reduce((sum, year) &#x3D;&gt; sum + year.standbys, 0);
  }
  onInput(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }

  //**********************Comparaison des Gardes et Astreintes des 3 dernières années**************************/
  selectedPractitioner: GardeAstreintePractitioner | null &#x3D; null;
  comparisonChartData: any;
  comparisonChartOptions: any;

  filteredPractitioners: GardeAstreintePractitioner[] &#x3D; [];

  filterPractitioners(event: any): void {
    const query &#x3D; event.query.toLowerCase();
    this.filteredPractitioners &#x3D;
      this.gardeAstreinteData?.data?.filter((practitioner) &#x3D;&gt;
        practitioner.praticienName.toLowerCase().includes(query)
      ) || [];
  }


  initializeComparisonChart(): void {
    if (!this.gardeAstreinteData?.data || !this.selectedPractitioner) return;

    const selectedPractitionerData &#x3D; this.gardeAstreinteData.data.find(
      (practitioner) &#x3D;&gt; practitioner.praticienId &#x3D;&#x3D;&#x3D; this.selectedPractitioner?.praticienId
    );

    if (!selectedPractitionerData) return;

    const years &#x3D; selectedPractitionerData.yearlyBreakdown.map((yearData) &#x3D;&gt; yearData.year);

    const datasets &#x3D; [
      {
        label: &#x60;${selectedPractitionerData.praticienName} - Gardes&#x60;,
        data: selectedPractitionerData.yearlyBreakdown.map((yearData) &#x3D;&gt; yearData.onCalls),
        borderColor: &#x27;#42a5f5&#x27;,
        backgroundColor: &#x27;rgba(66, 165, 245, 0.5)&#x27;,
        fill: true,
      },
      {
        label: &#x60;${selectedPractitionerData.praticienName} - Astreintes&#x60;,
        data: selectedPractitionerData.yearlyBreakdown.map((yearData) &#x3D;&gt; yearData.standbys),
        borderColor: &#x27;#66bb6a&#x27;,
        backgroundColor: &#x27;rgba(102, 187, 106, 0.5)&#x27;,
        fill: true,
      },
      ...this.gardeAstreinteData.data
        .filter((practitioner) &#x3D;&gt; practitioner.praticienId !&#x3D;&#x3D; this.selectedPractitioner?.praticienId)
        .map((practitioner) &#x3D;&gt; ({
          label: &#x60;${practitioner.praticienName} - Gardes&#x60;,
          data: practitioner.yearlyBreakdown.map((yearData) &#x3D;&gt; yearData.onCalls),
          borderColor: &#x27;#ffa726&#x27;,
          backgroundColor: &#x27;rgba(255, 167, 38, 0.5)&#x27;,
          fill: false,
          borderDash: [5, 5],
        })),
    ];

    this.comparisonChartData &#x3D; {
      labels: years,
      datasets,
    };

    this.comparisonChartOptions &#x3D; {
      plugins: {
        legend: {
          display: true,
          position: &#x27;top&#x27;,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: &#x27;Années&#x27;,
          },
        },
        y: {
          title: {
            display: true,
            text: &#x27;Nombre de Gardes et Astreintes&#x27;,
          },
        },
      }
    }
  }


}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;section&gt;
  &lt;h2 class&#x3D;&quot;text-xl font-bold mb-4&quot;&gt;Garde et Astreinte&lt;/h2&gt;

  &lt;!-- Dernière mise à jour --&gt;
  &lt;div class&#x3D;&quot;text-gray-600 mb-4&quot;&gt;
    Dernière actualisation : {{ gardeAstreinteData?.lastUpdated | date: &#x27;dd/MM/yyyy&#x27; }}
  &lt;/div&gt;

  &lt;!-- Tableau des données --&gt;
  &lt;div&gt;
    &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
      &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
      &lt;/div&gt;
      &lt;input
        type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
        (input)&#x3D;&quot;gardeAstreinteTable.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
        class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
        placeholder&#x3D;&quot;  Filtrer par praticien,service,uf ...&quot;
      /&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;p-table
    #gardeAstreinteTable
    [value]&#x3D;&quot;gardeAstreinteData?.data ?? []&quot;
    [paginator]&#x3D;&quot;true&quot;
    [rows]&#x3D;&quot;5&quot;
    [globalFilterFields]&#x3D;&quot;[&#x27;praticienName&#x27;, &#x27;service&#x27;, &#x27;uf&#x27;]&quot;
    [rowsPerPageOptions]&#x3D;&quot;[5, 10, 20]&quot;
    [showCurrentPageReport]&#x3D;&quot;true&quot;
    currentPageReportTemplate&#x3D;&quot;Affichage {first} à {last} sur {totalRecords}&quot;
    class&#x3D;&quot;mb-6&quot;
  &gt;
    &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
      &lt;tr&gt;
        &lt;th pSortableColumn&#x3D;&quot;praticienName&quot;&gt;Nom du praticien &lt;p-sortIcon field&#x3D;&quot;praticienName&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;service&quot;&gt;Service &lt;p-sortIcon field&#x3D;&quot;service&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;uf&quot;&gt;UF &lt;p-sortIcon field&#x3D;&quot;uf&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;th&gt;Total Gardes&lt;/th&gt;
        &lt;th&gt;Total Astreintes&lt;/th&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;
    &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-practitioner&gt;
      &lt;tr&gt;
        &lt;td&gt;{{ practitioner.praticienName }}&lt;/td&gt;
        &lt;td&gt;{{ practitioner.service }}&lt;/td&gt;
        &lt;td&gt;{{ practitioner.uf }}&lt;/td&gt;
        &lt;td&gt;{{ getTotalOnCalls(practitioner) }}&lt;/td&gt;
        &lt;td&gt;{{ getTotalStandbys(practitioner) }}&lt;/td&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;
  &lt;/p-table&gt;

  &lt;!-- Graphique en barres --&gt;
  &lt;h3 class&#x3D;&quot;text-lg font-semibold mb-3&quot;&gt;Répartition des Gardes et Astreintes&lt;/h3&gt;
  &lt;p-chart
    type&#x3D;&quot;bar&quot;
    [data]&#x3D;&quot;barChartData&quot;
    [options]&#x3D;&quot;barChartOptions&quot;
    class&#x3D;&quot;w-full h-96&quot;
  &gt;&lt;/p-chart&gt;
&lt;/section&gt;

&lt;section class&#x3D;&quot;mt-10&quot;&gt;
  &lt;h2 class&#x3D;&quot;text-lg font-semibold mb-4&quot;&gt;Comparaison des Gardes et Astreintes des 3 dernières années&lt;/h2&gt;
  &lt;div class&#x3D;&quot;mb-6&quot;&gt;
    &lt;label for&#x3D;&quot;practitionerSelect&quot; class&#x3D;&quot;block text-sm font-medium text-gray-700 mb-2&quot;&gt;
      Sélectionnez un praticien :
    &lt;/label&gt;
    &lt;div class&#x3D;&quot;relative w-full&quot;&gt;
      &lt;p-autoComplete
        [(ngModel)]&#x3D;&quot;selectedPractitioner&quot;
        [dropdown]&#x3D;&quot;true&quot;
        [suggestions]&#x3D;&quot;filteredPractitioners&quot;
        optionLabel&#x3D;&quot;praticienName&quot;
        [forceSelection]&#x3D;&quot;true&quot;
        (completeMethod)&#x3D;&quot;filterPractitioners($event)&quot;
        (onSelect)&#x3D;&quot;initializeComparisonChart()&quot;
        [placeholder]&#x3D;&quot;&#x27;Filtrer par praticien...&#x27;&quot;
        id&#x3D;&quot;practitionerSelect&quot;
        class&#x3D;&quot;w-full p-3 text-gray-700 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm&quot;
      &gt;&lt;/p-autoComplete&gt;
    &lt;/div&gt;
  &lt;/div&gt;

  &lt;!-- Graphique comparatif --&gt;
  &lt;p-chart
    type&#x3D;&quot;line&quot;
    [data]&#x3D;&quot;comparisonChartData&quot;
    [options]&#x3D;&quot;comparisonChartOptions&quot;
    *ngIf&#x3D;&quot;comparisonChartData&quot;
  &gt;&lt;/p-chart&gt;
&lt;/section&gt;

</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><section>  <h2 class="text-xl font-bold mb-4">Garde et Astreinte</h2>  <!-- Dernière mise à jour -->  <div class="text-gray-600 mb-4">    Dernière actualisation : {{ gardeAstreinteData?.lastUpdated | date: \'dd/MM/yyyy\' }}  </div>  <!-- Tableau des données -->  <div>    <div class="relative mt-2 rounded-md shadow-sm">      <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>      </div>      <input        type="text" name="price"        (input)="gardeAstreinteTable.filterGlobal(onInput($event), \'contains\')"        class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "        placeholder="  Filtrer par praticien,service,uf ..."      />    </div>  </div>  <p-table    #gardeAstreinteTable    [value]="gardeAstreinteData?.data ?? []"    [paginator]="true"    [rows]="5"    [globalFilterFields]="[\'praticienName\', \'service\', \'uf\']"    [rowsPerPageOptions]="[5, 10, 20]"    [showCurrentPageReport]="true"    currentPageReportTemplate="Affichage {first} à {last} sur {totalRecords}"    class="mb-6"  >    <ng-template pTemplate="header">      <tr>        <th pSortableColumn="praticienName">Nom du praticien <p-sortIcon field="praticienName"></p-sortIcon></th>        <th pSortableColumn="service">Service <p-sortIcon field="service"></p-sortIcon></th>        <th pSortableColumn="uf">UF <p-sortIcon field="uf"></p-sortIcon></th>        <th>Total Gardes</th>        <th>Total Astreintes</th>      </tr>    </ng-template>    <ng-template pTemplate="body" let-practitioner>      <tr>        <td>{{ practitioner.praticienName }}</td>        <td>{{ practitioner.service }}</td>        <td>{{ practitioner.uf }}</td>        <td>{{ getTotalOnCalls(practitioner) }}</td>        <td>{{ getTotalStandbys(practitioner) }}</td>      </tr>    </ng-template>  </p-table>  <!-- Graphique en barres -->  <h3 class="text-lg font-semibold mb-3">Répartition des Gardes et Astreintes</h3>  <p-chart    type="bar"    [data]="barChartData"    [options]="barChartOptions"    class="w-full h-96"  ></p-chart></section><section class="mt-10">  <h2 class="text-lg font-semibold mb-4">Comparaison des Gardes et Astreintes des 3 dernières années</h2>  <div class="mb-6">    <label for="practitionerSelect" class="block text-sm font-medium text-gray-700 mb-2">      Sélectionnez un praticien :    </label>    <div class="relative w-full">      <p-autoComplete        [(ngModel)]="selectedPractitioner"        [dropdown]="true"        [suggestions]="filteredPractitioners"        optionLabel="praticienName"        [forceSelection]="true"        (completeMethod)="filterPractitioners($event)"        (onSelect)="initializeComparisonChart()"        [placeholder]="\'Filtrer par praticien...\'"        id="practitionerSelect"        class="w-full p-3 text-gray-700 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"      ></p-autoComplete>    </div>  </div>  <!-- Graphique comparatif -->  <p-chart    type="line"    [data]="comparisonChartData"    [options]="comparisonChartOptions"    *ngIf="comparisonChartData"  ></p-chart></section></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'GardeAstreinteComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'GardeAstreinteComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
