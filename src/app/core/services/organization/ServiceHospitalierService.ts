import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {BehaviorSubject, map, Observable} from 'rxjs';
import {ServiceHospitalier} from "../../models/organization/ServiceHospitalier.model";

@Injectable({
    providedIn: 'root'
})
export class ServiceHospitalierService {
  private servicesHospitaliersSubject = new BehaviorSubject<ServiceHospitalier[]>([]);
  servicesHospitaliers$: Observable<ServiceHospitalier[]> = this.servicesHospitaliersSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadServicesHospitaliers();
  }

  private loadServicesHospitaliers() {
    this.http.get<ServiceHospitalier[]>('/assets/data/services-hospitaliers.json').subscribe((data) => {
      this.servicesHospitaliersSubject.next(data);
    });
  }

  getServiceHospitalierById(id: string): Observable<ServiceHospitalier | undefined> {
    return this.servicesHospitaliers$.pipe(
      map((services) => services.find((service) => service.id === id))
    );
  }
}
