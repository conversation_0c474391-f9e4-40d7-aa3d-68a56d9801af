export interface AffectationModel {
  '@id': string;
  '@type': string;
  
  // Dates
  dateDebut: string | null;
  dateFin: string | null;
  
  // Champs du DamAffectationTrait
  rgt: string | null;
  etpStatutaire: number | null;
  tauxAffectation: number | null;
  etp: number | null;
  sommeEtp: number | null;
  affectationPrincipale: string | null;
  typeGrade: string | null;
  libelleGrade: string | null;
  absences: number | null;
  
  // Champs du trait HorodatageTrait
  validFrom: string | null;
  validTo: string | null;
  periodeType: string;
  source: string | null;
  
  // Relations
  agent: AgentModel | null;
  ufs: UFModel | null;
  
  // Champs calculés
  effectifDeUf: number | null;
  
  // BaseDto fields
  isActif: boolean;
  dateCreation: string;
}

export interface UFModel {
  '@id': string;
  '@type': string;
  ufcode: string;
  libelle: string;
  cr: CRModel | null;
  isActif: boolean;
  dateCreation: string;
}

export interface CRModel {
  '@id': string;
  '@type': string;
  crcode: string;
  libelle: string;
  isActif: boolean;
  dateCreation: string;
}

export interface AgentModel {
  '@id': string;
  '@type': string;
  email: string | null;
  nom: string | null;
  prenom: string | null;
  matricule: string | null;
  hrUser: string | null;
  titre: string | null;
  categorie: string | null;
  etablissement: string | null;
  dateDepart: string | null;
  dateVenue: string | null;
  dateMaj: string | null;
  createurFiche: string | null;
  isAdmin: boolean;
  isAnesthesiste: boolean;
  fullName: string | null;
  imgUrl: string | null;
  badge: string | null;
  roles: string[];
  hopital: any | null;
  affectations: any[] | null;
  isActif: boolean;
  dateCreation: string;
}
