import {Component, OnInit} from '@angular/core';
import { Chart } from 'chart.js/auto';

@Component({
  selector: 'app-ngap-graph',
  standalone: true,
  imports: [],
  templateUrl: './ngap-graph.component.html',
  styleUrl: './ngap-graph.component.scss'
})
export class NgapGraphComponent implements OnInit{
  ngapChart!: any;
  ngapLineChart!: any;
  ngapLineByMonthChart!: any;
  ngapBarByDayChart!: any;
  ngapCombinedChart!: any;
  ngapCombinedChartV2!: any;
  quartilesChart!: any;

  constructor() {
  }
  ngOnInit():void {
    this.createChart();
    this.createLineChart();
    this.createBarChartByDay();
    this.createLineChartByMonth()
    this.createCombinedChart();
    this.createCombinedChartV2();
    this.createQuartilesChart();
  }

  private createChart(): void {
    this.ngapChart = new Chart('ngapChart', {
      type: 'bar', // Graphique en barres
      data: {
        labels: [
          'Consultation spécialiste',
          'Avis consultant hospitalier',
          'Consultant cabinet spécialiste',
          'TELEEXPERTISE NIVEAU 2',
          'Soins infirmiers',
          'Entretien',
          'Télé-consultation'
        ], // Actes NGAP
        datasets: [
          {
            label: 'Nombre d\'actes 2023',
            data: [201, 132, 9, 0, 6, 41, 0], // Données pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.7)', // Couleur bleue
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1,
          },
          {
            label: 'Nombre d\'actes 2024',
            data: [157, 110, 24, 8, 7, 33, 3], // Données pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.7)', // Couleur rouge
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1,
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Nombre d\'actes'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Nombre d\'actes NGAP pour Dr Morel Olivier (2023 vs 2024)'
          }
        }
      }
    });
  }

  private createLineChart(): void {
    this.ngapLineChart = new Chart('ngapLineChart', {
      type: 'line', // Graphique en courbe
      data: {
        labels: [
          'Consultation spécialiste',
          'Avis consultant hospitalier',
          'Consultant cabinet spécialiste',
          'TELEEXPERTISE NIVEAU 2',
          'Soins infirmiers',
          'Entretien',
          'Télé-consultation'
        ], // Actes NGAP
        datasets: [
          {
            label: 'Nombre d\'actes 2023',
            data: [201, 132, 9, 0, 6, 41, 0], // Données pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.2)', // Couleur bleue (transparente pour le remplissage)
            borderColor: 'rgba(54, 162, 235, 1)', // Bordure bleue
            borderWidth: 2,
            fill: true, // Remplir sous la courbe
            tension: 0.3 // Ajoute un peu de courbe aux lignes
          },
          {
            label: 'Nombre d\'actes 2024',
            data: [157, 110, 24, 8, 7, 33, 3], // Données pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.2)', // Couleur rouge (transparente pour le remplissage)
            borderColor: 'rgba(255, 99, 132, 1)', // Bordure rouge
            borderWidth: 2,
            fill: true, // Remplir sous la courbe
            tension: 0.3 // Ajoute un peu de courbe aux lignes
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Nombre d\'actes'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Courbe NGAP - Nombre d\'actes pour Dr Morel Olivier (2023 vs 2024)'
          }
        }
      }
    });
  }

  private createLineChartByMonth(): void {
    this.ngapLineByMonthChart = new Chart('ngapLineChartByMonth', {
      type: 'line', // Graphique en courbe
      data: {
        labels: ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'], // Mois de l'année
        datasets: [
          {
            label: 'Nombre d\'actes 2023',
            data: [100, 150, 120, 180, 140, 160, 110, 130, 150, 120, 170, 130], // Données fictives pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.2)', // Bleu transparent
            borderColor: 'rgba(54, 162, 235, 1)', // Bordure bleue
            borderWidth: 2,
            fill: true,
            tension: 0.3
          },
          {
            label: 'Nombre d\'actes 2024',
            data: [90, 140, 130, 170, 130, 150, 120, 140, 130, 160, 140, 150], // Données fictives pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.2)', // Rouge transparent
            borderColor: 'rgba(255, 99, 132, 1)', // Bordure rouge
            borderWidth: 2,
            fill: true,
            tension: 0.3
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Nombre d\'actes'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Comparaison mensuelle des actes NGAP (2023 vs 2024)'
          }
        }
      }
    });
  }
  private createBarChartByDay(): void {
    this.ngapBarByDayChart = new Chart('ngapBarChartByDay', {
      type: 'bar', // Graphique en barres
      data: {
        labels: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'], // Jours de la semaine
        datasets: [
          {
            label: 'Nombre d\'actes 2023',
            data: [10, 20, 15, 25, 18, 12, 8], // Données fictives pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.7)', // Bleu
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
          },
          {
            label: 'Nombre d\'actes 2024',
            data: [12, 18, 14, 20, 22, 10, 9], // Données fictives pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.7)', // Rouge
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Nombre d\'actes'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Comparaison journalière des actes NGAP (2023 vs 2024)'
          }
        }
      }
    });
  }

  private createCombinedChart(): void {
    this.ngapCombinedChart = new Chart('ngapCombinedChart', {
      type: 'bar', // Le graphique combiné commence avec un graphique en barres
      data: {
        labels: [
          'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
          'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
        ],
        datasets: [
          {
            label: 'Nombre d\'actes par mois 2023',
            data: [100, 150, 120, 180, 140, 160, 110, 130, 150, 120, 170, 130], // Données mensuelles 2023
            type: 'line', // Ce dataset est en courbe
            borderColor: 'rgba(54, 162, 235, 1)', // Bleu pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.2)', // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            yAxisID: 'y', // Utilise le premier axe Y
          },
          {
            label: 'Nombre d\'actes par mois 2024',
            data: [90, 140, 130, 170, 130, 150, 120, 140, 130, 160, 140, 150], // Données mensuelles 2024
            type: 'line', // Ce dataset est en courbe
            borderColor: 'rgba(255, 99, 132, 1)', // Rouge pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.2)', // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            yAxisID: 'y', // Utilise le premier axe Y
          },
          {
            label: 'Nombre d\'actes NGAP (par type) 2023',
            data: [201, 132, 9, 0, 6, 41, 0], // Données fictives NGAP par type 2023
            backgroundColor: 'rgba(54, 162, 235, 0.7)', // Bleu pour les barres 2023
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1,
            yAxisID: 'y1', // Utilise le deuxième axe Y
          },
          {
            label: 'Nombre d\'actes NGAP (par type) 2024',
            data: [157, 110, 24, 8, 7, 33, 3], // Données fictives NGAP par type 2024
            backgroundColor: 'rgba(255, 99, 132, 0.7)', // Rouge pour les barres 2024
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1,
            yAxisID: 'y1', // Utilise le deuxième axe Y
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            position: 'left',
            title: {
              display: true,
              text: 'Nombre d\'actes mensuels'
            }
          },
          y1: {
            beginAtZero: true,
            position: 'right',
            title: {
              display: true,
              text: 'Nombre d\'actes par type NGAP'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Comparaison des actes NGAP (2023 vs 2024) - Par mois et par type'
          }
        }
      }
    });
  }

  private createCombinedChartV2(): void {
    this.ngapCombinedChartV2 = new Chart('ngapCombinedChartV2', {
      type: 'bar', // Le graphique combiné commence avec un graphique en barres
      data: {
        labels: [
          'Consultation spécialiste', 'Avis consultant hospitalier', 'Consultant cabinet spécialiste',
          'TELEEXPERTISE NIVEAU 2', 'Soins infirmiers', 'Entretien', 'Télé-consultation'
        ], // Types d'actes NGAP
        datasets: [
          {
            label: 'Nombre d\'actes mensuels 2023',
            data: [100, 150, 120, 180, 140, 160, 110], // Données mensuelles 2023
            type: 'line', // Ce dataset est en courbe
            borderColor: 'rgba(54, 162, 235, 1)', // Bleu pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.2)', // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            tension: 0.3,
            yAxisID: 'y', // Utilise le premier axe Y
          },
          {
            label: 'Nombre d\'actes mensuels 2024',
            data: [90, 140, 130, 170, 130, 150, 120], // Données mensuelles 2024
            type: 'line', // Ce dataset est en courbe
            borderColor: 'rgba(255, 99, 132, 1)', // Rouge pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.2)', // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            tension: 0.3,
            yAxisID: 'y', // Utilise le premier axe Y
          },
          {
            label: 'Nombre d\'actes NGAP 2023',
            data: [201, 132, 9, 0, 6, 41, 0], // Données fictives NGAP 2023 par type
            backgroundColor: 'rgba(54, 162, 235, 0.7)', // Bleu pour les barres 2023
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1,
            yAxisID: 'y1', // Utilise le deuxième axe Y
          },
          {
            label: 'Nombre d\'actes NGAP 2024',
            data: [157, 110, 24, 8, 7, 33, 3], // Données fictives NGAP 2024 par type
            backgroundColor: 'rgba(255, 99, 132, 0.7)', // Rouge pour les barres 2024
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1,
            yAxisID: 'y1', // Utilise le deuxième axe Y
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            position: 'left',
            title: {
              display: true,
              text: 'Nombre d\'actes mensuels'
            }
          },
          y1: {
            beginAtZero: true,
            position: 'right',
            title: {
              display: true,
              text: 'Nombre d\'actes par type NGAP'
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                let label = context.dataset.label || '';

                if (label) {
                  label += ': ';
                }
                if (context.parsed.y !== null) {
                  label += `${context.parsed.y} actes`;
                }
                return label;
              },
              afterLabel: function(context) {
                // Ajout de détails supplémentaires sur les types d'actes
                let typeActe = '';
                switch (context.label) {
                  case 'Consultation spécialiste':
                    typeActe = 'Actes liés aux consultations avec des spécialistes';
                    break;
                  case 'Avis consultant hospitalier':
                    typeActe = 'Avis ponctuel des consultants hospitaliers';
                    break;
                  case 'Consultant cabinet spécialiste':
                    typeActe = 'Avis ponctuel au cabinet du spécialiste';
                    break;
                  case 'TELEEXPERTISE NIVEAU 2':
                    typeActe = 'Téléexpertise de niveau 2';
                    break;
                  case 'Soins infirmiers':
                    typeActe = 'Actes infirmiers réalisés';
                    break;
                  case 'Entretien':
                    typeActe = 'Entretien avec le patient';
                    break;
                  case 'Télé-consultation':
                    typeActe = 'Téléconsultations réalisées';
                    break;
                  default:
                    typeActe = 'Actes divers';
                    break;
                }
                return typeActe;
              }
            }
          },
          title: {
            display: true,
            text: 'Comparaison des actes NGAP par type pour Dr Morel Olivier (2023 vs 2024)'
          }
        }
      }
    });
  }

  // debut proposition avec quartile et mediane

  private createQuartilesChart(): void {
    this.quartilesChart = new Chart('quartilesChart', {
      type: 'bar',
      data: {
        labels: ['Périodes de faible activité', 'Période d\'activité moyenne', 'Périodes de forte activité'],
        datasets: [
          {
            label: 'Volume des consultations NGAP',
            data: [8, 14, 18],
            backgroundColor: [
              'rgba(75, 192, 192, 0.7)',
              'rgba(255, 159, 64, 0.7)',
              'rgba(153, 102, 255, 0.7)'
            ]
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Nombre de consultations NGAP'
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                label += `${context.parsed.y} consultations`;
                return label;
              }
            }
          },
          title: {
            display: true,
            text: 'Répartition des consultations NGAP (Quartile et Mediane) pour un praticien/service/pôle'
          }
        }
      }
    });
  }


}
