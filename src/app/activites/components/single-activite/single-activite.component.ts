import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router, RouterLink} from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { SkeletonModule } from 'primeng/skeleton';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { ChartModule } from 'primeng/chart';
import { BreadcrumbItem } from '../../../core/models/breadcrumbItem';
import { BreadcrumbComponent } from '../../../pages/breadcrumb/breadcrumb.component';
import { ActiviteModel } from '../../../core/models/activite';
import { ActiviteService } from '../../../core/services/activite';
import { GlobalFilterService, GlobalFilterState, DatePeriod } from '../../../core/services/global-filter/global-filter.service';
import { ActeStatsService } from '../../../core/services/acte-stats/acte-stats.service';
import { UfActeStats, PraticienActeStats } from '../../../core/models/acte-stats';
import { UfOrganisationService } from '../../../core/services/structure/uf-organisation.service';
import { UfOrganisation } from '../../../core/models/structure/UfOrganisation';
import { ActeTemporalData } from '../../../core/services/acte-stats/acte-stats.service';


@Component({
  selector: 'app-single-activite',
  templateUrl: './single-activite.component.html',
  styleUrls: ['./single-activite.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CardModule,
    TabViewModule,
    TooltipModule,
    SkeletonModule,
    TableModule,
    TagModule,
    ProgressBarModule,
    ChartModule,
    BreadcrumbComponent,
    RouterLink
  ]
})
export class SingleActiviteComponent implements OnInit {
  // Breadcrumb configuration
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Accueil', url: '/' },
    { label: 'Activités', url: '/activites/activites-list' },
    { label: 'Détails de l\'activité', url: '' }
  ];

  // Data properties
  activiteId: string = '';
  activite: ActiviteModel | null = null;

  // Stats data
  ufStats: UfActeStats[] = [];
  praticienStats: PraticienActeStats[] = [];

  // UF Organisation data
  ufPrincipalOrganisation: UfOrganisation | null = null;
  ufDemandeOrganisation: UfOrganisation | null = null;
  ufInterventionOrganisation: UfOrganisation | null = null;

  // Global filters and periods
  globalFilters: GlobalFilterState | null = null;
  periods: { p1: DatePeriod | null, p2: DatePeriod | null, p3: DatePeriod | null } = {
    p1: null, p2: null, p3: null
  };

  // UI state
  loading: boolean = true;
  loadingUfStats: boolean = false;
  loadingPraticienStats: boolean = false;
  loadingChartData: boolean = false;
  error: string = '';

  // Chart data
  chartData: any = {};
  chartOptions: any = {};

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private activiteService: ActiviteService,
    private globalFilterService: GlobalFilterService,
    private acteStatsService: ActeStatsService,
    private ufOrganisationService: UfOrganisationService
  ) {}

  ngOnInit(): void {
    // Load global filters and periods
    this.loadGlobalFilters();

    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.activiteId = id;
        this.loadActivite();
      } else {
        this.error = 'ID d\'activité non spécifié';
        this.loading = false;
      }
    });
  }

  /**
   * Load activite details from the service
   */
  loadActivite(): void {
    this.loading = true;
    this.activiteService.getById(this.activiteId).subscribe({
      next: (activite) => {
        this.activite = activite;
        this.loading = false;

        // Update breadcrumb with activite code
        if (activite.code) {
          this.breadcrumbItems[2].label = `Activité ${activite.code}`;
        }

        // Load stats, UF organisations and chart data after activite is loaded
        this.loadStats();
        this.loadUfOrganisations();
        this.loadChartData();
      },
      error: (error) => {
        console.error('Error loading activite:', error);
        this.error = 'Erreur lors du chargement des détails de l\'activité';
        this.loading = false;
      }
    });
  }

  /**
   * Format date for display
   * @param dateString Date string
   * @returns Formatted date string
   */
  formatDate(dateString: string | null): string {
    if (!dateString) return 'Non spécifié';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  }

  /**
   * Format Date object for display
   * @param date Date object
   * @returns Formatted date string
   */
  formatDateObject(date: Date | null): string {
    if (!date) return 'Non spécifié';
    return date.toLocaleDateString('fr-FR');
  }

  /**
   * Format date to month/year only (MM/YYYY)
   * @param dateString Date string or Date object
   * @returns Formatted month/year string
   */
  formatDateMonthYear(dateString: string | Date | null): string {
    if (!dateString) return 'Non spécifié';
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('fr-FR', {
      month: '2-digit',
      year: 'numeric'
    });
  }

  /**
   * Load global filters and periods from GlobalFilterService
   */
  private loadGlobalFilters(): void {
    this.globalFilters = this.globalFilterService.getCurrentFilterState();
    this.periods = {
      p1: this.globalFilters.p1,
      p2: this.globalFilters.p2,
      p3: this.globalFilters.p3
    };
  }

  /**
   * Load statistics for UF and Praticiens
   */
  private loadStats(): void {
    if (!this.activite?.code) return;

    this.loadUfStats();
    this.loadPraticienStats();
  }

  /**
   * Load UF statistics
   */
  private loadUfStats(): void {
    if (!this.activite?.code) return;

    this.loadingUfStats = true;
    this.acteStatsService.getUfStatsByActe(this.activite.code, this.periods.p1, this.periods.p2, this.periods.p3)
      .subscribe({
        next: (stats) => {
          this.ufStats = stats;
          this.loadingUfStats = false;
        },
        error: (error) => {
          console.error('Error loading UF stats:', error);
          this.loadingUfStats = false;
        }
      });
  }

  /**
   * Load Praticien statistics
   */
  private loadPraticienStats(): void {
    if (!this.activite?.code) return;

    this.loadingPraticienStats = true;
    this.acteStatsService.getPraticienStatsByActe(this.activite.code, this.periods.p1, this.periods.p2, this.periods.p3)
      .subscribe({
        next: (stats) => {
          this.praticienStats = stats;
          this.loadingPraticienStats = false;
        },
        error: (error) => {
          console.error('Error loading Praticien stats:', error);
          this.loadingPraticienStats = false;
        }
      });
  }

  /**
   * Format period for display
   */
  formatPeriod(period: DatePeriod | null): string {
    if (!period || !period.dateDebut || !period.dateFin) return 'Non défini';

    const start = period.dateDebut.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });
    const end = period.dateFin.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });

    return `${start} - ${end}`;
  }

  /**
   * Get severity class for frequency
   */
  getFrequencySeverity(frequency: number): "success" | "info" | "warning" | "danger" | "secondary" | "contrast" {
    if (frequency >= 20) return 'success';
    if (frequency >= 10) return 'warning';
    return 'danger';
  }

  /**
   * Navigate to UF details
   */
  navigateToUF(ufId: string): void {
    this.router.navigate(['/structure/uf', ufId]);
  }

  /**
   * Navigate to praticien details
   */
  navigateToPraticien(praticienId: string): void {
    this.router.navigate(['/graphic/praticien', praticienId]);
  }

  /**
   * Export UF statistics to CSV
   */
  exportUfStatsToCSV(): void {
    if (this.ufStats.length === 0) {
      console.warn('Aucune donnée UF à exporter');
      return;
    }

    const headers = [
      'Code UF',
      'Libellé UF',
      'P1 (Nombre)',
      'P1 (Fréquence %)',
      'P2 (Nombre)',
      'P2 (Fréquence %)',
      'P3 (Nombre)',
      'P3 (Fréquence %)',
      'Total',
      'Fréquence Globale (%)'
    ];

    const csvContent = [
      headers.join(','),
      ...this.ufStats.map(uf => [
        `"${uf.ufCode}"`,
        `"${uf.ufLibelle}"`,
        uf.p1Count,
        uf.p1Frequency.toFixed(1),
        uf.p2Count,
        uf.p2Frequency.toFixed(1),
        uf.p3Count,
        uf.p3Frequency.toFixed(1),
        uf.totalCount,
        uf.globalFrequency.toFixed(1)
      ].join(','))
    ].join('\n');

    this.downloadCSV(csvContent, `uf-stats-acte-${this.activite?.code || 'unknown'}.csv`);
  }

  /**
   * Export Praticien statistics to CSV
   */
  exportPraticienStatsToCSV(): void {
    if (this.praticienStats.length === 0) {
      console.warn('Aucune donnée Praticien à exporter');
      return;
    }

    const headers = [
      'Praticien',
      'P1 (Nombre)',
      'P1 (Fréquence %)',
      'P2 (Nombre)',
      'P2 (Fréquence %)',
      'P3 (Nombre)',
      'P3 (Fréquence %)',
      'Total',
      'Fréquence Globale (%)'
    ];

    const csvContent = [
      headers.join(','),
      ...this.praticienStats.map(praticien => [
        `"${praticien.praticienFullName}"`,
        praticien.p1Count,
        praticien.p1Frequency.toFixed(1),
        praticien.p2Count,
        praticien.p2Frequency.toFixed(1),
        praticien.p3Count,
        praticien.p3Frequency.toFixed(1),
        praticien.totalCount,
        praticien.globalFrequency.toFixed(1)
      ].join(','))
    ].join('\n');

    this.downloadCSV(csvContent, `praticien-stats-acte-${this.activite?.code || 'unknown'}.csv`);
  }

  /**
   * Download CSV file
   */
  private downloadCSV(content: string, filename: string): void {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  /**
   * Calculate total counts for UF statistics
   */
  getTotalP1Count(): number {
    return this.ufStats.reduce((total, uf) => total + uf.p1Count, 0);
  }

  getTotalP2Count(): number {
    return this.ufStats.reduce((total, uf) => total + uf.p2Count, 0);
  }

  getTotalP3Count(): number {
    return this.ufStats.reduce((total, uf) => total + uf.p3Count, 0);
  }

  getTotalGlobalCount(): number {
    return this.ufStats.reduce((total, uf) => total + uf.totalCount, 0);
  }

  /**
   * Calculate total counts for Praticien statistics
   */
  getTotalP1CountPraticiens(): number {
    return this.praticienStats.reduce((total, praticien) => total + praticien.p1Count, 0);
  }

  getTotalP2CountPraticiens(): number {
    return this.praticienStats.reduce((total, praticien) => total + praticien.p2Count, 0);
  }

  getTotalP3CountPraticiens(): number {
    return this.praticienStats.reduce((total, praticien) => total + praticien.p3Count, 0);
  }

  getTotalGlobalCountPraticiens(): number {
    return this.praticienStats.reduce((total, praticien) => total + praticien.totalCount, 0);
  }

  /**
   * Load UF organisations (CR → POLE) for all UFs
   */
  private loadUfOrganisations(): void {
    if (!this.activite) return;

    // Load organisation for UF Principal
    if (this.activite.ufPrincipal?.ufcode) {
      this.ufOrganisationService.getOrganisationByUfCode(this.activite.ufPrincipal.ufcode, this.periods.p1)
        .subscribe(org => this.ufPrincipalOrganisation = org);
    }

    // Load organisation for UF Demande (only if ufDemande is not null)
    if (this.activite.ufDemande?.ufcode) {
      this.ufOrganisationService.getOrganisationByUfCode(this.activite.ufDemande.ufcode, this.periods.p1)
        .subscribe(org => this.ufDemandeOrganisation = org);
    }

    // Load organisation for UF Intervention (only if ufIntervention is not null)
    if (this.activite.ufIntervention?.ufcode) {
      this.ufOrganisationService.getOrganisationByUfCode(this.activite.ufIntervention.ufcode, this.periods.p1)
        .subscribe(org => this.ufInterventionOrganisation = org);
    }
  }

  /**
   * Extract ID from API Platform @id URL
   * Example: "/api/ufs/1f03ba47-8dae-6c98-abe5-213d52d1a758" -> "1f03ba47-8dae-6c98-abe5-213d52d1a758"
   */
  extractIdFromApiUrl(apiUrl: string): string {
    if (!apiUrl) return '';
    const parts = apiUrl.split('/');
    return parts[parts.length - 1];
  }

  /**
   * Load chart data for monthly evolution
   */
  private loadChartData(): void {
    if (!this.activite?.code) return;

    this.loadingChartData = true;

    // Appeler la route backend pour obtenir les vraies données
    this.acteStatsService.getTemporalStatsByActe(this.activite.code, this.periods.p1, this.periods.p2, this.periods.p3)
      .subscribe({
        next: (data) => {
          if (data && data.length > 0) {
            this.generateChartDataFromAPI(data);
          } else {
            // Si pas de données, générer un graphique vide avec message
            this.generateEmptyChartData();
          }
          this.loadingChartData = false;
        },
        error: (error) => {
          console.error('Error loading temporal stats:', error);
          this.generateEmptyChartData(); // Afficher un graphique vide en cas d'erreur
          this.loadingChartData = false;
        }
      });
  }

  /**
   * Generate chart data from periods
   */
  private generateChartData(): void {
    const monthNames = [
      'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
      'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    // Générer les labels des mois basés sur les périodes
    const labels = this.generateMonthLabels();

    // Générer des données simulées pour chaque période
    const p1Data = this.generatePeriodData(this.periods.p1, labels.length);
    const p2Data = this.generatePeriodData(this.periods.p2, labels.length);
    const p3Data = this.generatePeriodData(this.periods.p3, labels.length);

    this.chartData = {
      labels: labels,
      datasets: [
        {
          label: `P1 (${this.getPeriodLabel(this.periods.p1)})`,
          data: p1Data,
          fill: true,
          backgroundColor: 'rgba(54, 162, 235, 0.3)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 3,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(54, 162, 235, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2
        },
        {
          label: `P2 (${this.getPeriodLabel(this.periods.p2)})`,
          data: p2Data,
          fill: true,
          backgroundColor: 'rgba(75, 192, 192, 0.3)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 3,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(75, 192, 192, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2
        },
        {
          label: `P3 (${this.getPeriodLabel(this.periods.p3)})`,
          data: p3Data,
          fill: true,
          backgroundColor: 'rgba(153, 102, 255, 0.3)',
          borderColor: 'rgba(153, 102, 255, 1)',
          borderWidth: 3,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(153, 102, 255, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2
        }
      ]
    };

    this.setupChartOptions();
    this.loadingChartData = false;
  }

  /**
   * Generate month labels based on periods
   */
  private generateMonthLabels(): string[] {
    const monthNames = [
      'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
      'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'
    ];

    // Utiliser la période P1 comme référence pour les mois
    if (this.periods.p1?.dateDebut && this.periods.p1?.dateFin) {
      const startMonth = this.periods.p1.dateDebut.getMonth();
      const endMonth = this.periods.p1.dateFin.getMonth();
      const startYear = this.periods.p1.dateDebut.getFullYear();
      const endYear = this.periods.p1.dateFin.getFullYear();

      const labels: string[] = [];
      let currentDate = new Date(startYear, startMonth, 1);
      const endDate = new Date(endYear, endMonth, 1);

      while (currentDate <= endDate) {
        const monthLabel = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
        labels.push(monthLabel);
        currentDate.setMonth(currentDate.getMonth() + 1);
      }

      return labels;
    }

    // Fallback: 12 mois par défaut
    return monthNames.map((month, index) => `${month} 2024`);
  }

  /**
   * Generate simulated data for a period
   */
  private generatePeriodData(period: DatePeriod | null, length: number): number[] {
    if (!period) {
      return new Array(length).fill(0);
    }

    // Générer des données simulées basées sur le nombre de réalisations de l'activité
    const baseValue = this.activite?.nombreDeRealisation || 10;
    const data: number[] = [];

    for (let i = 0; i < length; i++) {
      // Variation aléatoire autour de la valeur de base
      const variation = Math.random() * 0.4 - 0.2; // ±20%
      const value = Math.max(0, Math.round(baseValue * (1 + variation)));
      data.push(value);
    }

    return data;
  }

  /**
   * Generate chart data from API response
   */
  private generateChartDataFromAPI(data: ActeTemporalData[]): void {
    const monthNames = [
      'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
      'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'
    ];

    // Créer un map des données existantes groupées par mois (sans année)
    const dataByMonth = new Map<number, { p1: number, p2: number, p3: number }>();

    // Initialiser tous les mois à 0
    for (let month = 1; month <= 12; month++) {
      dataByMonth.set(month, { p1: 0, p2: 0, p3: 0 });
    }

    // Remplir avec les données réelles
    data.forEach(item => {
      const monthData = dataByMonth.get(item.moisNumero) || { p1: 0, p2: 0, p3: 0 };
      monthData.p1 += item.p1Count;
      monthData.p2 += item.p2Count;
      monthData.p3 += item.p3Count;
      dataByMonth.set(item.moisNumero, monthData);
    });

    // Créer les labels (12 mois) et les données
    const labels: string[] = [];
    const p1Data: number[] = [];
    const p2Data: number[] = [];
    const p3Data: number[] = [];

    for (let month = 1; month <= 12; month++) {
      // Ajouter le label du mois
      labels.push(monthNames[month - 1]);

      // Récupérer les données pour ce mois
      const monthData = dataByMonth.get(month)!;
      p1Data.push(monthData.p1);
      p2Data.push(monthData.p2);
      p3Data.push(monthData.p3);
    }

    this.chartData = {
      labels: labels,
      datasets: [
        {
          label: `P1 (${this.getPeriodLabel(this.periods.p1)})`,
          data: p1Data,
          fill: true,
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 2,
          tension: 0.4
        },
        {
          label: `P2 (${this.getPeriodLabel(this.periods.p2)})`,
          data: p2Data,
          fill: true,
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 2,
          tension: 0.4
        },
        {
          label: `P3 (${this.getPeriodLabel(this.periods.p3)})`,
          data: p3Data,
          fill: true,
          backgroundColor: 'rgba(153, 102, 255, 0.2)',
          borderColor: 'rgba(153, 102, 255, 1)',
          borderWidth: 2,
          tension: 0.4
        }
      ]
    };

    this.setupChartOptions();
  }

  /**
   * Setup chart options
   */
  private setupChartOptions(): void {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      layout: {
        padding: {
          top: 20,
          bottom: 20,
          left: 20,
          right: 20
        }
      },
      plugins: {
        title: {
          display: true,
          text: `Évolution mensuelle des réalisations - Acte ${this.activite?.code}`,
          font: {
            size: 18,
            weight: 'bold'
          },
          padding: {
            top: 10,
            bottom: 20
          }
        },
        legend: {
          display: true,
          position: 'top',
          labels: {
            padding: 20,
            font: {
              size: 14
            }
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleFont: {
            size: 14
          },
          bodyFont: {
            size: 13
          },
          callbacks: {
            title: function(context: any) {
              return context[0].label;
            },
            label: function(context: any) {
              return `${context.dataset.label}: ${context.parsed.y} réalisation(s)`;
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Mois',
            font: {
              size: 14,
              weight: 'bold'
            }
          },
          ticks: {
            font: {
              size: 12
            }
          },
          grid: {
            display: true,
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Nombre de réalisations',
            font: {
              size: 14,
              weight: 'bold'
            }
          },
          beginAtZero: true,
          min: 0,
          suggestedMax: 10, // Suggestion de maximum pour améliorer la visibilité avec peu de données
          ticks: {
            stepSize: 1, // Forcer les pas de 1 pour les petites valeurs
            font: {
              size: 12
            }
          },
          grid: {
            display: true,
            color: 'rgba(0, 0, 0, 0.1)'
          }
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      },
      elements: {
        point: {
          radius: 6, // Points plus gros pour une meilleure visibilité
          hoverRadius: 8
        },
        line: {
          borderWidth: 3 // Lignes plus épaisses
        }
      }
    };
  }

  /**
   * Generate empty chart data when no data is available
   */
  private generateEmptyChartData(): void {
    const monthNames = [
      'Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
      'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'
    ];

    // Créer les labels pour les 12 mois de l'année
    const labels = monthNames; // ['Jan', 'Fév', 'Mar', ..., 'Déc']

    // Créer des données vides (0) pour tous les mois
    const emptyData = new Array(12).fill(0);

    this.chartData = {
      labels: labels,
      datasets: [
        {
          label: `P1 (${this.getPeriodLabel(this.periods.p1)})`,
          data: [...emptyData],
          fill: true,
          backgroundColor: 'rgba(54, 162, 235, 0.3)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 3,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(54, 162, 235, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2
        },
        {
          label: `P2 (${this.getPeriodLabel(this.periods.p2)})`,
          data: [...emptyData],
          fill: true,
          backgroundColor: 'rgba(75, 192, 192, 0.3)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 3,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(75, 192, 192, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2
        },
        {
          label: `P3 (${this.getPeriodLabel(this.periods.p3)})`,
          data: [...emptyData],
          fill: true,
          backgroundColor: 'rgba(153, 102, 255, 0.3)',
          borderColor: 'rgba(153, 102, 255, 1)',
          borderWidth: 3,
          tension: 0.4,
          pointRadius: 6,
          pointHoverRadius: 8,
          pointBackgroundColor: 'rgba(153, 102, 255, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2
        }
      ]
    };

    this.setupChartOptions();
  }

  /**
   * Generate all months in a period (format YYYY-MM)
   */
  private generateAllMonthsInPeriod(period: DatePeriod | null): string[] {
    if (!period?.dateDebut || !period?.dateFin) {
      // Fallback: retourner 12 mois de l'année courante
      const currentYear = new Date().getFullYear();
      const months: string[] = [];
      for (let month = 1; month <= 12; month++) {
        const monthStr = month.toString().padStart(2, '0');
        months.push(`${currentYear}-${monthStr}`);
      }
      return months;
    }

    const months: string[] = [];
    const startDate = new Date(period.dateDebut);
    const endDate = new Date(period.dateFin);

    // Commencer au premier jour du mois de début
    const currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);

    // Aller jusqu'au dernier mois de la période
    while (currentDate <= endDate) {
      const year = currentDate.getFullYear();
      const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
      months.push(`${year}-${month}`);

      // Passer au mois suivant
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return months;
  }

  /**
   * Get period label for display
   */
  getPeriodLabel(period: DatePeriod | null): string {
    if (!period?.dateDebut) return 'Non défini';
    return period.dateDebut.getFullYear().toString();
  }

  /**
   * Navigate back to the activites list
   */
  goBack(): void {
    this.router.navigate(['/activites/activites-list']);
  }
}
