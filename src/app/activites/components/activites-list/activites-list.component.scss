/* Custom styles for activites-list component */

:host ::ng-deep {
  /* Table styles */
  .p-datatable {
    .p-datatable-thead > tr > th {
      background-color: #f8fafc;
      color: #334155;
      font-weight: 600;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f1f5f9;
      }
    }

    .p-datatable-tbody > tr {
      transition: background-color 0.2s, transform 0.1s;

      &:hover {
        background-color: #f1f5f9;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      > td {
        padding: 0.75rem 1rem;
        border-width: 0 0 1px 0;
        vertical-align: middle;
      }
    }

    /* Improve sort icons */
    .p-sortable-column {
      .p-sortable-column-icon {
        opacity: 0.5;
        transition: opacity 0.2s;
      }

      &:hover .p-sortable-column-icon {
        opacity: 1;
      }

      &.p-highlight {
        background-color: #f0f9ff !important;

        .p-sortable-column-icon {
          opacity: 1;
          color: #0ea5e9;
        }
      }
    }
  }

  /* Checkbox styles */
  .p-checkbox .p-checkbox-box {
    border: 2px solid #0ea5e9;
    border-radius: 6px;
    transition: all 0.2s;

    &:not(.p-disabled):hover {
      border-color: #0284c7;
      box-shadow: 0 0 0 1px rgba(14, 165, 233, 0.3);
      transform: scale(1.05);
    }

    &.p-highlight {
      border-color: #0ea5e9;
      background: #0ea5e9;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.4);
    }
  }

  .p-checkbox-lg .p-checkbox-box {
    width: 22px;
    height: 22px;
  }

  /* Button styles */
  .p-button.p-button-primary {
    background: #0ea5e9;
    border-color: #0ea5e9;
    transition: all 0.2s;

    &:hover {
      background: #0284c7;
      border-color: #0284c7;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.4);
    }
  }

  /* Paginator styles */
  .p-paginator {
    .p-paginator-current {
      margin-left: auto;
    }

    .p-paginator-page {
      transition: all 0.2s;

      &.p-highlight {
        background: #e0f2fe;
        border-color: #e0f2fe;
        color: #0284c7;
        font-weight: 600;
      }

      &:not(.p-highlight):hover {
        background: #f0f9ff;
        transform: translateY(-1px);
      }
    }
  }

  /* Fix for paginator dropdown - Force upward opening */
  .p-paginator {
    /* Add bottom margin to create space */
    margin-bottom: 120px !important;
    /* Reduce paginator height */
    padding: 0.5rem 1rem !important;
    min-height: auto !important;

    .p-dropdown {
      /* Ensure dropdown container has relative positioning */
      position: relative !important;

      /* Force dropdown to open upward */
      .p-dropdown-panel {
        z-index: 99999 !important;
        /* Position relative to the dropdown button, not the table */
        position: absolute !important;
        /* Position it just above the dropdown button */
        top: auto !important;
        bottom: calc(100% + 4px) !important;
        /* Keep horizontal alignment with the button */
        left: 0 !important;
        right: auto !important;
        /* Remove any transforms that might misposition it */
        transform: none !important;
      }
    }

    /* Reduce height of paginator elements */
    .p-paginator-element {
      padding: 0.25rem 0.5rem !important;
      min-height: 2rem !important;
      height: 2rem !important;
    }

    .p-paginator-page,
    .p-paginator-next,
    .p-paginator-prev,
    .p-paginator-first,
    .p-paginator-last {
      padding: 0.25rem 0.5rem !important;
      min-height: 2rem !important;
      height: 2rem !important;
      line-height: 1.5 !important;
    }

    .p-dropdown {
      height: 2rem !important;

      .p-dropdown-label {
        padding: 0.25rem 0.5rem !important;
        line-height: 1.5 !important;
      }

      .p-dropdown-trigger {
        height: 2rem !important;
        width: 1.5rem !important;
      }
    }

    .p-paginator-current {
      padding: 0.25rem 0.5rem !important;
      line-height: 1.5 !important;
    }
  }

  /* Type filter buttons */
  .flex.flex-wrap.gap-3 > div {
    min-width: 140px;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;

    /* Add subtle background pattern for selected buttons */
    &.bg-blue-600, &.bg-green-600, &.bg-purple-600, &.bg-cyan-700 {
      background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      /* Subtle pulse animation for selected buttons */
      animation: subtle-pulse 3s infinite;

      /* Improve text readability with text shadow */
      .font-medium, .text-xs, i {
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      /* Make icons slightly larger and brighter in selected state */
      i {
        font-size: 1.25rem;
        opacity: 0.9;
      }
    }

    /* Specific styling for each type when selected */
    &.bg-blue-600 {
      border-bottom: 3px solid #2563eb;
    }

    &.bg-green-600 {
      border-bottom: 3px solid #16a34a;
    }

    &.bg-purple-600 {
      border-bottom: 3px solid #9333ea;
    }

    &.bg-cyan-700 {
      border-bottom: 3px solid #0e7490;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }

    /* Active state */
    &:active {
      transform: translateY(-1px);
      transition: all 0.1s;
    }

    /* Count badge styling */
    .text-xs {
      font-weight: 600;
      letter-spacing: 0.01em;
    }

    /* Active indicator dot */
    .absolute.rounded-full {
      box-shadow: 0 0 0 2px white;
      animation: pulse 2s infinite;
    }
  }

  /* Animations */
  @keyframes subtle-pulse {
    0% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    50% {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    100% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    70% {
      box-shadow: 0 0 0 5px rgba(255, 255, 255, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
  }

  /* Badge styles for activity types */
  .px-2.py-1.rounded-full {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* ===== PREMIUM SPINNER SIMPLE ===== */

  .premium-spinner-simple {
    position: relative;
    width: 50px;
    height: 50px;
    display: inline-block;

    .premium-spinner-ring {
      position: absolute;
      border: 3px solid transparent;
      border-radius: 50%;
      animation: spin 1.5s linear infinite;

      &:nth-child(1) {
        width: 50px;
        height: 50px;
        border-top: 3px solid #0ea5e9;
        border-right: 3px solid #0ea5e9;
        animation-duration: 1.2s;
      }

      &:nth-child(2) {
        width: 40px;
        height: 40px;
        top: 5px;
        left: 5px;
        border-bottom: 3px solid #06b6d4;
        border-left: 3px solid #06b6d4;
        animation-duration: 1.8s;
        animation-direction: reverse;
      }

      &:nth-child(3) {
        width: 30px;
        height: 30px;
        top: 10px;
        left: 10px;
        border: 3px solid #0891b2;
        border-top: 3px solid transparent;
        animation-duration: 1s;
      }
    }
  }
}



  /* Animation pour le spinner */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  :host ::ng-deep {
    .p-datatable .p-datatable-thead > tr > th,
    .p-datatable .p-datatable-tbody > tr > td {
      padding: 0.5rem;
    }

    /* Adjust type filter buttons for smaller screens */
    .flex.flex-wrap.gap-3 > div {
      min-width: 110px;
      padding: 0.625rem;
      margin-bottom: 0.5rem;

      /* Slightly reduce animations on mobile for better performance */
      animation-duration: 4s;

      /* Adjust typography for smaller screens */
      .font-medium {
        font-size: 0.8125rem;
      }

      .text-xs {
        font-size: 0.6875rem;
      }

      i {
        font-size: 1rem;
      }

      /* Reduce hover effect on touch devices */
      &:hover {
        transform: translateY(-2px);
      }
    }

    /* Mobile adjustments for spinner */
    .premium-spinner-simple {
      width: 40px;
      height: 40px;

      .premium-spinner-ring {
        &:nth-child(1) {
          width: 40px;
          height: 40px;
        }

        &:nth-child(2) {
          width: 32px;
          height: 32px;
          top: 4px;
          left: 4px;
        }

        &:nth-child(3) {
          width: 24px;
          height: 24px;
          top: 8px;
          left: 8px;
        }
      }
    }
  }
}

/* Additional responsive adjustments for very small screens */
@media screen and (max-width: 480px) {
  :host ::ng-deep {
    /* Make type filter buttons more compact */
    .flex.flex-wrap.gap-3 {
      gap: 0.5rem !important;
    }

    .flex.flex-wrap.gap-3 > div {
      min-width: calc(50% - 0.5rem);
      padding: 0.5rem;

      /* Simplify animations for better performance */
      animation: none;

      /* Adjust the active indicator position */
      .absolute.rounded-full {
        width: 0.5rem;
        height: 0.5rem;
        top: 0.25rem;
        right: 0.25rem;
        animation: none;
      }
    }
  }
}
