<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  AffectationPraticienComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-affectation-praticien</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                                <code><a href="../components/OverviewComponent.html" target="_self" >OverviewComponent</a></code>
                                <code><a href="../components/ResumeProfilsComponent.html" target="_self" >ResumeProfilsComponent</a></code>
                                <code><a href="../components/BreadcrumbComponent.html" target="_self" >BreadcrumbComponent</a></code>
                            <code>ChartModule</code>
                            <code>StyleClassModule</code>
                            <code>NgForOf</code>
                            <code>DialogModule</code>
                            <code>FieldsetModule</code>
                            <code>AccordionModule</code>
                            <code>AvatarModule</code>
                            <code>BadgeModule</code>
                            <code>TableModule</code>
                            <code>NgIf</code>
                            <code>NgClass</code>
                            <code>StepsModule</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./affectation-praticien.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./affectation-praticien.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#activeGraphe" >activeGraphe</a>
                            </li>
                            <li>
                                <a href="#activeTable" >activeTable</a>
                            </li>
                            <li>
                                <a href="#anneeN" >anneeN</a>
                            </li>
                            <li>
                                <a href="#anneeNmoinsUn" >anneeNmoinsUn</a>
                            </li>
                            <li>
                                <a href="#breadcrumbItems" >breadcrumbItems</a>
                            </li>
                            <li>
                                <a href="#ccamBarChartData" >ccamBarChartData</a>
                            </li>
                            <li>
                                <a href="#ccamData" >ccamData</a>
                            </li>
                            <li>
                                <a href="#ccamLineChartData" >ccamLineChartData</a>
                            </li>
                            <li>
                                <a href="#ccamMonthlyTableData" >ccamMonthlyTableData</a>
                            </li>
                            <li>
                                <a href="#ccamWeeklyTableData" >ccamWeeklyTableData</a>
                            </li>
                            <li>
                                <a href="#chartOptions" >chartOptions</a>
                            </li>
                            <li>
                                <a href="#dialogState" >dialogState</a>
                            </li>
                            <li>
                                <a href="#laboBarChartData" >laboBarChartData</a>
                            </li>
                            <li>
                                <a href="#laboData" >laboData</a>
                            </li>
                            <li>
                                <a href="#laboLineChartData" >laboLineChartData</a>
                            </li>
                            <li>
                                <a href="#laboMonthlyTableData" >laboMonthlyTableData</a>
                            </li>
                            <li>
                                <a href="#laboWeeklyTableData" >laboWeeklyTableData</a>
                            </li>
                            <li>
                                <a href="#ngapBarChartData" >ngapBarChartData</a>
                            </li>
                            <li>
                                <a href="#ngapData" >ngapData</a>
                            </li>
                            <li>
                                <a href="#ngapLineChartData" >ngapLineChartData</a>
                            </li>
                            <li>
                                <a href="#ngapMonthlyTableData" >ngapMonthlyTableData</a>
                            </li>
                            <li>
                                <a href="#ngapWeeklyTableData" >ngapWeeklyTableData</a>
                            </li>
                            <li>
                                <a href="#praticienData" >praticienData</a>
                            </li>
                            <li>
                                <a href="#praticienInfo" >praticienInfo</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#getTotalForYearByTypeActe" >getTotalForYearByTypeActe</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#initCcamData" >initCcamData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#initLaboData" >initLaboData</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#initNgapData" >initNgapData</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#onInput" >onInput</a>
                            </li>
                            <li>
                                <a href="#showDialog" >showDialog</a>
                            </li>
                            <li>
                                <a href="#showGraphe" >showGraphe</a>
                            </li>
                            <li>
                                <a href="#showTable" >showTable</a>
                            </li>
                            <li>
                                <a href="#viewActeDetails" >viewActeDetails</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(singlePraticienService: SinglePraticienService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="106" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:106</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>singlePraticienService</td>

                                                        <td>
                                                                    <code>SinglePraticienService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getTotalForYearByTypeActe"></a>
                    <span class="name">
                        <span ><b>getTotalForYearByTypeActe</b></span>
                        <a href="#getTotalForYearByTypeActe"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getTotalForYearByTypeActe(year: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, typeActe: "CCAM" | "NGAP" | "Labo")</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="252"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:252</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>year</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>typeActe</td>
                                            <td>
                                                        <code>&quot;CCAM&quot; | &quot;NGAP&quot; | &quot;Labo&quot;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initCcamData"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>initCcamData</b></span>
                        <a href="#initCcamData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initCcamData(ccam: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="127"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:127</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ccam</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initLaboData"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>initLaboData</b></span>
                        <a href="#initLaboData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initLaboData(labo: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="210"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:210</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>labo</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initNgapData"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>initNgapData</b></span>
                        <a href="#initNgapData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>initNgapData(ngap: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="170"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:170</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ngap</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="110"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:110</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onInput"></a>
                    <span class="name">
                        <span ><b>onInput</b></span>
                        <a href="#onInput"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onInput(event: Event)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="292"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:292</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                        <code>Event</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="showDialog"></a>
                    <span class="name">
                        <span ><b>showDialog</b></span>
                        <a href="#showDialog"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>showDialog(dialogType: "evolutionMensuelle" | "repartitionHebdomadaire" | "evolutionMensuelleCCAM" | "repartitionHebdomadaireCCAM" | "evolutionMensuelleLABO" | "repartitionHebdomadaireLABO")</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="288"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:288</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>dialogType</td>
                                            <td>
                                                        <code>&quot;evolutionMensuelle&quot; | &quot;repartitionHebdomadaire&quot; | &quot;evolutionMensuelleCCAM&quot; | &quot;repartitionHebdomadaireCCAM&quot; | &quot;evolutionMensuelleLABO&quot; | &quot;repartitionHebdomadaireLABO&quot;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="showGraphe"></a>
                    <span class="name">
                        <span ><b>showGraphe</b></span>
                        <a href="#showGraphe"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>showGraphe(grapheType: "CCAMGRAPHE" | "NGAPGRAPHE" | "LABOGRAPHE")</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="309"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:309</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>grapheType</td>
                                            <td>
                                                        <code>&quot;CCAMGRAPHE&quot; | &quot;NGAPGRAPHE&quot; | &quot;LABOGRAPHE&quot;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="showTable"></a>
                    <span class="name">
                        <span ><b>showTable</b></span>
                        <a href="#showTable"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>showTable(tableType: "CCAM" | "NGAP" | "LABO")</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="305"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:305</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>tableType</td>
                                            <td>
                                                        <code>&quot;CCAM&quot; | &quot;NGAP&quot; | &quot;LABO&quot;</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="viewActeDetails"></a>
                    <span class="name">
                        <span ><b>viewActeDetails</b></span>
                        <a href="#viewActeDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>viewActeDetails(acte: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="297"
                                    class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:297</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>acte</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="activeGraphe"></a>
                    <span class="name">
                        <span ><b>activeGraphe</b></span>
                        <a href="#activeGraphe"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>&quot;CCAMGRAPHE&quot; | &quot;NGAPGRAPHE&quot; | &quot;LABOGRAPHE&quot;</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;CCAMGRAPHE&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="303" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:303</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="activeTable"></a>
                    <span class="name">
                        <span ><b>activeTable</b></span>
                        <a href="#activeTable"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>&quot;CCAM&quot; | &quot;NGAP&quot; | &quot;LABO&quot;</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;CCAM&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="302" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:302</a></div>
                        </td>
                    </tr>

            <tr>
                <td class="col-md-4">
                    <div class="io-description"><p>STEPPER</p>
</div>
                </td>
            </tr>

        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="anneeN"></a>
                    <span class="name">
                        <span ><b>anneeN</b></span>
                        <a href="#anneeN"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;2024&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="102" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:102</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="anneeNmoinsUn"></a>
                    <span class="name">
                        <span ><b>anneeNmoinsUn</b></span>
                        <a href="#anneeNmoinsUn"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&quot;2023&quot;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="101" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:101</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="breadcrumbItems"></a>
                    <span class="name">
                        <span ><b>breadcrumbItems</b></span>
                        <a href="#breadcrumbItems"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>BreadcrumbItem[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
    { label: &#x27;Praticiens&#x27;, url: &#x27;/single-praticien&#x27; },
    { label: &#x27;Récapitulatif des actes du praticien&#x27; }
  ]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="49" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:49</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamBarChartData"></a>
                    <span class="name">
                        <span ><b>ccamBarChartData</b></span>
                        <a href="#ccamBarChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="55" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:55</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamData"></a>
                    <span class="name">
                        <span ><b>ccamData</b></span>
                        <a href="#ccamData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>TableauComparatif[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="104" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:104</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamLineChartData"></a>
                    <span class="name">
                        <span ><b>ccamLineChartData</b></span>
                        <a href="#ccamLineChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="54" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:54</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamMonthlyTableData"></a>
                    <span class="name">
                        <span ><b>ccamMonthlyTableData</b></span>
                        <a href="#ccamMonthlyTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="56" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:56</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ccamWeeklyTableData"></a>
                    <span class="name">
                        <span ><b>ccamWeeklyTableData</b></span>
                        <a href="#ccamWeeklyTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="57" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:57</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chartOptions"></a>
                    <span class="name">
                        <span ><b>chartOptions</b></span>
                        <a href="#chartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio:0.8,
    // scales: {
    //   x: {
    //     title: {
    //       display: true,
    //       text: &#x27;Jours de la semaine&#x27;,
    //     },
    //     ticks: {
    //       autoSkip: false,
    //     },
    //   },
    //   y: {
    //     beginAtZero: true,
    //     title: {
    //       display: true,
    //       text: &#x27;Nombre d\&#x27;actes CCAM&#x27;
    //     }
    //   }
    // },
    plugins: {
      legend: {
        position: &#x27;top&#x27;,
      },
    },
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="72" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:72</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="dialogState"></a>
                    <span class="name">
                        <span ><b>dialogState</b></span>
                        <a href="#dialogState"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    evolutionMensuelle: false,
    repartitionHebdomadaire: false,
    evolutionMensuelleCCAM: false,
    repartitionHebdomadaireCCAM: false,
    evolutionMensuelleLABO: false, // Pour le LABO
    repartitionHebdomadaireLABO: false, // Pour le LABO
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="279" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:279</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="laboBarChartData"></a>
                    <span class="name">
                        <span ><b>laboBarChartData</b></span>
                        <a href="#laboBarChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="67" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:67</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="laboData"></a>
                    <span class="name">
                        <span ><b>laboData</b></span>
                        <a href="#laboData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>TableauComparatif[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="106" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:106</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="laboLineChartData"></a>
                    <span class="name">
                        <span ><b>laboLineChartData</b></span>
                        <a href="#laboLineChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="66" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:66</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="laboMonthlyTableData"></a>
                    <span class="name">
                        <span ><b>laboMonthlyTableData</b></span>
                        <a href="#laboMonthlyTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="68" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:68</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="laboWeeklyTableData"></a>
                    <span class="name">
                        <span ><b>laboWeeklyTableData</b></span>
                        <a href="#laboWeeklyTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="69" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:69</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapBarChartData"></a>
                    <span class="name">
                        <span ><b>ngapBarChartData</b></span>
                        <a href="#ngapBarChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="61" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:61</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapData"></a>
                    <span class="name">
                        <span ><b>ngapData</b></span>
                        <a href="#ngapData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>TableauComparatif[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="105" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:105</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapLineChartData"></a>
                    <span class="name">
                        <span ><b>ngapLineChartData</b></span>
                        <a href="#ngapLineChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="60" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:60</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapMonthlyTableData"></a>
                    <span class="name">
                        <span ><b>ngapMonthlyTableData</b></span>
                        <a href="#ngapMonthlyTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="62" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:62</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapWeeklyTableData"></a>
                    <span class="name">
                        <span ><b>ngapWeeklyTableData</b></span>
                        <a href="#ngapWeeklyTableData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="63" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:63</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="praticienData"></a>
                    <span class="name">
                        <span ><b>praticienData</b></span>
                        <a href="#praticienData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>RepartitionUF[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="45" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:45</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="praticienInfo"></a>
                    <span class="name">
                        <span ><b>praticienInfo</b></span>
                        <a href="#praticienInfo"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="46" class="link-to-prism">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts:46</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import { Chart } from &#x27;chart.js/auto&#x27;;
import {OverviewComponent} from &quot;../../../pages/overview/overview.component&quot;;
import {ResumeProfilsComponent} from &quot;../../../pages/resume-profils/resume-profils.component&quot;;
import {BreadcrumbComponent} from &quot;../../../pages/breadcrumb/breadcrumb.component&quot;;
import {BreadcrumbItem} from &quot;../../../core/models/breadcrumbItem&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {SinglePraticienService} from &quot;../../../core/services/overview/single-praticien-overview.service&quot;;
import {StyleClassModule} from &quot;primeng/styleclass&quot;;
import {NgClass, NgForOf, NgIf} from &quot;@angular/common&quot;;
import {DialogModule} from &quot;primeng/dialog&quot;;
import {FieldsetModule} from &quot;primeng/fieldset&quot;;
import {AccordionModule} from &quot;primeng/accordion&quot;;
import {AvatarModule} from &quot;primeng/avatar&quot;;
import {BadgeModule} from &quot;primeng/badge&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {RepartitionUF, TableauComparatif} from &quot;../../../core/models/overview/singlePraticien-overview&quot;;
import {StepsModule} from &quot;primeng/steps&quot;;


@Component({
  selector: &#x27;app-affectation-praticien&#x27;,
  standalone: true,
  imports: [
    OverviewComponent,
    ResumeProfilsComponent,
    BreadcrumbComponent,
    ChartModule,
    StyleClassModule,
    NgForOf,
    DialogModule,
    FieldsetModule,
    AccordionModule,
    AvatarModule,
    BadgeModule,
    TableModule,
    NgIf,
    NgClass,
    StepsModule
  ],
  templateUrl: &#x27;./affectation-praticien.component.html&#x27;,
  styleUrl: &#x27;./affectation-praticien.component.scss&#x27;
})
export class AffectationPraticienComponent implements OnInit{
  praticienData: RepartitionUF[] &#x3D; [];
  praticienInfo: any;

  // fil d&#x27;ariane
  breadcrumbItems: BreadcrumbItem[] &#x3D; [
    { label: &#x27;Praticiens&#x27;, url: &#x27;/single-praticien&#x27; },
    { label: &#x27;Récapitulatif des actes du praticien&#x27; }
  ];
  //ccam
  ccamLineChartData: any;
  ccamBarChartData: any;
  ccamMonthlyTableData: any[] &#x3D; [];
  ccamWeeklyTableData: any[] &#x3D; [];

  //ngap
  ngapLineChartData: any;
  ngapBarChartData: any;
  ngapMonthlyTableData: any[] &#x3D; [];
  ngapWeeklyTableData: any[] &#x3D; [];

  //labo
  laboLineChartData: any;
  laboBarChartData: any;
  laboMonthlyTableData: any[] &#x3D; [];
  laboWeeklyTableData: any[] &#x3D; [];


  chartOptions &#x3D; {
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio:0.8,
    // scales: {
    //   x: {
    //     title: {
    //       display: true,
    //       text: &#x27;Jours de la semaine&#x27;,
    //     },
    //     ticks: {
    //       autoSkip: false,
    //     },
    //   },
    //   y: {
    //     beginAtZero: true,
    //     title: {
    //       display: true,
    //       text: &#x27;Nombre d\&#x27;actes CCAM&#x27;
    //     }
    //   }
    // },
    plugins: {
      legend: {
        position: &#x27;top&#x27;,
      },
    },
  };

  anneeNmoinsUn &#x3D; &quot;2023&quot;
  anneeN &#x3D; &quot;2024&quot;
  //
  ccamData: TableauComparatif[] &#x3D; [];
  ngapData: TableauComparatif[] &#x3D; [];
  laboData: TableauComparatif[] &#x3D; [];

  constructor(private singlePraticienService: SinglePraticienService) {}

  ngOnInit(): void {
    this.singlePraticienService.praticienData$.subscribe((data) &#x3D;&gt; {
      if (data) {
        this.initNgapData(data.ngap);
        this.initCcamData(data.ccam);
        this.initLaboData(data.labo);
        //
        this.ccamData &#x3D; data.ccam.tableauComparatif;
        this.ngapData &#x3D; data.ngap.tableauComparatif;
        this.laboData &#x3D; data.labo.tableauComparatif;
        //
        this.praticienData &#x3D; data.praticien.repartitionUF; // Données pour le tableau
        this.praticienInfo &#x3D; data.praticien; // Autres informations du praticien
      }
    });
  }

  private initCcamData(ccam: any): void {
    // Line Chart pour l&#x27;évolution mensuelle
    this.ccamLineChartData &#x3D; {
      labels: ccam.evolutionMensuelle.map((item: any) &#x3D;&gt; item.mois),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ccam.evolutionMensuelle.map((item: any) &#x3D;&gt; item.anneeNmoinsUn),
          borderColor: &#x27;#66BB6A&#x27;,
          fill: false,
        },
        {
          label: this.anneeN,
          data: ccam.evolutionMensuelle.map((item: any) &#x3D;&gt; item.anneeN),
          borderColor: &#x27;#FFCA28&#x27;,
          fill: false,
        },
      ],
    };

    // Bar Chart pour la répartition hebdomadaire
    this.ccamBarChartData &#x3D; {
      labels: ccam.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.semaine),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ccam.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.anneeNmoinsUn),
          backgroundColor: &#x27;#66BB6A&#x27;,
        },
        {
          label: this.anneeN,
          data: ccam.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.anneeN),
          backgroundColor: &#x27;#FFCA28&#x27;,
        },
      ],
    };

    // Tableaux pour les données mensuelles et hebdomadaires
    this.ccamMonthlyTableData &#x3D; ccam.evolutionMensuelle;
    this.ccamWeeklyTableData &#x3D; ccam.repartitionHebdomadaire;
  }


  private initNgapData(ngap: any): void {
    this.ngapLineChartData &#x3D; {
      labels: ngap.evolutionMensuelle.map((item: any) &#x3D;&gt; item.mois),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ngap.evolutionMensuelle.map((item: any) &#x3D;&gt; item.anneeNmoinsUn),
          borderColor: &#x27;#42A5F5&#x27;,
          fill: false,
        },
        {
          label: this.anneeN,
          data: ngap.evolutionMensuelle.map((item: any) &#x3D;&gt; item.anneeN),
          borderColor: &#x27;#FFA726&#x27;,
          fill: false,
        },
      ],
    };

    this.ngapBarChartData &#x3D; {
      labels: ngap.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.semaine),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ngap.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.anneeNmoinsUn),
          backgroundColor: &#x27;#42A5F5&#x27;,
        },
        {
          label: this.anneeN,
          data: ngap.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.anneeN),
          backgroundColor: &#x27;#FFA726&#x27;,
        },
      ],
    };

    this.ngapMonthlyTableData &#x3D; ngap.evolutionMensuelle;
    this.ngapWeeklyTableData &#x3D; ngap.repartitionHebdomadaire;
  }


  private initLaboData(labo: any): void {
    // Line Chart pour l&#x27;évolution mensuelle
    this.laboLineChartData &#x3D; {
      labels: labo.evolutionMensuelle.map((item: any) &#x3D;&gt; item.mois),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: labo.evolutionMensuelle.map((item: any) &#x3D;&gt; item.anneeNmoinsUn),
          borderColor: &#x27;#AB47BC&#x27;, // Couleur pour LABO - Année N-1
          fill: false,
        },
        {
          label: this.anneeN,
          data: labo.evolutionMensuelle.map((item: any) &#x3D;&gt; item.anneeN),
          borderColor: &#x27;#FF7043&#x27;, // Couleur pour LABO - Année N
          fill: false,
        },
      ],
    };

    // Bar Chart pour la répartition hebdomadaire
    this.laboBarChartData &#x3D; {
      labels: labo.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.semaine),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: labo.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.anneeNmoinsUn),
          backgroundColor: &#x27;#AB47BC&#x27;, // Couleur pour LABO - Année N-1
        },
        {
          label: this.anneeN,
          data: labo.repartitionHebdomadaire.map((item: any) &#x3D;&gt; item.anneeN),
          backgroundColor: &#x27;#FF7043&#x27;, // Couleur pour LABO - Année N
        },
      ],
    };

    // Tableaux pour les données mensuelles et hebdomadaires
    this.laboMonthlyTableData &#x3D; labo.evolutionMensuelle;
    this.laboWeeklyTableData &#x3D; labo.repartitionHebdomadaire;
  }

  getTotalForYearByTypeActe(year: string, typeActe: &#x27;CCAM&#x27; | &#x27;NGAP&#x27; | &#x27;Labo&#x27;): number {
    let data: TableauComparatif[] &#x3D; [];

    // Select the appropriate data based on the type of act
    switch (typeActe) {
      case &#x27;CCAM&#x27;:
        data &#x3D; this.ccamData;
        break;
      case &#x27;NGAP&#x27;:
        data &#x3D; this.ngapData;
        break;
      case &#x27;Labo&#x27;:
        data &#x3D; this.laboData;
        break;
      default:
        return 0;
    }

    // Sum the total for the specified year
    let total &#x3D; data.reduce((sum, item) &#x3D;&gt; {
      return sum + (year &#x3D;&#x3D;&#x3D; &#x27;2023&#x27; ? item.totalAnneeNmoinsUn : item.totalAnneeN);
    }, 0);

    return total;
  }


  dialogState &#x3D; {
    evolutionMensuelle: false,
    repartitionHebdomadaire: false,
    evolutionMensuelleCCAM: false,
    repartitionHebdomadaireCCAM: false,
    evolutionMensuelleLABO: false, // Pour le LABO
    repartitionHebdomadaireLABO: false, // Pour le LABO
  };

  showDialog(dialogType: &#x27;evolutionMensuelle&#x27; | &#x27;repartitionHebdomadaire&#x27; | &#x27;evolutionMensuelleCCAM&#x27; | &#x27;repartitionHebdomadaireCCAM&#x27; | &#x27;evolutionMensuelleLABO&#x27; | &#x27;repartitionHebdomadaireLABO&#x27;): void {
    this.dialogState[dialogType] &#x3D; true;
  }

  onInput(event: Event): string {
    //console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }

  viewActeDetails(acte: any) {
    alert(&#x60;${acte.name}: ${acte.code}&#x60;);
  }

  /************************************* STEPPER ************************************************************/
  activeTable: &#x27;CCAM&#x27; | &#x27;NGAP&#x27; | &#x27;LABO&#x27; &#x3D; &#x27;CCAM&#x27;;
  activeGraphe: &#x27;CCAMGRAPHE&#x27; | &#x27;NGAPGRAPHE&#x27; | &#x27;LABOGRAPHE&#x27; &#x3D; &#x27;CCAMGRAPHE&#x27;;

  showTable(tableType: &#x27;CCAM&#x27; | &#x27;NGAP&#x27; | &#x27;LABO&#x27; ): void {

    this.activeTable &#x3D; tableType;
  }
  showGraphe(grapheType: &#x27;CCAMGRAPHE&#x27; | &#x27;NGAPGRAPHE&#x27; | &#x27;LABOGRAPHE&#x27; ): void {

    this.activeGraphe &#x3D; grapheType;
  }
    /******************************************************************************************************/
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">
&lt;div class&#x3D;&quot;bg-white shadow&quot;&gt;
  &lt;app-breadcrumb [items]&#x3D;&quot;breadcrumbItems&quot;&gt;&lt;/app-breadcrumb&gt;

  &lt;div class&#x3D;&quot;px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8&quot;&gt;
    &lt;app-resume-profils&gt;&lt;/app-resume-profils&gt;
  &lt;/div&gt;
&lt;/div&gt;

&lt;!--&lt;app-overview&gt;&lt;/app-overview&gt;--&gt;
&lt;!--DEBUT TABLEAU COMPARATIVE --&gt;
&lt;section class&#x3D;&quot;lg:border-t&quot;&gt;
  &lt;p-table
    [value]&#x3D;&quot;praticienData&quot;
    [paginator]&#x3D;&quot;true&quot;
    [rows]&#x3D;&quot;5&quot;
    [rowsPerPageOptions]&#x3D;&quot;[5, 10, 20]&quot;
    responsiveLayout&#x3D;&quot;scroll&quot;
  &gt;
    &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
      &lt;tr&gt;
        &lt;th pSortableColumn&#x3D;&quot;uf&quot;&gt;UF &lt;p-sortIcon field&#x3D;&quot;uf&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;repartition&quot;&gt;Répartition &lt;p-sortIcon field&#x3D;&quot;repartition&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;th&gt;Répartition Réelle&lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;effectifPraticienDansUF&quot;&gt;Effectif Praticien dans UF &lt;p-sortIcon field&#x3D;&quot;effectifPraticienDansUF&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;effectifUF&quot;&gt;Effectif UF &lt;p-sortIcon field&#x3D;&quot;effectifUF&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;partPraticienDansUF&quot;&gt;Part du Praticien dans UF &lt;p-sortIcon field&#x3D;&quot;partPraticienDansUF&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;th&gt;Part Réelle&lt;/th&gt;
        &lt;th&gt;Entrée&lt;/th&gt;
        &lt;th pSortableColumn&#x3D;&quot;sortie&quot;&gt;Sortie &lt;p-sortIcon field&#x3D;&quot;sortie&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;

    &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-uf&gt;
      &lt;tr&gt;
        &lt;td&gt;{{ uf.uf }}&lt;/td&gt;
        &lt;td&gt;{{ uf.repartition }}&lt;/td&gt;
        &lt;td&gt;
          &lt;span class&#x3D;&quot;block text-xs text-gray-500&quot;&gt;CCAM: {{ uf.repartitionReelle.ccam }}&lt;/span&gt;
          &lt;span class&#x3D;&quot;block text-xs text-gray-500&quot;&gt;NGAP: {{ uf.repartitionReelle.ngap }}&lt;/span&gt;
          &lt;span class&#x3D;&quot;block text-xs text-gray-500&quot;&gt;LABO: {{ uf.repartitionReelle.labo }}&lt;/span&gt;
        &lt;/td&gt;
        &lt;td&gt;{{ uf.effectifPraticienDansUF }}&lt;/td&gt;
        &lt;td&gt;{{ uf.effectifUF }}&lt;/td&gt;
        &lt;td&gt;{{ uf.partPraticienDansUF }}&lt;/td&gt;
        &lt;td&gt;
          &lt;span class&#x3D;&quot;block text-xs text-gray-500&quot;&gt;CCAM: {{ uf.partReelle.ccam }}&lt;/span&gt;
          &lt;span class&#x3D;&quot;block text-xs text-gray-500&quot;&gt;NGAP: {{ uf.partReelle.ngap }}&lt;/span&gt;
          &lt;span class&#x3D;&quot;block text-xs text-gray-500&quot;&gt;LABO: {{ uf.partReelle.labo }}&lt;/span&gt;
        &lt;/td&gt;
        &lt;td&gt;{{ uf.entree }}&lt;/td&gt;
        &lt;td&gt;{{ uf.sortie }}&lt;/td&gt;
      &lt;/tr&gt;
    &lt;/ng-template&gt;
  &lt;/p-table&gt;
&lt;/section&gt;


&lt;section class&#x3D;&quot;mt-8 &quot;&gt;
&lt;div class&#x3D;&quot;px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8&quot;&gt;
  &lt;ol class&#x3D;&quot;flex items-center w-full text-sm text-gray-500 font-medium sm:text-base&quot;&gt;
    &lt;!-- Step 1: CCAM --&gt;
    &lt;li

      class&#x3D;&quot;flex md:w-full items-center text-indigo-600 sm:after:content-[&#x27;&#x27;] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8&quot;
    &gt;
      &lt;div
        class&#x3D;&quot;flex items-center whitespace-nowrap after:content-[&#x27;/&#x27;] sm:after:hidden after:mx-2 cursor-pointer&quot;
        (click)&#x3D;&quot;showTable(&#x27;CCAM&#x27;)&quot;
      &gt;
      &lt;span
        [class.bg-indigo-600]&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;&quot;
        [class.text-white]&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;&quot;
        [class.bg-gray-100]&#x3D;&quot;activeTable !&#x3D;&#x3D; &#x27;CCAM&#x27;&quot;
        [class.text-gray-600]&#x3D;&quot;activeTable !&#x3D;&#x3D; &#x27;CCAM&#x27;&quot;
        class&#x3D;&quot;w-6 h-6  border border-indigo-200 rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10&quot;
      &gt;1&lt;/span
      &gt;
        CCAM
      &lt;/div&gt;
    &lt;/li&gt;

    &lt;!-- Step 2: NGAP --&gt;
    &lt;li
      class&#x3D;&quot;flex md:w-full items-center text-indigo-600 sm:after:content-[&#x27;&#x27;] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8&quot;
    &gt;
      &lt;div
        class&#x3D;&quot;flex items-center whitespace-nowrap after:content-[&#x27;/&#x27;] sm:after:hidden after:mx-2 cursor-pointer&quot;
        (click)&#x3D;&quot;showTable(&#x27;NGAP&#x27;)&quot;
      &gt;
      &lt;span
        [class.bg-indigo-600]&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;&quot;
        [class.text-white]&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;&quot;
        [class.bg-gray-100]&#x3D;&quot;activeTable !&#x3D;&#x3D; &#x27;NGAP&#x27;&quot;
        [class.text-gray-600]&#x3D;&quot;activeTable !&#x3D;&#x3D; &#x27;NGAP&#x27;&quot;
        class&#x3D;&quot;w-6 h-6 bg-gray-100 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10&quot;
      &gt;2&lt;/span
      &gt;
        NGAP
      &lt;/div&gt;
    &lt;/li&gt;

    &lt;!-- Step 3: LABO --&gt;
    &lt;li class&#x3D;&quot;flex md:w-full items-center text-indigo-600&quot;&gt;
      &lt;div
        class&#x3D;&quot;flex items-center cursor-pointer&quot;
        (click)&#x3D;&quot;showTable(&#x27;LABO&#x27;)&quot;
      &gt;
      &lt;span
        [class.bg-indigo-600]&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;LABO&#x27;&quot;
        [class.text-white]&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;LABO&#x27;&quot;
        [class.bg-gray-100]&#x3D;&quot;activeTable !&#x3D;&#x3D; &#x27;LABO&#x27;&quot;
        [class.text-gray-600]&#x3D;&quot;activeTable !&#x3D;&#x3D; &#x27;LABO&#x27;&quot;
        class&#x3D;&quot;w-6 h-6   border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10&quot;
      &gt;3&lt;/span
      &gt;
        LABO
      &lt;/div&gt;
    &lt;/li&gt;
  &lt;/ol&gt;
&lt;/div&gt;
  &lt;div *ngIf&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;&quot; class&#x3D;&quot;p-4&quot;&gt;
    &lt;h5 class&#x3D;&quot;text-lg font-bold mb-3&quot;&gt;
      Analyse comparative des actes CCAM [ {{anneeNmoinsUn}}-{{ anneeN }} ]
    &lt;/h5&gt;
    &lt;div&gt;
      &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
        &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
        &lt;/div&gt;
        &lt;input
          type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
          (input)&#x3D;&quot;ccamTableParActeParAnnee.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
          class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
          placeholder&#x3D;&quot;  Filtrer par ccam&quot;
        /&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    &lt;p-table
      #ccamTableParActeParAnnee
      [value]&#x3D;&quot;ccamData&quot;
      [scrollable]&#x3D;&quot;true&quot;
      scrollHeight&#x3D;&quot;400px&quot;
      [tableStyle]&#x3D;&quot;{&#x27;min-width&#x27;: &#x27;50rem&#x27;}&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;code&#x27;, &#x27;description&#x27;,&#x27;totalAnneeNMoins1&#x27;,&#x27;totalAnneeN&#x27;]&quot;
    &gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            &lt;i
              class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
              title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
            &gt;&lt;/i&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;code&quot;&gt;
            Code
            &lt;p-sortIcon field&#x3D;&quot;code&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;description&quot;&gt;
            Description
            &lt;p-sortIcon field&#x3D;&quot;description&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th  pSortableColumn&#x3D;&quot;totalAnneeNmoinsUn&quot; &gt;
            Total actes {{ anneeNmoinsUn }}
            &lt;p-sortIcon field&#x3D;&quot;totalAnneeNmoinsUn&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th  pSortableColumn&#x3D;&quot;totalAnneeN&quot; &gt;
            Total actes {{ anneeN }}
            &lt;p-sortIcon field&#x3D;&quot;totalAnneeN&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                title&#x3D;&quot;Voir les détails de {{acte.code }} {{acte.description}}&quot;
                (click)&#x3D;&quot;viewActeDetails(acte)&quot;
              &gt;&lt;/i&gt;
            &lt;/p&gt;
          &lt;/td&gt;
          &lt;td&gt;{{acte.code}}&lt;/td&gt;
          &lt;td&gt;{{acte.description}}&lt;/td&gt;
          &lt;td&gt; {{acte.totalAnneeNmoinsUn}}  &lt;/td&gt;
          &lt;td&gt; {{acte.totalAnneeN}}  &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
      &lt;!-- Ligne de total --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
        &lt;tr  &gt;
          &lt;td colspan&#x3D;&quot;3&quot; class&#x3D;&quot;font-bold text-left&quot;&gt;TOTAL&lt;/td&gt;
          &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(anneeNmoinsUn,&#x27;CCAM&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
          &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(anneeN,&#x27;CCAM&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-3&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
          &lt;td&gt;&lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;
  &lt;/div&gt;
  &lt;div *ngIf&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;&quot; class&#x3D;&quot;p-4&quot;&gt;
    &lt;h5 class&#x3D;&quot;text-lg font-bold mb-3&quot;&gt;
      Analyse comparative des actes NGAP [ {{anneeNmoinsUn}}-{{ anneeN }} ]
    &lt;/h5&gt;
    &lt;div&gt;
      &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
        &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
        &lt;/div&gt;
        &lt;input
          type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
          (input)&#x3D;&quot;ngapTableParActeParAnnee.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
          class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
          placeholder&#x3D;&quot;  Filtrer par ngap&quot;
        /&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    &lt;p-table
      #ngapTableParActeParAnnee
      [value]&#x3D;&quot;ngapData&quot;
      [scrollable]&#x3D;&quot;true&quot;
      scrollHeight&#x3D;&quot;400px&quot;
      [tableStyle]&#x3D;&quot;{&#x27;min-width&#x27;: &#x27;50rem&#x27;}&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;code&#x27;, &#x27;description&#x27;,&#x27;totalAnneeNMoins1&#x27;,&#x27;totalAnneeN&#x27;]&quot;
    &gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            &lt;i
              class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
              title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
            &gt;&lt;/i&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;code&quot;&gt;
            Code
            &lt;p-sortIcon field&#x3D;&quot;code&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;description&quot;&gt;
            Description
            &lt;p-sortIcon field&#x3D;&quot;description&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th  pSortableColumn&#x3D;&quot;totalAnneeNmoinsUn&quot; &gt;
            Total actes {{ anneeNmoinsUn }}
            &lt;p-sortIcon field&#x3D;&quot;totalAnneeNmoinsUn&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th  pSortableColumn&#x3D;&quot;totalAnneeN&quot; &gt;
            Total actes {{ anneeN }}
            &lt;p-sortIcon field&#x3D;&quot;totalAnneeN&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                title&#x3D;&quot;Voir les détails de {{acte.code }} {{acte.description}}&quot;
                (click)&#x3D;&quot;viewActeDetails(acte)&quot;
              &gt;&lt;/i&gt;
            &lt;/p&gt;
          &lt;/td&gt;
          &lt;td&gt;{{acte.code}}&lt;/td&gt;
          &lt;td&gt;{{acte.description}}&lt;/td&gt;
          &lt;td&gt; {{acte.totalAnneeNmoinsUn}}  &lt;/td&gt;
          &lt;td&gt; {{acte.totalAnneeN}}  &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
      &lt;!-- Ligne de total --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
        &lt;tr  &gt;
          &lt;td colspan&#x3D;&quot;3&quot; class&#x3D;&quot;font-bold text-left&quot;&gt;TOTAL&lt;/td&gt;
          &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(anneeNmoinsUn,&#x27;NGAP&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
          &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(anneeN,&#x27;NGAP&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-3&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
          &lt;td&gt;&lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;
  &lt;/div&gt;
  &lt;div *ngIf&#x3D;&quot;activeTable &#x3D;&#x3D;&#x3D; &#x27;LABO&#x27;&quot; class&#x3D;&quot;p-4&quot;&gt;
    &lt;h5 class&#x3D;&quot;text-lg font-bold mb-3&quot;&gt;
      Analyse comparative des actes LABO [ {{anneeNmoinsUn}}-{{ anneeN }} ]
    &lt;/h5&gt;
    &lt;div&gt;
      &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
        &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
        &lt;/div&gt;
        &lt;input
          type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
          (input)&#x3D;&quot;laboTableParActeParAnnee.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
          class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
          placeholder&#x3D;&quot;  Filtrer par nabm&quot;
        /&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    &lt;p-table
      #laboTableParActeParAnnee
      [value]&#x3D;&quot;laboData&quot;
      [scrollable]&#x3D;&quot;true&quot;
      scrollHeight&#x3D;&quot;400px&quot;
      [tableStyle]&#x3D;&quot;{&#x27;min-width&#x27;: &#x27;50rem&#x27;}&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;code&#x27;, &#x27;description&#x27;,&#x27;totalAnneeNmoinsUn&#x27;,&#x27;totalAnneeN&#x27;]&quot;
    &gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            &lt;i
              class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
              title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
            &gt;&lt;/i&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;code&quot;&gt;
            Code
            &lt;p-sortIcon field&#x3D;&quot;code&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;description&quot;&gt;
            Description
            &lt;p-sortIcon field&#x3D;&quot;description&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th  pSortableColumn&#x3D;&quot;totalAnneeNmoinsUn&quot; &gt;
            Total actes {{ anneeNmoinsUn }}
            &lt;p-sortIcon field&#x3D;&quot;totalAnneeNmoinsUn&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th  pSortableColumn&#x3D;&quot;totalAnneeN&quot; &gt;
            Total actes {{ anneeN }}
            &lt;p-sortIcon field&#x3D;&quot;totalAnneeN&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-acte&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                title&#x3D;&quot;Voir les détails de {{acte.code }} {{acte.description}}&quot;
                (click)&#x3D;&quot;viewActeDetails(acte)&quot;
              &gt;&lt;/i&gt;
            &lt;/p&gt;
          &lt;/td&gt;
          &lt;td&gt;{{acte.code}}&lt;/td&gt;
          &lt;td&gt;{{acte.description}}&lt;/td&gt;
          &lt;td&gt; {{acte.totalAnneeNmoinsUn}}  &lt;/td&gt;
          &lt;td&gt; {{acte.totalAnneeN}}  &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
      &lt;!-- Ligne de total --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
        &lt;tr  &gt;
          &lt;td colspan&#x3D;&quot;3&quot; class&#x3D;&quot;font-bold text-left&quot;&gt;TOTAL&lt;/td&gt;
          &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(anneeNmoinsUn,&#x27;Labo&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-2&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
          &lt;td class&#x3D;&quot;font-bold&quot;&gt;&lt;p-badge [value]&#x3D;&quot;getTotalForYearByTypeActe(anneeN,&#x27;Labo&#x27;)&quot; severity&#x3D;&quot;info&quot; styleClass&#x3D;&quot;ml-3&quot;&gt;&lt;/p-badge&gt;&lt;/td&gt;
          &lt;td&gt;&lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;
  &lt;/div&gt;
&lt;/section&gt;

&lt;!--DEBUT GRAPHIQUE--&gt;
&lt;section&gt;
  &lt;div class&#x3D;&quot;px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8&quot;&gt;
    &lt;ol class&#x3D;&quot;flex items-center w-full text-sm text-gray-500 font-medium sm:text-base&quot;&gt;
      &lt;!-- Step 1: CCAM --&gt;
      &lt;li

        class&#x3D;&quot;flex md:w-full items-center text-indigo-600 sm:after:content-[&#x27;&#x27;] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8&quot;
      &gt;
        &lt;div
          class&#x3D;&quot;flex items-center whitespace-nowrap after:content-[&#x27;/&#x27;] sm:after:hidden after:mx-2 cursor-pointer&quot;
          (click)&#x3D;&quot;showGraphe(&#x27;CCAMGRAPHE&#x27;)&quot;
        &gt;
      &lt;span
        [class.bg-indigo-600]&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;CCAMGRAPHE&#x27;&quot;
        [class.text-white]&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;CCAMGRAPHE&#x27;&quot;
        [class.bg-gray-100]&#x3D;&quot;activeGraphe !&#x3D;&#x3D; &#x27;CCAMGRAPHE&#x27;&quot;
        [class.text-gray-600]&#x3D;&quot;activeGraphe !&#x3D;&#x3D; &#x27;CCAMGRAPHE&#x27;&quot;
        class&#x3D;&quot;w-6 h-6  border border-indigo-200 rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10&quot;
      &gt;1&lt;/span
      &gt;
          CCAM
        &lt;/div&gt;
      &lt;/li&gt;

      &lt;!-- Step 2: NGAP --&gt;
      &lt;li
        class&#x3D;&quot;flex md:w-full items-center text-indigo-600 sm:after:content-[&#x27;&#x27;] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8&quot;
      &gt;
        &lt;div
          class&#x3D;&quot;flex items-center whitespace-nowrap after:content-[&#x27;/&#x27;] sm:after:hidden after:mx-2 cursor-pointer&quot;
          (click)&#x3D;&quot;showGraphe(&#x27;NGAPGRAPHE&#x27;)&quot;
        &gt;
      &lt;span
        [class.bg-indigo-600]&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;NGAPGRAPHE&#x27;&quot;
        [class.text-white]&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;NGAPGRAPHE&#x27;&quot;
        [class.bg-gray-100]&#x3D;&quot;activeGraphe !&#x3D;&#x3D; &#x27;NGAPGRAPHE&#x27;&quot;
        [class.text-gray-600]&#x3D;&quot;activeGraphe !&#x3D;&#x3D; &#x27;NGAPGRAPHE&#x27;&quot;
        class&#x3D;&quot;w-6 h-6 bg-gray-100 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10&quot;
      &gt;2&lt;/span
      &gt;
          NGAP
        &lt;/div&gt;
      &lt;/li&gt;

      &lt;!-- Step 3: LABO --&gt;
      &lt;li class&#x3D;&quot;flex md:w-full items-center text-indigo-600&quot;&gt;
        &lt;div
          class&#x3D;&quot;flex items-center cursor-pointer&quot;
          (click)&#x3D;&quot;showGraphe(&#x27;LABOGRAPHE&#x27;)&quot;
        &gt;
      &lt;span
        [class.bg-indigo-600]&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;LABOGRAPHE&#x27;&quot;
        [class.text-white]&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;LABOGRAPHE&#x27;&quot;
        [class.bg-gray-100]&#x3D;&quot;activeGraphe !&#x3D;&#x3D; &#x27;LABOGRAPHE&#x27;&quot;
        [class.text-gray-600]&#x3D;&quot;activeGraphe !&#x3D;&#x3D; &#x27;LABOGRAPHE&#x27;&quot;
        class&#x3D;&quot;w-6 h-6   border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10&quot;
      &gt;3&lt;/span
      &gt;
          LABO
        &lt;/div&gt;
      &lt;/li&gt;
    &lt;/ol&gt;
  &lt;/div&gt;
  &lt;section  *ngIf&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;CCAMGRAPHE&#x27;&quot;  class&#x3D;&quot;mb-8&quot;&gt;

    &lt;!-- Graphiques et tableaux --&gt;
    &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4&quot;&gt;
      &lt;!-- Line Chart pour l&#x27;évolution mensuelle --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg relative&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center&quot;&gt;
          Évolution mensuelle des actes CCAM
          &lt;button
            class&#x3D;&quot;text-blue-500 hover:underline text-sm flex items-center&quot;
            (click)&#x3D;&quot;showDialog(&#x27;evolutionMensuelleCCAM&#x27;)&quot;
          &gt;
            &lt;i class&#x3D;&quot;pi pi-expand mr-2&quot;&gt;&lt;/i&gt; Agrandir
          &lt;/button&gt;
        &lt;/h5&gt;
        &lt;p-chart
          type&#x3D;&quot;line&quot;
          [data]&#x3D;&quot;ccamLineChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;Données mensuelles&lt;/h5&gt;
        &lt;ul&gt;
          &lt;li *ngFor&#x3D;&quot;let item of ccamMonthlyTableData&quot; class&#x3D;&quot;flex justify-between py-2 border-b&quot;&gt;
            &lt;span&gt;{{ item.mois }}&lt;/span&gt;
            &lt;span&gt;{{ item.anneeNmoinsUn }} / {{ item.anneeN }}&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;

      &lt;!-- Bar Chart pour la répartition hebdomadaire --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg relative&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3 flex justify-between items-center&quot;&gt;
          Répartition hebdomadaire
          &lt;button
            class&#x3D;&quot;text-blue-500 hover:underline text-sm flex items-center&quot;
            (click)&#x3D;&quot;showDialog(&#x27;repartitionHebdomadaireCCAM&#x27;)&quot;
          &gt;
            &lt;i class&#x3D;&quot;pi pi-expand mr-2&quot;&gt;&lt;/i&gt; Agrandir
          &lt;/button&gt;
        &lt;/h5&gt;
        &lt;p-chart
          type&#x3D;&quot;bar&quot;
          [data]&#x3D;&quot;ccamBarChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;

      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;Données hebdomadaires&lt;/h5&gt;
        &lt;ul&gt;
          &lt;li *ngFor&#x3D;&quot;let item of ccamWeeklyTableData&quot; class&#x3D;&quot;flex justify-between py-2 border-b&quot;&gt;
            &lt;span&gt;{{ item.semaine }}&lt;/span&gt;
            &lt;span&gt;{{ item.anneeNmoinsUn }} / {{ item.anneeN }}&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Dialog pour Évolution mensuelle --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogState.evolutionMensuelleCCAM&quot;
      [modal]&#x3D;&quot;true&quot;
      [header]&#x3D;&quot;&#x27;Évolution mensuelle des actes CCAM&#x27;&quot;
      [style]&#x3D;&quot;{ width: &#x27;80vw&#x27; }&quot;
      [closable]&#x3D;&quot;true&quot;
      [dismissableMask]&#x3D;&quot;true&quot;
     &gt;
      &lt;div class&#x3D;&quot;p-4&quot;&gt;
        &lt;p-chart
          type&#x3D;&quot;line&quot;
          [data]&#x3D;&quot;ccamLineChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-96&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;
    &lt;/p-dialog&gt;

    &lt;!-- Dialog pour Répartition hebdomadaire --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogState.repartitionHebdomadaireCCAM&quot;
      [modal]&#x3D;&quot;true&quot;
      [header]&#x3D;&quot;&#x27;Répartition hebdomadaire des actes CCAM&#x27;&quot;
      [style]&#x3D;&quot;{ width: &#x27;80vw&#x27; }&quot;
      [closable]&#x3D;&quot;true&quot;
      [dismissableMask]&#x3D;&quot;true&quot;
    &gt;
      &lt;div class&#x3D;&quot;p-4&quot;&gt;
        &lt;p-chart
          type&#x3D;&quot;bar&quot;
          [data]&#x3D;&quot;ccamBarChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-96&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;
    &lt;/p-dialog&gt;
  &lt;/section&gt;
  &lt;section *ngIf&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;NGAPGRAPHE&#x27;&quot; class&#x3D;&quot;mb-8&quot;&gt;

    &lt;!-- Graphiques et tableaux --&gt;
    &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4&quot;&gt;
      &lt;!-- Line Chart pour l&#x27;évolution mensuelle --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg relative&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center&quot;&gt;
          Évolution mensuelle des actes NGAP
          &lt;button
            class&#x3D;&quot;text-blue-500 hover:underline text-sm flex items-center&quot;
            (click)&#x3D;&quot;showDialog(&#x27;evolutionMensuelle&#x27;)&quot;
          &gt;
            &lt;i class&#x3D;&quot;pi pi-expand mr-2&quot;&gt;&lt;/i&gt; Agrandir
          &lt;/button&gt;
        &lt;/h5&gt;
        &lt;p-chart
          type&#x3D;&quot;line&quot;
          [data]&#x3D;&quot;ngapLineChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;Données mensuelles&lt;/h5&gt;
        &lt;ul&gt;
          &lt;li *ngFor&#x3D;&quot;let item of ngapMonthlyTableData&quot; class&#x3D;&quot;flex justify-between py-2 border-b&quot;&gt;
            &lt;span&gt;{{ item.mois }}&lt;/span&gt;
            &lt;span&gt;{{ item.anneeNmoinsUn }} / {{ item.anneeN }}&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;

      &lt;!-- Bar Chart pour la répartition hebdomadaire --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg relative&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3 flex justify-between items-center&quot;&gt;
          Répartition hebdomadaire
          &lt;button
            class&#x3D;&quot;text-blue-500 hover:underline text-sm flex items-center&quot;
            (click)&#x3D;&quot;showDialog(&#x27;repartitionHebdomadaire&#x27;)&quot;
          &gt;
            &lt;i class&#x3D;&quot;pi pi-expand mr-2&quot;&gt;&lt;/i&gt; Agrandir
          &lt;/button&gt;
        &lt;/h5&gt;
        &lt;p-chart
          type&#x3D;&quot;bar&quot;
          [data]&#x3D;&quot;ngapBarChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;

      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;Données hebdomadaires&lt;/h5&gt;
        &lt;ul&gt;
          &lt;li *ngFor&#x3D;&quot;let item of ngapWeeklyTableData&quot; class&#x3D;&quot;flex justify-between py-2 border-b&quot;&gt;
            &lt;span&gt;{{ item.semaine }}&lt;/span&gt;
            &lt;span&gt;{{ item.anneeNmoinsUn }} / {{ item.anneeN }}&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Dialog pour Évolution mensuelle --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogState.evolutionMensuelle&quot;
      [modal]&#x3D;&quot;true&quot;
      [header]&#x3D;&quot;&#x27;Évolution mensuelle des actes NGAP&#x27;&quot;
      [style]&#x3D;&quot;{ width: &#x27;80vw&#x27; }&quot;
      [closable]&#x3D;&quot;true&quot;
      [dismissableMask]&#x3D;&quot;true&quot;
    &gt;
      &lt;div class&#x3D;&quot;p-4&quot;&gt;
        &lt;p-chart
          type&#x3D;&quot;line&quot;
          [data]&#x3D;&quot;ngapLineChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-96&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;
    &lt;/p-dialog&gt;

    &lt;!-- Dialog pour Répartition hebdomadaire --&gt;
    &lt;p-dialog
      [(visible)]&#x3D;&quot;dialogState.repartitionHebdomadaire&quot;
      [modal]&#x3D;&quot;true&quot;
      [header]&#x3D;&quot;&#x27;Répartition hebdomadaire des actes NGAP&#x27;&quot;
      [style]&#x3D;&quot;{ width: &#x27;80vw&#x27; }&quot;
      [closable]&#x3D;&quot;true&quot;
      [dismissableMask]&#x3D;&quot;true&quot;
    &gt;
      &lt;div class&#x3D;&quot;p-4&quot;&gt;
        &lt;p-chart
          type&#x3D;&quot;bar&quot;
          [data]&#x3D;&quot;ngapBarChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-96&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;
    &lt;/p-dialog&gt;
  &lt;/section&gt;
  &lt;section *ngIf&#x3D;&quot;activeGraphe &#x3D;&#x3D;&#x3D; &#x27;LABOGRAPHE&#x27;&quot; class&#x3D;&quot;mb-8&quot;&gt;
    &lt;!-- Graphiques et tableaux --&gt;
    &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4&quot;&gt;
      &lt;!-- Line Chart pour l&#x27;évolution mensuelle --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;

        &lt;h5 class&#x3D;&quot;text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center&quot;&gt;
          Évolution mensuelle des actes LABO
          &lt;button
            class&#x3D;&quot;text-blue-500 hover:underline text-sm flex items-center&quot;
            (click)&#x3D;&quot;showDialog(&#x27;evolutionMensuelleLABO&#x27;)&quot;
          &gt;
            &lt;i class&#x3D;&quot;pi pi-expand mr-2&quot;&gt;&lt;/i&gt; Agrandir
          &lt;/button&gt;
        &lt;/h5&gt;
        &lt;p-chart
          type&#x3D;&quot;line&quot;
          [data]&#x3D;&quot;laboLineChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;

      &lt;!-- Tableau des données mensuelles --&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;Données mensuelles&lt;/h5&gt;
        &lt;ul&gt;
          &lt;li *ngFor&#x3D;&quot;let item of laboMonthlyTableData&quot; class&#x3D;&quot;flex justify-between py-2 border-b&quot;&gt;
            &lt;span&gt;{{ item.mois }}&lt;/span&gt;
            &lt;span&gt;{{ item.anneeNmoinsUn }} / {{ item.anneeN }}&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;

      &lt;!-- Bar Chart pour la répartition hebdomadaire --&gt;
      &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3 flex justify-between items-center&quot;&gt;
          Répartition hebdomadaire
          &lt;button
            class&#x3D;&quot;text-blue-500 hover:underline text-sm flex items-center&quot;
            (click)&#x3D;&quot;showDialog(&#x27;repartitionHebdomadaireLABO&#x27;)&quot;
          &gt;
            &lt;i class&#x3D;&quot;pi pi-expand mr-2&quot;&gt;&lt;/i&gt; Agrandir
          &lt;/button&gt;
        &lt;/h5&gt;
        &lt;p-chart
          type&#x3D;&quot;bar&quot;
          [data]&#x3D;&quot;laboBarChartData&quot;
          [options]&#x3D;&quot;chartOptions&quot;
          pStyleClass&#x3D;&quot;w-full h-64&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/div&gt;

      &lt;!-- Tableau des données hebdomadaires --&gt;
      &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
        &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;Données hebdomadaires&lt;/h5&gt;
        &lt;ul&gt;
          &lt;li *ngFor&#x3D;&quot;let item of laboWeeklyTableData&quot; class&#x3D;&quot;flex justify-between py-2 border-b&quot;&gt;
            &lt;span&gt;{{ item.semaine }}&lt;/span&gt;
            &lt;span&gt;{{ item.anneeNmoinsUn }} / {{ item.anneeN }}&lt;/span&gt;
          &lt;/li&gt;
        &lt;/ul&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- Dialogs pour agrandir --&gt;
    &lt;p-dialog [(visible)]&#x3D;&quot;dialogState.evolutionMensuelleLABO&quot; header&#x3D;&quot;Évolution mensuelle des actes LABO&quot; [modal]&#x3D;&quot;true&quot; [style]&#x3D;&quot;{ width: &#x27;80vw&#x27; }&quot;&gt;
      &lt;p-chart type&#x3D;&quot;line&quot; [data]&#x3D;&quot;laboLineChartData&quot; [options]&#x3D;&quot;chartOptions&quot; pStyleClass&#x3D;&quot;w-full h-96&quot;&gt;&lt;/p-chart&gt;
    &lt;/p-dialog&gt;

    &lt;p-dialog [(visible)]&#x3D;&quot;dialogState.repartitionHebdomadaireLABO&quot; header&#x3D;&quot;Répartition hebdomadaire des actes LABO&quot; [modal]&#x3D;&quot;true&quot; [style]&#x3D;&quot;{ width: &#x27;80vw&#x27; }&quot;&gt;
      &lt;p-chart type&#x3D;&quot;bar&quot; [data]&#x3D;&quot;laboBarChartData&quot; [options]&#x3D;&quot;chartOptions&quot; pStyleClass&#x3D;&quot;w-full h-96&quot;&gt;&lt;/p-chart&gt;
    &lt;/p-dialog&gt;
  &lt;/section&gt;
&lt;/section&gt;







</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><div class="bg-white shadow">  <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>  <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">    <app-resume-profils></app-resume-profils>  </div></div><!--<app-overview></app-overview>--><!--DEBUT TABLEAU COMPARATIVE --><section class="lg:border-t">  <p-table    [value]="praticienData"    [paginator]="true"    [rows]="5"    [rowsPerPageOptions]="[5, 10, 20]"    responsiveLayout="scroll"  >    <ng-template pTemplate="header">      <tr>        <th pSortableColumn="uf">UF <p-sortIcon field="uf"></p-sortIcon></th>        <th pSortableColumn="repartition">Répartition <p-sortIcon field="repartition"></p-sortIcon></th>        <th>Répartition Réelle</th>        <th pSortableColumn="effectifPraticienDansUF">Effectif Praticien dans UF <p-sortIcon field="effectifPraticienDansUF"></p-sortIcon></th>        <th pSortableColumn="effectifUF">Effectif UF <p-sortIcon field="effectifUF"></p-sortIcon></th>        <th pSortableColumn="partPraticienDansUF">Part du Praticien dans UF <p-sortIcon field="partPraticienDansUF"></p-sortIcon></th>        <th>Part Réelle</th>        <th>Entrée</th>        <th pSortableColumn="sortie">Sortie <p-sortIcon field="sortie"></p-sortIcon></th>      </tr>    </ng-template>    <ng-template pTemplate="body" let-uf>      <tr>        <td>{{ uf.uf }}</td>        <td>{{ uf.repartition }}</td>        <td>          <span class="block text-xs text-gray-500">CCAM: {{ uf.repartitionReelle.ccam }}</span>          <span class="block text-xs text-gray-500">NGAP: {{ uf.repartitionReelle.ngap }}</span>          <span class="block text-xs text-gray-500">LABO: {{ uf.repartitionReelle.labo }}</span>        </td>        <td>{{ uf.effectifPraticienDansUF }}</td>        <td>{{ uf.effectifUF }}</td>        <td>{{ uf.partPraticienDansUF }}</td>        <td>          <span class="block text-xs text-gray-500">CCAM: {{ uf.partReelle.ccam }}</span>          <span class="block text-xs text-gray-500">NGAP: {{ uf.partReelle.ngap }}</span>          <span class="block text-xs text-gray-500">LABO: {{ uf.partReelle.labo }}</span>        </td>        <td>{{ uf.entree }}</td>        <td>{{ uf.sortie }}</td>      </tr>    </ng-template>  </p-table></section><section class="mt-8 "><div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">  <ol class="flex items-center w-full text-sm text-gray-500 font-medium sm:text-base">    <!-- Step 1: CCAM -->    <li      class="flex md:w-full items-center text-indigo-600 sm:after:content-[\'\'] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8"    >      <div        class="flex items-center whitespace-nowrap after:content-[\'/\'] sm:after:hidden after:mx-2 cursor-pointer"        (click)="showTable(\'CCAM\')"      >      <span        [class.bg-indigo-600]="activeTable === \'CCAM\'"        [class.text-white]="activeTable === \'CCAM\'"        [class.bg-gray-100]="activeTable !== \'CCAM\'"        [class.text-gray-600]="activeTable !== \'CCAM\'"        class="w-6 h-6  border border-indigo-200 rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10"      >1</span      >        CCAM      </div>    </li>    <!-- Step 2: NGAP -->    <li      class="flex md:w-full items-center text-indigo-600 sm:after:content-[\'\'] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8"    >      <div        class="flex items-center whitespace-nowrap after:content-[\'/\'] sm:after:hidden after:mx-2 cursor-pointer"        (click)="showTable(\'NGAP\')"      >      <span        [class.bg-indigo-600]="activeTable === \'NGAP\'"        [class.text-white]="activeTable === \'NGAP\'"        [class.bg-gray-100]="activeTable !== \'NGAP\'"        [class.text-gray-600]="activeTable !== \'NGAP\'"        class="w-6 h-6 bg-gray-100 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10"      >2</span      >        NGAP      </div>    </li>    <!-- Step 3: LABO -->    <li class="flex md:w-full items-center text-indigo-600">      <div        class="flex items-center cursor-pointer"        (click)="showTable(\'LABO\')"      >      <span        [class.bg-indigo-600]="activeTable === \'LABO\'"        [class.text-white]="activeTable === \'LABO\'"        [class.bg-gray-100]="activeTable !== \'LABO\'"        [class.text-gray-600]="activeTable !== \'LABO\'"        class="w-6 h-6   border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10"      >3</span      >        LABO      </div>    </li>  </ol></div>  <div *ngIf="activeTable === \'CCAM\'" class="p-4">    <h5 class="text-lg font-bold mb-3">      Analyse comparative des actes CCAM [ {{anneeNmoinsUn}}-{{ anneeN }} ]    </h5>    <div>      <div class="relative mt-2 rounded-md shadow-sm">        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>        </div>        <input          type="text" name="price"          (input)="ccamTableParActeParAnnee.filterGlobal(onInput($event), \'contains\')"          class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "          placeholder="  Filtrer par ccam"        />      </div>    </div>    <p-table      #ccamTableParActeParAnnee      [value]="ccamData"      [scrollable]="true"      scrollHeight="400px"      [tableStyle]="{\'min-width\': \'50rem\'}"      [globalFilterFields]="[\'code\', \'description\',\'totalAnneeNMoins1\',\'totalAnneeN\']"    >      <ng-template pTemplate="header">        <tr class="border-b hover:bg-gray-50">          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            <i              class="pi pi-hashtag cursor-pointer ml-2"              title="Voir les détails de l\'acte"            ></i>          </th>          <th pSortableColumn="code">            Code            <p-sortIcon field="code"></p-sortIcon>          </th>          <th pSortableColumn="description">            Description            <p-sortIcon field="description"></p-sortIcon>          </th>          <th  pSortableColumn="totalAnneeNmoinsUn" >            Total actes {{ anneeNmoinsUn }}            <p-sortIcon field="totalAnneeNmoinsUn"></p-sortIcon>          </th>          <th  pSortableColumn="totalAnneeN" >            Total actes {{ anneeN }}            <p-sortIcon field="totalAnneeN"></p-sortIcon>          </th>        </tr>      </ng-template>      <ng-template pTemplate="body" let-acte>        <tr class="border-b hover:bg-gray-50">          <td style="padding: 0.75rem; text-align: left;">            <p class="text-sm font-bold text-gray-600 flex items-center">              <i                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                title="Voir les détails de {{acte.code }} {{acte.description}}"                (click)="viewActeDetails(acte)"              ></i>            </p>          </td>          <td>{{acte.code}}</td>          <td>{{acte.description}}</td>          <td> {{acte.totalAnneeNmoinsUn}}  </td>          <td> {{acte.totalAnneeN}}  </td>        </tr>      </ng-template>      <!-- Ligne de total -->      <ng-template pTemplate="footer">        <tr  >          <td colspan="3" class="font-bold text-left">TOTAL</td>          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeNmoinsUn,\'CCAM\')" severity="info" styleClass="ml-2"></p-badge></td>          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeN,\'CCAM\')" severity="info" styleClass="ml-3"></p-badge></td>          <td></td>        </tr>      </ng-template>    </p-table>  </div>  <div *ngIf="activeTable === \'NGAP\'" class="p-4">    <h5 class="text-lg font-bold mb-3">      Analyse comparative des actes NGAP [ {{anneeNmoinsUn}}-{{ anneeN }} ]    </h5>    <div>      <div class="relative mt-2 rounded-md shadow-sm">        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>        </div>        <input          type="text" name="price"          (input)="ngapTableParActeParAnnee.filterGlobal(onInput($event), \'contains\')"          class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "          placeholder="  Filtrer par ngap"        />      </div>    </div>    <p-table      #ngapTableParActeParAnnee      [value]="ngapData"      [scrollable]="true"      scrollHeight="400px"      [tableStyle]="{\'min-width\': \'50rem\'}"      [globalFilterFields]="[\'code\', \'description\',\'totalAnneeNMoins1\',\'totalAnneeN\']"    >      <ng-template pTemplate="header">        <tr class="border-b hover:bg-gray-50">          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            <i              class="pi pi-hashtag cursor-pointer ml-2"              title="Voir les détails de l\'acte"            ></i>          </th>          <th pSortableColumn="code">            Code            <p-sortIcon field="code"></p-sortIcon>          </th>          <th pSortableColumn="description">            Description            <p-sortIcon field="description"></p-sortIcon>          </th>          <th  pSortableColumn="totalAnneeNmoinsUn" >            Total actes {{ anneeNmoinsUn }}            <p-sortIcon field="totalAnneeNmoinsUn"></p-sortIcon>          </th>          <th  pSortableColumn="totalAnneeN" >            Total actes {{ anneeN }}            <p-sortIcon field="totalAnneeN"></p-sortIcon>          </th>        </tr>      </ng-template>      <ng-template pTemplate="body" let-acte>        <tr class="border-b hover:bg-gray-50">          <td style="padding: 0.75rem; text-align: left;">            <p class="text-sm font-bold text-gray-600 flex items-center">              <i                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                title="Voir les détails de {{acte.code }} {{acte.description}}"                (click)="viewActeDetails(acte)"              ></i>            </p>          </td>          <td>{{acte.code}}</td>          <td>{{acte.description}}</td>          <td> {{acte.totalAnneeNmoinsUn}}  </td>          <td> {{acte.totalAnneeN}}  </td>        </tr>      </ng-template>      <!-- Ligne de total -->      <ng-template pTemplate="footer">        <tr  >          <td colspan="3" class="font-bold text-left">TOTAL</td>          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeNmoinsUn,\'NGAP\')" severity="info" styleClass="ml-2"></p-badge></td>          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeN,\'NGAP\')" severity="info" styleClass="ml-3"></p-badge></td>          <td></td>        </tr>      </ng-template>    </p-table>  </div>  <div *ngIf="activeTable === \'LABO\'" class="p-4">    <h5 class="text-lg font-bold mb-3">      Analyse comparative des actes LABO [ {{anneeNmoinsUn}}-{{ anneeN }} ]    </h5>    <div>      <div class="relative mt-2 rounded-md shadow-sm">        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>        </div>        <input          type="text" name="price"          (input)="laboTableParActeParAnnee.filterGlobal(onInput($event), \'contains\')"          class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "          placeholder="  Filtrer par nabm"        />      </div>    </div>    <p-table      #laboTableParActeParAnnee      [value]="laboData"      [scrollable]="true"      scrollHeight="400px"      [tableStyle]="{\'min-width\': \'50rem\'}"      [globalFilterFields]="[\'code\', \'description\',\'totalAnneeNmoinsUn\',\'totalAnneeN\']"    >      <ng-template pTemplate="header">        <tr class="border-b hover:bg-gray-50">          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            <i              class="pi pi-hashtag cursor-pointer ml-2"              title="Voir les détails de l\'acte"            ></i>          </th>          <th pSortableColumn="code">            Code            <p-sortIcon field="code"></p-sortIcon>          </th>          <th pSortableColumn="description">            Description            <p-sortIcon field="description"></p-sortIcon>          </th>          <th  pSortableColumn="totalAnneeNmoinsUn" >            Total actes {{ anneeNmoinsUn }}            <p-sortIcon field="totalAnneeNmoinsUn"></p-sortIcon>          </th>          <th  pSortableColumn="totalAnneeN" >            Total actes {{ anneeN }}            <p-sortIcon field="totalAnneeN"></p-sortIcon>          </th>        </tr>      </ng-template>      <ng-template pTemplate="body" let-acte>        <tr class="border-b hover:bg-gray-50">          <td style="padding: 0.75rem; text-align: left;">            <p class="text-sm font-bold text-gray-600 flex items-center">              <i                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                title="Voir les détails de {{acte.code }} {{acte.description}}"                (click)="viewActeDetails(acte)"              ></i>            </p>          </td>          <td>{{acte.code}}</td>          <td>{{acte.description}}</td>          <td> {{acte.totalAnneeNmoinsUn}}  </td>          <td> {{acte.totalAnneeN}}  </td>        </tr>      </ng-template>      <!-- Ligne de total -->      <ng-template pTemplate="footer">        <tr  >          <td colspan="3" class="font-bold text-left">TOTAL</td>          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeNmoinsUn,\'Labo\')" severity="info" styleClass="ml-2"></p-badge></td>          <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(anneeN,\'Labo\')" severity="info" styleClass="ml-3"></p-badge></td>          <td></td>        </tr>      </ng-template>    </p-table>  </div></section><!--DEBUT GRAPHIQUE--><section>  <div class="px-4 sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">    <ol class="flex items-center w-full text-sm text-gray-500 font-medium sm:text-base">      <!-- Step 1: CCAM -->      <li        class="flex md:w-full items-center text-indigo-600 sm:after:content-[\'\'] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8"      >        <div          class="flex items-center whitespace-nowrap after:content-[\'/\'] sm:after:hidden after:mx-2 cursor-pointer"          (click)="showGraphe(\'CCAMGRAPHE\')"        >      <span        [class.bg-indigo-600]="activeGraphe === \'CCAMGRAPHE\'"        [class.text-white]="activeGraphe === \'CCAMGRAPHE\'"        [class.bg-gray-100]="activeGraphe !== \'CCAMGRAPHE\'"        [class.text-gray-600]="activeGraphe !== \'CCAMGRAPHE\'"        class="w-6 h-6  border border-indigo-200 rounded-full flex justify-center items-center mr-3 text-sm text-white lg:w-10 lg:h-10"      >1</span      >          CCAM        </div>      </li>      <!-- Step 2: NGAP -->      <li        class="flex md:w-full items-center text-indigo-600 sm:after:content-[\'\'] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-4 xl:after:mx-8"      >        <div          class="flex items-center whitespace-nowrap after:content-[\'/\'] sm:after:hidden after:mx-2 cursor-pointer"          (click)="showGraphe(\'NGAPGRAPHE\')"        >      <span        [class.bg-indigo-600]="activeGraphe === \'NGAPGRAPHE\'"        [class.text-white]="activeGraphe === \'NGAPGRAPHE\'"        [class.bg-gray-100]="activeGraphe !== \'NGAPGRAPHE\'"        [class.text-gray-600]="activeGraphe !== \'NGAPGRAPHE\'"        class="w-6 h-6 bg-gray-100 border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10"      >2</span      >          NGAP        </div>      </li>      <!-- Step 3: LABO -->      <li class="flex md:w-full items-center text-indigo-600">        <div          class="flex items-center cursor-pointer"          (click)="showGraphe(\'LABOGRAPHE\')"        >      <span        [class.bg-indigo-600]="activeGraphe === \'LABOGRAPHE\'"        [class.text-white]="activeGraphe === \'LABOGRAPHE\'"        [class.bg-gray-100]="activeGraphe !== \'LABOGRAPHE\'"        [class.text-gray-600]="activeGraphe !== \'LABOGRAPHE\'"        class="w-6 h-6   border border-gray-200 rounded-full flex justify-center items-center mr-3 lg:w-10 lg:h-10"      >3</span      >          LABO        </div>      </li>    </ol>  </div>  <section  *ngIf="activeGraphe === \'CCAMGRAPHE\'"  class="mb-8">    <!-- Graphiques et tableaux -->    <div class="grid grid-cols-12 gap-4">      <!-- Line Chart pour l\'évolution mensuelle -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg relative">        <h5 class="text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center">          Évolution mensuelle des actes CCAM          <button            class="text-blue-500 hover:underline text-sm flex items-center"            (click)="showDialog(\'evolutionMensuelleCCAM\')"          >            <i class="pi pi-expand mr-2"></i> Agrandir          </button>        </h5>        <p-chart          type="line"          [data]="ccamLineChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">Données mensuelles</h5>        <ul>          <li *ngFor="let item of ccamMonthlyTableData" class="flex justify-between py-2 border-b">            <span>{{ item.mois }}</span>            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>          </li>        </ul>      </div>      <!-- Bar Chart pour la répartition hebdomadaire -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg relative">        <h5 class="text-lg font-bold text-gray-700 mb-3 flex justify-between items-center">          Répartition hebdomadaire          <button            class="text-blue-500 hover:underline text-sm flex items-center"            (click)="showDialog(\'repartitionHebdomadaireCCAM\')"          >            <i class="pi pi-expand mr-2"></i> Agrandir          </button>        </h5>        <p-chart          type="bar"          [data]="ccamBarChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">Données hebdomadaires</h5>        <ul>          <li *ngFor="let item of ccamWeeklyTableData" class="flex justify-between py-2 border-b">            <span>{{ item.semaine }}</span>            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>          </li>        </ul>      </div>    </div>    <!-- Dialog pour Évolution mensuelle -->    <p-dialog      [(visible)]="dialogState.evolutionMensuelleCCAM"      [modal]="true"      [header]="\'Évolution mensuelle des actes CCAM\'"      [style]="{ width: \'80vw\' }"      [closable]="true"      [dismissableMask]="true"     >      <div class="p-4">        <p-chart          type="line"          [data]="ccamLineChartData"          [options]="chartOptions"          pStyleClass="w-full h-96"        ></p-chart>      </div>    </p-dialog>    <!-- Dialog pour Répartition hebdomadaire -->    <p-dialog      [(visible)]="dialogState.repartitionHebdomadaireCCAM"      [modal]="true"      [header]="\'Répartition hebdomadaire des actes CCAM\'"      [style]="{ width: \'80vw\' }"      [closable]="true"      [dismissableMask]="true"    >      <div class="p-4">        <p-chart          type="bar"          [data]="ccamBarChartData"          [options]="chartOptions"          pStyleClass="w-full h-96"        ></p-chart>      </div>    </p-dialog>  </section>  <section *ngIf="activeGraphe === \'NGAPGRAPHE\'" class="mb-8">    <!-- Graphiques et tableaux -->    <div class="grid grid-cols-12 gap-4">      <!-- Line Chart pour l\'évolution mensuelle -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg relative">        <h5 class="text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center">          Évolution mensuelle des actes NGAP          <button            class="text-blue-500 hover:underline text-sm flex items-center"            (click)="showDialog(\'evolutionMensuelle\')"          >            <i class="pi pi-expand mr-2"></i> Agrandir          </button>        </h5>        <p-chart          type="line"          [data]="ngapLineChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">Données mensuelles</h5>        <ul>          <li *ngFor="let item of ngapMonthlyTableData" class="flex justify-between py-2 border-b">            <span>{{ item.mois }}</span>            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>          </li>        </ul>      </div>      <!-- Bar Chart pour la répartition hebdomadaire -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg relative">        <h5 class="text-lg font-bold text-gray-700 mb-3 flex justify-between items-center">          Répartition hebdomadaire          <button            class="text-blue-500 hover:underline text-sm flex items-center"            (click)="showDialog(\'repartitionHebdomadaire\')"          >            <i class="pi pi-expand mr-2"></i> Agrandir          </button>        </h5>        <p-chart          type="bar"          [data]="ngapBarChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">Données hebdomadaires</h5>        <ul>          <li *ngFor="let item of ngapWeeklyTableData" class="flex justify-between py-2 border-b">            <span>{{ item.semaine }}</span>            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>          </li>        </ul>      </div>    </div>    <!-- Dialog pour Évolution mensuelle -->    <p-dialog      [(visible)]="dialogState.evolutionMensuelle"      [modal]="true"      [header]="\'Évolution mensuelle des actes NGAP\'"      [style]="{ width: \'80vw\' }"      [closable]="true"      [dismissableMask]="true"    >      <div class="p-4">        <p-chart          type="line"          [data]="ngapLineChartData"          [options]="chartOptions"          pStyleClass="w-full h-96"        ></p-chart>      </div>    </p-dialog>    <!-- Dialog pour Répartition hebdomadaire -->    <p-dialog      [(visible)]="dialogState.repartitionHebdomadaire"      [modal]="true"      [header]="\'Répartition hebdomadaire des actes NGAP\'"      [style]="{ width: \'80vw\' }"      [closable]="true"      [dismissableMask]="true"    >      <div class="p-4">        <p-chart          type="bar"          [data]="ngapBarChartData"          [options]="chartOptions"          pStyleClass="w-full h-96"        ></p-chart>      </div>    </p-dialog>  </section>  <section *ngIf="activeGraphe === \'LABOGRAPHE\'" class="mb-8">    <!-- Graphiques et tableaux -->    <div class="grid grid-cols-12 gap-4">      <!-- Line Chart pour l\'évolution mensuelle -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-cyan-700 mb-3 flex justify-between items-center">          Évolution mensuelle des actes LABO          <button            class="text-blue-500 hover:underline text-sm flex items-center"            (click)="showDialog(\'evolutionMensuelleLABO\')"          >            <i class="pi pi-expand mr-2"></i> Agrandir          </button>        </h5>        <p-chart          type="line"          [data]="laboLineChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <!-- Tableau des données mensuelles -->      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">Données mensuelles</h5>        <ul>          <li *ngFor="let item of laboMonthlyTableData" class="flex justify-between py-2 border-b">            <span>{{ item.mois }}</span>            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>          </li>        </ul>      </div>      <!-- Bar Chart pour la répartition hebdomadaire -->      <div class="col-span-8 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3 flex justify-between items-center">          Répartition hebdomadaire          <button            class="text-blue-500 hover:underline text-sm flex items-center"            (click)="showDialog(\'repartitionHebdomadaireLABO\')"          >            <i class="pi pi-expand mr-2"></i> Agrandir          </button>        </h5>        <p-chart          type="bar"          [data]="laboBarChartData"          [options]="chartOptions"          pStyleClass="w-full h-64"        ></p-chart>      </div>      <!-- Tableau des données hebdomadaires -->      <div class="col-span-4 bg-white p-4 shadow rounded-lg">        <h5 class="text-lg font-bold text-gray-700 mb-3">Données hebdomadaires</h5>        <ul>          <li *ngFor="let item of laboWeeklyTableData" class="flex justify-between py-2 border-b">            <span>{{ item.semaine }}</span>            <span>{{ item.anneeNmoinsUn }} / {{ item.anneeN }}</span>          </li>        </ul>      </div>    </div>    <!-- Dialogs pour agrandir -->    <p-dialog [(visible)]="dialogState.evolutionMensuelleLABO" header="Évolution mensuelle des actes LABO" [modal]="true" [style]="{ width: \'80vw\' }">      <p-chart type="line" [data]="laboLineChartData" [options]="chartOptions" pStyleClass="w-full h-96"></p-chart>    </p-dialog>    <p-dialog [(visible)]="dialogState.repartitionHebdomadaireLABO" header="Répartition hebdomadaire des actes LABO" [modal]="true" [style]="{ width: \'80vw\' }">      <p-chart type="bar" [data]="laboBarChartData" [options]="chartOptions" pStyleClass="w-full h-96"></p-chart>    </p-dialog>  </section></section></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'AffectationPraticienComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'AffectationPraticienComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
