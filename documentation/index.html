<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
	   <link rel="stylesheet" href="./styles/style.css">
        <link rel="stylesheet" href="./styles/dark.css">
        <link rel="stylesheet" href="./styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="./" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content getting-started">
                   <div class="content-data">

<h1>Supra-Front</h1>
<p>Supra Front est la partie frontend de l&#39;application Supra, une solution modernisée de gestion des statistiques hospitalières au CHRU. Ce projet utilise Angular (18.0.3), Tailwind CSS pour la mise en page, et PrimeNG pour d&#39;autre composants d&#39;interface utilisateur.</p>
<h2>Technologies</h2>
<ul>
<li>Angular : 18.0.3</li>
<li>Tailwind CSS : pour une mise en page moderne et réactive.</li>
<li>TypeScript</li>
</ul>
<h1>Installation</h1>
<p>Prérequis</p>
<ul>
<li>Node.js : &gt;= 16.x</li>
<li>npm : &gt;= 8.x</li>
<li>Angular CLI : &gt;= 18.x</li>
<li>Git</li>
</ul>
<h1>Étapes d&#39;installation</h1>
<ul>
<li>Clonez le dépôt :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell">git clone https://gilly.chu-nancy.fr/u074647/supra-front.git
cd supra-front</code></pre></div><ul>
<li>Installez les dépendances du projet :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell">npm install</code></pre></div><ul>
<li>Configurez les variables d&#39;environnement</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell">cd src/environement</code></pre></div><ul>
<li>Exemple d&#39;environnement de développement (src/environments/environment.ts) :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell"> export const environment = {
  production: false,
  apiBaseUrl: &#39;http://localhost:8080&#39;, // URL de l&#39;API backend local 
};</code></pre></div><ul>
<li>Exemple d&#39;environnement de production (src/environments/environment.prod.ts) :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell"> 
export const environment = {
  production: true,
  apiBaseUrl: &#39;https://api.supra.fr&#39;, // URL de l&#39;API backend prod 
};</code></pre></div><ul>
<li>Lancez le projet localement :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell">ng serve</code></pre></div><h2>Documentation</h2>
<ul>
<li>Lancer Storybook (documentation des composants UI) :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell"> npm run storybook</code></pre></div><ul>
<li>Accédez à Storybook dans votre navigateur à l&#39;adresse :<a href="http://localhost:6006">http://localhost:6006</a></li>
</ul>
<ul>
<li>Lancer Compodoc (documentation technique Angular) :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell"> npm run compodoc</code></pre></div><ul>
<li>Accédez à Compodoc dans votre navigateur à l&#39;adresse : <a href="http://localhost:8080">http://localhost:8080</a></li>
</ul>
<h2>Commandes Utiles</h2>
<ul>
<li>Lancer l&#39;application en mode développement :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell"> ng serve</code></pre></div><ul>
<li>Construire l&#39;application pour la production :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell">ng build --prod</code></pre></div><ul>
<li>Tests Unitaires</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell">ng test</code></pre></div><ul>
<li>Tests End-to-End (E2E)
Les tests End-to-End utilisent Cypress, un outil puissant pour simuler les interactions utilisateurs.</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell">cypress/
  ├── e2e/
  │   ├── login.cy.ts
  │   ├── dashboard.cy.ts
  │   └── filter_search.cy.ts
  ├── fixtures/
  ├── support/</code></pre></div><ul>
<li>Exécuter les test e2e :</li>
</ul>
<b>Example :</b><div><pre class="line-numbers"><code class="language-shell">npx cypress run  
npx cypress open   </code></pre></div><h1>Structure des Dossiers</h1>
<p>L&#39;architecture du projet suit une approche modulaire avec Lazy Loading pour optimiser les performances. Chaque fonctionnalité dispose de son propre module pour assurer une organisation claire et évolutive.</p>
<ul>
<li><code>core/</code> : Contient les éléments partagés à travers l&#39;application, tels que les services, les modèles et les pipes, ainsi que le module principal core.module.ts.</li>
<li><code> graphical/</code> et <code>organization/</code> : Correspondent à des modules fonctionnels avec leurs propres composants et configurations de routing (par exemple, graphical-routing.module.ts et organization-routing.module.ts).</li>
<li><code>shared/</code> : Regroupe les composants partagés entre plusieurs fonctionnalités.</li>
<li><code>pages/</code> : Contient les pages principales de l&#39;application.</li>
</ul>
<p>Ce découpage permet une maintenance facilitée et une évolutivité pour le développement de nouvelles fonctionnalités.</p>
<h1>Bonnes Pratiques</h1>
<ul>
<li>Respectez les conventions Angular et Tailwind.</li>
<li>Assurez-vous que le code passe tous les tests.</li>
<li>Ajoutez des tests pour chaque nouvelle fonctionnalité.</li>
</ul>





















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 0;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'getting-started';
            var COMPODOC_CURRENT_PAGE_URL = 'index.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="./js/libs/custom-elements.min.js"></script>
       <script src="./js/libs/lit-html.js"></script>

       <script src="./js/menu-wc.js" defer></script>
       <script nomodule src="./js/menu-wc_es5.js" defer></script>

       <script src="./js/libs/bootstrap-native.js"></script>

       <script src="./js/libs/es6-shim.min.js"></script>
       <script src="./js/libs/EventDispatcher.js"></script>
       <script src="./js/libs/promise.min.js"></script>

       <script src="./js/compodoc.js"></script>

       <script src="./js/tabs.js"></script>
       <script src="./js/menu.js"></script>
       <script src="./js/libs/clipboard.min.js"></script>
       <script src="./js/libs/prism.js"></script>
       <script src="./js/sourceCode.js"></script>
          <script src="./js/search/search.js"></script>
          <script src="./js/search/lunr.min.js"></script>
          <script src="./js/search/search-lunr.js"></script>
          <script src="./js/search/search_index.js"></script>
       <script src="./js/lazy-load-graphs.js"></script>


    </body>
</html>
