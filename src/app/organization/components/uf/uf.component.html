<!--&lt;!&ndash;LISTE DES UF &ndash;&gt;-->
<!--<div class="p-4 bg-gray-100 min-h-screen">-->
<!--  <h1 class="text-2xl font-semibold text-gray-800 mb-4">Liste des Unités Fonctionnelles (UF)</h1>-->

<!--  <div *ngIf="ufs.length === 0" class="text-gray-500 text-center p-4">-->
<!--    Aucune unité fonctionnelle disponible.-->
<!--  </div>-->

<!--  <div *ngFor="let uf of ufs" class="bg-white shadow rounded-lg p-4 mb-4">-->
<!--    <h2 class="text-xl font-bold text-cyan-700">{{ uf.name }}</h2>-->
<!--    <div class="text-gray-500 text-sm mt-2">-->
<!--      <span class="font-semibold">Code:</span> {{ uf.code }}-->
<!--    </div>-->
<!--  </div>-->
<!--</div>-->

<!--POUR L'INSTANT CE COMPONENTE SE COMPORTE COMME UN SINGLE-UF-->

<div *ngIf="!currentUfId" class="p-4 text-center text-gray-500">
  <p>Chargement des détails...</p>
</div>

<div class="p-4 bg-gray-100 min-h-screen">
  <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
<!--  <p>-->
<!--    todo revoir le app information panel-->
<!--  </p>-->
  <div class="flex items-center p-4 bg-blue-100 text-blue-700 rounded-lg">
    <svg class="w-6 h-6 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 2a10 10 0 1010 10A10 10 0 0012 2z"/>
    </svg>
    <span>Cette page affichera les mêmes infos que l'onglet "Service", pondérées par UF.</span>
  </div>

  <app-information-panel [elementId]="this.currentUfId" elementType="uf"></app-information-panel>

</div>/
