import {Injectable} from "@angular/core";
import {BehaviorSubject, Observable} from "rxjs";
import {HttpClient} from "@angular/common/http";
import {LiberalData} from "../models/liberal.model";
import {catchError, tap} from "rxjs/operators";

@Injectable({
  providedIn: 'root',
})
export class LiberalService {
  private liberalUrl = './assets/data/liberal.json'; // Chemin vers les données libérales
  private liberal$ = new BehaviorSubject<LiberalData | null>(null);

  constructor(private http: HttpClient) {}

  /**
   * Charge les données initiales pour l'activité libérale.
   */
  loadInitialData(): void {
    this.http
      .get<LiberalData>(this.liberalUrl)
      .pipe(
        tap((data) => {
          this.liberal$.next(data); // Met à jour les données dans le BehaviorSubject
        }),
        catchError((error) => {
          console.error('Erreur lors du chargement des données Liberal:', error);
          this.liberal$.next(null); // En cas d'erreur, passe les données à null
          throw error; // Propager l'erreur pour un traitement additionnel si nécessaire
        })
      )
      .subscribe();
  }

  /**
   * Retourne un Observable pour s'abonner aux données libérales.
   */
  getLiberals(): Observable<LiberalData | null> {
    return this.liberal$.asObservable();
  }
}
