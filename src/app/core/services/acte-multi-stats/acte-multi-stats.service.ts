import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { DatePeriod } from '../global-filter/global-filter.service';

// Interface pour les données d'un acte multi-stats
export interface ActeMultiStatsData {
  acteCode: string;
  acteDescription: string;
  p1Count: number;
  p2Count: number;
  p3Count: number;
  totalActivites: number;
  totalRealisations: number;
}

// Interface pour la réponse API
export interface ActeMultiStatsResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  totalItems: number;
  member: ActeMultiStatsData[];
}

@Injectable({
  providedIn: 'root'
})
export class ActeMultiStatsService {

  private readonly apiUrl = `${environment.api_url}/api/actes/multi-stats`;

  constructor(private http: HttpClient) {}

  /**
   * Récupère les statistiques multi-actes pour une liste de codes d'actes
   *
   * @param acteCodes Liste des codes d'actes
   * @param p1 Période P1
   * @param p2 Période P2
   * @param p3 Période P3
   * @returns Observable<ActeMultiStatsData[]>
   */
  getMultiActeStats(
    acteCodes: string[],
    p1: DatePeriod | null,
    p2: DatePeriod | null,
    p3: DatePeriod | null
  ): Observable<ActeMultiStatsData[]> {

    if (!acteCodes.length) {
      return of([]);
    }

    let params = new HttpParams();

    // Ajouter les codes d'actes
    params = params.set('acteCodes', acteCodes.join(','));

    // Ajouter les périodes si elles existent
    if (p1?.dateDebut && p1?.dateFin) {
      params = params.set('p1Start', this.formatDate(p1.dateDebut));
      params = params.set('p1End', this.formatDate(p1.dateFin));
    }

    if (p2?.dateDebut && p2?.dateFin) {
      params = params.set('p2Start', this.formatDate(p2.dateDebut));
      params = params.set('p2End', this.formatDate(p2.dateFin));
    }

    if (p3?.dateDebut && p3?.dateFin) {
      params = params.set('p3Start', this.formatDate(p3.dateDebut));
      params = params.set('p3End', this.formatDate(p3.dateFin));
    }

    return this.http.get<ActeMultiStatsResponse>(this.apiUrl, { params })
      .pipe(
        map(response => this.aggregateByActeCode(response.member || []))
      );
  }

  /**
   * Agrège les données par code d'acte (fusion des doublons)
   */
  private aggregateByActeCode(data: ActeMultiStatsData[]): ActeMultiStatsData[] {
    const aggregatedMap = new Map<string, ActeMultiStatsData>();

    data.forEach(item => {
      const existingItem = aggregatedMap.get(item.acteCode);

      if (existingItem) {
        // Fusionner avec l'élément existant
        aggregatedMap.set(item.acteCode, {
          acteCode: item.acteCode,
          acteDescription: existingItem.acteDescription, // Garder la première description
          p1Count: existingItem.p1Count + item.p1Count,
          p2Count: existingItem.p2Count + item.p2Count,
          p3Count: existingItem.p3Count + item.p3Count,
          totalActivites: existingItem.totalActivites + item.totalActivites,
          totalRealisations: existingItem.totalRealisations + item.totalRealisations
        });
      } else {
        // Ajouter le nouvel élément
        aggregatedMap.set(item.acteCode, { ...item });
      }
    });

    // Convertir la Map en array et trier par totalRealisations décroissant
    return Array.from(aggregatedMap.values())
      .sort((a, b) => b.totalRealisations - a.totalRealisations);
  }

  /**
   * Formate une date pour l'API (YYYY-MM-DD)
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}
