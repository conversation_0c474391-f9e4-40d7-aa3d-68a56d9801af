<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  SidebarComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/core/components/sidebar/sidebar.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-sidebar</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                                <code><a href="../components/ResumeProfilsComponent.html" target="_self" >ResumeProfilsComponent</a></code>
                            <code>CoreModule</code>
                                <code><a href="../components/OverviewComponent.html" target="_self" >OverviewComponent</a></code>
                            <code>CalendarModule</code>
                            <code>FormsModule</code>
                            <code>FloatLabelModule</code>
                            <code>RouterLink</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./sidebar.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./sidebar.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#dateRange" >dateRange</a>
                            </li>
                            <li>
                                <a href="#isSidebarOpen" >isSidebarOpen</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#closeSidebar" >closeSidebar</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#toggleSidebar" >toggleSidebar</a>
                            </li>
                            <li>
                                <a href="#translation" >translation</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(sidebarService: SidebarService, primengConfig: PrimeNGConfig)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/app/core/components/sidebar/sidebar.component.ts:29</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>sidebarService</td>

                                                        <td>
                                                                    <code>SidebarService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>primengConfig</td>

                                                        <td>
                                                                    <code>PrimeNGConfig</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="closeSidebar"></a>
                    <span class="name">
                        <span ><b>closeSidebar</b></span>
                        <a href="#closeSidebar"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>closeSidebar()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="57"
                                    class="link-to-prism">src/app/core/components/sidebar/sidebar.component.ts:57</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="39"
                                    class="link-to-prism">src/app/core/components/sidebar/sidebar.component.ts:39</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="toggleSidebar"></a>
                    <span class="name">
                        <span ><b>toggleSidebar</b></span>
                        <a href="#toggleSidebar"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>toggleSidebar()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="53"
                                    class="link-to-prism">src/app/core/components/sidebar/sidebar.component.ts:53</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="translation"></a>
                    <span class="name">
                        <span ><b>translation</b></span>
                        <a href="#translation"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>translation()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="61"
                                    class="link-to-prism">src/app/core/components/sidebar/sidebar.component.ts:61</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="dateRange"></a>
                    <span class="name">
                        <span ><b>dateRange</b></span>
                        <a href="#dateRange"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>Date[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/app/core/components/sidebar/sidebar.component.ts:29</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isSidebarOpen"></a>
                    <span class="name">
                        <span ><b>isSidebarOpen</b></span>
                        <a href="#isSidebarOpen"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>false</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="28" class="link-to-prism">src/app/core/components/sidebar/sidebar.component.ts:28</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import {ResumeProfilsComponent} from &quot;../../../pages/resume-profils/resume-profils.component&quot;;
import {CoreModule} from &quot;../../core.module&quot;;
import {OverviewComponent} from &quot;../../../pages/overview/overview.component&quot;;
import {SidebarService} from &quot;../../services/sidebar.service&quot;;
import {CalendarModule} from &quot;primeng/calendar&quot;;
import {FormsModule} from &quot;@angular/forms&quot;;
import {FloatLabelModule} from &quot;primeng/floatlabel&quot;;
import {PrimeNGConfig} from &quot;primeng/api&quot;;
import {RouterLink} from &quot;@angular/router&quot;;

@Component({
  selector: &#x27;app-sidebar&#x27;,
  standalone: true,
  imports: [
    ResumeProfilsComponent,
    CoreModule,
    OverviewComponent,
    CalendarModule,
    FormsModule,
    FloatLabelModule,
    RouterLink
  ],
  templateUrl: &#x27;./sidebar.component.html&#x27;,
  styleUrl: &#x27;./sidebar.component.scss&#x27;
})
export class SidebarComponent implements OnInit {
  isSidebarOpen &#x3D; false;
  dateRange: Date[] &#x3D; [];




  constructor(
    private sidebarService: SidebarService,
    private primengConfig: PrimeNGConfig
  ) {
  }
  ngOnInit(): void {
    this.sidebarService.sidebarOpen$.subscribe(isOpen &#x3D;&gt; {
      this.isSidebarOpen &#x3D; isOpen;
    });
    const currentDate &#x3D; new Date();

    const startDate &#x3D; new Date(currentDate.getFullYear() - 1, currentDate.getMonth(), 1);

    const endDate &#x3D; new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

    this.dateRange &#x3D; [startDate, endDate];
    this.translation()
  }

  toggleSidebar() {
    this.isSidebarOpen &#x3D; !this.isSidebarOpen;
  }

  closeSidebar() {
    this.sidebarService.closeSidebar();
  }

  translation(){
    this.primengConfig.setTranslation({
      accept: &#x27;Accepter&#x27;,
      reject: &#x27;Annuler&#x27;,
      dayNames: [&#x27;Dimanche&#x27;, &#x27;Lundi&#x27;, &#x27;Mardi&#x27;, &#x27;Mercredi&#x27;, &#x27;Jeudi&#x27;, &#x27;Vendredi&#x27;, &#x27;Samedi&#x27;],
      dayNamesShort: [&#x27;Dim&#x27;, &#x27;Lun&#x27;, &#x27;Mar&#x27;, &#x27;Mer&#x27;, &#x27;Jeu&#x27;, &#x27;Ven&#x27;, &#x27;Sam&#x27;],
      dayNamesMin: [&#x27;D&#x27;, &#x27;L&#x27;, &#x27;M&#x27;, &#x27;M&#x27;, &#x27;J&#x27;, &#x27;V&#x27;, &#x27;S&#x27;],
      monthNames: [
        &#x27;Janvier&#x27;, &#x27;Février&#x27;, &#x27;Mars&#x27;, &#x27;Avril&#x27;, &#x27;Mai&#x27;, &#x27;Juin&#x27;,
        &#x27;Juillet&#x27;, &#x27;Août&#x27;, &#x27;Septembre&#x27;, &#x27;Octobre&#x27;, &#x27;Novembre&#x27;, &#x27;Décembre&#x27;
      ],
      monthNamesShort: [&#x27;Jan&#x27;, &#x27;Fév&#x27;, &#x27;Mar&#x27;, &#x27;Avr&#x27;, &#x27;Mai&#x27;, &#x27;Jun&#x27;, &#x27;Jul&#x27;, &#x27;Aoû&#x27;, &#x27;Sep&#x27;, &#x27;Oct&#x27;, &#x27;Nov&#x27;, &#x27;Déc&#x27;],
      today: &#x27;Aujourd\&#x27;hui&#x27;,
      clear: &#x27;Effacer&#x27;,
      dateFormat: &#x27;mm/yy&#x27;,
      weekHeader: &#x27;Sem&#x27;
    });
  }

}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">
&lt;!--
  This example requires updating your template:

  &#x60;&#x60;&#x60;
  &lt;html class&#x3D;&quot;h-full bg-gray-100&quot;&gt;
  &lt;body class&#x3D;&quot;h-full&quot;&gt;
  &#x60;&#x60;&#x60;
--&gt;

&lt;!--- ***************************************** --&gt;
  &lt;!-- Off-canvas menu for mobile, show/hide based on off-canvas menu state. --&gt;
&lt;!--- ***************************************** --&gt;

@if (isSidebarOpen){
  &lt;div   class&#x3D;&quot;fixed inset-0 flex z-40 lg:hidden&quot; role&#x3D;&quot;dialog&quot; aria-modal&#x3D;&quot;true&quot;&gt;
    &lt;div class&#x3D;&quot;fixed inset-0 bg-gray-600 bg-opacity-75&quot; aria-hidden&#x3D;&quot;true&quot;&gt;&lt;/div&gt;
    &lt;div  class&#x3D;&quot;relative flex-1 flex flex-col max-w-xs w-full pt-5 pb-4 bg-cyan-700&quot;&gt;
      &lt;div class&#x3D;&quot;absolute top-0 right-0 -mr-12 pt-2&quot;&gt;
        &lt;button  (click)&#x3D;&quot;closeSidebar()&quot;  type&#x3D;&quot;button&quot; class&#x3D;&quot;ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white&quot;&gt;
          &lt;span class&#x3D;&quot;sr-only&quot;&gt;Close sidebar&lt;/span&gt;
          &lt;!-- Heroicon name: outline/x --&gt;
          &lt;svg class&#x3D;&quot;h-6 w-6 text-white&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
            &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M6 18L18 6M6 6l12 12&quot; /&gt;
          &lt;/svg&gt;
        &lt;/button&gt;
      &lt;/div&gt;

      &lt;div class&#x3D;&quot;flex-shrink-0 flex items-center px-4&quot;&gt;
        &lt;img class&#x3D;&quot;h-8 w-auto&quot; src&#x3D;&quot;https://tailwindui.com/img/logos/easywire-logo-cyan-300-mark-white-text.svg&quot; alt&#x3D;&quot;Easywire logo&quot;&gt;
      &lt;/div&gt;
      &lt;nav  class&#x3D;&quot;mt-5 flex-shrink-0 h-full divide-y divide-cyan-800 overflow-y-auto&quot; aria-label&#x3D;&quot;Sidebar&quot;&gt;
        &lt;div class&#x3D;&quot;px-2 space-y-1&quot;&gt;
          &lt;!-- Current: &quot;bg-cyan-800 text-white&quot;, Default: &quot;text-cyan-100 hover:text-white hover:bg-cyan-600&quot; --&gt;
          &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;bg-cyan-800 text-white group flex items-center px-2 py-2 text-base font-medium rounded-md&quot; aria-current&#x3D;&quot;page&quot;&gt;
            &lt;!-- Heroicon name: outline/home --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6&quot; /&gt;
            &lt;/svg&gt;
            Accueil
          &lt;/a&gt;

          &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/clock --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z&quot; /&gt;
            &lt;/svg&gt;
            History
          &lt;/a&gt;

          &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/scale --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3&quot; /&gt;
            &lt;/svg&gt;
            Balances
          &lt;/a&gt;

          &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/credit-card --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z&quot; /&gt;
            &lt;/svg&gt;
            Cards
          &lt;/a&gt;

          &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/user-group --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z&quot; /&gt;
            &lt;/svg&gt;
            Recipients
          &lt;/a&gt;

          &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/document-report --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z&quot; /&gt;
            &lt;/svg&gt;
            Service
          &lt;/a&gt;
        &lt;/div&gt;
        &lt;div class&#x3D;&quot;mt-6 pt-6&quot;&gt;
          &lt;div class&#x3D;&quot;px-2 space-y-1&quot;&gt;
            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;
              &lt;!-- Heroicon name: outline/cog --&gt;
              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z&quot; /&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M15 12a3 3 0 11-6 0 3 3 0 016 0z&quot; /&gt;
              &lt;/svg&gt;
              Settings
            &lt;/a&gt;

            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;
              &lt;!-- Heroicon name: outline/question-mark-circle --&gt;
              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z&quot; /&gt;
              &lt;/svg&gt;
              Help
            &lt;/a&gt;

            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;
              &lt;!-- Heroicon name: outline/shield-check --&gt;
              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z&quot; /&gt;
              &lt;/svg&gt;
              Privacy
            &lt;/a&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/nav&gt;
    &lt;/div&gt;

    &lt;div class&#x3D;&quot;flex-shrink-0 w-14&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
      &lt;!-- Dummy element to force sidebar to shrink to fit close icon --&gt;
    &lt;/div&gt;
  &lt;/div&gt;
}

&lt;div&gt;

&lt;/div&gt;

&lt;!--- ***************************************** --&gt;
  &lt;!-- Static sidebar for desktop --&gt;
&lt;!--- ***************************************** --&gt;

  &lt;div class&#x3D;&quot;hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0&quot;&gt;
    &lt;!-- Sidebar component, swap this element with another sidebar if you like --&gt;
    &lt;div class&#x3D;&quot;flex flex-col flex-grow bg-cyan-700 pt-5 pb-4 overflow-y-auto&quot;&gt;
      &lt;div class&#x3D;&quot;flex items-center flex-shrink-0 px-4&quot;&gt;
&lt;!--        &lt;img class&#x3D;&quot;h-8 w-auto&quot; src&#x3D;&quot;https://tailwindui.com/img/logos/easywire-logo-cyan-300-mark-white-text.svg&quot; alt&#x3D;&quot;Easywire logo&quot;&gt;--&gt;
        &lt;h1 class&#x3D;&quot;text-white text-2xl font-bold tracking-wide&quot;&gt;Supra&lt;/h1&gt;
      &lt;/div&gt;
      &lt;nav class&#x3D;&quot;mt-5 flex-1 flex flex-col divide-y divide-cyan-800 overflow-y-auto&quot; aria-label&#x3D;&quot;Sidebar&quot;&gt;
        &lt;div class&#x3D;&quot;px-2 space-y-1&quot;&gt;
          &lt;!-- Current: &quot;bg-cyan-800 text-white&quot;, Default: &quot;text-cyan-100 hover:text-white hover:bg-cyan-600&quot; --&gt;
          &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;bg-cyan-800 text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md&quot; aria-current&#x3D;&quot;page&quot;&gt;
            &lt;!-- Heroicon name: outline/home --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6&quot; /&gt;
            &lt;/svg&gt;
            Accueil
          &lt;/a&gt;

          &lt;a routerLink&#x3D;&quot;/graphic/dashboard&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/clock --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot;  &gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M6 20.25h12m-7.5-3v3m3-3v3m-10.125-3h17.25c.621 0 1.125-.504 1.125-1.125V4.875c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125Z&quot; /&gt;
            &lt;/svg&gt;
            Tableau de Bord
          &lt;/a&gt;


          &lt;a routerLink&#x3D;&quot;/acte/single-acte&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/scale --&gt;
            &lt;svg  class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot; &gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z&quot; /&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z&quot; /&gt;
            &lt;/svg&gt;
            Activités
          &lt;/a&gt;


          &lt;a routerLink&#x3D;&quot;/graphic/praticien&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/user-group --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z&quot; /&gt;
            &lt;/svg&gt;
            Praticiens
          &lt;/a&gt;

          &lt;a routerLink&#x3D;&quot;/organisation/service&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/building-office --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot; &gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z&quot; /&gt;
            &lt;/svg&gt;

            Services
          &lt;/a&gt;
          &lt;a routerLink&#x3D;&quot;/organisation/uf&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md&quot;&gt;
            &lt;!-- Heroicon name: outline/building-office --&gt;
            &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot;  &gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21&quot; /&gt;
            &lt;/svg&gt;

            UF
          &lt;/a&gt;

        &lt;/div&gt;

&lt;!--        tiret un  --&gt;
        &lt;div class&#x3D;&quot;mt-6 pt-6&quot;&gt;
&lt;!--          SIGAPS--&gt;
          &lt;div class&#x3D;&quot;px-2 space-y-1&quot;&gt;
            &lt;a routerLink&#x3D;&quot;/sigaps&quot; class&#x3D;&quot;text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md&quot;&gt;
              &lt;!-- Heroicon name: outline/credit-card --&gt;
              &lt;svg class&#x3D;&quot;mr-4 flex-shrink-0 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z&quot; /&gt;
              &lt;/svg&gt;
              SIGAPS
            &lt;/a&gt;

            &lt;a routerLink&#x3D;&quot;enseignement&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;
              &lt;!-- Heroicon name: outline/cog --&gt;

              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5&quot; /&gt;
              &lt;/svg&gt;
              Enseignements
            &lt;/a&gt;

            &lt;a routerLink&#x3D;&quot;garde-et-astreinte&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;
              &lt;!-- Heroicon name: outline/moon --&gt;
              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot;  xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z&quot; /&gt;
              &lt;/svg&gt;
              Garde et Astreinte
            &lt;/a&gt;
          &lt;/div&gt;
&lt;!--          DFAC--&gt;
&lt;!--          &lt;div class&#x3D;&quot;px-2 space-y-1&quot;&gt;--&gt;
&lt;!--            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;--&gt;
&lt;!--              &amp;lt;!&amp;ndash; Icône money &amp;ndash;&amp;gt;--&gt;
&lt;!--              &lt;svg  class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot;&gt;--&gt;
&lt;!--                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M14.121 7.629A3 3 0 0 0 9.017 9.43c-.023.212-.002.425.028.636l.506 3.541a4.5 4.5 0 0 1-.43 2.65L9 16.5l1.539-.513a2.25 2.25 0 0 1 1.422 0l.655.218a2.25 2.25 0 0 0 1.718-.122L15 15.75M8.25 12H12m9 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z&quot; /&gt;--&gt;
&lt;!--              &lt;/svg&gt;--&gt;

&lt;!--              Suivi Financier--&gt;
&lt;!--            &lt;/a&gt;--&gt;

&lt;!--            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;--&gt;
&lt;!--              &amp;lt;!&amp;ndash; Icône Stat &amp;ndash;&amp;gt;--&gt;
&lt;!--              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot;&gt;--&gt;
&lt;!--                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0 0 20.25 18V6A2.25 2.25 0 0 0 18 3.75H6A2.25 2.25 0 0 0 3.75 6v12A2.25 2.25 0 0 0 6 20.25Z&quot; /&gt;--&gt;
&lt;!--              &lt;/svg&gt;--&gt;
&lt;!--              Rapports Statistiques--&gt;
&lt;!--            &lt;/a&gt;--&gt;
&lt;!--          &lt;/div&gt;--&gt;
        &lt;/div&gt;

        &lt;!-- Tiret deux--&gt;
        &lt;div class&#x3D;&quot;mt-6 pt-6&quot;&gt;
          &lt;div class&#x3D;&quot;px-2 space-y-1&quot;&gt;
&lt;!--            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;--&gt;
&lt;!--              &amp;lt;!&amp;ndash; Heroicon name: outline/cog &amp;ndash;&amp;gt;--&gt;
&lt;!--              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;--&gt;
&lt;!--                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z&quot; /&gt;--&gt;
&lt;!--                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M15 12a3 3 0 11-6 0 3 3 0 016 0z&quot; /&gt;--&gt;
&lt;!--              &lt;/svg&gt;--&gt;
&lt;!--              Paramètres--&gt;
&lt;!--            &lt;/a&gt;--&gt;

            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;
              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z&quot; /&gt;
              &lt;/svg&gt;
              Aide
            &lt;/a&gt;

            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600&quot;&gt;
              &lt;!-- Heroicon name: outline/shield-check --&gt;
              &lt;svg class&#x3D;&quot;mr-4 h-6 w-6 text-cyan-200&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z&quot; /&gt;
              &lt;/svg&gt;
              Confidentialité
            &lt;/a&gt;
          &lt;/div&gt;
        &lt;/div&gt;


        &lt;!-- Sélecteur d&#x27;année en bas de la sidebar --&gt;
&lt;!--        &lt;div class&#x3D;&quot;px-4 py-4 border-t border-cyan-600 mt-auto&quot;&gt;--&gt;
&lt;!--          &lt;label for&#x3D;&quot;startYear&quot; class&#x3D;&quot;block text-sm font-medium text-cyan-200 mb-2&quot;&gt;Sélectionner la plage d&#x27;années&lt;/label&gt;--&gt;
&lt;!--          &lt;div class&#x3D;&quot;flex space-x-2&quot;&gt;--&gt;
&lt;!--            &lt;select id&#x3D;&quot;startYear&quot; class&#x3D;&quot;w-1/2 px-3 py-2 bg-cyan-600 text-white text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent&quot;&gt;--&gt;
&lt;!--              &lt;option value&#x3D;&quot;2022&quot;&gt;2022&lt;/option&gt;--&gt;
&lt;!--              &lt;option value&#x3D;&quot;2023&quot;&gt;2023&lt;/option&gt;--&gt;
&lt;!--              &lt;option value&#x3D;&quot;2024&quot;&gt;2024&lt;/option&gt;--&gt;
&lt;!--              &lt;option value&#x3D;&quot;2025&quot;&gt;2025&lt;/option&gt;--&gt;
&lt;!--            &lt;/select&gt;--&gt;
&lt;!--            &lt;span class&#x3D;&quot;text-cyan-200&quot;&gt;-&lt;/span&gt;--&gt;
&lt;!--            &lt;select id&#x3D;&quot;endYear&quot; class&#x3D;&quot;w-1/2 px-3 py-2 bg-cyan-600 text-white text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent&quot;&gt;--&gt;
&lt;!--              &lt;option value&#x3D;&quot;2023&quot;&gt;2023&lt;/option&gt;--&gt;
&lt;!--              &lt;option value&#x3D;&quot;2024&quot;&gt;2024&lt;/option&gt;--&gt;
&lt;!--              &lt;option value&#x3D;&quot;2025&quot;&gt;2025&lt;/option&gt;--&gt;
&lt;!--              &lt;option value&#x3D;&quot;2026&quot;&gt;2026&lt;/option&gt;--&gt;
&lt;!--            &lt;/select&gt;--&gt;
&lt;!--          &lt;/div&gt;--&gt;
&lt;!--        &lt;/div&gt;--&gt;

        &lt;div class&#x3D;&quot;p-4 border-t border-cyan-600 mt-auto bg-blend-darken&quot;&gt;
          &lt;label class&#x3D;&quot;text-white text-sm font-semibold mb-2 block&quot;&gt;Sélectionner une période&lt;/label&gt;
          &lt;div class&#x3D;&quot;flex items-center gap-4&quot;&gt;
            &lt;div class&#x3D;&quot;card flex justify-content-center&quot;&gt;
              &lt;p-calendar
                [(ngModel)]&#x3D;&quot;dateRange&quot;
                view&#x3D;&quot;month&quot;
                dateFormat&#x3D;&quot;mm/yy&quot;
                selectionMode&#x3D;&quot;range&quot;
                [readonlyInput]&#x3D;&quot;true&quot;
                showButtonBar&#x3D;&quot;true&quot;
                [inputStyle]&#x3D;&quot;{ &#x27;background-color&#x27;: &#x27;#0e7490&#x27;, &#x27;color&#x27;: &#x27;#ffffff&#x27;, &#x27;padding&#x27;: &#x27;8px&#x27;, &#x27;border-radius&#x27;: &#x27;6px&#x27;, &#x27;border&#x27;: &#x27;1px solid #0e7490&#x27; }&quot;
                panelStyleClass&#x3D;&quot;p-datepicker-calendar bg-cyan-700 text-white&quot;
                class&#x3D;&quot;w-full bg-cyan-800 border border-cyan-600   px-2 py-1 text-sm leading-6 font-medium rounded-md text-cyan-900 hover:text-cyan-950 hover:bg-cyan-600&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/nav&gt;
    &lt;/div&gt;
  &lt;/div&gt;
</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><!--  This example requires updating your template:  ```  <html class="h-full bg-gray-100">  <body class="h-full">  ```--><!--- ***************************************** -->  <!-- Off-canvas menu for mobile, show/hide based on off-canvas menu state. --><!--- ***************************************** -->@if (isSidebarOpen){  <div   class="fixed inset-0 flex z-40 lg:hidden" role="dialog" aria-modal="true">    <div class="fixed inset-0 bg-gray-600 bg-opacity-75" aria-hidden="true"></div>    <div  class="relative flex-1 flex flex-col max-w-xs w-full pt-5 pb-4 bg-cyan-700">      <div class="absolute top-0 right-0 -mr-12 pt-2">        <button  (click)="closeSidebar()"  type="button" class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">          <span class="sr-only">Close sidebar</span>          <!-- Heroicon name: outline/x -->          <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />          </svg>        </button>      </div>      <div class="flex-shrink-0 flex items-center px-4">        <img class="h-8 w-auto" src="https://tailwindui.com/img/logos/easywire-logo-cyan-300-mark-white-text.svg" alt="Easywire logo">      </div>      <nav  class="mt-5 flex-shrink-0 h-full divide-y divide-cyan-800 overflow-y-auto" aria-label="Sidebar">        <div class="px-2 space-y-1">          <!-- Current: "bg-cyan-800 text-white", Default: "text-cyan-100 hover:text-white hover:bg-cyan-600" -->          <a href="#" class="bg-cyan-800 text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" aria-current="page">            <!-- Heroicon name: outline/home -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />            </svg>            Accueil          </a>          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">            <!-- Heroicon name: outline/clock -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />            </svg>            History          </a>          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">            <!-- Heroicon name: outline/scale -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />            </svg>            Balances          </a>          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">            <!-- Heroicon name: outline/credit-card -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />            </svg>            Cards          </a>          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">            <!-- Heroicon name: outline/user-group -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />            </svg>            Recipients          </a>          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">            <!-- Heroicon name: outline/document-report -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />            </svg>            Service          </a>        </div>        <div class="mt-6 pt-6">          <div class="px-2 space-y-1">            <a href="#" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">              <!-- Heroicon name: outline/cog -->              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />              </svg>              Settings            </a>            <a href="#" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">              <!-- Heroicon name: outline/question-mark-circle -->              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />              </svg>              Help            </a>            <a href="#" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">              <!-- Heroicon name: outline/shield-check -->              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />              </svg>              Privacy            </a>          </div>        </div>      </nav>    </div>    <div class="flex-shrink-0 w-14" aria-hidden="true">      <!-- Dummy element to force sidebar to shrink to fit close icon -->    </div>  </div>}<div></div><!--- ***************************************** -->  <!-- Static sidebar for desktop --><!--- ***************************************** -->  <div class="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">    <!-- Sidebar component, swap this element with another sidebar if you like -->    <div class="flex flex-col flex-grow bg-cyan-700 pt-5 pb-4 overflow-y-auto">      <div class="flex items-center flex-shrink-0 px-4"><!--        <img class="h-8 w-auto" src="https://tailwindui.com/img/logos/easywire-logo-cyan-300-mark-white-text.svg" alt="Easywire logo">-->        <h1 class="text-white text-2xl font-bold tracking-wide">Supra</h1>      </div>      <nav class="mt-5 flex-1 flex flex-col divide-y divide-cyan-800 overflow-y-auto" aria-label="Sidebar">        <div class="px-2 space-y-1">          <!-- Current: "bg-cyan-800 text-white", Default: "text-cyan-100 hover:text-white hover:bg-cyan-600" -->          <a href="#" class="bg-cyan-800 text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md" aria-current="page">            <!-- Heroicon name: outline/home -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />            </svg>            Accueil          </a>          <a routerLink="/graphic/dashboard" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md">            <!-- Heroicon name: outline/clock -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"  >              <path stroke-linecap="round" stroke-linejoin="round" d="M6 20.25h12m-7.5-3v3m3-3v3m-10.125-3h17.25c.621 0 1.125-.504 1.125-1.125V4.875c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125Z" />            </svg>            Tableau de Bord          </a>          <a routerLink="/acte/single-acte" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md">            <!-- Heroicon name: outline/scale -->            <svg  class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >              <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />              <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />            </svg>            Activités          </a>          <a routerLink="/graphic/praticien" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md">            <!-- Heroicon name: outline/user-group -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />            </svg>            Praticiens          </a>          <a routerLink="/organisation/service" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md">            <!-- Heroicon name: outline/building-office -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >              <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z" />            </svg>            Services          </a>          <a routerLink="/organisation/uf" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md">            <!-- Heroicon name: outline/building-office -->            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"  >              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21" />            </svg>            UF          </a>        </div><!--        tiret un  -->        <div class="mt-6 pt-6"><!--          SIGAPS-->          <div class="px-2 space-y-1">            <a routerLink="/sigaps" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md">              <!-- Heroicon name: outline/credit-card -->              <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />              </svg>              SIGAPS            </a>            <a routerLink="enseignement" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">              <!-- Heroicon name: outline/cog -->              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">                <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5" />              </svg>              Enseignements            </a>            <a routerLink="garde-et-astreinte" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">              <!-- Heroicon name: outline/moon -->              <svg class="mr-4 h-6 w-6 text-cyan-200"  xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">                <path stroke-linecap="round" stroke-linejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z" />              </svg>              Garde et Astreinte            </a>          </div><!--          DFAC--><!--          <div class="px-2 space-y-1">--><!--            <a href="#" class="group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">--><!--              &lt;!&ndash; Icône money &ndash;&gt;--><!--              <svg  class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">--><!--                <path stroke-linecap="round" stroke-linejoin="round" d="M14.121 7.629A3 3 0 0 0 9.017 9.43c-.023.212-.002.425.028.636l.506 3.541a4.5 4.5 0 0 1-.43 2.65L9 16.5l1.539-.513a2.25 2.25 0 0 1 1.422 0l.655.218a2.25 2.25 0 0 0 1.718-.122L15 15.75M8.25 12H12m9 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />--><!--              </svg>--><!--              Suivi Financier--><!--            </a>--><!--            <a href="#" class="group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">--><!--              &lt;!&ndash; Icône Stat &ndash;&gt;--><!--              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">--><!--                <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 14.25v2.25m3-4.5v4.5m3-6.75v6.75m3-9v9M6 20.25h12A2.25 2.25 0 0 0 20.25 18V6A2.25 2.25 0 0 0 18 3.75H6A2.25 2.25 0 0 0 3.75 6v12A2.25 2.25 0 0 0 6 20.25Z" />--><!--              </svg>--><!--              Rapports Statistiques--><!--            </a>--><!--          </div>-->        </div>        <!-- Tiret deux-->        <div class="mt-6 pt-6">          <div class="px-2 space-y-1"><!--            <a href="#" class="group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">--><!--              &lt;!&ndash; Heroicon name: outline/cog &ndash;&gt;--><!--              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">--><!--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />--><!--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />--><!--              </svg>--><!--              Paramètres--><!--            </a>-->            <a href="#" class="group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />              </svg>              Aide            </a>            <a href="#" class="group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">              <!-- Heroicon name: outline/shield-check -->              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />              </svg>              Confidentialité            </a>          </div>        </div>        <!-- Sélecteur d\'année en bas de la sidebar --><!--        <div class="px-4 py-4 border-t border-cyan-600 mt-auto">--><!--          <label for="startYear" class="block text-sm font-medium text-cyan-200 mb-2">Sélectionner la plage d\'années</label>--><!--          <div class="flex space-x-2">--><!--            <select id="startYear" class="w-1/2 px-3 py-2 bg-cyan-600 text-white text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent">--><!--              <option value="2022">2022</option>--><!--              <option value="2023">2023</option>--><!--              <option value="2024">2024</option>--><!--              <option value="2025">2025</option>--><!--            </select>--><!--            <span class="text-cyan-200">-</span>--><!--            <select id="endYear" class="w-1/2 px-3 py-2 bg-cyan-600 text-white text-sm rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent">--><!--              <option value="2023">2023</option>--><!--              <option value="2024">2024</option>--><!--              <option value="2025">2025</option>--><!--              <option value="2026">2026</option>--><!--            </select>--><!--          </div>--><!--        </div>-->        <div class="p-4 border-t border-cyan-600 mt-auto bg-blend-darken">          <label class="text-white text-sm font-semibold mb-2 block">Sélectionner une période</label>          <div class="flex items-center gap-4">            <div class="card flex justify-content-center">              <p-calendar                [(ngModel)]="dateRange"                view="month"                dateFormat="mm/yy"                selectionMode="range"                [readonlyInput]="true"                showButtonBar="true"                [inputStyle]="{ \'background-color\': \'#0e7490\', \'color\': \'#ffffff\', \'padding\': \'8px\', \'border-radius\': \'6px\', \'border\': \'1px solid #0e7490\' }"                panelStyleClass="p-datepicker-calendar bg-cyan-700 text-white"                class="w-full bg-cyan-800 border border-cyan-600   px-2 py-1 text-sm leading-6 font-medium rounded-md text-cyan-900 hover:text-cyan-950 hover:bg-cyan-600"              />            </div>          </div>        </div>      </nav>    </div>  </div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'SidebarComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'SidebarComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
