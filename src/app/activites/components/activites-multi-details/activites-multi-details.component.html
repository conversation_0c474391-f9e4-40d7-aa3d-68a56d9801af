<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

<!-- Loading state -->
<div *ngIf="loading" class="p-4">
  <div class="flex justify-center items-center p-8">
    <i class="pi pi-spin pi-spinner text-cyan-700 text-2xl mr-2"></i>
    <span>Chargement des données...</span>
  </div>
</div>

<!-- Error state -->
<div *ngIf="!loading && error" class="p-4">
  <div class="p-4 bg-red-100 text-red-800 rounded-lg">
    <div class="flex items-center">
      <i class="pi pi-exclamation-triangle text-red-600 text-xl mr-2"></i>
      <span>{{ error }}</span>
    </div>
    <div class="mt-4">
      <button pButton label="Retour à la liste" icon="pi pi-arrow-left" (click)="goBack()" class="p-button-secondary"></button>
    </div>
  </div>
</div>

<!-- No activities selected -->
<div *ngIf="!loading && !error && activites.length === 0" class="p-4">
  <div class="p-4 bg-yellow-100 text-yellow-800 rounded-lg">
    <div class="flex items-center">
      <i class="pi pi-info-circle text-yellow-600 text-xl mr-2"></i>
      <span>Aucune activité sélectionnée</span>
    </div>
    <div class="mt-4">
      <button pButton label="Retour à la liste" icon="pi pi-arrow-left" (click)="goBack()" class="p-button-secondary"></button>
    </div>
  </div>
</div>

<!-- Content when data is loaded -->
<div *ngIf="!loading && !error && activites.length > 0" class="p-4">
  <!-- Header with actions -->
  <div class="flex justify-between items-center mb-4">
    <div class="flex items-center">
      <i class="pi pi-list text-cyan-700 text-2xl mr-2"></i>
      <h1 class="text-xl font-bold text-gray-700">Détails des activités sélectionnées ({{ activites.length }})</h1>
    </div>
    <div>
      <button pButton label="Retour à la liste" icon="pi pi-arrow-left" (click)="goBack()" class="p-button-secondary mr-2"></button>
    </div>
  </div>

  <!-- Summary Card -->
  <p-card styleClass="mb-4">
    <ng-template pTemplate="header">
      <div class="p-3 bg-gray-50 border-b">
        <h2 class="text-lg font-semibold text-gray-700">Résumé des activités</h2>
      </div>
    </ng-template>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Nombre d'activités</label>
        <div class="text-base font-bold">{{ activites.length }}</div>
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Types d'actes</label>
        <div class="flex flex-wrap gap-2">
          <span *ngIf="hasActivityType('CCAM')"
                class="px-2 py-1 rounded-full text-xs font-medium"
                [ngClass]="getActivityTypeClass('CCAM')">
            CCAM ({{ countActivitiesByType('CCAM') }})
          </span>
          <span *ngIf="hasActivityType('NGAP')"
                class="px-2 py-1 rounded-full text-xs font-medium"
                [ngClass]="getActivityTypeClass('NGAP')">
            NGAP ({{ countActivitiesByType('NGAP') }})
          </span>
          <span *ngIf="hasActivityType('LABO')"
                class="px-2 py-1 rounded-full text-xs font-medium"
                [ngClass]="getActivityTypeClass('LABO')">
            LABO ({{ countActivitiesByType('LABO') }})
          </span>
          <span *ngIf="hasActivityType('NABM')"
                class="px-2 py-1 rounded-full text-xs font-medium"
                [ngClass]="getActivityTypeClass('NABM')">
            NABM ({{ countActivitiesByType('NABM') }})
          </span>
        </div>
      </div>

      <div class="p-field">
        <label class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
        <div class="flex flex-wrap gap-2">
          <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Actifs ({{ countActiveActivities() }})
          </span>
          <span *ngIf="hasInactiveActivities()" class="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
            Inactifs ({{ countInactiveActivities() }})
          </span>
        </div>
      </div>
    </div>
  </p-card>

  <!-- Activities Table -->
  <p-card styleClass="mb-4">
    <ng-template pTemplate="header">
      <div class="p-3 bg-gray-50 border-b">
        <h2 class="text-lg font-semibold text-gray-700 mb-2">Liste des activités</h2>
        <div class="flex items-center text-sm text-gray-600">
          <i class="pi pi-info-circle mr-2 text-blue-500"></i>
          <span>Cliquez sur la flèche <i class="pi pi-chevron-right mx-1 text-xs"></i> pour ouvrir/fermer les détails de chaque activité</span>
        </div>
      </div>
    </ng-template>

    <p-table [value]="activites" [paginator]="true" [rows]="10" [rowsPerPageOptions]="[5, 10, 20, 50]"
             styleClass="p-datatable-sm" [globalFilterFields]="['code', 'description', 'typeActe']" dataKey="@id"
             [expandedRowKeys]="expandedRows">
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 3rem"></th>
          <th pSortableColumn="code">Code <p-sortIcon field="code"></p-sortIcon></th>
          <th pSortableColumn="description">Description <p-sortIcon field="description"></p-sortIcon></th>
          <th pSortableColumn="typeActe">Type <p-sortIcon field="typeActe"></p-sortIcon></th>
          <th pSortableColumn="dateRealisation">Date <p-sortIcon field="dateRealisation"></p-sortIcon></th>
          <th>Praticien</th>
          <th>UF</th>
          <th>Détails</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-activite let-expanded="expanded" let-i="rowIndex">
        <tr>
          <td>
            <button type="button" pButton pRipple [pRowToggler]="activite" class="p-button-text p-button-rounded p-button-plain"
                    [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
          </td>
          <td>
            <a
              class="font-semibold text-cyan-700 hover:text-cyan-900 hover:underline cursor-pointer"
              (click)="navigateToSingleActivite(activite['@id'])"
              [title]="'Voir les détails de l\'activité ' + activite.code">
              {{ activite.code }}
            </a>
          </td>
          <td>{{ activite.description }}</td>
          <td>
            <span class="px-2 py-1 rounded-full text-xs font-medium" [ngClass]="getActivityTypeClass(activite.typeActe)">
              {{ activite.typeActe }}
            </span>
          </td>
          <td>{{ formatDate(activite.dateRealisation) }}</td>
          <td>{{ activite.agent.fullName || activite.agent.prenom + ' ' + activite.agent.nom }}</td>
          <td>{{ activite.ufPrincipal.libelle }} ({{ activite.ufPrincipal.ufcode }})</td>
          <td>
            <div class="flex flex-wrap gap-2">
              <!-- Nombre de réalisations -->
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" pTooltip="nombre de realisation" tooltipPosition="top">
                <i class="pi pi-hashtag mr-1"></i>NB: {{ activite.nombreDeRealisation }}
              </span>

              <!-- Type de venue -->
              <span class="px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800" pTooltip="Type de venue" tooltipPosition="top">
                <i class="pi pi-tag mr-1"></i>{{ activite.libTypeVenue }}
              </span>

              <!-- ICR, Coefficient, Lettre - only for LABO/NABM -->
              <ng-container *ngIf="activite.icrA">
                <span class="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800" pTooltip="ICR" tooltipPosition="top">
                  <i class="pi pi-chart-line mr-1"></i>ICR: {{ activite.icrA }}
                </span>
              </ng-container>

              <ng-container *ngIf="activite.coefficient">
                <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" pTooltip="Coefficient" tooltipPosition="top">
                  <i class="pi pi-percentage mr-1"></i>Coef: {{ activite.coefficient }}
                </span>
              </ng-container>

              <ng-container *ngIf="activite.lettreCoef">
                <span class="px-2 py-1 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800" pTooltip="Lettre" tooltipPosition="top">
                  <i class="pi pi-bookmark mr-1"></i>{{ activite.lettreCoef }}
                </span>
              </ng-container>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="rowexpansion" let-activite>
        <tr>
          <td colspan="8">
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left column: Basic info with improved styling -->
                <div class="space-y-4">
                  <h3 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                    <i class="pi pi-info-circle text-cyan-600 mr-2"></i>
                    Informations générales
                  </h3>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Code avec badge -->
                    <div class="p-field">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-tag text-cyan-600 mr-1"></i>
                        Code
                      </label>
                      <div class="text-base">
                        <a
                          class="px-3 py-1 bg-cyan-100 text-cyan-800 rounded-lg font-semibold hover:bg-cyan-200 cursor-pointer transition-colors"
                          (click)="navigateToSingleActivite(activite['@id'])"
                          [title]="'Voir les détails de l\'activité ' + activite.code">
                          {{ activite.code }}
                        </a>
                      </div>
                    </div>

                    <!-- Type d'acte avec badge coloré -->
                    <div class="p-field">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-bookmark text-blue-600 mr-1"></i>
                        Type d'acte
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 rounded-lg font-medium" [ngClass]="getActivityTypeClass(activite.typeActe)">
                          {{ activite.typeActe }}
                        </span>
                      </div>
                    </div>

                    <!-- Activité -->
                    <div class="p-field">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-folder text-purple-600 mr-1"></i>
                        Activité
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-lg font-semibold">{{ activite.activite }}</span>
                      </div>
                    </div>

                    <!-- Libellé activité -->
                    <div class="p-field">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-sitemap text-indigo-600 mr-1"></i>
                        Libellé activité
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-lg">{{ activite.activiteLib }}</span>
                      </div>
                    </div>

                    <!-- Date de réalisation -->
                    <div class="p-field">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-calendar text-green-600 mr-1"></i>
                        Date de réalisation
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-lg">{{ formatDate(activite.dateRealisation) }}</span>
                      </div>
                    </div>

                    <!-- Nombre de réalisations -->
                    <div class="p-field">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-hashtag text-orange-600 mr-1"></i>
                        Nombre de réalisations
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-lg font-bold">{{ activite.nombreDeRealisation }}</span>
                      </div>
                    </div>

                    <!-- Type de venue -->
                    <div class="p-field">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-map-marker text-red-600 mr-1"></i>
                        Type de venue
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 bg-red-100 text-red-800 rounded-lg">{{ activite.libTypeVenue }}</span>
                      </div>
                    </div>

                    <!-- Champs spécifiques LABO/NABM -->
                    <div class="p-field" *ngIf="activite.icrA">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-star text-yellow-600 mr-1"></i>
                        ICR A
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-lg font-medium">{{ activite.icrA }}</span>
                      </div>
                    </div>

                    <div class="p-field" *ngIf="activite.coefficient">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-percentage text-red-600 mr-1"></i>
                        Coefficient
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 bg-red-100 text-red-800 rounded-lg font-medium">{{ activite.coefficient }}</span>
                      </div>
                    </div>

                    <div class="p-field" *ngIf="activite.lettreCoef">
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="pi pi-sort-alpha-down text-pink-600 mr-1"></i>
                        Lettre coefficient
                      </label>
                      <div class="text-base">
                        <span class="px-3 py-1 bg-pink-100 text-pink-800 rounded-lg font-medium">{{ activite.lettreCoef }}</span>
                      </div>
                    </div>

                    <!-- Description avec bouton d'action -->
                    <div class="p-field col-span-2">
                      <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">
                          <i class="pi pi-file-edit text-gray-600 mr-1"></i>
                          Description
                        </label>
                        <button
                          pButton
                          icon="pi pi-external-link"
                          label="Voir détails"
                          class="p-button-sm p-button-outlined p-button-primary"
                          (click)="navigateToSingleActivite(activite['@id'])"
                          [pTooltip]="'Voir tous les détails de l\'activité ' + activite.code"
                          tooltipPosition="left">
                        </button>
                      </div>
                      <div class="text-base p-3 bg-gray-50 rounded-lg border">{{ activite.description }}</div>
                    </div>
                  </div>
                </div>

                <!-- Right column: UF Tabs -->
                <div class="space-y-4">
                  <h3 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                    <i class="pi pi-building text-indigo-600 mr-2"></i>
                    Unités Fonctionnelles
                  </h3>

                  <p-tabView styleClass="custom-tabview">
                    <!-- UF Principal -->
                    <p-tabPanel header="UF Principal">
                      <div class="space-y-3">
                        <div class="flex items-center gap-3 mb-3">
                          <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-blue-100 text-blue-800">
                            <i class="pi pi-home mr-2"></i>
                            {{ activite.ufPrincipal.ufcode }}
                          </span>
                          <button
                            pButton
                            icon="pi pi-external-link"
                            label="Voir UF"
                            class="p-button-sm p-button-outlined p-button-secondary"
                            [routerLink]="['/structure/uf', extractIdFromApiUrl(activite.ufPrincipal['@id'])]"
                            pTooltip="Voir la fiche complète de cette UF"
                            tooltipPosition="left">
                          </button>
                        </div>

                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-tag text-blue-600 mr-1"></i>
                            Code UF
                          </label>
                          <div class="text-base">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded font-medium">{{ activite.ufPrincipal.ufcode }}</span>
                          </div>
                        </div>

                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-bookmark text-blue-600 mr-1"></i>
                            Libellé
                          </label>
                          <div class="text-base">{{ activite.ufPrincipal.libelle }}</div>
                        </div>

                        <!-- Organisation UF Principal -->
                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-sitemap text-indigo-600 mr-1"></i>
                            Organisation
                            <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium" *ngIf="periods.p1">
                              P1: {{ formatDateMonthYear(periods.p1.dateDebut) }} - {{ formatDateMonthYear(periods.p1.dateFin) }}
                            </span>
                          </label>

                          <!-- Loading state -->
                          <div *ngIf="isOrganisationLoading(activite, 'principal')" class="flex items-center gap-2 py-2">
                            <i class="pi pi-spin pi-spinner text-indigo-600"></i>
                            <span class="text-sm text-gray-600">Chargement de l'organisation...</span>
                          </div>

                          <!-- Organisation data -->
                          <div *ngIf="!isOrganisationLoading(activite, 'principal') && getOrganisationForActivite(activite, 'principal') as orgPrincipal" class="text-base">
                            <div class="flex items-center gap-2 mb-2">
                              <span class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm font-medium">
                                CR: {{ orgPrincipal.crCode }}
                              </span>
                              <span class="text-gray-600 text-sm">{{ orgPrincipal.crLibelle }}</span>
                            </div>
                            <div class="flex items-center gap-2">
                              <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">
                                PÔLE: {{ orgPrincipal.poleCode }}
                              </span>
                              <span class="text-gray-600 text-sm">{{ orgPrincipal.poleLibelle }}</span>
                            </div>
                          </div>

                          <!-- Message si organisation non disponible -->
                          <div *ngIf="!isOrganisationLoading(activite, 'principal') && !getOrganisationForActivite(activite, 'principal')" class="text-base">
                            <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic text-sm">
                              Organisation non disponible
                            </span>
                          </div>
                        </div>
                      </div>
                    </p-tabPanel>

                    <!-- UF Demande -->
                    <p-tabPanel header="UF Demande">
                      <!-- Cas où ufDemande est null -->
                      <div *ngIf="!activite.ufDemande" class="p-4 text-center">
                        <div class="flex flex-col items-center">
                          <i class="pi pi-exclamation-triangle text-red-500 text-2xl mb-2"></i>
                          <span class="text-red-600 font-medium mb-1">Données UF Demande indisponibles</span>
                          <span class="text-gray-500 text-sm">La récupération de l'UF Demande a échoué (contacter l'équipe technique)</span>
                        </div>
                      </div>

                      <!-- Cas où ufDemande existe -->
                      <div *ngIf="activite.ufDemande" class="space-y-3">
                        <div class="flex items-center gap-3 mb-3">
                          <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-orange-100 text-orange-800">
                            <i class="pi pi-send mr-2"></i>
                            {{ activite.ufDemande.ufcode }}
                          </span>
                          <button
                            pButton
                            icon="pi pi-external-link"
                            label="Voir UF"
                            class="p-button-sm p-button-outlined p-button-secondary"
                            [routerLink]="['/structure/uf', extractIdFromApiUrl(activite.ufDemande['@id'])]"
                            pTooltip="Voir la fiche complète de cette UF"
                            tooltipPosition="left">
                          </button>
                        </div>

                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-tag text-orange-600 mr-1"></i>
                            Code UF
                          </label>
                          <div class="text-base">
                            <span class="px-2 py-1 bg-orange-100 text-orange-800 rounded font-medium">{{ activite.ufDemande.ufcode }}</span>
                          </div>
                        </div>

                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-bookmark text-blue-600 mr-1"></i>
                            Libellé
                          </label>
                          <div class="text-base">{{ activite.ufDemande.libelle }}</div>
                        </div>

                        <!-- Organisation UF Demande -->
                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-sitemap text-indigo-600 mr-1"></i>
                            Organisation
                            <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium" *ngIf="periods.p1">
                              P1: {{ formatDateMonthYear(periods.p1.dateDebut) }} - {{ formatDateMonthYear(periods.p1.dateFin) }}
                            </span>
                          </label>

                          <!-- Loading state -->
                          <div *ngIf="isOrganisationLoading(activite, 'demande')" class="flex items-center gap-2 py-2">
                            <i class="pi pi-spin pi-spinner text-indigo-600"></i>
                            <span class="text-sm text-gray-600">Chargement de l'organisation...</span>
                          </div>

                          <!-- Organisation data -->
                          <div *ngIf="!isOrganisationLoading(activite, 'demande') && getOrganisationForActivite(activite, 'demande') as orgDemande" class="text-base">
                            <div class="flex items-center gap-2 mb-2">
                              <span class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm font-medium">
                                CR: {{ orgDemande.crCode }}
                              </span>
                              <span class="text-gray-600 text-sm">{{ orgDemande.crLibelle }}</span>
                            </div>
                            <div class="flex items-center gap-2">
                              <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">
                                PÔLE: {{ orgDemande.poleCode }}
                              </span>
                              <span class="text-gray-600 text-sm">{{ orgDemande.poleLibelle }}</span>
                            </div>
                          </div>

                          <!-- Message si organisation non disponible -->
                          <div *ngIf="!isOrganisationLoading(activite, 'demande') && !getOrganisationForActivite(activite, 'demande')" class="text-base">
                            <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic text-sm">
                              Organisation non disponible
                            </span>
                          </div>
                        </div>
                      </div>
                    </p-tabPanel>

                    <!-- UF Intervention -->
                    <p-tabPanel header="UF Intervention">
                      <!-- Cas où ufIntervention est null -->
                      <div *ngIf="!activite.ufIntervention" class="p-4 text-center">
                        <div class="flex flex-col items-center">
                          <i class="pi pi-exclamation-triangle text-red-500 text-2xl mb-2"></i>
                          <span class="text-red-600 font-medium mb-1">Données UF Intervention indisponibles</span>
                          <span class="text-gray-500 text-sm">La récupération de l'UF Intervention a échoué (contacter l'équipe technique)</span>
                        </div>
                      </div>

                      <!-- Cas où ufIntervention existe -->
                      <div *ngIf="activite.ufIntervention" class="space-y-3">
                        <div class="flex items-center gap-3 mb-3">
                          <span class="inline-flex items-center px-3 py-2 rounded-lg text-sm font-semibold bg-green-100 text-green-800">
                            <i class="pi pi-cog mr-2"></i>
                            {{ activite.ufIntervention.ufcode }}
                          </span>
                          <button
                            pButton
                            icon="pi pi-external-link"
                            label="Voir UF"
                            class="p-button-sm p-button-outlined p-button-secondary"
                            [routerLink]="['/structure/uf', extractIdFromApiUrl(activite.ufIntervention['@id'])]"
                            pTooltip="Voir la fiche complète de cette UF"
                            tooltipPosition="left">
                          </button>
                        </div>

                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-tag text-green-600 mr-1"></i>
                            Code UF
                          </label>
                          <div class="text-base">
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded font-medium">{{ activite.ufIntervention.ufcode }}</span>
                          </div>
                        </div>

                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-bookmark text-blue-600 mr-1"></i>
                            Libellé
                          </label>
                          <div class="text-base">{{ activite.ufIntervention.libelle }}</div>
                        </div>

                        <!-- Organisation UF Intervention -->
                        <div class="p-field">
                          <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="pi pi-sitemap text-indigo-600 mr-1"></i>
                            Organisation
                            <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium" *ngIf="periods.p1">
                              P1: {{ formatDateObject(periods.p1.dateDebut) }} - {{ formatDateObject(periods.p1.dateFin) }}
                            </span>
                          </label>

                          <!-- Loading state -->
                          <div *ngIf="isOrganisationLoading(activite, 'intervention')" class="flex items-center gap-2 py-2">
                            <i class="pi pi-spin pi-spinner text-indigo-600"></i>
                            <span class="text-sm text-gray-600">Chargement de l'organisation...</span>
                          </div>

                          <!-- Organisation data -->
                          <div *ngIf="!isOrganisationLoading(activite, 'intervention') && getOrganisationForActivite(activite, 'intervention') as orgIntervention" class="text-base">
                            <div class="flex items-center gap-2 mb-2">
                              <span class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm font-medium">
                                CR: {{ orgIntervention.crCode }}
                              </span>
                              <span class="text-gray-600 text-sm">{{ orgIntervention.crLibelle }}</span>
                            </div>
                            <div class="flex items-center gap-2">
                              <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">
                                PÔLE: {{ orgIntervention.poleCode }}
                              </span>
                              <span class="text-gray-600 text-sm">{{ orgIntervention.poleLibelle }}</span>
                            </div>
                          </div>

                          <!-- Message si organisation non disponible -->
                          <div *ngIf="!isOrganisationLoading(activite, 'intervention') && !getOrganisationForActivite(activite, 'intervention')" class="text-base">
                            <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic text-sm">
                              Organisation non disponible
                            </span>
                          </div>
                        </div>
                      </div>
                    </p-tabPanel>
                  </p-tabView>
                </div>

              </div>

              <!-- Agent Section - Compact -->
              <div class="mt-6 pt-4 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                  <i class="pi pi-user text-teal-600 mr-2"></i>
                  Praticien
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-id-card text-teal-600 mr-1"></i>
                      Nom complet
                    </label>
                    <div class="text-base">
                      <span class="px-3 py-1 bg-teal-100 text-teal-800 rounded-lg font-semibold">
                        {{ activite.agent.titre }} {{ activite.agent.prenom }} {{ activite.agent.nom }}
                      </span>
                    </div>
                  </div>

                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-hashtag text-gray-600 mr-1"></i>
                      Matricule
                    </label>
                    <div class="text-base">
                      <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-lg">{{ activite.agent.matricule || 'Non spécifié' }}</span>
                    </div>
                  </div>

                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-envelope text-blue-600 mr-1"></i>
                      Email
                    </label>
                    <div class="text-base">
                      <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-lg">{{ activite.agent.email || 'Non spécifié' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="text-center py-4">
            <div class="py-8">
              <div class="text-gray-400 text-6xl mb-4">
                <i class="pi pi-inbox"></i>
              </div>
              <p class="text-xl text-gray-600 font-light">Aucune activité trouvée</p>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </p-card>

  <!-- Chart Section -->
  <div class="mb-6">
    <p-card>
      <ng-template pTemplate="header">
        <div class="p-3 bg-gray-50 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-700">
              <i class="pi pi-chart-bar mr-2 text-cyan-700"></i>
              Analyse comparative des réalisations (par code d'acte)
            </h2>
            <div class="flex items-center space-x-2">
              <div *ngIf="loadingChartData" class="flex items-center">
                <i class="pi pi-spin pi-spinner text-cyan-700 mr-2"></i>
                <span class="text-sm text-gray-600">Chargement du graphique...</span>
              </div>
            </div>
          </div>
        </div>
      </ng-template>

      <!-- Chart content -->
      <div class="p-4">
        <div class="mb-4">
          <p class="text-gray-600 text-sm">
            <i class="pi pi-info-circle mr-1"></i>
            Comparaison du nombre total de réalisations <strong>par code d'acte</strong> entre les trois périodes sélectionnées.
            Chaque barre représente le cumul des réalisations pour toutes les activités ayant le même code d'acte.
            <span *ngIf="showActeSelector" class="block mt-1">
              <i class="pi pi-filter mr-1 text-indigo-600"></i>
              <strong>Plus de 5 codes d'actes détectés :</strong> Utilisez le panneau de sélection ci-dessous pour choisir les codes d'actes à afficher dans le graphique.
            </span>
          </p>
        </div>

        <!-- Acte Selector Panel (only show if more than 5 actes) -->
        <div *ngIf="showActeSelector && !loadingChartData" class="mb-4 p-4 bg-gray-50 rounded-lg border">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-md font-semibold text-gray-700 flex items-center">
              <i class="pi pi-filter mr-2 text-indigo-600"></i>
              Sélection des codes d'actes à afficher
              <span class="ml-2 px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs font-medium">
                {{ getSelectedActesCount() }}/{{ getTotalActesCount() }} sélectionnés
              </span>
            </h3>
            <div class="flex gap-2">
              <button
                pButton
                label="Tout sélectionner"
                icon="pi pi-check"
                class="p-button-sm p-button-outlined p-button-secondary"
                (click)="selectAllActes()"
                [disabled]="getSelectedActesCount() === getTotalActesCount()">
              </button>
              <button
                pButton
                label="Tout désélectionner"
                icon="pi pi-times"
                class="p-button-sm p-button-outlined p-button-secondary"
                (click)="deselectAllActes()"
                [disabled]="getSelectedActesCount() === 0">
              </button>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            <div *ngFor="let acte of chartData" class="flex items-center p-2 bg-white rounded border hover:bg-gray-50 transition-colors">
              <p-checkbox
                [binary]="true"
                [ngModel]="isActeSelected(acte.acteCode)"
                (ngModelChange)="toggleActeSelection(acte.acteCode)"
                [inputId]="'acte-' + acte.acteCode"
                styleClass="mr-3">
              </p-checkbox>
              <label [for]="'acte-' + acte.acteCode" class="cursor-pointer flex-1">
                <div class="font-medium text-gray-800">{{ acte.acteCode }}</div>
                <div class="text-xs text-gray-500 truncate">{{ acte.acteDescription }}</div>
                <div class="text-xs text-indigo-600 mt-1">
                  {{ acte.totalActivites }} activité(s) •
                  Total: {{ acte.p1Count + acte.p2Count + acte.p3Count }} réalisations
                </div>
              </label>
            </div>
          </div>

          <div *ngIf="getSelectedActesCount() === 0" class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <div class="flex items-center">
              <i class="pi pi-exclamation-triangle text-yellow-600 mr-2"></i>
              <span class="text-yellow-800 text-sm">Aucun code d'acte sélectionné. Le graphique sera vide.</span>
            </div>
          </div>
        </div>

        <!-- Loading state with skeleton -->
        <div *ngIf="loadingChartData" class="space-y-4">
          <div class="flex items-center justify-center h-8">
            <i class="pi pi-spin pi-spinner text-cyan-700 mr-2"></i>
            <span class="text-sm text-gray-600">Chargement des statistiques...</span>
          </div>
          <!-- Skeleton chart -->
          <div class="w-full h-96 bg-gray-100 rounded-lg animate-pulse flex items-center justify-center">
            <div class="text-center">
              <i class="pi pi-chart-bar text-gray-400 text-4xl mb-2"></i>
              <p class="text-gray-500 text-sm">Préparation du graphique</p>
            </div>
          </div>
        </div>

        <!-- Chart -->
        <div *ngIf="!loadingChartData && chartData.length > 0" class="w-full" style="height: 400px; min-height: 400px;">
          <p-chart
            type="bar"
            [data]="barChartData"
            [options]="chartOptions"
            width="100%"
            height="400px"
            styleClass="w-full h-full">
          </p-chart>
        </div>

        <!-- No data message -->
        <div *ngIf="!loadingChartData && chartData.length === 0"
             class="flex justify-center items-center h-64">
          <div class="text-center">
            <i class="pi pi-chart-bar text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-600 mb-2">Aucune donnée disponible</h3>
            <p class="text-gray-500 text-sm">
              Impossible de générer le graphique pour les codes d'actes des activités sélectionnées.
            </p>
          </div>
        </div>

        <!-- Period legend -->
        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4" *ngIf="!loadingChartData && chartData.length > 0">
          <div class="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
            <div>
              <span class="font-medium text-blue-800">P1 - {{ getPeriodLabel(periods.p1) }}</span>
              <p class="text-sm text-blue-600" *ngIf="periods.p1">
                {{ formatPeriodMonthYear(periods.p1) }}
              </p>
            </div>
          </div>
          <div class="flex items-center p-3 bg-teal-50 rounded-lg border border-teal-200">
            <div class="w-4 h-4 bg-teal-500 rounded mr-3"></div>
            <div>
              <span class="font-medium text-teal-800">P2 - {{ getPeriodLabel(periods.p2) }}</span>
              <p class="text-sm text-teal-600" *ngIf="periods.p2">
                {{ formatPeriodMonthYear(periods.p2) }}
              </p>
            </div>
          </div>
          <div class="flex items-center p-3 bg-purple-50 rounded-lg border border-purple-200">
            <div class="w-4 h-4 bg-purple-500 rounded mr-3"></div>
            <div>
              <span class="font-medium text-purple-800">P3 - {{ getPeriodLabel(periods.p3) }}</span>
              <p class="text-sm text-purple-600" *ngIf="periods.p3">
                {{ formatPeriodMonthYear(periods.p3) }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </p-card>
  </div>
</div>

<style>
  /* Custom styles for the multi-details view */
  ::ng-deep .p-datatable .p-datatable-tbody > tr.p-row-expanded {
    background-color: #f9fafb;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr.p-row-expanded + tr > td {
    padding: 0;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr.p-row-expanded + tr > td > div {
    margin: 0.5rem;
  }

  /* Custom TabView styles for compact display */
  ::ng-deep .custom-tabview .p-tabview-panels {
    padding: 1rem 0.5rem;
    background: #f8fafc;
    border-radius: 0 0 6px 6px;
  }

  ::ng-deep .custom-tabview .p-tabview-nav {
    background: #ffffff;
    border-radius: 6px 6px 0 0;
  }

  ::ng-deep .custom-tabview .p-tabview-nav li .p-tabview-nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
  }

  ::ng-deep .custom-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    background: #e0f2fe;
    color: #0369a1;
    border-color: #0369a1;
  }
</style>
