import {Component, OnInit} from '@angular/core';
import {ChartModule} from "primeng/chart";
import {StyleClassModule} from "primeng/styleclass";
import {PrimeTemplate} from "primeng/api";
import {TableModule} from "primeng/table";
import {ActeOverviewService} from "../../core/services/overview/acte-overview.service";
import {ActeSummary} from "../../core/models/overview/acte-overview.model";

@Component({
  selector: 'app-acte-details',
  standalone: true,
  imports: [
    ChartModule,
    StyleClassModule,
    PrimeTemplate,
    TableModule
  ],
  templateUrl: './acte-details.component.html',
  styleUrl: './acte-details.component.scss'
})
export class ActeDetailsComponent  implements OnInit{
  acteTitle: string = ''; // Titre dynamique
  groupedBarChartData: any;
  chartOptions: any;

  acteParUfSummary: any[] = []; // Données pour le tableau des UF
  acteParPraticienSummary: any[] = []; // Données pour le tableau des praticiens
  anneeNmoinsUn: string = ''; // Année N-1
  anneeN: string = ''; // Année N


  //----
  totalNbActeUfAnneeNmoinsUn: number = 0; // Total UF N-1
  totalNbActeUfAnneeN: number = 0; // Total UF N
  totalPartUfAnneeNmoinsUn: string = '100%';
  totalPartUfAnneeN: string = '100%';

  totalPartPraticienAnneeNmoinsUn: string = '100%';
  totalPartPraticienAnneeN: string = '100%';
  totalNbActePraticienAnneeNmoinsUn: number = 0; // Total praticien N-1
  totalNbActePraticienAnneeN: number = 0; // Total praticien N

  //--


  constructor(
    private acteOverviewService:ActeOverviewService
  ) {
  }

  ngOnInit(): void {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      aspectRatio: 0.9,
      plugins: {
        legend: {
          position: 'top',
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Unités Fonctionnelles (UF)',
          },
        },
        y: {
          title: {
            display: true,
            text: 'Nombre d\'actes',
          },
          beginAtZero: true,
        },
      },
    };

    this.loadActeData();
  }

  loadActeData(): void {
    this.acteOverviewService.acteSummary$.subscribe((data: ActeSummary | null) => {
      if (data) {
        // Titre et années
        this.acteTitle = `${data.actesDetails.code} - ${data.actesDetails.description}`;
        this.anneeNmoinsUn = data.anneeNmoinsUn;
        this.anneeN = data.anneeN;

        // Données pour le graphique
        this.initGroupedGraph(data.ufs);

        // Données pour les tableaux
        this.loadActeParUfSummary(data.ufs, data.actesDetails);
        this.loadActeParPraticienSummary(data.acteParPraticienSummary);
      }
    });
  }

  initGroupedGraph(ufs: any[]): void {
    this.groupedBarChartData = {
      labels: ufs.map((uf) => uf.nomUF),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ufs.map((uf) => uf.nbActesAnneeNmoinsUn),
          backgroundColor: '#42A5F5',
        },
        {
          label: this.anneeN,
          data: ufs.map((uf) => uf.nbActesAnneeN),
          backgroundColor: '#FFA726',
        },
      ],
    };
  }

  loadActeParUfSummary444(ufs: any[], actesDetails: any): void {
    // Calcul des totaux UF pour N-1 et N
    this.totalNbActeUfAnneeNmoinsUn = ufs.reduce((sum, uf) => sum + uf.nbActesAnneeNmoinsUn, 0);
    this.totalNbActeUfAnneeN = ufs.reduce((sum, uf) => sum + uf.nbActesAnneeN, 0);

    //

    this.acteParUfSummary = ufs.map((uf) => ({
      nom: uf.nomUF,
      actesNmoinsUn: uf.nbActesAnneeNmoinsUn,
      partUfNmoinsUn: ((uf.nbActesAnneeNmoinsUn / this.totalNbActeUfAnneeNmoinsUn ) * 100).toFixed(2) + '%',
      actesAnneeN: uf.nbActesAnneeN,
      partUfAnneeN: Math.min((uf.nbActesAnneeN / this.totalNbActeUfAnneeN) * 100).toFixed(2) + '%',
    }));

    // Calcul des totaux des parts
    this.totalPartUfAnneeNmoinsUn = Math.min(
      100,
      this.acteParUfSummary.reduce((sum, uf) => sum + parseFloat(uf.partUfNmoinsUn), 0)
    ).toFixed(2) + '%';

    this.totalPartUfAnneeN = Math.min(
      100,
      this.acteParUfSummary.reduce((sum, uf) => sum + parseFloat(uf.partUfAnneeN), 0)
    ).toFixed(2) + '%';

  }

  loadActeParUfSummary(ufs: any[], actesDetails: any): void {
    // Calcul des totaux UF pour N-1 et N
    this.totalNbActeUfAnneeNmoinsUn = ufs.reduce((sum, uf) => sum + uf.nbActesAnneeNmoinsUn, 0);
    this.totalNbActeUfAnneeN = ufs.reduce((sum, uf) => sum + uf.nbActesAnneeN, 0);

    // Préparer les données sans arrondi pour ajuster la dernière valeur
    let partsNmoinsUn: number[] = [];
    let partsAnneeN: number[] = [];
    let totalPartsNmoinsUn = 0;
    let totalPartsAnneeN = 0;

    // Calcul initial des parts
    this.acteParUfSummary = ufs.map((uf, index) => {
      const partNmoinsUn = (uf.nbActesAnneeNmoinsUn / this.totalNbActeUfAnneeNmoinsUn) * 100;
      const partAnneeN = (uf.nbActesAnneeN / this.totalNbActeUfAnneeN) * 100;

      partsNmoinsUn.push(partNmoinsUn);
      partsAnneeN.push(partAnneeN);

      totalPartsNmoinsUn += partNmoinsUn;
      totalPartsAnneeN += partAnneeN;

      return {
        nom: uf.nomUF,
        actesNmoinsUn: uf.nbActesAnneeNmoinsUn,
        actesAnneeN: uf.nbActesAnneeN,
        partUfNmoinsUn: 0, // Placeholder
        partUfAnneeN: 0, // Placeholder
      };
    });

    // Ajuster la dernière valeur pour que le total soit exactement 100 %
    const diffNmoinsUn = 100 - totalPartsNmoinsUn;
    const diffAnneeN = 100 - totalPartsAnneeN;

    if (this.acteParUfSummary.length > 0) {
      partsNmoinsUn[partsNmoinsUn.length - 1] += diffNmoinsUn;
      partsAnneeN[partsAnneeN.length - 1] += diffAnneeN;
    }

    // Appliquer les arrondis finaux
    this.acteParUfSummary = this.acteParUfSummary.map((item, index) => ({
      ...item,
      partUfNmoinsUn: partsNmoinsUn[index].toFixed(2) + '%',
      partUfAnneeN: partsAnneeN[index].toFixed(2) + '%',
    }));

    // Calcul des totaux des parts
    this.totalPartUfAnneeNmoinsUn = '100.00%';
    this.totalPartUfAnneeN = '100.00%';
  }




  loadActeParPraticienSummary(praticiens: any[]): void {
    // Calcul des totaux des actes pour N-1 et N
    this.totalNbActePraticienAnneeNmoinsUn = praticiens.reduce(
      (sum, praticien) => sum + praticien.nbActesAnneeNmoinsUn,
      0
    );
    this.totalNbActePraticienAnneeN = praticiens.reduce(
      (sum, praticien) => sum + praticien.nbActesAnneeN,
      0
    );

    // Calcul des parts pour chaque praticien
    this.acteParPraticienSummary = praticiens.map((praticien) => {
      const partAnneeNmoinsUn = this.totalNbActePraticienAnneeNmoinsUn
        ? ((praticien.nbActesAnneeNmoinsUn / this.totalNbActePraticienAnneeNmoinsUn) * 100).toFixed(2) + '%'
        : '0%';

      const partAnneeN = this.totalNbActePraticienAnneeN
        ? ((praticien.nbActesAnneeN / this.totalNbActePraticienAnneeN) * 100).toFixed(2) + '%'
        : '0%';

      return {
        ...praticien,
        partAnneeNmoinsUn,
        partAnneeN,
      };
    });

    // Calcul des totaux des parts (si nécessaire)
    this.totalPartPraticienAnneeNmoinsUn = Math.min(
      100,
      this.acteParPraticienSummary.reduce(
        (sum, praticien) => sum + parseFloat(praticien.partAnneeNmoinsUn.replace('%', '')),
        0
      )
    ).toFixed(2) + '%';

    this.totalPartPraticienAnneeN = Math.min(
      100,
      this.acteParPraticienSummary.reduce(
        (sum, praticien) => sum + parseFloat(praticien.partAnneeN.replace('%', '')),
        0
      )
    ).toFixed(2) + '%';
  }

  onInput(event: Event): string {
    console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }

  viewUfDetails(uf: any) {
    alert(`Voir les détails pour l'UF : ${uf.nom}`);
  }

  viewPraticienDetails(praticien: any): void {
    console.log('Voir les détails pour le praticien :', praticien.nom, praticien.prenom);
  }

  truncateActeDescription(acte: string): string {
    const words = acte.split(' ');
    if (words.length > 2) {
      return words.slice(0, 3).join(' ') + '...';
    }
    return acte;
  }
}
