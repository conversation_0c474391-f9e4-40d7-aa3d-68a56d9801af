import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { HeaderComponent } from './header.component';
import { CoreModule } from '../../core.module';

const meta: Meta<HeaderComponent> = {
  title: 'Layout/Header',
  component: HeaderComponent,
  decorators: [
    moduleMetadata({
      imports: [CoreModule],
      // On part du principe que HeaderComponent est exporté par CoreModule.
    }),
  ],
  tags: ['autodocs'], // Ajout du tag autodocs pour une doc automatique
  parameters: {
    docs: {
      description: {
        component: `
Le **HeaderComponent** est la barre supérieure de l'application, comprenant :
- Un bouton pour ouvrir/fermer la sidebar (côté gauche).
- Un champ de recherche pour filtrer ou trouver rapidement un praticien ou un service.
- Une icône de notification (non fonctionnelle dans cet exemple, mais indicative).
- Un menu utilisateur (avatar et nom du praticien) permettant d’accéder au profil, aux paramètres ou de se déconnecter.

C'est un composant clé pour la navigation et l'utilisation au quotidien de SUPRA V2.
        `,
      },
    },
    compodoc: {
      component: HeaderComponent,
    },
  },
  argTypes: {
    // Le composant n'a pas d'@Input, mais on peut les ajouter ici plus tard.
  },
};

export default meta;
type Story = StoryObj<HeaderComponent>;

export const Default: Story = {
  name: 'Par Défaut',
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'Cette variante montre le header avec une autre image de profil, sans modifier le template ou le code du composant.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    const img = canvasElement.querySelector('img.h-8.w-8.rounded-full') as HTMLImageElement | null;
    if (img) {
      // On change la source de l'image après le rendu
      img.src = 'https://img.icons8.com/?size=100&id=0lg0kb05hrOz&format=png&color=000000';
    }
  },
};

export const WithUserMenuOpen: Story = {
  name: 'Menu Utilisateur Ouvert',
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'Cette variante montre le header lorsque le menu utilisateur est ouvert, révélant les options de profil, paramètres, et déconnexion.',
      },
    },
  },
  play: async ({ canvasElement }) => {
    const img = canvasElement.querySelector('img.h-8.w-8.rounded-full') as HTMLImageElement | null;
    if (img) {
      img.src = 'https://img.icons8.com/?size=100&id=0lg0kb05hrOz&format=png&color=000000';
    }

    const userMenuButton = canvasElement.querySelector('button[aria-haspopup="true"]');
    if (userMenuButton) {
      userMenuButton.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    }
  },
};
