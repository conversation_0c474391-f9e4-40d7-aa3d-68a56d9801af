export interface RepartitionActesParType {
  type: string;
  totalP1: number;
  totalP2: number;
  totalP3: number;
  total: number;
}

export interface EvolutionMensuelle {
  mois: string;
  anneeNmoinsUn: number;
  anneeN: number;
  anneeNmoins2?: number; // Added for p3 data
}

export interface EvolutionMensuelleParType {
  mois: string;
  ccamP1: number;
  ccamP2: number;
  ccamP3: number;
  ngapP1: number;
  ngapP2: number;
  ngapP3: number;
  laboP1: number;
  laboP2: number;
  laboP3: number;
}

export interface RepartitionActesParServices {
  service: string;
  totalP1: number;
  totalP2: number;
  totalP3: number;
  total: number;
}

export interface PerformancePraticien {
  nom: string;
  prenom: string;
  total: number;
}

export interface ActesInterneExterne {
  type: string;
  totalP1: number;
  totalP2: number;
  totalP3: number;
  total: number;
}

// New interfaces for additional charts

export interface RepartitionParCategoriePrincipale {
  categorie: string;
  totalP1: number;
  totalP2: number;
  totalP3: number;
  total: number;
}

export interface RepartitionParUF {
  uf: string;
  totalP1: number;
  totalP2: number;
  totalP3: number;
  total: number;
}

export interface TauxVariation {
  label: string;
  p1Total: number;
  p2Total: number;
  p3Total?: number;
  variationP1P2: number; // Percentage
  variationP2P3?: number; // Percentage
}

export interface HeatmapData {
  semaine: number;
  jour: number; // 0-6 for Sunday-Saturday
  totalP1: number;
  totalP2: number;
  totalP3: number;
  total: number;
  actType?: string; // CCAM, NGAP, LABO
}

export interface RepartitionParProvenance {
  provenance: string;
  totalP1: number;
  totalP2: number;
  totalP3: number;
  total: number;
}

export interface DashboardData {
  // Original charts
  repartitionActesParType: RepartitionActesParType[];
  evolutionMensuelle: EvolutionMensuelle[];
  evolutionMensuelleParType?: EvolutionMensuelleParType[]; // Added for act type differentiation
  repartitionActesParServices: RepartitionActesParServices[];
  performancePraticiens: PerformancePraticien[];
  actesInterneExterne: ActesInterneExterne[];

  // New charts
  repartitionParCategoriePrincipale?: RepartitionParCategoriePrincipale[];
  repartitionParUF?: RepartitionParUF[];
  tauxVariation?: TauxVariation[];
  heatmapData?: HeatmapData[];
  repartitionParProvenance?: RepartitionParProvenance[];
}
