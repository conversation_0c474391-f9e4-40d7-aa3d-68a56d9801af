import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ChangeDetectorRef } from '@angular/core';
import { <PERSON>ync<PERSON>ip<PERSON>, DecimalPipe, NgClass, <PERSON>For<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgOptimizedImage } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { Table, TableModule } from "primeng/table";
import { Router } from "@angular/router";
import { HttpClient } from '@angular/common/http';
import { BreadcrumbItem } from "../../../core/models/breadcrumbItem";
import { BreadcrumbComponent } from "../../../pages/breadcrumb/breadcrumb.component";
import { Button, ButtonDirective } from "primeng/button";
import { ActiviteModel } from "../../../core/models/activite";
import { ActiviteService } from "../../../core/services/activite";
import { LazyLoadEvent } from 'primeng/api';
import { SkeletonModule } from 'primeng/skeleton';
import { TooltipModule } from 'primeng/tooltip';
import { CheckboxModule } from 'primeng/checkbox';
import { environment } from '../../../../environments/environment';
import { GlobalFilterService, GlobalFilterState, VenueType, DatePeriod } from "../../../core/services/global-filter";
import { AuthService } from "../../../core/services/auth/auth.service";
import { FeatureFlagService } from "../../../core/services/feature-flag.service";
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { SubheaderComponent } from './subheader/subheader.component';


@Component({
  selector: 'app-activites-list',
  templateUrl: './activites-list.component.html',
  styleUrls: ['./activites-list.component.scss'],
  standalone: true,
  imports: [
    NgIf,
    NgForOf,
    NgClass,
    AsyncPipe,
    DecimalPipe,
    NgOptimizedImage,
    FormsModule,
    TableModule,
    BreadcrumbComponent,
    ButtonDirective,
    Button,
    SkeletonModule,
    TooltipModule,
    CheckboxModule,
    SubheaderComponent
  ]
})
export class ActivitesListComponent implements OnInit, OnDestroy {
  @ViewChild('activitesTable') activitesTable!: Table;

  // Subscription management
  private subscriptions: Subscription[] = [];

  // Breadcrumb configuration
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Accueil', url: '/' },
    { label: 'Activités', url: '/activites/activites-list' }
  ];

  // Data properties for activities
  activites: ActiviteModel[] = [];
  allActivites: ActiviteModel[] = [];
  selectedActivites: ActiviteModel[] = [];
  filteredActivites: ActiviteModel[] = [];

  // Activity type filter
  selectedActivityType: string = 'ALL'; // ALL, CCAM, NGAP, LABO
  activityTypes = [
    { label: 'Tous les types', value: 'ALL' },
    { label: 'CCAM', value: 'CCAM' },
    { label: 'NGAP', value: 'NGAP' },
    { label: 'LABO/NABM', value: 'LABO' }
  ];

  // Counts by type
  totalCcamActivites = 0;
  totalNgapActivites = 0;
  totalLaboActivites = 0;

  // Original totals (before any filtering)
  private originalTotalAllActivites = 0;
  private originalTotalCcamActivites = 0;
  private originalTotalNgapActivites = 0;
  private originalTotalLaboActivites = 0;

  // Pagination properties
  first = 0;
  pageSize = 10;
  totalActivites = 0;
  totalAllActivites = 0; // Total count of all activities, regardless of type

  // Server-side pagination properties
  currentPage = 1;
  totalRecords = 0;
  serverSidePagination = true; // Flag to enable server-side pagination
  totalActiveActivites = 0;
  rowsPerPageOptions = [5, 10, 20, 50];

  // UI state
  loading = false;

  // System health indicator
  systemHealthStatus: 'excellent' | 'good' | 'slow' | 'offline' = 'offline';
  systemHealthMessage = 'Vitesse de chargement - Hors ligne';
  generationTimeMs = 0;

  // Sorting state
  sortField: string = '';
  sortOrder: number = 1;

  // Search/filter state
  searchTerm: string = '';
  private searchTimeout: any;

  // Global filter state
  public globalFilters: GlobalFilterState | null = null;

  constructor(
    private activiteService: ActiviteService,
    private router: Router,
    private http: HttpClient,
    private globalFilterService: GlobalFilterService,
    private authService: AuthService,
    private cdr: ChangeDetectorRef,
    private featureFlagService: FeatureFlagService
  ) {}

  ngOnInit(): void {
    console.log('🚀 ngOnInit: Starting component initialization');

    // Charger les métadonnées UNE SEULE FOIS au démarrage
    console.log('📊 ngOnInit: Loading base metadata...');
    this.loadBaseMetadata();

    // Use server-side pagination instead of loading all data
    console.log('📊 ngOnInit: Loading activities with pagination...');
    this.loadActivitiesWithServerPagination();

    // Subscribe to global filter changes
    const filterSubscription = this.globalFilterService.getFilterState().subscribe(filters => {
      this.globalFilters = filters;
      // Reload data with new filters (SANS recharger les métadonnées)
      this.resetPaginationAndReload();
    });

    // Store the subscription for cleanup
    this.subscriptions.push(filterSubscription);
  }

  /**
   * Apply global filters to the loaded activities
   * This filters the data client-side without making a new API request
   *
   * This is an example of how to use the GlobalFilterService to filter data locally:
   * 1. Subscribe to the GlobalFilterService.getFilterState() observable in ngOnInit
   * 2. Store the filter state in a component property
   * 3. Apply the filters to the local data when they change
   * 4. Use the filtered data in the template
   *
   * This approach allows for instant filtering without making additional API requests,
   * providing a more responsive user experience.
   */
  private applyGlobalFilters(): void {
    console.log('🔍 Applying global filters to', this.activites.length, 'activities');
    console.log('🔍 Current filters:', {
      practitioner: this.globalFilters?.practitioner?.nom + ' ' + this.globalFilters?.practitioner?.prenom,
      pole: this.globalFilters?.pole?.libelle,
      cr: this.globalFilters?.service?.libelle, // CR utilise le champ service du filtre global
      venueType: this.globalFilters?.venueType
    });

    // If no filters are set, use all activities
    if (!this.globalFilters ||
        (!this.globalFilters.practitioner &&
         !this.globalFilters.pole &&
         !this.globalFilters.service &&
         !this.globalFilters.venueType)) {
      this.filteredActivites = [...this.activites];
      this.restoreOriginalTotals();
      console.log('🔍 No filters applied, showing all', this.filteredActivites.length, 'activities');
      return;
    }

    // Apply filters with OR logic for praticien/pole/service and AND for venue type
    this.filteredActivites = this.activites.filter(activite => {

      // 1. First apply venue type filter (ET obligatoire)
      if (this.globalFilters?.venueType &&
          activite.typeVenue !== undefined) {

        // Si "Tous les types" est sélectionné, afficher tout (pas de filtrage)
        if (this.globalFilters.venueType === VenueType.BOTH) {
          // Ne pas filtrer, afficher toutes les activités
        } else {
          // Filtrer selon le type spécifique en utilisant les valeurs correctes
          if (this.globalFilters.venueType === VenueType.CONSULTATION && activite.typeVenue !== 1) {
            return false;
          }
          if (this.globalFilters.venueType === VenueType.HOSPITALIZATION && activite.typeVenue !== 2) {
            return false;
          }
          if (this.globalFilters.venueType === VenueType.URGENCE && activite.typeVenue !== 3) {
            return false;
          }
        }
      }

      // 2. Apply filters: Praticien ET (Pôle OU Service) dans ufIntervention

      // Check practitioner filter (obligatoire si défini)
      if (this.globalFilters?.practitioner) {
        if (!activite.agent || activite.agent['@id'] !== this.globalFilters.practitioner['@id']) {
          return false;
        }
      }

      // Check pole/CR filters dans ufIntervention (au moins un doit matcher si défini)
      const hasPole = this.globalFilters?.pole;
      const hasCr = this.globalFilters?.service; // CR utilise le champ service du filtre global

      if (hasPole || hasCr) {
        if (!activite.ufIntervention) {
          console.log('🔍 Activity rejected: no ufIntervention', activite.code);
          return false; // Pas d'ufIntervention, ne peut pas matcher
        }

        let matchesPoleOrCr = false;

        // Check pole match dans ufIntervention
        if (hasPole && this.globalFilters?.pole) {
          const ufPole = (activite.ufIntervention as any).pole;
          console.log('🔍 Checking pole match:', {
            activityCode: activite.code,
            ufPole: ufPole,
            ufPoleType: typeof ufPole,
            filterPoleId: this.globalFilters.pole['@id']
          });

          // Handle both string URI and object cases
          if (ufPole) {
            const ufPoleId = typeof ufPole === 'string' ? ufPole : ufPole['@id'];
            if (ufPoleId === this.globalFilters.pole['@id']) {
              matchesPoleOrCr = true;
              console.log('✅ Pole match found for activity:', activite.code);
            }
          }
        }

        // Check CR match dans ufIntervention
        if (hasCr && this.globalFilters?.service) {
          const ufCr = activite.ufIntervention.cr;
          console.log('🔍 Checking CR match:', {
            activityCode: activite.code,
            ufCr: ufCr,
            ufCrType: typeof ufCr,
            filterCrId: this.globalFilters.service['@id']
          });

          // Handle both string URI and object cases
          if (ufCr) {
            const ufCrId = typeof ufCr === 'string' ? ufCr : ufCr['@id'];
            if (ufCrId === this.globalFilters.service['@id']) {
              matchesPoleOrCr = true;
              console.log('✅ CR match found for activity:', activite.code);
            }
          }
        }

        // Si un filtre pôle/CR est défini mais aucun ne match
        if (!matchesPoleOrCr) {
          console.log('❌ Activity rejected: no pole/CR match', activite.code);
          return false;
        }
      }

      // Si tous les filtres passent, inclure cette activité
      return true;
    });

    // Recalculate totals based on filtered activities
    this.updateFilteredTotals();

    console.log(`Applied global filters. ${this.filteredActivites.length} activities match the criteria.`);
    console.log('Filter criteria (Praticien ET (Pôle OU CR) dans ufIntervention ET TypeVenue):', {
      practitioner: this.globalFilters?.practitioner?.['@id'],
      poleInUfIntervention: this.globalFilters?.pole?.['@id'],
      crInUfIntervention: this.globalFilters?.service?.['@id'], // CR utilise le champ service du filtre global
      venueType: this.globalFilters?.venueType
    });
  }

  /**
   * Load all activites from the API in one call (no pagination)
   * This is used for client-side operations like filtering and pagination
   */
  loadAllActivites(): void {
    this.loading = true;
    console.log('📊 Loading ALL activities from API...');

    // Load all activities in one call (no pagination) avec filtres
    const apiUrl = this.buildApiUrlWithFilters(`${environment.api_url}/api/actes`);
    const subscription = this.http.get<any>(apiUrl).subscribe({
      next: (response) => {
        console.log('📊 All activities response:', {
          totalItems: response.totalItems,
          memberLength: response.member?.length,
          hydraMemberLength: response['hydra:member']?.length
        });

        const allActivities = response.member || response['hydra:member'] || [];

        // Store all activities
        this.allActivites = allActivities;
        this.activites = allActivities; // Initially show all

        // Calculate totals from loaded data
        this.totalActivites = allActivities.length;
        // NOTE: Ne PAS écraser totalAllActivites et les totaux par type ici
        // Ces valeurs viennent des métadonnées et doivent rester constantes
        // this.totalAllActivites = allActivities.length;
        // this.originalTotalAllActivites = allActivities.length;

        // NOTE: Ne PAS appeler calculateCountsByType() ici car cela écraserait
        // les valeurs des métadonnées qui doivent rester constantes
        // this.calculateCountsByType();

        // Apply current type filter
        this.applyActivityTypeFilter();

        // Initialize filtered activities
        this.filteredActivites = [...this.activites];

        // Apply global filters if any
        this.applyGlobalFilters();

        this.loading = false;
        console.log(`✅ Loaded ${allActivities.length} activities successfully`);
      },
      error: (error) => {
        console.error('❌ Error loading all activities:', error);
        this.loading = false;
      }
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Load activities with server-side pagination
   * Uses API Platform's native pagination with totalItems and view metadata
   * Includes periods P1, P2, P3 from GlobalFilterService for ActesPeriodesExtension
   */
  loadActivitiesWithServerPagination(): void {
    this.loading = true;

    // Get current global filters (including periods P1, P2, P3)
    // Use this.globalFilters if available (updated from subheader), otherwise get from service
    const globalFilters = this.globalFilters || this.globalFilterService.getCurrentFilterState();

    console.log('📊 Loading activities with global filters:', {
      hasP1: !!globalFilters.p1,
      hasP2: !!globalFilters.p2,
      hasP3: !!globalFilters.p3,
      source: this.globalFilters ? 'component' : 'service'
    });

    // Get ejcode from user token
    const ejcode = this.getEjCodeFromToken();
    if (!ejcode) {
      console.error('❌ No ejcode found in user token');
      this.loading = false;
      return;
    }

    // Build URL with pagination parameters and ejcode as query parameter
    let url = `${environment.api_url}/api/actes?page=${this.currentPage}&itemsPerPage=${this.pageSize}&ejcode=${ejcode}`;

    // Add periods P1, P2, P3 for ActesPeriodesExtension
    if (globalFilters.p1?.dateDebut && globalFilters.p1?.dateFin) {
      const p1Start = this.formatDateForAPI(globalFilters.p1.dateDebut);
      const p1End = this.formatDateForAPI(globalFilters.p1.dateFin);
      url += `&p1Start=${p1Start}&p1End=${p1End}`;
    }

    if (globalFilters.p2?.dateDebut && globalFilters.p2?.dateFin) {
      const p2Start = this.formatDateForAPI(globalFilters.p2.dateDebut);
      const p2End = this.formatDateForAPI(globalFilters.p2.dateFin);
      url += `&p2Start=${p2Start}&p2End=${p2End}`;
    }

    if (globalFilters.p3?.dateDebut && globalFilters.p3?.dateFin) {
      const p3Start = this.formatDateForAPI(globalFilters.p3.dateDebut);
      const p3End = this.formatDateForAPI(globalFilters.p3.dateFin);
      url += `&p3Start=${p3Start}&p3End=${p3End}`;
    }

    // Add type filter if selected
    if (this.selectedActivityType && this.selectedActivityType !== 'ALL') {
      if (this.selectedActivityType === 'LABO') {
        // For LABO, we need to handle LABO and NABM separately or use a different approach
        url += `&typeActe[]=${this.selectedActivityType}&typeActe[]=NABM`;
      } else {
        url += `&typeActe=${this.selectedActivityType}`;
      }
    }

    // Add search filter if present
    if (this.searchTerm && this.searchTerm.trim()) {
      url += `&search=${encodeURIComponent(this.searchTerm.trim())}`;
    }

    // Add sorting if present
    if (this.sortField) {
      const sortDirection = this.sortOrder === 1 ? 'asc' : 'desc';
      url += `&order[${this.sortField}]=${sortDirection}`;
    }

    console.log('🔄 Loading activities with server pagination:', {
      page: this.currentPage,
      pageSize: this.pageSize,
      type: this.selectedActivityType,
      search: this.searchTerm,
      sort: this.sortField ? `${this.sortField}:${this.sortOrder === 1 ? 'asc' : 'desc'}` : 'none',
      periods: {
        p1: globalFilters.p1 ? `${this.formatDateForAPI(globalFilters.p1.dateDebut)} → ${this.formatDateForAPI(globalFilters.p1.dateFin)}` : null,
        p2: globalFilters.p2 ? `${this.formatDateForAPI(globalFilters.p2.dateDebut)} → ${this.formatDateForAPI(globalFilters.p2.dateFin)}` : null,
        p3: globalFilters.p3 ? `${this.formatDateForAPI(globalFilters.p3.dateDebut)} → ${this.formatDateForAPI(globalFilters.p3.dateFin)}` : null
      },
      url
    });

    // Ajouter les filtres en cascade à l'URL de pagination
    const urlWithFilters = this.buildApiUrlWithFilters(url);
    this.http.get<any>(urlWithFilters).subscribe({
      next: (response) => {
        console.log('✅ Server pagination response:', response);

        // Extract data from API Platform response
        this.activites = response.member || [];
        this.totalRecords = response.totalItems || 0;
        // Note: totalAllActivites et les totaux par type sont maintenant gérés par loadBaseMetadata()
        // Ne pas écraser les valeurs des métadonnées ici

        // Store pagination metadata for future use
        this.storePaginationMetadata(response.view);

        this.loading = false;

        console.log('📊 Activities loaded:', {
          count: this.activites.length,
          totalRecords: this.totalRecords,
          currentPage: this.currentPage,
          pageSize: this.pageSize,
          totalPages: Math.ceil(this.totalRecords / this.pageSize),
          hasNext: this.currentPage < Math.ceil(this.totalRecords / this.pageSize),
          hasPrevious: this.currentPage > 1
        });
      },
      error: (error) => {
        console.error('❌ Error loading activities with server pagination:', error);
        this.loading = false;

        // Fallback to empty state
        this.activites = [];
        this.totalRecords = 0;
        this.totalAllActivites = 0;
      }
    });
  }

  /**
   * Store pagination metadata from API Platform response
   */
  private storePaginationMetadata(view: any): void {
    if (view) {
      console.log('📄 Pagination metadata:', {
        current: view['@id'],
        first: view.first,
        last: view.last,
        next: view.next,
        previous: view.previous
      });
    }
  }

  /**
   * Reset pagination and reload data
   */
  resetPaginationAndReload(): void {
    this.currentPage = 1;
    this.first = 0;
    this.loadActivitiesWithServerPagination();
  }

  /**
   * Calculate counts by type from loaded data
   */
  private calculateCountsByType(): void {
    this.totalCcamActivites = this.allActivites.filter(a => a.typeActe === 'CCAM').length;
    this.totalNgapActivites = this.allActivites.filter(a => a.typeActe === 'NGAP').length;
    this.totalLaboActivites = this.allActivites.filter(a => a.typeActe === 'LABO' || a.typeActe === 'NABM').length;

    // Save originals
    this.originalTotalCcamActivites = this.totalCcamActivites;
    this.originalTotalNgapActivites = this.totalNgapActivites;
    this.originalTotalLaboActivites = this.totalLaboActivites;

    console.log('📊 Activity counts by type:', {
      CCAM: this.totalCcamActivites,
      NGAP: this.totalNgapActivites,
      LABO: this.totalLaboActivites,
      Total: this.totalAllActivites
    });
  }

  /**
   * Apply activity type filter to loaded data
   */
  private applyActivityTypeFilter(): void {
    console.log(`📊 Applying activity type filter: ${this.selectedActivityType}`);

    switch (this.selectedActivityType) {
      case 'CCAM':
        this.activites = this.allActivites.filter(a => a.typeActe === 'CCAM');
        break;
      case 'NGAP':
        this.activites = this.allActivites.filter(a => a.typeActe === 'NGAP');
        break;
      case 'LABO':
        this.activites = this.allActivites.filter(a => a.typeActe === 'LABO' || a.typeActe === 'NABM');
        break;
      default: // 'ALL'
        this.activites = [...this.allActivites];
        break;
    }

    this.totalActivites = this.activites.length;

    // Update filteredActivites and apply global filters
    this.filteredActivites = [...this.activites];
    this.applyGlobalFilters(); // Apply global filters after type filtering

    console.log(`📊 Filtered to ${this.activites.length} activities of type ${this.selectedActivityType}`);
  }

  /**
   * Get count of CCAM activities (deprecated - now calculated from loaded data)
   */
  getCcamCount(): void {
    this.http.get<any>(`${environment.api_url}/api/actes?typeActe=CCAM&itemsPerPage=1`).subscribe({
      next: (response) => {
        this.totalCcamActivites = response.totalItems || 0;
        this.originalTotalCcamActivites = response.totalItems || 0; // Save original
      },
      error: (error) => {
        console.error('Error getting CCAM count:', error);
        this.totalCcamActivites = 0;
        this.originalTotalCcamActivites = 0;
      }
    });
  }

  /**
   * Get count of NGAP activities
   */
  getNgapCount(): void {
    this.http.get<any>(`${environment.api_url}/api/actes?typeActe=NGAP&itemsPerPage=1`).subscribe({
      next: (response) => {
        this.totalNgapActivites = response.totalItems || 0;
        this.originalTotalNgapActivites = response.totalItems || 0; // Save original
      },
      error: (error) => {
        console.error('Error getting NGAP count:', error);
        this.totalNgapActivites = 0;
        this.originalTotalNgapActivites = 0;
      }
    });
  }

  /**
   * Get count of LABO/NABM activities
   */
  getLaboCount(): void {
    // Use a combined query to get both LABO and NABM activities in one request
    this.http.get<any>(`${environment.api_url}/api/actes?typeActe[]=LABO&typeActe[]=NABM&itemsPerPage=1`).subscribe({
      next: (response) => {
        // Use the totalItems from the combined query
        this.totalLaboActivites = response.totalItems || 0;
        this.originalTotalLaboActivites = response.totalItems || 0; // Save original
      },
      error: (error) => {
        console.error('Error getting combined LABO/NABM count:', error);

        // Fallback to separate queries if combined query fails
        this.getLaboCountFallback();
      }
    });
  }

  /**
   * Fallback method to get LABO/NABM count with separate queries
   * This is used if the combined query fails
   */
  getLaboCountFallback(): void {
    // First get LABO count
    this.http.get<any>(`${environment.api_url}/api/actes?typeActe=LABO&itemsPerPage=1`).subscribe({
      next: (response) => {
        const laboCount = response.totalItems || 0;

        // Then get NABM count
        this.http.get<any>(`${environment.api_url}/api/actes?typeActe=NABM&itemsPerPage=1`).subscribe({
          next: (nabmResponse) => {
            const nabmCount = nabmResponse.totalItems || 0;
            this.totalLaboActivites = laboCount + nabmCount;
          },
          error: (error) => {
            console.error('Error getting NABM count:', error);
            this.totalLaboActivites = laboCount; // Use only LABO count if NABM fails
          }
        });
      },
      error: (error) => {
        console.error('Error getting LABO count:', error);

        // Try to get NABM count if LABO fails
        this.http.get<any>(`${environment.api_url}/api/actes?typeActe=NABM&itemsPerPage=1`).subscribe({
          next: (nabmResponse) => {
            this.totalLaboActivites = nabmResponse.totalItems || 0;
          },
          error: (nabmError) => {
            console.error('Error getting NABM count:', nabmError);
            this.totalLaboActivites = 0;
          }
        });
      }
    });
  }

  /**
   * Load activities based on the selected type filter (now uses loaded data)
   */
  loadActivitiesBySelectedType(): void {
    console.log(`📊 Filtering activities by type: ${this.selectedActivityType}`);

    // 1. Apply type filter to loaded data
    this.applyActivityTypeFilter();

    // 2. Apply search filter if provided (this will also apply global filters)
    this.applySearchFilter();

    console.log(`📊 Final filtered activities: ${this.filteredActivites.length} displayed of ${this.activites.length} type-filtered of ${this.allActivites.length} total`);
  }

  /**
   * Apply search filter to filtered activities (after global filters)
   */
  private applySearchFilter(): void {
    if (!this.searchTerm || this.searchTerm.length < 2) {
      // If no search term, just apply global filters
      this.applyGlobalFilters();
      return;
    }

    const searchTerm = this.searchTerm.toLowerCase();
    console.log(`🔍 Applying search filter: "${searchTerm}" to ${this.filteredActivites.length} activities`);

    // Apply search to already filtered activities (by global filters)
    this.filteredActivites = this.filteredActivites.filter(activite => {
      // Search in various fields
      const codeMatch = activite.code && activite.code.toLowerCase().includes(searchTerm);
      const descriptionMatch = activite.description && activite.description.toLowerCase().includes(searchTerm);

      // Search in agent fields if available
      let agentMatch = false;
      if (activite.agent) {
        const agentNom = activite.agent.nom ? activite.agent.nom.toLowerCase().includes(searchTerm) : false;
        const agentPrenom = activite.agent.prenom ? activite.agent.prenom.toLowerCase().includes(searchTerm) : false;
        const agentEmail = activite.agent.email ? activite.agent.email.toLowerCase().includes(searchTerm) : false;
        agentMatch = agentNom || agentPrenom || agentEmail;
      }

      return codeMatch || descriptionMatch || agentMatch;
    });

    console.log(`🔍 Search filtered to ${this.filteredActivites.length} activities`);
  }




  /**
   * Fallback method to load LABO/NABM activities with two separate calls
   * This is used if the combined approach fails
   */
  loadLaboActivitiesFallback(): void {
    // Calculate the current page (1-based)
    const page = Math.floor(this.first / this.pageSize) + 1;
    this.loading = true;

    // Create URLs for LABO and NABM
    // Convert numeric sort order to 'asc' or 'desc' as expected by the API
    const direction = this.sortOrder === 1 ? 'asc' : 'desc';

    // Convert camelCase to snake_case for specific fields
    let sortField = this.sortField;
    if (sortField === 'dateRealisation') {
      sortField = 'date_realisation';
    } else if (sortField === 'nombreDeRealisation') {
      sortField = 'nombre_de_realisation';
    }

    const sortParam = this.sortField ? `&order[${sortField}]=${direction}` : '';
    const laboUrl = `${environment.api_url}/api/actes?typeActe=LABO&page=${page}&itemsPerPage=${this.pageSize}` + sortParam;
    const nabmUrl = `${environment.api_url}/api/actes?typeActe=NABM&page=${page}&itemsPerPage=${this.pageSize}` + sortParam;

    // First try to get LABO activities
    this.http.get<any>(laboUrl).subscribe({
      next: (response) => {
        // Store the LABO activities
        this.activites = response.member || [];
        let laboTotal = response.totalItems || 0;

        // Now get NABM activities
        this.http.get<any>(nabmUrl).subscribe({
          next: (nabmResponse) => {
            // Add NABM activities to the list
            if (nabmResponse.member && nabmResponse.member.length > 0) {
              this.activites = [...this.activites, ...nabmResponse.member];
            }

            let nabmTotal = nabmResponse.totalItems || 0;

            // Use the sum of both totals for the correct count
            this.totalActivites = laboTotal + nabmTotal;

            // Apply global filters to the loaded data
            this.applyGlobalFilters();

            this.updateRowsPerPageOptions();
            this.loading = false;
          },
          error: (error) => {
            console.error('Error loading NABM activites:', error);
            // Continue with just LABO activities
            this.totalActivites = laboTotal;

            // Apply global filters to the loaded data
            this.applyGlobalFilters();

            this.updateRowsPerPageOptions();
            this.loading = false;
          }
        });
      },
      error: (error) => {
        console.error('Error loading LABO activites:', error);

        // Try to get just NABM activities
        this.http.get<any>(nabmUrl).subscribe({
          next: (nabmResponse) => {
            this.activites = nabmResponse.member || [];
            this.totalActivites = nabmResponse.totalItems || 0;

            // Apply global filters to the loaded data
            this.applyGlobalFilters();

            this.updateRowsPerPageOptions();
            this.loading = false;
          },
          error: (nabmError) => {
            console.error('Error loading NABM activites:', nabmError);
            // Both failed, show empty list
            this.activites = [];
            this.totalActivites = 0;

            // Apply global filters to the empty list
            this.filteredActivites = [];

            this.updateRowsPerPageOptions();
            this.loading = false;
          }
        });
      }
    });
  }

  /**
   * Handle change in activity type filter
   */
  onActivityTypeChange(): void {
    console.log(`📊 Activity type changed to: ${this.selectedActivityType}`);

    // Reset pagination and reload with new filter
    this.resetPaginationAndReload();
  }

  /**
   * Load activites with pagination
   * @param page Page number (1-based)
   * @param pageSize Items per page
   * @param sortField Field to sort by
   * @param sortOrder Sort order (1 for ascending, -1 for descending)
   */
  loadActivites(page: number, pageSize: number, sortField?: string, sortOrder?: number): void {
    // Update sorting state
    if (sortField) {
      this.sortField = sortField;
    }
    if (sortOrder) {
      this.sortOrder = sortOrder;
    }

    // Load activities with the selected type filter
    this.loadActivitiesBySelectedType();
  }

  /**
   * Handle input event for filtering - Server-side search
   * @param event Input event
   */
  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value.trim();

    // Clear previous timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Update search term
    this.searchTerm = value;

    // Debounce the search to avoid too many API calls
    this.searchTimeout = setTimeout(() => {
      // Reset pagination and reload with search term
      this.resetPaginationAndReload();
    }, 500); // Wait 500ms after user stops typing
  }

  /**
   * Navigate to activite details page
   * @param activiteId Activite ID
   */
  viewActiviteDetails(activiteId: string): void {
    // Vérifier si la feature est activée
    if (!this.featureFlagService.isFeatureEnabled('activites-single')) {
      console.warn('🚧 Feature "activites-single" désactivée pour ce sprint');
      // Optionnel: Afficher un message à l'utilisateur
      alert('Cette fonctionnalité sera disponible dans le prochain sprint.');
      return;
    }

    // Extract the ID from the IRI
    const id = activiteId.split('/').pop();
    this.router.navigate(['/activites/activite', id]);
  }

  /**
   * Navigate to the multi-details page for selected activites
   */
  viewSelectedActivitesDetails(): void {
    // Vérifier si la feature est activée
    if (!this.featureFlagService.isFeatureEnabled('activites-multi-details')) {
      console.warn('🚧 Feature "activites-multi-details" désactivée pour ce sprint');
      // Optionnel: Afficher un message à l'utilisateur
      alert('Cette fonctionnalité sera disponible dans le prochain sprint.');
      return;
    }

    if (this.selectedActivites.length === 0) return;

    // Extract IDs from selected activites
    const ids = this.selectedActivites.map(activite => {
      // Extract the ID from the IRI
      return activite['@id'].split('/').pop();
    }).join(',');

    // Navigate to the multi-details page with IDs as query parameters
    this.router.navigate(['/activites/activites-multi-details'], {
      queryParams: { ids }
    });
  }

  /**
   * Vérifie si une feature est activée (pour l'affichage conditionnel)
   */
  isFeatureEnabled(featureName: string): boolean {
    return this.featureFlagService.isFeatureEnabled(featureName);
  }

  /**
   * Obtient le message d'indisponibilité d'une feature
   */
  getFeatureMessage(featureName: string): string {
    return this.featureFlagService.getMaintenanceMessage(featureName);
  }

  /**
   * Format date for display
   * @param dateString Date string
   * @returns Formatted date string
   */
  formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  }

  /**
   * Handle page change event for the table (now handled by PrimeNG client-side)
   * @param event Table event
   */
  onPageChange(event: any): void {
    console.log('📄 Page change event (server-side pagination):', event);

    this.first = event.first || 0;
    this.pageSize = event.rows || this.pageSize;

    // Calculate the current page (1-based for API)
    this.currentPage = Math.floor(this.first / this.pageSize) + 1;

    // Extract sort field and order if present
    if (event.sortField) {
      this.sortField = event.sortField;
      this.sortOrder = event.sortOrder === 1 ? 1 : -1;
    }

    // Reload data from server with new pagination parameters
    this.loadActivitiesWithServerPagination();

    console.log(`📄 Server pagination: page=${this.currentPage}, first=${this.first}, pageSize=${this.pageSize}`);
  }

  /**
   * Handle lazy loading events from PrimeNG table
   * This is the main entry point for server-side pagination
   */
  onLazyLoad(event: any): void {
    console.log('🔄 Lazy load event:', event);

    // Update pagination parameters (handle null/undefined values)
    this.first = event.first ?? 0;
    this.pageSize = event.rows ?? this.pageSize;
    this.currentPage = Math.floor(this.first / this.pageSize) + 1;

    // Update sorting parameters
    if (event.sortField) {
      this.sortField = event.sortField;
      this.sortOrder = event.sortOrder ?? 1;
    }

    // Load data with current parameters
    this.loadActivitiesWithServerPagination();
  }

  /**
   * Handle page change request from SubHeader (no longer needed - search works on all data)
   */
  onRequestPageChange(pageNumber: number): void {
    console.log(`📄 SubHeader requested page change to page ${pageNumber} (no longer needed)`);

    // Calculate the 'first' for PrimeNG (0-based) but no need to reload data
    this.first = (pageNumber - 1) * this.pageSize;

    console.log(`📄 Updated first to: ${this.first} for page ${pageNumber}`);
  }

  /**
   * Check if any global filters are active
   */
  hasActiveFilters(): boolean {
    return !!(
      this.globalFilters?.practitioner ||
      this.globalFilters?.pole ||
      this.globalFilters?.service ||
      this.globalFilters?.venueType
    );
  }

  /**
   * Format a date for API consumption (YYYY-MM-DD format)
   * @param date Date to format
   * @returns Formatted date string or empty string if date is null
   */
  private formatDateForAPI(date: Date | null): string {
    if (!date) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  /**
   * Get ejcode from user token
   * @returns ejcode string or null if not found
   */
  private getEjCodeFromToken(): string | null {
    try {
      const tokenPayload = this.authService.decodeToken();
      if (tokenPayload && tokenPayload.code_ej) {
        console.log('🏥 EJ Code from token:', tokenPayload.code_ej);
        return tokenPayload.code_ej;
      }

      console.warn('⚠️ No code_ej found in token payload:', tokenPayload);
      return null;
    } catch (error) {
      console.error('❌ Error extracting ejcode from token:', error);
      return null;
    }
  }



  /**
   * Get agent display name (handles titre + prenom + nom instead of fullName)
   */
  getAgentDisplayName(agent: any): string {
    if (!agent) return 'Agent inconnu';

    // Construct from individual fields (titre + prenom + nom)
    const parts = [
      agent.titre,
      agent.prenom,
      agent.nom
    ].filter(part => part && part.trim());

    return parts.join(' ').trim() || agent.nom || 'Agent inconnu';
  }

  /**
   * Update rows per page options based on total items
   */
  updateRowsPerPageOptions(): void {
    this.rowsPerPageOptions = [5, 10, 20, 50];

    // Add an option for showing all items if there are fewer than 100
    if (this.totalActivites > 0 && this.totalActivites <= 100) {
      this.rowsPerPageOptions.push(this.totalActivites);
    }

    // Sort options
    this.rowsPerPageOptions.sort((a, b) => a - b);

    // Remove duplicates
    this.rowsPerPageOptions = this.rowsPerPageOptions.filter((option, index, self) =>
      self.indexOf(option) === index
    );
  }


  /**
   * Check if all activites are selected (pour la page actuelle)
   * @returns True if all activites of current page are selected
   */
  isAllSelected(): boolean {
    return this.activites.length > 0 && this.activites.every(activite =>
      this.selectedActivites.some(selected => selected['@id'] === activite['@id'])
    );
  }

  /**
   * Toggle selection of all activites (pour la page actuelle)
   * @param event Checkbox change event
   */
  toggleSelectAll(event: any): void {
    if (event.checked) {
      // Ajouter toutes les activités de la page actuelle qui ne sont pas déjà sélectionnées
      const newSelections = this.activites.filter(activite =>
        !this.selectedActivites.some(selected => selected['@id'] === activite['@id'])
      );
      this.selectedActivites = [...this.selectedActivites, ...newSelections];
    } else {
      // Retirer toutes les activités de la page actuelle de la sélection
      this.selectedActivites = this.selectedActivites.filter(selected =>
        !this.activites.some(activite => activite['@id'] === selected['@id'])
      );
    }

    console.log(`📋 Toggle select all: ${event.checked ? 'selected' : 'deselected'} ${this.activites.length} activities from current page. Total selected: ${this.selectedActivites.length}`);
  }

  /**
   * Check if a specific activite is selected
   * @param activite Activite to check
   * @returns True if the activite is selected
   */
  isActiviteSelected(activite: ActiviteModel): boolean {
    return this.selectedActivites.some(p => p['@id'] === activite['@id']);
  }

  /**
   * Toggle selection of a specific activite
   * @param activite Activite to toggle
   */
  toggleActiviteSelection(activite: ActiviteModel): void {
    if (this.isActiviteSelected(activite)) {
      this.selectedActivites = this.selectedActivites.filter(p => p['@id'] !== activite['@id']);
    } else {
      this.selectedActivites.push(activite);
    }
  }


  /**
   * Export selected activites as CSV
   */
  exportSelectedActivites(): void {
    if (this.selectedActivites.length === 0) return;

    // Determine if we need to include LABO/NABM specific columns
    const hasLaboActivites = this.selectedActivites.some(
      activite => activite.typeActe === 'LABO' || activite.typeActe === 'NABM'
    );

    // Create CSV content with relevant columns
    const headers = [
      'Code',
      'Description',
      'Type',
      'Date de réalisation',
      'Praticien',
      'UF Intervention',
      'Nombre de réalisations',
      'Type de venue',
      'Détails' // Added Details column
    ];

    // Add LABO/NABM specific columns if needed
    if (hasLaboActivites) {
      headers.push('ICR', 'Coefficient', 'Lettre');
    }

    // Helper function to escape CSV fields properly
    const escapeCSV = (field: any): string => {
      if (field === null || field === undefined) return '""';
      return `"${String(field).replace(/"/g, '""')}"`;
    };

    const csvContent = [
      headers.join(','),
      ...this.selectedActivites.map(activite => {
        // Create details string combining all the badge information
        let details = [];

        // Add nombre de realisations to details
        if (activite.nombreDeRealisation) {
          details.push(`NB: ${activite.nombreDeRealisation}`);
        }

        // Add type de venue to details
        if (activite.libTypeVenue) {
          details.push(`Type: ${activite.libTypeVenue}`);
        }

        // Add ICR, Coefficient, Lettre to details if they exist
        if (activite.icrA) {
          details.push(`ICR: ${activite.icrA}`);
        }

        if (activite.coefficient) {
          details.push(`Coef: ${activite.coefficient}`);
        }

        if (activite.lettreCoef) {
          details.push(`Lettre: ${activite.lettreCoef}`);
        }

        const detailsString = details.join(' | ');

        const row = [
          escapeCSV(activite.code),
          escapeCSV(activite.description),
          escapeCSV(activite.typeActe),
          escapeCSV(this.formatDate(activite.dateRealisation)),
          escapeCSV(activite.agent.fullName || `${activite.agent.prenom} ${activite.agent.nom}`),
          escapeCSV(activite.ufIntervention ? `${activite.ufIntervention.libelle} (${activite.ufIntervention.ufcode})` : 'Pas connu de supra'),
          escapeCSV(activite.nombreDeRealisation),
          escapeCSV(activite.libTypeVenue),
          escapeCSV(detailsString) // Add the details column
        ];

        // Add LABO/NABM specific columns if needed
        if (hasLaboActivites) {
          row.push(
            escapeCSV(activite.icrA || ''),
            escapeCSV(activite.coefficient || ''),
            escapeCSV(activite.lettreCoef || '')
          );
        }

        return row.join(',');
      })
    ].join('\n');

    // Add BOM for proper UTF-8 encoding with French characters
    const BOM = '\uFEFF';
    const csvContentWithBOM = BOM + csvContent;

    // Create and download the file
    const blob = new Blob([csvContentWithBOM], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    // Use the selected type in the filename if filtering is active
    const filePrefix = this.selectedActivityType !== 'ALL' ?
      this.selectedActivityType.toLowerCase() : 'activites';

    link.setAttribute('href', url);
    link.setAttribute('download', `${filePrefix}_export_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * Format a period for display
   * @param period The period to format (can be null or undefined)
   * @returns Formatted period string
   */
  formatPeriod(period: DatePeriod | null | undefined): string {
    if (!period || !period.dateDebut || !period.dateFin) {
      return '';
    }

    const startMonth = period.dateDebut.toLocaleDateString('fr-FR', { month: 'short' });
    const endMonth = period.dateFin.toLocaleDateString('fr-FR', { month: 'short' });
    const year = period.dateDebut.getFullYear();

    if (startMonth === endMonth) {
      return `${startMonth} ${year}`;
    }

    return `${startMonth}-${endMonth} ${year}`;
  }

  /**
   * Calculate percentage for chart display
   * @param value The value
   * @param total The total
   * @returns Percentage
   */
  getPercentage(value: number, total: number): number {
    if (total === 0) return 0;
    return Math.round((value / total) * 100);
  }

  /**
   * Format large numbers for better readability
   * 1000000 -> 1M
   * 1500000 -> 1.5M
   * 1200 -> 1.2K
   * 500 -> 500
   */
  formatLargeNumber(value: number): string {
    if (!value && value !== 0) return '0';

    const absValue = Math.abs(value);

    if (absValue >= 1000000) {
      const millions = value / 1000000;
      return millions % 1 === 0 ? `${millions}M` : `${millions.toFixed(1)}M`;
    } else if (absValue >= 1000) {
      const thousands = value / 1000;
      return thousands % 1 === 0 ? `${thousands}K` : `${thousands.toFixed(1)}K`;
    } else {
      return value.toString();
    }
  }

  /**
   * Handle filters changed from SubHeader component
   * @param filters The new filter state
   */
  onFiltersChanged(filters: GlobalFilterState): void {
    console.log('🔄 Filters changed from SubHeader:', filters);

    // Update global filters
    this.globalFilters = filters;

    console.log('📊 Updated component globalFilters:', {
      hasP1: !!this.globalFilters.p1,
      hasP2: !!this.globalFilters.p2,
      hasP3: !!this.globalFilters.p3,
      practitioner: !!this.globalFilters.practitioner,
      pole: !!this.globalFilters.pole,
      service: !!this.globalFilters.service,
      venueType: this.globalFilters.venueType
    });

    // Recharger les données depuis l'API avec les nouveaux filtres
    console.log('🔄 Reloading data from API with new filters...');
    this.reloadDataWithFilters();
  }



  /**
   * Construit l'URL avec les filtres actifs
   */
  private buildApiUrlWithFilters(baseUrl: string): string {
    const url = new URL(baseUrl, window.location.origin);

    // Ajouter les filtres actifs
    if (this.globalFilters?.practitioner) {
      // Essayer différentes propriétés selon le type d'objet (avec cast any pour éviter les erreurs TypeScript)
      const practitioner = this.globalFilters.practitioner as any;
      const practitionerId = practitioner['@id'] ||
                            practitioner.id ||
                            practitioner.agentId ||
                            practitioner;
      url.searchParams.set('practitioner', practitionerId);
    }

    if (this.globalFilters?.pole) {
      const pole = this.globalFilters.pole as any;
      const poleId = pole['@id'] ||
                     pole.id ||
                     pole.poleId ||
                     pole;
      url.searchParams.set('pole', poleId);
    }

    if (this.globalFilters?.service) {
      const service = this.globalFilters.service as any;
      const crId = service['@id'] ||
                   service.id ||
                   service.crId ||
                   service;
      url.searchParams.set('cr', crId);
    }

    if (this.globalFilters?.venueType && this.globalFilters.venueType !== 'BOTH') {
      // Convertir l'enum en valeur numérique
      const typeVenueValue = this.getVenueTypeValue(this.globalFilters.venueType);
      if (typeVenueValue) {
        url.searchParams.set('typeVenue', typeVenueValue.toString());
      }
    }

    console.log('🔗 Built API URL with filters:', url.toString());
    return url.toString();
  }

  /**
   * Convertir VenueType enum en valeur numérique
   */
  private getVenueTypeValue(venueType: any): number | null {
    switch (venueType) {
      case 'CONSULTATION': return 1;
      case 'HOSPITALIZATION': return 2;
      case 'URGENCE': return 3;
      default: return null;
    }
  }

  /**
   * Charge les activités CCAM avec filtres
   */
  loadCcamActivites(page: number): void {
    this.loading = true;
    const baseUrl = `${environment.api_url}/api/actes?typeActe=CCAM&page=${page}&itemsPerPage=${this.pageSize}`;
    const urlWithFilters = this.buildApiUrlWithFilters(baseUrl);

    console.log('📊 Loading CCAM activities with filters:', urlWithFilters);

    this.http.get<any>(urlWithFilters).subscribe({
      next: (response) => {
        this.activites = response.member || [];
        this.totalRecords = response.totalItems || 0;
        this.selectedActivityType = 'CCAM';
        this.loading = false;
        console.log('✅ CCAM activities loaded with filters:', this.activites.length);
      },
      error: (error) => {
        console.error('❌ Error loading CCAM activities with filters:', error);
        this.loading = false;
      }
    });
  }

  /**
   * Charge les activités NGAP avec filtres
   */
  loadNgapActivites(page: number): void {
    this.loading = true;
    const baseUrl = `${environment.api_url}/api/actes?typeActe=NGAP&page=${page}&itemsPerPage=${this.pageSize}`;
    const urlWithFilters = this.buildApiUrlWithFilters(baseUrl);

    console.log('📊 Loading NGAP activities with filters:', urlWithFilters);

    this.http.get<any>(urlWithFilters).subscribe({
      next: (response) => {
        this.activites = response.member || [];
        this.totalRecords = response.totalItems || 0;
        this.selectedActivityType = 'NGAP';
        this.loading = false;
        console.log('✅ NGAP activities loaded with filters:', this.activites.length);
      },
      error: (error) => {
        console.error('❌ Error loading NGAP activities with filters:', error);
        this.loading = false;
      }
    });
  }

  /**
   * Charge les activités LABO avec filtres
   */
  loadLaboActivites(page: number): void {
    this.loading = true;
    const baseUrl = `${environment.api_url}/api/actes?typeActe[]=LABO&typeActe[]=NABM&page=${page}&itemsPerPage=${this.pageSize}`;
    const urlWithFilters = this.buildApiUrlWithFilters(baseUrl);

    console.log('📊 Loading LABO activities with filters:', urlWithFilters);

    this.http.get<any>(urlWithFilters).subscribe({
      next: (response) => {
        this.activites = response.member || [];
        this.totalRecords = response.totalItems || 0;
        this.selectedActivityType = 'LABO';
        this.loading = false;
        console.log('✅ LABO activities loaded with filters:', this.activites.length);
      },
      error: (error) => {
        console.error('❌ Error loading LABO activities with filters:', error);
        this.loading = false;
      }
    });
  }

  /**
   * Update totals based on global filters (not type filter)
   * Calculates CCAM, NGAP, LABO totals from ALL types with global filters applied
   */
  private updateFilteredTotals(): void {
    console.log('🔢 Updating filtered totals based on global filters...');

    // Get activities with ONLY global filters applied (ignore type filter)
    let globallyFilteredActivites = [...this.allActivites];

    // Apply the same global filter logic but on ALL activities
    if (this.globalFilters &&
        (this.globalFilters.practitioner ||
         this.globalFilters.pole ||
         this.globalFilters.service ||
         this.globalFilters.venueType)) {

      globallyFilteredActivites = this.allActivites.filter(activite => {
        // Apply venue type filter
        if (this.globalFilters?.venueType && activite.typeVenue !== undefined) {
          // Si "Les deux" est sélectionné, afficher tout (pas de filtrage)
          if (this.globalFilters.venueType === VenueType.BOTH) {
            // Ne pas filtrer, afficher toutes les activités
          } else {
            // Filtrer selon le type spécifique
            if (this.globalFilters.venueType === VenueType.HOSPITALIZATION && activite.typeVenue !== 1) {
              return false;
            }
            if (this.globalFilters.venueType === VenueType.URGENCE && activite.typeVenue !== 3) {
              return false;
            }
          }
        }

        // Apply practitioner filter
        if (this.globalFilters?.practitioner) {
          if (!activite.agent || activite.agent['@id'] !== this.globalFilters.practitioner['@id']) {
            return false;
          }
        }

        // Apply pole/CR filters
        const hasPole = this.globalFilters?.pole;
        const hasCr = this.globalFilters?.service; // CR utilise le champ service du filtre global

        if (hasPole || hasCr) {
          if (!activite.ufIntervention) return false;

          let matchesPoleOrCr = false;

          if (hasPole && this.globalFilters?.pole) {
            const ufPole = (activite.ufIntervention as any).pole;
            // Handle both string URI and object cases
            if (ufPole) {
              const ufPoleId = typeof ufPole === 'string' ? ufPole : ufPole['@id'];
              if (ufPoleId === this.globalFilters.pole['@id']) {
                matchesPoleOrCr = true;
              }
            }
          }

          if (hasCr && this.globalFilters?.service) {
            const ufCr = activite.ufIntervention.cr; // Utilise .cr au lieu de .service
            // Handle both string URI and object cases
            if (ufCr) {
              const ufCrId = typeof ufCr === 'string' ? ufCr : ufCr['@id'];
              if (ufCrId === this.globalFilters.service['@id']) {
                matchesPoleOrCr = true;
              }
            }
          }

          if (!matchesPoleOrCr) return false;
        }

        return true;
      });
    }

    // IMPORTANT: Ne PAS écraser les totaux des métadonnées pour la section "Répartition par Type"
    // Ces totaux doivent rester constants (valeurs des métadonnées)
    // this.totalCcamActivites = globallyFilteredActivites.filter(a => a.typeActe === 'CCAM').length;
    // this.totalNgapActivites = globallyFilteredActivites.filter(a => a.typeActe === 'NGAP').length;
    // this.totalLaboActivites = globallyFilteredActivites.filter(a => a.typeActe === 'LABO' || a.typeActe === 'NABM').length;
    // this.totalAllActivites = globallyFilteredActivites.length;

    console.log('🔢 Keeping metadata totals unchanged for "Répartition par Type" section');

    console.log('🔢 Updated filtered totals (with global filters only):', {
      totalAll: this.totalAllActivites,
      totalCcam: this.totalCcamActivites,
      totalNgap: this.totalNgapActivites,
      totalLabo: this.totalLaboActivites,
      globalFiltersApplied: !!(this.globalFilters?.practitioner || this.globalFilters?.pole || this.globalFilters?.service || this.globalFilters?.venueType)
    });
  }

  /**
   * Restore original totals (when no filters are applied)
   */
  private restoreOriginalTotals(): void {
    this.totalAllActivites = this.originalTotalAllActivites;
    this.totalCcamActivites = this.originalTotalCcamActivites;
    this.totalNgapActivites = this.originalTotalNgapActivites;
    this.totalLaboActivites = this.originalTotalLaboActivites;

    console.log('Restored original totals:', {
      totalAll: this.totalAllActivites,
      totalCcam: this.totalCcamActivites,
      totalNgap: this.totalNgapActivites,
      totalLabo: this.totalLaboActivites
    });
  }

  /**
   * Cleanup subscriptions when component is destroyed
   */
  ngOnDestroy(): void {
    // Clear search timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Unsubscribe from all subscriptions to prevent memory leaks
    this.subscriptions.forEach(subscription => {
      if (subscription && !subscription.closed) {
        subscription.unsubscribe();
      }
    });
  }

  /**
   * Recharge les données depuis l'API avec les filtres actifs
   * IMPORTANT: Ne recharge PAS les métadonnées pour préserver les totaux CCAM/NGAP/LABO
   */
  private reloadDataWithFilters(): void {
    console.log('🔄 Reloading data with filters (preserving metadata totals)...');
    this.currentPage = 1; // Reset à la première page
    this.first = 0;

    // Utiliser la pagination côté serveur qui préserve les totaux des métadonnées
    this.loadActivitiesWithServerPagination();
  }

  /**
   * Charge les métadonnées de base UNE SEULE FOIS (totaux sans filtres)
   */
  private loadBaseMetadata(): void {
    // Récupérer l'ejcode depuis le token (comme les autres appels)
    const ejcode = this.getEjCodeFromToken();
    if (!ejcode) {
      console.error('❌ No ejcode found in user token for metadata');
      return;
    }

    // Construire l'URL de base
    let url = `${environment.api_url}/api/current/metadata/actes`;

    // Ajouter les paramètres
    const params = new URLSearchParams();

    // Ajouter l'ejcode (comme les autres appels)
    params.set('ejcode', ejcode);

    // Ajouter SEULEMENT les périodes (pas les filtres utilisateur)
    const globalFilters = this.getGlobalFiltersFromStorage();
    console.log('📊 Global filters for metadata (periods only):', globalFilters);

    if (globalFilters) {
      if (globalFilters.p1?.dateDebut && globalFilters.p1?.dateFin) {
        params.set('p1Start', globalFilters.p1.dateDebut);
        params.set('p1End', globalFilters.p1.dateFin);
        console.log('📊 Added P1:', globalFilters.p1.dateDebut, 'to', globalFilters.p1.dateFin);
      }
      if (globalFilters.p2?.dateDebut && globalFilters.p2?.dateFin) {
        params.set('p2Start', globalFilters.p2.dateDebut);
        params.set('p2End', globalFilters.p2.dateFin);
        console.log('📊 Added P2:', globalFilters.p2.dateDebut, 'to', globalFilters.p2.dateFin);
      }
      if (globalFilters.p3?.dateDebut && globalFilters.p3?.dateFin) {
        params.set('p3Start', globalFilters.p3.dateDebut);
        params.set('p3End', globalFilters.p3.dateFin);
        console.log('📊 Added P3:', globalFilters.p3.dateDebut, 'to', globalFilters.p3.dateFin);
      }
    } else {
      console.warn('📊 No global filters found for periods');
    }

    // IMPORTANT: Ne PAS ajouter les filtres utilisateur (practitioner, pole, cr, typeVenue)
    console.log('📊 Loading metadata WITHOUT user filters (always base totals)...');

    // Construire l'URL finale
    const finalUrl = params.toString() ? `${url}?${params.toString()}` : url;
    console.log('📊 Fetching metadata from:', finalUrl);

    // Faire l'appel HTTP
    this.http.get<any>(finalUrl).subscribe({
      next: (metadata) => {
        console.log('📊 Metadata loaded (base totals):', metadata);
        console.log('📊 About to call updateTotalsFromMetadata...');
        this.updateTotalsFromMetadata(metadata);
        console.log('📊 updateTotalsFromMetadata completed');
      },
      error: (error) => {
        console.error('❌ Error loading metadata:', error);
        // Mettre le système en état "hors ligne"
        this.updateSystemHealth(0, true);
        // Fallback sur le calcul côté frontend
        this.updateFilteredTotals();
      }
    });
  }

  /**
   * Met à jour l'indicateur de santé du système basé sur le temps de génération
   * @param generationTimeMs Temps de génération en millisecondes
   * @param isError Indique si c'est suite à une erreur
   */
  private updateSystemHealth(generationTimeMs: number, isError: boolean = false): void {
    this.generationTimeMs = generationTimeMs;

    if (isError) {
      this.systemHealthStatus = 'offline';
      this.systemHealthMessage = 'Vitesse de chargement - Hors ligne';
      return;
    }

    // Définir les seuils de performance
    if (generationTimeMs <= 500) {
      this.systemHealthStatus = 'excellent';
      this.systemHealthMessage = `Vitesse de chargement - Excellent (${generationTimeMs}ms)`;
    } else if (generationTimeMs <= 1500) {
      this.systemHealthStatus = 'good';
      this.systemHealthMessage = `Vitesse de chargement - Bon (${generationTimeMs}ms)`;
    } else {
      this.systemHealthStatus = 'slow';
      this.systemHealthMessage = `Vitesse de chargement - Lent (${generationTimeMs}ms)`;
    }

    console.log('🏥 System health updated:', {
      status: this.systemHealthStatus,
      message: this.systemHealthMessage,
      generationTime: generationTimeMs + 'ms'
    });
  }





  /**
   * Met à jour les totaux depuis les métadonnées API
   */
  private updateTotalsFromMetadata(metadata: any): void {
    console.log('📊 Updating totals from metadata API...');
    console.log('📊 Raw metadata received:', metadata);

    // Vérifier la structure des données reçues
    if (!metadata) {
      console.error('❌ No metadata received');
      return;
    }

    // Assigner directement les valeurs de l'API metadata
    this.totalRecords = metadata.totalItems || 0;
    this.totalAllActivites = metadata.totalItems || 0;

    // Vérifier si totalByType existe
    if (metadata.totalByType) {
      this.totalCcamActivites = metadata.totalByType.CCAM || 0;
      this.totalNgapActivites = metadata.totalByType.NGAP || 0;
      this.totalLaboActivites = metadata.totalByType.LABO || 0;
    } else {
      console.warn('⚠️ totalByType not found in metadata, setting to 0');
      this.totalCcamActivites = 0;
      this.totalNgapActivites = 0;
      this.totalLaboActivites = 0;
    }

    // Mettre à jour l'indicateur de santé du système
    this.updateSystemHealth(metadata.generationTimeMs || 0);

    console.log('📊 Totals from metadata:', {
      totalItems: metadata.totalItems,
      totalByType: metadata.totalByType,
      CCAM: metadata.totalByType?.CCAM,
      NGAP: metadata.totalByType?.NGAP,
      LABO: metadata.totalByType?.LABO,
      generationTime: metadata.generationTimeMs + 'ms'
    });

    console.log('📊 Component totals assigned:', {
      totalCcamActivites: this.totalCcamActivites,
      totalNgapActivites: this.totalNgapActivites,
      totalLaboActivites: this.totalLaboActivites,
      totalAllActivites: this.totalAllActivites,
      systemHealth: this.systemHealthStatus
    });

    // Forcer la détection de changement pour mettre à jour l'interface
    this.cdr.detectChanges();
    console.log('🔄 Change detection forced for UI update');
  }

  /**
   * Récupère les filtres globaux depuis le localStorage
   */
  private getGlobalFiltersFromStorage(): any {
    try {
      const stored = localStorage.getItem('global_filters');
      const filters = stored ? JSON.parse(stored) : null;

      console.log('📊 Global filters from localStorage:', filters);

      // Si pas de filtres dans localStorage, utiliser les filtres du service
      if (!filters) {
        const serviceFilters = this.globalFilterService.getCurrentFilterState();
        console.log('📊 Using filters from service:', serviceFilters);
        return serviceFilters;
      }

      return filters;
    } catch (error) {
      console.error('Error parsing global filters from localStorage:', error);
      // Fallback sur les filtres du service
      return this.globalFilterService.getCurrentFilterState();
    }
  }




}
