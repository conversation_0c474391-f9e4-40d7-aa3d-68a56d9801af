<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  PageComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#styleData" class="nav-link"
                role="tab" id="styleData-tab" data-bs-toggle="tab" data-link="style">Styles</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/stories/page.component.ts</code>
</p>






<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>storybook-page</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>CommonModule</code>
                                <code><a href="../components/HeaderComponent.html" target="_self" >HeaderComponent</a></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-3">styleUrls</td>
                <td class="col-md-9"><code>./page.css</code></td>
            </tr>


            <tr>
                <td class="col-md-3">template</td>
                <td class="col-md-9"><pre class="line-numbers"><code class="language-html">&lt;article&gt;  &lt;storybook-header
    [user]&#x3D;&quot;user&quot;
    (onLogout)&#x3D;&quot;doLogout()&quot;
    (onLogin)&#x3D;&quot;doLogin()&quot;
    (onCreateAccount)&#x3D;&quot;doCreateAccount()&quot;
  &gt;&lt;/storybook-header&gt;
  &lt;section class&#x3D;&quot;storybook-page&quot;&gt;
    &lt;h2&gt;Pages in Storybook&lt;/h2&gt;
    &lt;p&gt;
      We recommend building UIs with a
      &lt;a href&#x3D;&quot;https://componentdriven.org&quot; target&#x3D;&quot;_blank&quot; rel&#x3D;&quot;noopener noreferrer&quot;&gt;
        &lt;strong&gt;component-driven&lt;/strong&gt;
      &lt;/a&gt;
      process starting with atomic components and ending with pages.
    &lt;/p&gt;
    &lt;p&gt;
      Render pages with mock data. This makes it easy to build and review page states without
      needing to navigate to them in your app. Here are some handy patterns for managing page data
      in Storybook:
    &lt;/p&gt;
    &lt;ul&gt;
      &lt;li&gt;
        Use a higher-level connected component. Storybook helps you compose such data from the
        &quot;args&quot; of child component stories
      &lt;/li&gt;
      &lt;li&gt;
        Assemble data in the page component from your services. You can mock these services out
        using Storybook.
      &lt;/li&gt;
    &lt;/ul&gt;
    &lt;p&gt;
      Get a guided tutorial on component-driven development at
      &lt;a href&#x3D;&quot;https://storybook.js.org/tutorials/&quot; target&#x3D;&quot;_blank&quot; rel&#x3D;&quot;noopener noreferrer&quot;&gt;
        Storybook tutorials
      &lt;/a&gt;
      . Read more in the
      &lt;a href&#x3D;&quot;https://storybook.js.org/docs&quot; target&#x3D;&quot;_blank&quot; rel&#x3D;&quot;noopener noreferrer&quot;&gt; docs &lt;/a&gt;
      .
    &lt;/p&gt;
    &lt;div class&#x3D;&quot;tip-wrapper&quot;&gt;
      &lt;span class&#x3D;&quot;tip&quot;&gt;Tip&lt;/span&gt; Adjust the width of the canvas with the
      &lt;svg width&#x3D;&quot;10&quot; height&#x3D;&quot;10&quot; viewBox&#x3D;&quot;0 0 12 12&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;&gt;
        &lt;g fill&#x3D;&quot;none&quot; fillRule&#x3D;&quot;evenodd&quot;&gt;
          &lt;path
            d&#x3D;&quot;M1.5 5.2h4.8c.3 0 .5.2.5.4v5.1c-.1.2-.3.3-.4.3H1.4a.5.5 0 01-.5-.4V5.7c0-.3.2-.5.5-.5zm0-2.1h6.9c.3 0 .5.2.5.4v7a.5.5 0 01-1 0V4H1.5a.5.5 0 010-1zm0-2.1h9c.3 0 .5.2.5.4v9.1a.5.5 0 01-1 0V2H1.5a.5.5 0 010-1zm4.3 5.2H2V10h3.8V6.2z&quot;
            id&#x3D;&quot;a&quot;
            fill&#x3D;&quot;#999&quot;
          /&gt;
        &lt;/g&gt;
      &lt;/svg&gt;
      Viewports addon in the toolbar
    &lt;/div&gt;
  &lt;/section&gt;
&lt;/article&gt;</code></pre></td>
            </tr>









        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#user" >user</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#doCreateAccount" >doCreateAccount</a>
                            </li>
                            <li>
                                <a href="#doLogin" >doLogin</a>
                            </li>
                            <li>
                                <a href="#doLogout" >doLogout</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>






    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="doCreateAccount"></a>
                    <span class="name">
                        <span ><b>doCreateAccount</b></span>
                        <a href="#doCreateAccount"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>doCreateAccount()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="79"
                                    class="link-to-prism">src/stories/page.component.ts:79</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="doLogin"></a>
                    <span class="name">
                        <span ><b>doLogin</b></span>
                        <a href="#doLogin"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>doLogin()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="75"
                                    class="link-to-prism">src/stories/page.component.ts:75</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="doLogout"></a>
                    <span class="name">
                        <span ><b>doLogout</b></span>
                        <a href="#doLogout"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>doLogout()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="71"
                                    class="link-to-prism">src/stories/page.component.ts:71</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="user"></a>
                    <span class="name">
                        <span ><b>user</b></span>
                        <a href="#user"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>User | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>null</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="69" class="link-to-prism">src/stories/page.component.ts:69</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Component } from &#x27;@angular/core&#x27;;
import { CommonModule } from &#x27;@angular/common&#x27;;

import { HeaderComponent } from &#x27;./header.component&#x27;;
import type { User } from &#x27;./user&#x27;;

@Component({
  selector: &#x27;storybook-page&#x27;,
  standalone: true,
  imports: [CommonModule, HeaderComponent],
  template: &#x60;&lt;article&gt;
  &lt;storybook-header
    [user]&#x3D;&quot;user&quot;
    (onLogout)&#x3D;&quot;doLogout()&quot;
    (onLogin)&#x3D;&quot;doLogin()&quot;
    (onCreateAccount)&#x3D;&quot;doCreateAccount()&quot;
  &gt;&lt;/storybook-header&gt;
  &lt;section class&#x3D;&quot;storybook-page&quot;&gt;
    &lt;h2&gt;Pages in Storybook&lt;/h2&gt;
    &lt;p&gt;
      We recommend building UIs with a
      &lt;a href&#x3D;&quot;https://componentdriven.org&quot; target&#x3D;&quot;_blank&quot; rel&#x3D;&quot;noopener noreferrer&quot;&gt;
        &lt;strong&gt;component-driven&lt;/strong&gt;
      &lt;/a&gt;
      process starting with atomic components and ending with pages.
    &lt;/p&gt;
    &lt;p&gt;
      Render pages with mock data. This makes it easy to build and review page states without
      needing to navigate to them in your app. Here are some handy patterns for managing page data
      in Storybook:
    &lt;/p&gt;
    &lt;ul&gt;
      &lt;li&gt;
        Use a higher-level connected component. Storybook helps you compose such data from the
        &quot;args&quot; of child component stories
      &lt;/li&gt;
      &lt;li&gt;
        Assemble data in the page component from your services. You can mock these services out
        using Storybook.
      &lt;/li&gt;
    &lt;/ul&gt;
    &lt;p&gt;
      Get a guided tutorial on component-driven development at
      &lt;a href&#x3D;&quot;https://storybook.js.org/tutorials/&quot; target&#x3D;&quot;_blank&quot; rel&#x3D;&quot;noopener noreferrer&quot;&gt;
        Storybook tutorials
      &lt;/a&gt;
      . Read more in the
      &lt;a href&#x3D;&quot;https://storybook.js.org/docs&quot; target&#x3D;&quot;_blank&quot; rel&#x3D;&quot;noopener noreferrer&quot;&gt; docs &lt;/a&gt;
      .
    &lt;/p&gt;
    &lt;div class&#x3D;&quot;tip-wrapper&quot;&gt;
      &lt;span class&#x3D;&quot;tip&quot;&gt;Tip&lt;/span&gt; Adjust the width of the canvas with the
      &lt;svg width&#x3D;&quot;10&quot; height&#x3D;&quot;10&quot; viewBox&#x3D;&quot;0 0 12 12&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;&gt;
        &lt;g fill&#x3D;&quot;none&quot; fillRule&#x3D;&quot;evenodd&quot;&gt;
          &lt;path
            d&#x3D;&quot;M1.5 5.2h4.8c.3 0 .5.2.5.4v5.1c-.1.2-.3.3-.4.3H1.4a.5.5 0 01-.5-.4V5.7c0-.3.2-.5.5-.5zm0-2.1h6.9c.3 0 .5.2.5.4v7a.5.5 0 01-1 0V4H1.5a.5.5 0 010-1zm0-2.1h9c.3 0 .5.2.5.4v9.1a.5.5 0 01-1 0V2H1.5a.5.5 0 010-1zm4.3 5.2H2V10h3.8V6.2z&quot;
            id&#x3D;&quot;a&quot;
            fill&#x3D;&quot;#999&quot;
          /&gt;
        &lt;/g&gt;
      &lt;/svg&gt;
      Viewports addon in the toolbar
    &lt;/div&gt;
  &lt;/section&gt;
&lt;/article&gt;&#x60;,
  styleUrls: [&#x27;./page.css&#x27;],
})
export class PageComponent {
  user: User | null &#x3D; null;

  doLogout() {
    this.user &#x3D; null;
  }

  doLogin() {
    this.user &#x3D; { name: &#x27;Jane Doe&#x27; };
  }

  doCreateAccount() {
    this.user &#x3D; { name: &#x27;Jane Doe&#x27; };
  }
}
</code></pre>
    </div>


    <div class="tab-pane fade " id="styleData">
                <p class="comment">
                    <code>./page.css</code>
                </p>
                <pre class="line-numbers"><code class="language-scss">.storybook-page {
  margin: 0 auto;
  padding: 48px 20px;
  max-width: 600px;
  color: #333;
  font-size: 14px;
  line-height: 24px;
  font-family: &#x27;Nunito Sans&#x27;, &#x27;Helvetica Neue&#x27;, Helvetica, Arial, sans-serif;
}

.storybook-page h2 {
  display: inline-block;
  vertical-align: top;
  margin: 0 0 4px;
  font-weight: 700;
  font-size: 32px;
  line-height: 1;
}

.storybook-page p {
  margin: 1em 0;
}

.storybook-page a {
  color: #1ea7fd;
  text-decoration: none;
}

.storybook-page ul {
  margin: 1em 0;
  padding-left: 30px;
}

.storybook-page li {
  margin-bottom: 8px;
}

.storybook-page .tip {
  display: inline-block;
  vertical-align: top;
  margin-right: 10px;
  border-radius: 1em;
  background: #e7fdd8;
  padding: 4px 12px;
  color: #66bf3c;
  font-weight: 700;
  font-size: 11px;
  line-height: 12px;
}

.storybook-page .tip-wrapper {
  margin-top: 40px;
  margin-bottom: 40px;
  font-size: 13px;
  line-height: 20px;
}

.storybook-page .tip-wrapper svg {
  display: inline-block;
  vertical-align: top;
  margin-top: 3px;
  margin-right: 4px;
  width: 12px;
  height: 12px;
}

.storybook-page .tip-wrapper svg path {
  fill: #1ea7fd;
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><article>  <storybook-header    [user]="user"    (onLogout)="doLogout()"    (onLogin)="doLogin()"    (onCreateAccount)="doCreateAccount()"  ></storybook-header>  <section class="storybook-page">    <h2>Pages in Storybook</h2>    <p>      We recommend building UIs with a      <a href="https://componentdriven.org" target="_blank" rel="noopener noreferrer">        <strong>component-driven</strong>      </a>      process starting with atomic components and ending with pages.    </p>    <p>      Render pages with mock data. This makes it easy to build and review page states without      needing to navigate to them in your app. Here are some handy patterns for managing page data      in Storybook:    </p>    <ul>      <li>        Use a higher-level connected component. Storybook helps you compose such data from the        "args" of child component stories      </li>      <li>        Assemble data in the page component from your services. You can mock these services out        using Storybook.      </li>    </ul>    <p>      Get a guided tutorial on component-driven development at      <a href="https://storybook.js.org/tutorials/" target="_blank" rel="noopener noreferrer">        Storybook tutorials      </a>      . Read more in the      <a href="https://storybook.js.org/docs" target="_blank" rel="noopener noreferrer"> docs </a>      .    </p>    <div class="tip-wrapper">      <span class="tip">Tip</span> Adjust the width of the canvas with the      <svg width="10" height="10" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg">        <g fill="none" fillRule="evenodd">          <path            d="M1.5 5.2h4.8c.3 0 .5.2.5.4v5.1c-.1.2-.3.3-.4.3H1.4a.5.5 0 01-.5-.4V5.7c0-.3.2-.5.5-.5zm0-2.1h6.9c.3 0 .5.2.5.4v7a.5.5 0 01-1 0V4H1.5a.5.5 0 010-1zm0-2.1h9c.3 0 .5.2.5.4v9.1a.5.5 0 01-1 0V2H1.5a.5.5 0 010-1zm4.3 5.2H2V10h3.8V6.2z"            id="a"            fill="#999"          />        </g>      </svg>      Viewports addon in the toolbar    </div>  </section></article></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'PageComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'PageComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
