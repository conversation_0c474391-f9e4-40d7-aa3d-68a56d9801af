import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivitesRoutingModule } from './activites-routing.module';

// PrimeNG Modules
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { SkeletonModule } from 'primeng/skeleton';
import { CheckboxModule } from 'primeng/checkbox';

// Note: Components are standalone and don't need to be declared here

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    ActivitesRoutingModule,
    TableModule,
    ButtonModule,
    CardModule,
    TabViewModule,
    TooltipModule,
    SkeletonModule,
    CheckboxModule
  ]
})
export class ActivitesModule { }
