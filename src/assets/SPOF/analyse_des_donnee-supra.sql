

-- Le Describe table

DESCRIBE  homere.EX_MOIS_ACTES2;


-- Analyser les types d'actes (TYPEACTE) :
SELECT DISTINCT TYPEACTE, COUNT(*) AS NOMBRE
FROM homere.EX_MOIS_ACTES2
GROUP BY TYPEACTE;


-- Lister les CODE_ACTE pour chaque type : Exemple pour NGAP :
SELECT DISTINCT CODE_ACTE, LIBELLEL_A
FROM homere.EX_MOIS_ACTES2
WHERE TYPEACTE = 'NGAP'
ORDER BY CODE_ACTE;


-- Explorer les UF (unités fonctionnelles) :
SELECT DISTINCT UFPRINCIPAL, LIBSEPRINCIPAL
FROM homere.EX_MOIS_ACTES2
WHERE TYPEACTE = 'CCAM'
ORDER BY UFPRINCIPAL;
