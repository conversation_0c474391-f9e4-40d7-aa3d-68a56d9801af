<section>
  <div class="mb-6">
    <h2 class="text-2xl font-bold text-cyan-700 text-center">
      {{ acteTitle }}
    </h2>
  </div>
  <div class="grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg">
    <!-- Graphique en barres groupées -->
    <div class="col-span-12 bg-white p-4 shadow rounded-lg">
      <h5 class="text-lg font-bold text-gray-700 mb-3">
        Comparaison du nombre d'actes par UF pour la période 2023 et 2024
      </h5>
      <p-chart
        type="bar"
        [data]="groupedBarChartData"
        [options]="chartOptions"
        pStyleClass="w-full h-96"
      ></p-chart>
    </div>
  </div>

</section>

<!-- Détails des actes réalisés par UF pour la période 2023 et 2024-->
<section class="mb-8 mt-8">
  <h2 class="text-2xl font-bold text-cyan-700 mb-4">
    Détails des actes réalisés par UF pour la période 2023 et 2024
  </h2>
  <div class="p-card">
    <p-table
      #acteParUfTable
      [value]="acteParUfSummary"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [responsiveLayout]="'scroll'"
      [globalFilterFields]="['nom', 'nbActesAnneeNmoinsUn','nbActesAnneeN']"
      class="min-w-full bg-white border rounded-lg shadow"
    >
      <!-- Caption with Title, Total, and Search Input -->
      <ng-template pTemplate="caption">
        <div class="flex justify-between items-center p-3 rounded-t-lg">
          <div class="text-lg font-bold text-cyan-700 flex items-center">
            <!--             drop down hear if needed-->
          </div>
          <div>
            <div class="relative mt-2 rounded-md shadow-sm">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <span class="text-gray-500 sm:text-sm mr-3">
            <i class="pi pi-search"></i>
          </span>
              </div>
              <input
                type="text"
                name="search"
                (input)="acteParUfTable.filterGlobal( onInput($event), 'contains')"
                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm"
                placeholder="Filtrer par nom UF"
              />
            </div>
          </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>
          <th pSortableColumn="nom" class="px-4 py-2 border-b text-left font-semibold">
            UF
            <p-sortIcon field="nom"></p-sortIcon>
          </th>

          <th pSortableColumn="nbActesAnneeNmoinsUn" class="px-4 py-2 border text-center font-semibold">
            {{ anneeNmoinsUn }}
            <p-sortIcon field="nbActesAnneeNmoinsUn"></p-sortIcon>
            <div class="flex justify-center space-x-8">
              <span>NB actes</span>
              <span>Part de l'UF</span>
            </div>
          </th>
          <th pSortableColumn="nbActesAnneeN" class="px-4 py-2 border text-center font-semibold">
            {{anneeN}}
            <p-sortIcon field="nbActesAnneeN"></p-sortIcon>
            <div class="flex justify-center space-x-8">
              <span>NB actes</span>
              <span>Part de l'UF</span>
            </div>
          </th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-uf>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                title="Voir les détails de {{uf.nom}}"
                (click)="viewUfDetails(uf)"
              ></i>
            </p>
          </td>
          <td class="px-4 py-2">{{ uf.nom }}</td>
          <td class="px-4 py-2 border text-center">
            <div class="flex justify-center space-x-8">
              <span>{{ uf.actesNmoinsUn || "0" }}</span>
              <span>{{ uf.partUfNmoinsUn || "0%" }}</span>
            </div>
          </td>
          <td class="px-4 py-2 border text-center">
            <div class="flex justify-center space-x-8">
              <span>{{ uf.actesAnneeN || "0" }}</span>
              <span>{{ uf.partUfAnneeN || "0%" }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="footer">
        <tr class="font-semibold bg-gray-100">
          <td colspan="2" class="px-4 py-2 text-left">TOTAL</td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalNbActeUfAnneeNmoinsUn || "0" }}</span>
              <span>{{ totalPartUfAnneeNmoinsUn || "0%" }}</span>
            </div>
          </td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalNbActeUfAnneeN || "0" }}</span>
              <span>{{ totalPartUfAnneeN || "0%" }}</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>

</section>

<!-- Détails des actes réalisés par praticien pour la période 2023 et 2024 -->
<section class="mb-8 mt-8">
  <h2 class="text-2xl font-bold text-cyan-700 mb-4">
    Détails des actes réalisés par Praticien pour la période 2023 et 2024
  </h2>

  <div class="p-card">
    <p-table
      #acteParPraticien
      [value]="acteParPraticienSummary"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [responsiveLayout]="'scroll'"
      [globalFilterFields]="['nom', 'prenom', 'nbActesAnneeNmoinsUn','nbActesAnneeN']"
      class="min-w-full bg-white border rounded-lg shadow"
    >
      <!-- Caption with Search Input -->
      <ng-template pTemplate="caption">
        <div class="flex justify-between items-center p-3 rounded-t-lg">
          <div class="text-lg font-bold text-cyan-700 flex items-center">
            <!-- Placeholder for dropdown or additional controls -->
          </div>
          <div>
            <div class="relative mt-2 rounded-md shadow-sm">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <span class="text-gray-500 sm:text-sm mr-3">
                  <i class="pi pi-search"></i>
                </span>
              </div>
              <input
                type="text"
                name="search"
                (input)="acteParPraticien.filterGlobal(onInput($event), 'contains')"
                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
                  ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset
                  focus:ring-indigo-600 sm:text-sm"
                placeholder="Filtrer par nom ou prénom"
              />
            </div>
          </div>
        </div>
      </ng-template>

      <!-- Table Header -->
      <ng-template pTemplate="header">
        <tr>
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails du praticien"
            ></i>
          </th>
          <th pSortableColumn="nom" class="px-4 py-2 border-b text-left font-semibold">
            Nom
            <p-sortIcon field="nom"></p-sortIcon>
          </th>
          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left font-semibold">
            Prénom
            <p-sortIcon field="prenom"></p-sortIcon>
          </th>
          <th pSortableColumn="nbActesAnneeNmoinsUn" class="px-4 py-2 border text-center font-semibold">
            {{ anneeNmoinsUn }}
            <p-sortIcon field="nbActesAnneeNmoinsUn"></p-sortIcon>
            <div class="flex justify-center space-x-8">
              <span>NB actes</span>
              <span>Part du praticien</span>
            </div>
          </th>
          <th pSortableColumn="nbActesAnneeN" class="px-4 py-2 border text-center font-semibold">
            {{ anneeN }}
            <p-sortIcon field="nbActesAnneeN"></p-sortIcon>
            <div class="flex justify-center space-x-8">
              <span>NB actes</span>
              <span>Part du praticien</span>
            </div>
          </th>
        </tr>
      </ng-template>

      <!-- Table Body -->
      <ng-template pTemplate="body" let-praticien>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                title="Voir les détails de {{ praticien.nom }} {{ praticien.prenom }}"
                (click)="viewPraticienDetails(praticien)"
              ></i>
            </p>
          </td>
          <td class="px-4 py-2">{{ praticien.nom }}</td>
          <td class="px-4 py-2">{{ praticien.prenom }}</td>
          <td class="px-4 py-2 border text-center">
            <div class="flex justify-center space-x-8">
              <span>{{ praticien.nbActesAnneeNmoinsUn || "0" }}</span>
              <span>{{ praticien.partAnneeNmoinsUn || "0%" }}</span>
            </div>
          </td>
          <td class="px-4 py-2 border text-center">
            <div class="flex justify-center space-x-8">
              <span>{{ praticien.nbActesAnneeN || "0" }}</span>
              <span>{{ praticien.partAnneeN || "0%" }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Footer -->
      <ng-template pTemplate="footer">
        <tr class="font-semibold bg-gray-100">
          <td colspan="3" class="px-4 py-2 text-left">TOTAL</td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalNbActePraticienAnneeNmoinsUn || "0" }}</span>
              <span>{{ totalPartPraticienAnneeNmoinsUn || "0%" }}</span>
            </div>
          </td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalNbActePraticienAnneeN || "0" }}</span>
              <span>{{ totalPartPraticienAnneeN || "0%" }}</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</section>

