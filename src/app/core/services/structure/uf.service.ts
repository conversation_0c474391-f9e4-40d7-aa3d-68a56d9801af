import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseStructureService, PaginatedResult } from './base-structure.service';
import { UFModel } from '../../models/structure/UFModel';

interface UFResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': UFModel[];
}

@Injectable({
  providedIn: 'root'
})
export class UFService extends BaseStructureService<UFModel, UFResponse> {
  constructor(http: HttpClient) {
    super(http, '/api/ufs');
  }

  /**
   * Extract UFModel array and total count from API response
   * @param response API response
   * @returns PaginatedResult containing UFModel array and total count
   */
  protected extractData(response: UFResponse): PaginatedResult<UFModel> {
    return {
      items: response.member,
      totalItems: response.totalItems
    };
  }
}
