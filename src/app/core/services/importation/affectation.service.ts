import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../auth/auth.service';
import { BaseImportService, ImportValidation } from './base-import.service';

export interface AffectationImportError {
  uConnexion: string;
  codeUf: string;
  error: string;
}

export interface AffectationImportResponse {
  status: 'success' | 'error';
  message: string;
  processed?: number;
  created?: number;
  updated?: number;
  errors?: AffectationImportError[];
}

@Injectable({
  providedIn: 'root'
})
export class AffectationService extends BaseImportService {

  constructor(
    protected override http: HttpClient,
    protected override authService: AuthService
  ) {
    super(http, authService);
  }

  /**
   * Construire l'URL d'import pour les affectations
   */
  protected buildImportUrl(): string {
    const ejcode = this.getEjCodeFromToken();
    if (!ejcode) {
      throw new Error('EJ Code requis pour l\'import d\'affectations');
    }
    return `${environment.api_url}/api/admin/import/${ejcode}/affectations`;
  }

  /**
   * Valider les données d'affectation
   */
  protected validateData(data: any[]): ImportValidation {
    const errors: string[] = [];

    if (!data || data.length === 0) {
      errors.push('Aucune donnée à importer');
      return { isValid: false, errors };
    }

    // Vérifier que l'ejcode est disponible
    const ejcode = this.getEjCodeFromToken();
    if (!ejcode) {
      errors.push('Code établissement (EJ) requis pour l\'import d\'affectations');
    }

    // Validation basique des champs requis pour les affectations
    data.forEach((item, index) => {
      if (!item.uConnexion && !item.matricule) {
        errors.push(`Ligne ${index + 1}: uConnexion ou matricule requis`);
      }
      if (!item.codeUf) {
        errors.push(`Ligne ${index + 1}: codeUf requis`);
      }
      if (!item.dateDebut) {
        errors.push(`Ligne ${index + 1}: dateDebut requise`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
