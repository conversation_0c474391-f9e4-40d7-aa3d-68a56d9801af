import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { ActiviteModel } from '../../models/activite/ActiviteModel';
import { DatePeriod } from '../global-filter';

/**
 * Interface pour les données d'actes agrégées par praticien
 */
export interface PraticienActeData {
  code: string;
  description: string;
  typeActe: string;
  totalP1: number;
  totalP2: number;
  totalP3: number;
}



/**
 * Interface pour la réponse de l'API actes
 */
export interface ActesApiResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  totalItems: number;
  member: ActiviteModel[];
  view?: any;
}

/**
 * Service pour récupérer et traiter les données d'actes d'un praticien
 */
@Injectable({
  providedIn: 'root'
})
export class PraticienActesService {
  private readonly API_BASE_URL = `${environment.api_url}/api/actes`;

  constructor(private http: HttpClient) {}

  /**
   * Récupère les données d'actes pour un praticien avec filtrage par périodes et type d'acte
   *
   * @param agentHrUser - Identifiant HR du praticien
   * @param typeActe - Type d'acte (CCAM, NGAP, LABO)
   * @param p1 - Période P1 (actuelle)
   * @param p2 - Période P2 (année précédente)
   * @param p3 - Période P3 (il y a deux ans)
   * @returns Observable<PraticienActeData[]>
   */
  getActesByPraticienAndType(
    agentHrUser: string,
    typeActe: string,
    p1: DatePeriod | null = null,
    p2: DatePeriod | null = null,
    p3: DatePeriod | null = null
  ): Observable<PraticienActeData[]> {
    console.log(`🔍 Loading ${typeActe} actes for agent:`, agentHrUser);

    let params = new HttpParams();

    // Filtre par agent HR User
    params = params.set('agent.hrUser', agentHrUser);

    // Filtre par type d'acte
    params = params.set('typeActe', typeActe);

    // Ajouter les périodes si disponibles
    if (p1 && p1.dateDebut && p1.dateFin) {
      params = params.set('p1Start', this.formatDate(p1.dateDebut));
      params = params.set('p1End', this.formatDate(p1.dateFin));
    }
    if (p2 && p2.dateDebut && p2.dateFin) {
      params = params.set('p2Start', this.formatDate(p2.dateDebut));
      params = params.set('p2End', this.formatDate(p2.dateFin));
    }
    if (p3 && p3.dateDebut && p3.dateFin) {
      params = params.set('p3Start', this.formatDate(p3.dateDebut));
      params = params.set('p3End', this.formatDate(p3.dateFin));
    }

    // Récupérer toutes les données (pas de pagination pour l'agrégation)
    params = params.set('itemsPerPage', '10000');

    return this.http.get<ActesApiResponse>(this.API_BASE_URL, { params }).pipe(
      map(response => this.aggregateActeData(response.member, p1, p2, p3))
    );
  }

  /**
   * Agrège les données d'actes par code et calcule les totaux par période
   *
   * @param actes - Liste des actes de l'API
   * @param p1 - Période P1
   * @param p2 - Période P2
   * @param p3 - Période P3
   * @returns PraticienActeData[]
   */
  private aggregateActeData(
    actes: ActiviteModel[],
    p1: DatePeriod | null,
    p2: DatePeriod | null,
    p3: DatePeriod | null
  ): PraticienActeData[] {
    const aggregatedData = new Map<string, PraticienActeData>();

    actes.forEach(acte => {
      const key = acte.code;
      const dateRealisation = new Date(acte.dateRealisation);
      const nombreRealisation = acte.nombreDeRealisation || 0;

      // Créer l'entrée si elle n'existe pas
      if (!aggregatedData.has(key)) {
        aggregatedData.set(key, {
          code: acte.code,
          description: acte.description,
          typeActe: acte.typeActe,
          totalP1: 0,
          totalP2: 0,
          totalP3: 0
        });
      }

      const aggregated = aggregatedData.get(key)!;

      // Calculer dans quelle période tombe cette réalisation et additionner
      if (p1 && this.isDateInPeriod(dateRealisation, p1)) {
        aggregated.totalP1 += nombreRealisation;
      }
      if (p2 && this.isDateInPeriod(dateRealisation, p2)) {
        aggregated.totalP2 += nombreRealisation;
      }
      if (p3 && this.isDateInPeriod(dateRealisation, p3)) {
        aggregated.totalP3 += nombreRealisation;
      }
    });

    // Créer de nouveaux objets pour éviter les références partagées
    const result = Array.from(aggregatedData.values()).map(acte => ({
      code: acte.code,
      description: acte.description,
      typeActe: acte.typeActe,
      totalP1: acte.totalP1,
      totalP2: acte.totalP2,
      totalP3: acte.totalP3
    }));

    // Filtrer les actes qui ont au moins une réalisation dans une des périodes
    const filteredResult = result.filter(acte =>
      acte.totalP1 > 0 || acte.totalP2 > 0 || acte.totalP3 > 0
    );

    return filteredResult;
  }

  /**
   * Vérifie si une date est dans une période donnée
   */
  private isDateInPeriod(date: Date, period: DatePeriod): boolean {
    if (!period.dateDebut || !period.dateFin) {
      return false;
    }

    return date >= period.dateDebut && date <= period.dateFin;
  }

  /**
   * Formate une date au format YYYY-MM-DD pour l'API
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}
