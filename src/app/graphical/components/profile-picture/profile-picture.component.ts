import {Component, Input, OnChanges, OnInit} from '@angular/core';
import {Ng<PERSON>lass, NgStyle} from "@angular/common";

@Component({
  selector: 'app-profile-picture',
  standalone: true,
  imports: [
    Ng<PERSON><PERSON>,
    NgStyle
  ],
  templateUrl: './profile-picture.component.html',
  styleUrl: './profile-picture.component.scss'
})
export class ProfilePictureComponent implements OnInit, OnChanges{
  @Input() nom: string = '';
  @Input() prenom: string = '';
  @Input() status: 'online' | 'away' | 'offline' = 'offline';
  initials: string = '';
  statusTooltip: string = '';
  dynamicBackground: string = '';

  ngOnInit(): void {
    if (this.statusColor === 'bg-red-500') {
      this.statusTooltip = 'Praticien parti du CHRU';
    } else if (this.statusColor === 'bg-green-500') {
      this.statusTooltip = 'Praticien encore au CHRU';
    } else if (this.statusColor === 'bg-yellow-500') {
      this.statusTooltip = 'Statut indéterminé pour le moment';
    } else {
      this.statusTooltip = 'Statut inconnu';
    }
  }
  ngOnChanges(): void {
    this.initials = this.getInitials();
    this.dynamicBackground = this.generateBackground();
  }

  getInitials(): string {
    const nomInitial = this.nom.charAt(0).toUpperCase();
    const prenomInitial = this.prenom.charAt(0).toUpperCase();
    return `${prenomInitial}${nomInitial}`;
  }

  generateBackground(): string {
    const colors = [
      '#FF6F61', // Coral
      '#6B5B95', // Purple
      '#88B04B', // Green
      '#F7CAC9', // Pink
      '#92A8D1', // Light Blue
      '#955251', // Rose Brown
      '#B565A7', // Purple Orchid
    ];

    const color1 = colors[this.nom.charCodeAt(0) % colors.length];
    const color2 = colors[this.prenom.charCodeAt(0) % colors.length];

    return `linear-gradient(135deg, ${color1}, ${color2})`;
  }

  get statusColor(): string {
    switch (this.status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
      default:
        return 'bg-red-500';
    }
  }
  // Retourner la couleur du badge en fonction du statut
  getBadgeColor(): string {
    switch (this.statusColor) {
      case 'bg-green-500':
        return '#10B981'; // Vert
      case 'bg-red-500':
        return '#EF4444'; // Rouge
      case 'bg-yellow-500':
        return '#F59E0B'; // Jaune
      default:
        return '#6B7280'; // Gris
    }
  }
}
