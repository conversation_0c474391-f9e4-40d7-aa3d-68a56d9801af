import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HostListener} from '@angular/core';
import {ResumeProfilsComponent} from "../../../pages/resume-profils/resume-profils.component";
import {CoreModule} from "../../core.module";
import {OverviewComponent} from "../../../pages/overview/overview.component";
import {SidebarService} from "../../services/sidebar.service";
import {CalendarModule} from "primeng/calendar";
import {FormsModule} from "@angular/forms";
import {FloatLabelModule} from "primeng/floatlabel";
import {PrimeNGConfig} from "primeng/api";
import {Router, RouterLink, RouterLinkActive} from "@angular/router";
import {DatePipe, NgClass, NgIf} from "@angular/common";
import {GlobalFilterService} from "../../services/global-filter/global-filter.service";
import {DialogModule} from "primeng/dialog";
import {AuthService} from "../../services/auth/auth.service";
import {environment} from "../../../../environments/environment";
import {FeatureFlagService} from "../../services/feature-flag.service";
import {FeatureFlagDirective} from "../../directives/feature-flag.directive";

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [
    ResumeProfilsComponent,
    CoreModule,
    OverviewComponent,
    CalendarModule,
    FormsModule,
    FloatLabelModule,
    RouterLink,
    RouterLinkActive,
    NgClass,
    NgIf,
    DatePipe,
    DialogModule,
    FeatureFlagDirective
  ],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss'
})
export class SidebarComponent implements OnInit, OnDestroy {
  isSidebarOpen = false;
  isStructureMenuOpen = false;

  // Nouvelle propriété pour le collapse/expand
  isCollapsed = false;
  private readonly SIDEBAR_COLLAPSED_KEY = 'sidebar_collapsed';
  private isInitialized = false;
  private currentBreakpoint = '';
  private mediaChangeTimeout: any;

  // For debugging
  console = console;

  dateDebut: Date | null = null;
  dateFin: Date | null = null;
  minDate: Date = new Date(2020, 0, 1); // January 1, 2020
  maxDate: Date = new Date(); // Today

  // Dialog properties
  showDateErrorDialog: boolean = false;
  dateErrorMessage: string = '';




  constructor(
    private sidebarService: SidebarService,
    private primengConfig: PrimeNGConfig,
    public router: Router,
    private globalFilterService: GlobalFilterService,
    private authService: AuthService,
    public featureFlagService: FeatureFlagService
  ) {
  }
  ngOnInit(): void {
    // Ensure maxDate is always the current date
    this.maxDate = new Date();

    // Restaurer l'état collapsed depuis localStorage
    this.loadCollapsedStateWithMetadata();

    this.sidebarService.sidebarOpen$.subscribe(isOpen => {
      this.isSidebarOpen = isOpen;
      console.log('Sidebar state changed:', isOpen, 'isSidebarOpen:', this.isSidebarOpen);
    });

    // Initialize date range from global filter service or set default
    this.globalFilterService.getFilterState().subscribe(state => {
      console.log('Filter state in sidebar init:', state);
      console.log('State dateDebut:', state.dateDebut, 'type:', typeof state.dateDebut);
      console.log('State dateFin:', state.dateFin, 'type:', typeof state.dateFin);

      if (state.dateDebut && state.dateFin) {
        console.log('Using dates from filter state:', state.dateDebut, state.dateFin);
        // Ensure we're working with Date objects
        this.dateDebut = new Date(state.dateDebut);
        this.dateFin = new Date(state.dateFin);
        console.log('Converted dates:', this.dateDebut, this.dateFin);
      } else {
        console.log('No dates in filter state, setting defaults');
        const currentDate = new Date();
        // Set to first day of 6 months ago
        this.dateDebut = new Date(currentDate.getFullYear(), currentDate.getMonth() - 6, 1);
        // Set to last day of current month
        const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();
        this.dateFin = new Date(currentDate.getFullYear(), currentDate.getMonth(), lastDay);
        console.log('Default dates:', this.dateDebut, this.dateFin);

        // Set initial date range in global filter service
        this.globalFilterService.setDateRange(this.dateDebut, this.dateFin);
      }
    });

    this.translation();

    // Check if any structure-related route is active
    this.checkStructureRouteActive();

    // Auto-collapse sur mobile
    this.handleResponsiveCollapse();

    // Initialiser l'état dans le service avec setTimeout pour éviter les erreurs
    setTimeout(() => {
      this.sidebarService.setSidebarCollapsed(this.isCollapsed);
      this.isInitialized = true;
    }, 0);
  }

  ngOnDestroy(): void {
    // Nettoyer les timeouts
    if (this.mediaChangeTimeout) {
      clearTimeout(this.mediaChangeTimeout);
    }
  }

  /**
   * Handle date range change and update global filter
   */
  onDateRangeChange(): void {
    console.log('onDateRangeChange called, dateDebut:', this.dateDebut, 'dateFin:', this.dateFin);

    // Handle case where dateDebut or dateFin is not set
    if (!this.dateDebut || !this.dateFin) {
      console.log('dateDebut or dateFin is not set:', this.dateDebut, this.dateFin);
      return;
    }

    // Create new Date objects with the year and month from the selected dates
    // Set the day to 1 to ensure we're at the start of the month
    // This avoids timezone issues by explicitly setting the date components
    const dateDebutYear = this.dateDebut.getFullYear();
    const dateDebutMonth = this.dateDebut.getMonth();
    const dateDebut = new Date(dateDebutYear, dateDebutMonth, 1, 12, 0, 0);

    const dateFinYear = this.dateFin.getFullYear();
    const dateFinMonth = this.dateFin.getMonth();
    // For dateFin, we want the current day of the month if we're in the current month,
    // otherwise use the last day of the month
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    const currentDay = currentDate.getDate();

    let dateFinDay;
    if (dateFinYear === currentYear && dateFinMonth === currentMonth) {
      // If dateFin is the current month, use the current day
      dateFinDay = currentDay;
    } else {
      // Otherwise, use the last day of the month
      dateFinDay = new Date(dateFinYear, dateFinMonth + 1, 0).getDate();
    }

    const dateFin = new Date(dateFinYear, dateFinMonth, dateFinDay, 12, 0, 0);

    console.log('Adjusted dates for update:', dateDebut, dateFin);

    // Check if either date is in the future
    if (dateDebutYear > currentYear ||
        (dateDebutYear === currentYear && dateDebutMonth > currentMonth)) {
      console.log('Error: dateDebut is in the future');
      this.dateErrorMessage = 'La date de début ne peut pas être dans le futur.';
      this.showDateErrorDialog = true;
      return;
    }

    if (dateFinYear > currentYear ||
        (dateFinYear === currentYear && dateFinMonth > currentMonth)) {
      console.log('Error: dateFin is in the future');
      this.dateErrorMessage = 'La date de fin ne peut pas être dans le futur.';
      this.showDateErrorDialog = true;
      return;
    }

    // Check if dateDebut is before dateFin
    if (dateDebut >= dateFin) {
      console.log('Error: dateDebut must be before dateFin');
      this.dateErrorMessage = 'La date de début doit être antérieure à la date de fin.';
      this.showDateErrorDialog = true;
      return;
    }

    // Update the dateDebut and dateFin with the converted dates
    this.dateDebut = dateDebut;
    this.dateFin = dateFin;
    console.log('Updated dateDebut and dateFin:', this.dateDebut, this.dateFin);

    // Update the global filter service
    this.globalFilterService.setDateRange(dateDebut, dateFin);
    console.log('Global filter service updated with dates:', dateDebut, dateFin);
  }


  toggleSidebar() {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  closeSidebar() {
    this.sidebarService.closeSidebar();
  }

  toggleStructureMenu() {
    this.isStructureMenuOpen = !this.isStructureMenuOpen;
  }

  /**
   * Check if any structure-related route is active and expand the Structure menu accordingly
   */
  checkStructureRouteActive() {
    const structureRoutes = [
      '/structure/pole-list-list',
      '/structure/cr-list',
      '/structure/service-list',
      '/structure/uf-list'
    ];

    // If any structure route is active, open the structure menu
    this.isStructureMenuOpen = structureRoutes.some(route => this.router.isActive(route, true));
  }

  translation(){
    this.primengConfig.setTranslation({
      accept: 'Accepter',
      reject: 'Annuler',
      dayNames: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'],
      dayNamesShort: ['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'],
      dayNamesMin: ['D', 'L', 'M', 'M', 'J', 'V', 'S'],
      monthNames: [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
      ],
      monthNamesShort: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
      today: 'Aujourd\'hui',
      clear: 'Effacer',
      dateFormat: 'mm/yy',
      weekHeader: 'Sem'
    });
  }

  /**
   * Check if the current user has admin roles
   * @returns true if the user has any of the admin roles, false otherwise
   */
  hasAdminRole(): boolean {
    return this.authService.hasAdminRole();
  }

  /**
   * Get the admin dashboard URL from environment
   * @returns the admin dashboard URL
   */
  getAdminDashboardUrl(): string {
    return this.authService.getAdminDashboardUrl();
  }

  /**
   * Toggle sidebar collapse/expand state
   */
  toggleCollapse(): void {
    this.isCollapsed = !this.isCollapsed;
    this.saveCollapsedStateWithMetadata();

    // Communiquer l'état au service pour que l'app component puisse s'adapter
    this.sidebarService.setSidebarCollapsed(this.isCollapsed);

    // Fermer le menu Structure si on collapse
    if (this.isCollapsed) {
      this.isStructureMenuOpen = false;
    }

    // Ajouter une classe CSS pour les animations
    const sidebarElement = document.querySelector('.sidebar-container');
    if (sidebarElement) {
      sidebarElement.classList.add('transitioning');
      setTimeout(() => {
        sidebarElement.classList.remove('transitioning');
      }, 300); // Durée de l'animation
    }

    console.log('Sidebar collapsed state:', this.isCollapsed);
  }

  /**
   * Sauvegarder l'état collapsed dans localStorage
   */
  private saveCollapsedState(): void {
    try {
      localStorage.setItem(this.SIDEBAR_COLLAPSED_KEY, JSON.stringify(this.isCollapsed));
    } catch (error) {
      console.warn('Impossible de sauvegarder l\'état de la sidebar:', error);
    }
  }

  /**
   * Charger l'état collapsed depuis localStorage
   */
  private loadCollapsedState(): void {
    try {
      const saved = localStorage.getItem(this.SIDEBAR_COLLAPSED_KEY);
      if (saved !== null) {
        this.isCollapsed = JSON.parse(saved);
        console.log('État collapsed restauré:', this.isCollapsed);
      }
    } catch (error) {
      console.warn('Impossible de charger l\'état de la sidebar:', error);
      this.isCollapsed = false;
    }
  }

  /**
   * Gérer le collapse automatique sur mobile/tablette
   */
  private handleResponsiveCollapse(): void {
    const updateBreakpoint = () => {
      // Déterminer le breakpoint actuel
      let newBreakpoint = '';
      if (window.innerWidth <= 767) {
        newBreakpoint = 'mobile';
      } else if (window.innerWidth <= 1023) {
        newBreakpoint = 'tablet';
      } else {
        newBreakpoint = 'desktop';
      }

      // Ne rien faire si le breakpoint n'a pas changé
      if (this.currentBreakpoint === newBreakpoint) {
        return;
      }

      console.log(`Breakpoint changed: ${this.currentBreakpoint} → ${newBreakpoint}`);
      this.currentBreakpoint = newBreakpoint;

      // Annuler le timeout précédent
      if (this.mediaChangeTimeout) {
        clearTimeout(this.mediaChangeTimeout);
      }

      // Appliquer les changements avec un délai
      this.mediaChangeTimeout = setTimeout(() => {
        if (!this.isInitialized) {
          return;
        }

        switch (newBreakpoint) {
          case 'mobile':
          case 'tablet':
            // Mobile/Tablette : forcer collapsed
            if (!this.isCollapsed) {
              this.isCollapsed = true;
              this.sidebarService.setSidebarCollapsed(this.isCollapsed);
              console.log(`Mode ${newBreakpoint} : sidebar collapsed`);
            }
            break;

          case 'desktop':
            // Desktop : restaurer la préférence utilisateur
            const savedState = this.getSavedCollapsedState();
            const newCollapsedState = savedState !== null ? savedState : false;

            if (this.isCollapsed !== newCollapsedState) {
              this.isCollapsed = newCollapsedState;
              this.sidebarService.setSidebarCollapsed(this.isCollapsed);
              console.log('Mode desktop : préférence restaurée:', this.isCollapsed);
            }

            // Fermer le menu mobile si ouvert
            if (this.isSidebarOpen) {
              this.closeSidebar();
            }
            break;
        }
      }, 100); // Délai pour éviter les appels multiples
    };

    // Vérifier l'état initial
    updateBreakpoint();

    // Écouter les changements de taille de fenêtre
    window.addEventListener('resize', updateBreakpoint);
  }

  /**
   * Obtenir l'état sauvegardé sans le charger automatiquement
   */
  private getSavedCollapsedState(): boolean | null {
    try {
      const saved = localStorage.getItem(this.SIDEBAR_COLLAPSED_KEY);
      return saved !== null ? JSON.parse(saved) : null;
    } catch (error) {
      console.warn('Impossible de lire l\'état sauvegardé:', error);
      return null;
    }
  }

  /**
   * Sauvegarder l'état avec métadonnées
   */
  private saveCollapsedStateWithMetadata(): void {
    try {
      const stateData = {
        isCollapsed: this.isCollapsed,
        timestamp: Date.now(),
        userAgent: navigator.userAgent.substring(0, 100) // Limiter la taille
      };
      localStorage.setItem(this.SIDEBAR_COLLAPSED_KEY, JSON.stringify(stateData));
      console.log('État sidebar sauvegardé avec métadonnées');
    } catch (error) {
      console.warn('Impossible de sauvegarder l\'état avec métadonnées:', error);
      // Fallback vers la sauvegarde simple
      this.saveCollapsedState();
    }
  }

  /**
   * Charger l'état avec gestion des métadonnées
   */
  private loadCollapsedStateWithMetadata(): void {
    try {
      const saved = localStorage.getItem(this.SIDEBAR_COLLAPSED_KEY);
      if (saved !== null) {
        const parsed = JSON.parse(saved);

        // Vérifier si c'est le nouveau format avec métadonnées
        if (typeof parsed === 'object' && parsed.hasOwnProperty('isCollapsed')) {
          // Nouveau format
          this.isCollapsed = parsed.isCollapsed;

          // Vérifier l'âge de la préférence (expirer après 30 jours)
          const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
          if (parsed.timestamp && parsed.timestamp < thirtyDaysAgo) {
            console.log('Préférence expirée, utilisation des valeurs par défaut');
            this.isCollapsed = false;
            localStorage.removeItem(this.SIDEBAR_COLLAPSED_KEY);
          } else {
            console.log('État collapsed restauré avec métadonnées:', this.isCollapsed);
          }
        } else {
          // Ancien format (boolean simple)
          this.isCollapsed = parsed;
          console.log('État collapsed restauré (ancien format):', this.isCollapsed);
          // Migrer vers le nouveau format
          this.saveCollapsedStateWithMetadata();
        }
      }
    } catch (error) {
      console.warn('Impossible de charger l\'état avec métadonnées:', error);
      this.isCollapsed = false;
    }
  }

  /**
   * Support clavier pour l'accessibilité
   */
  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    // Ctrl + B ou Ctrl + Shift + B pour toggle la sidebar
    if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
      event.preventDefault();
      this.toggleCollapse();
      this.announceStateChange();
    }

    // Échap pour fermer les menus ouverts
    if (event.key === 'Escape') {
      if (this.isStructureMenuOpen) {
        this.isStructureMenuOpen = false;
        this.announceMenuClosed();
      }
    }

    // Flèches pour navigation dans les menus
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
      this.handleArrowNavigation(event);
    }
  }

  /**
   * Gérer la navigation au clavier avec les flèches
   */
  private handleArrowNavigation(event: KeyboardEvent): void {
    const focusableElements = document.querySelectorAll(
      '.sidebar-container a[href], .sidebar-container button:not([disabled])'
    );

    const currentIndex = Array.from(focusableElements).indexOf(document.activeElement as Element);

    if (currentIndex !== -1) {
      event.preventDefault();
      let nextIndex: number;

      if (event.key === 'ArrowDown') {
        nextIndex = (currentIndex + 1) % focusableElements.length;
      } else {
        nextIndex = currentIndex === 0 ? focusableElements.length - 1 : currentIndex - 1;
      }

      (focusableElements[nextIndex] as HTMLElement).focus();
    }
  }

  /**
   * Annoncer les changements d'état pour les lecteurs d'écran
   */
  private announceStateChange(): void {
    const message = this.isCollapsed ?
      'Sidebar réduite' :
      'Sidebar développée';

    this.announceToScreenReader(message);
  }

  /**
   * Annoncer la fermeture de menu
   */
  private announceMenuClosed(): void {
    this.announceToScreenReader('Menu fermé');
  }

  /**
   * Annoncer un message aux lecteurs d'écran
   */
  private announceToScreenReader(message: string): void {
    // Créer un élément temporaire pour l'annonce
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Supprimer après l'annonce
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }

  /**
   * Obtenir le texte d'aide pour l'accessibilité
   */
  getAccessibilityHelp(): string {
    return `Navigation principale. ${this.isCollapsed ? 'Sidebar réduite' : 'Sidebar développée'}.
            Utilisez Ctrl+B pour basculer l'affichage.
            Utilisez les flèches haut/bas pour naviguer entre les éléments.`;
  }

  /**
   * Obtenir l'état ARIA pour les éléments de menu
   */
  getMenuAriaExpanded(menuName: string): string {
    switch (menuName) {
      case 'structure':
        return this.isStructureMenuOpen.toString();
      default:
        return 'false';
    }
  }

  /**
   * Obtenir l'ID ARIA pour les sous-menus
   */
  getSubmenuAriaId(menuName: string): string {
    return `submenu-${menuName}`;
  }
}
