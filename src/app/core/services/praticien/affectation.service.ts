import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay, tap } from 'rxjs/operators';
import { BaseStructureService, PaginatedResult } from '../structure/base-structure.service';
import { AffectationModel } from '../../models/praticien/AffectationModel';
import { environment } from '../../../../environments/environment';

interface AffectationResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': AffectationModel[];
  'view': {
    '@id': string;
    '@type': string;
    'first': string;
    'last': string;
    'next': string;
  };
  'search': any;
}

@Injectable({
  providedIn: 'root'
})
export class AffectationService extends BaseStructureService<AffectationModel, AffectationResponse> {

  constructor(http: HttpClient) {
    super(http, '/api/affectations');
  }

  /**
   * Extract AffectationModel array and total count from API response
   * @param response API response
   * @returns PaginatedResult containing AffectationModel array and total count
   */
  protected extractData(response: AffectationResponse): PaginatedResult<AffectationModel> {
    return {
      items: response.member,
      totalItems: response.totalItems
    };
  }

  /**
   * Get active affectations with optional filters
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @param filters Additional filters (optional)
   * @returns Observable of PaginatedResult containing active AffectationModel array and total count
   */
  getActiveAffectations(page?: number, limit?: number, filters?: any): Observable<PaginatedResult<AffectationModel>> {
    let params = new HttpParams();
    params = params.set('isActif', 'true'); // Filtrer seulement les affectations actives

    if (page) params = params.set('page', page.toString());
    if (limit) params = params.set('itemsPerPage', limit.toString());

    // Add search filters
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
          params = params.set(key, filters[key]);
        }
      });
    }

    return this.http.get<AffectationResponse>(
      `${environment.api_url}${this.apiPath}`,
      { params }
    ).pipe(
      map(response => this.extractData(response)),
      tap(result => {
        console.log(`🏥 Loaded ${result.items.length} active affectations`);
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Get affectations for a specific agent by hrUser
   * @param hrUser Agent HR User (like "HR072398")
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @returns Observable of PaginatedResult containing AffectationModel array and total count
   */
  getAffectationsByAgentHrUser(hrUser: string, page?: number, limit?: number): Observable<PaginatedResult<AffectationModel>> {
    const filters = {
      'agent.hrUser': hrUser
    };

    console.log(`🔍 Searching affectations for agent.hrUser: ${hrUser}`);

    return this.getActiveAffectations(page, limit, filters);
  }

  /**
   * Get affectations for a specific agent by hrUser with additional filters (including periods)
   * @param hrUser Agent HR User (like "HR072398")
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @param additionalFilters Additional filters including periods (p1Start, p1End, etc.)
   * @returns Observable of PaginatedResult containing AffectationModel array and total count
   */
  getAffectationsByAgentHrUserWithFilters(
    hrUser: string,
    page?: number,
    limit?: number,
    additionalFilters?: any
  ): Observable<PaginatedResult<AffectationModel>> {
    const filters = {
      'agent.hrUser': hrUser,
      ...additionalFilters
    };

    console.log(`🔍 Searching affectations for agent.hrUser: ${hrUser} with filters:`, additionalFilters);

    return this.getActiveAffectations(page, limit, filters);
  }



  /**
   * Get affectations for a specific agent (legacy method - kept for compatibility)
   * @param agentId Agent ID (should be the @id from API like "/api/agents/123")
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @returns Observable of PaginatedResult containing AffectationModel array and total count
   */
  getAffectationsByAgent(agentId: string, page?: number, limit?: number): Observable<PaginatedResult<AffectationModel>> {
    // Extract just the ID part if it's a full API path
    const cleanAgentId = agentId.includes('/api/agents/') ? agentId : `/api/agents/${agentId}`;

    const filters = {
      'agent': cleanAgentId
    };

    console.log(`🔍 Searching affectations for agent: ${cleanAgentId}`);

    return this.getActiveAffectations(page, limit, filters);
  }

  /**
   * Get affectations for a specific UF
   * @param ufId UF ID
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @returns Observable of PaginatedResult containing AffectationModel array and total count
   */
  getAffectationsByUf(ufId: string, page?: number, limit?: number): Observable<PaginatedResult<AffectationModel>> {
    const filters = {
      'ufs.id': ufId
    };

    return this.getActiveAffectations(page, limit, filters);
  }

  /**
   * Search affectations with text query
   * @param query Search query
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @returns Observable of PaginatedResult containing AffectationModel array and total count
   */
  searchAffectations(query: string, page?: number, limit?: number): Observable<PaginatedResult<AffectationModel>> {
    const filters = {
      'rgt': query,
      'typeGrade': query,
      'libelleGrade': query
    };

    return this.getActiveAffectations(page, limit, filters);
  }
}
