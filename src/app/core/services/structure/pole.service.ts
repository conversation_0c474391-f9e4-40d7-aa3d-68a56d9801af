import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BaseStructureService, PaginatedResult } from './base-structure.service';
import { PoleModel } from '../../models/structure/PoleModel';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

interface PoleResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': PoleModel[];
}

@Injectable({
  providedIn: 'root'
})
export class PoleService extends BaseStructureService<PoleModel, PoleResponse> {
  // BehaviorSubject to store all poles
  private allPoles = new BehaviorSubject<PoleModel[]>([]);

  // Observable to expose all poles
  readonly allPoles$ = this.allPoles.asObservable();

  // Flag to track if initial data has been loaded
  private dataLoaded = false;

  constructor(http: HttpClient) {
    super(http, '/api/poles');

    // Load all poles when service is initialized
    this.loadAllPoles();
  }

  /**
   * Extract PoleModel array and total count from API response
   * @param response API response
   * @returns PaginatedResult containing PoleModel array and total count
   */
  protected extractData(response: PoleResponse): PaginatedResult<PoleModel> {
    return {
      items: response.member,
      totalItems: response.totalItems
    };
  }

  /**
   * Load all poles from the API
   * This is called once when the service is initialized
   */
  private loadAllPoles(): void {
    // Only load if not already loaded
    if (this.dataLoaded) {
      return;
    }

    // Set a reasonable limit for the number of poles to fetch
    // Adjust this based on your application's needs
    const params = new HttpParams()
      .set('itemsPerPage', '1000')
      .set('isActif', 'true');

    this.http.get<PoleResponse>(`${environment.api_url}/api/poles`, { params }).pipe(
      map(response => response.member || []),
      catchError(error => {
        console.error('Error loading all poles:', error);
        return of([]);
      })
    ).subscribe(poles => {
      this.allPoles.next(poles);
      this.dataLoaded = true;
      console.log(`Loaded ${poles.length} poles for client-side filtering`);
    });
  }

  /**
   * Filter poles based on search query
   * @param poles Array of poles to filter
   * @param query Search query
   * @param limit Maximum number of results to return
   * @returns Filtered array of poles
   */
  private filterPoles(poles: PoleModel[], query: string, limit: number): PoleModel[] {
    const lowerQuery = query.toLowerCase().trim();

    // Filter poles based on various properties
    return poles.filter(pole => {
      // Check each property that might contain the search term
      return (
        (pole.polecode && pole.polecode.toLowerCase().includes(lowerQuery)) ||
        (pole.libelle && pole.libelle.toLowerCase().includes(lowerQuery))
      );
    }).slice(0, limit); // Limit the number of results
  }

  /**
   * Get all poles (for autocomplete dropdowns)
   * @param limit Maximum number of results to return
   * @returns Observable of all poles
   */
  getAllPoles(limit: number = 1000): Observable<PoleModel[]> {
    // Ensure data is loaded
    if (!this.dataLoaded) {
      // If data isn't loaded yet, load it and then return all poles
      this.loadAllPoles();
      return this.allPoles$.pipe(
        map(poles => poles.slice(0, limit))
      );
    }

    // Return all poles
    return of(this.allPoles.getValue().slice(0, limit));
  }

  /**
   * Search for poles by code or name
   * @param query The search query
   * @param limit Maximum number of results to return
   * @returns Observable of pole search results
   */
  searchPoles(query: string, limit: number = 10): Observable<PoleModel[]> {
    // If query is empty or too short, return all poles for dropdown
    if (!query || query.trim().length < 2) {
      return this.getAllPoles(limit);
    }

    // Ensure data is loaded
    if (!this.dataLoaded) {
      // If data isn't loaded yet, load it and then perform the search
      this.loadAllPoles();
      // Return the first set of filtered results when they're available
      return this.allPoles$.pipe(
        map(poles => this.filterPoles(poles, query, limit))
      );
    }

    // Filter poles client-side
    return of(this.filterPoles(this.allPoles.getValue(), query, limit));
  }

  /**
   * Refresh the poles data
   * This can be called to force a refresh of the data
   */
  refreshPoles(): void {
    this.dataLoaded = false;
    this.loadAllPoles();
  }
}
