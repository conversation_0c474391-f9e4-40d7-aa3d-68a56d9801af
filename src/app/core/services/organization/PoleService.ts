import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {BehaviorSubject, map, Observable} from 'rxjs';
import {Pole} from "../../models/organization/Pole.model";

@Injectable({
  providedIn: 'root'
})
export class PoleService {
  private polesSubject = new BehaviorSubject<Pole[]>([]);
  poles$: Observable<Pole[]> = this.polesSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadPoles();
  }

  private loadPoles() {
    this.http.get<Pole[]>('/assets/data/poles.json').subscribe((data) => {
      this.polesSubject.next(data);
    });
  }

  getPoleById(id: string): Observable<Pole | undefined> {
    return this.poles$.pipe(
      map((poles) => poles.find((pole) => pole.id === id))
    );
  }
}
