<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>



<div class="p-4">
  <!-- Indicateur de chargement -->
  <div *ngIf="isLoading" class="flex justify-center items-center h-40">
    <div class="text-gray-500">Chargement des données...</div>
  </div>

  <!-- Erreur de chargement -->
  <div *ngIf="hasError && !isLoading" class="text-red-500">
    Impossible de charger les données SIGAPS. Veuillez réessayer plus tard.
  </div>

  <!-- Contenu principal -->
  <div *ngIf="sigapsData && !isLoading && !hasError">
    <!-- Date de dernière actualisation -->
    <p class="text-gray-600 mb-4">
      Dernière actualisation :
      <span class="font-semibold">{{ sigapsData.lastUpdated | date: 'dd/MM/yyyy' }}</span>
    </p>
    <div *ngIf="sigapsData && !isLoading && !hasError">
      <div>
        <div class="relative mt-2 rounded-md shadow-sm">
          <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
          </div>
          <input
            type="text" name="price"
            (input)="scoreTab.filterGlobal(onInput($event), 'contains')"
            class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
            placeholder="  Filtrer par service,uf,auteur ..."
          />
        </div>
      </div>

      <p-table
        #scoreTab
        [value]="sigapsData?.data || []"
        [paginator]="true" [rows]="10"
        [globalFilterFields]="['authorName', 'service', 'uf']"
        [responsiveLayout]="'scroll'" [showCurrentPageReport]="true"
        currentPageReportTemplate="Affichage {first} à {last} sur {totalRecords}"
        [rowsPerPageOptions]="[5, 10, 20]"
      >
        <ng-template pTemplate="header">
          <tr>
            <th pSortableColumn="authorName">
              Nom de l'auteur
              <p-sortIcon field="authorName"></p-sortIcon>
            </th>
            <th pSortableColumn="service">
              Service
              <p-sortIcon field="service"></p-sortIcon>
            </th>
            <th pSortableColumn="uf">
              UF
              <p-sortIcon field="uf"></p-sortIcon>
            </th>
            <th pSortableColumn="totalPublications" class="text-center">
              Total publications
              <p-sortIcon field="totalPublications"></p-sortIcon>
            </th>
            <th pSortableColumn="totalSigapsScore" class="text-center">
              Score SIGAPS total
              <p-sortIcon field="totalSigapsScore"></p-sortIcon>
            </th>
            <th>
              Répartition par catégorie
            </th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-author>
          <tr>
            <td>{{ author.authorName }}</td>
            <td>{{ author.service }}</td>
            <td>{{ author.uf }}</td>
            <td class="text-center">{{ author.totalPublications }}</td>
            <td class="text-center">{{ author.totalSigapsScore }}</td>
            <td>
              A+: {{ author.categories.APlus || 0 }},
              A: {{ author.categories.A || 0 }},
              B: {{ author.categories.B || 0 }},
              C: {{ author.categories.C || 0 }},
              D: {{ author.categories.D || 0 }}
            </td>
          </tr>
        </ng-template>


      </p-table>

    </div>


  </div>
  <div class="p-grid p-mt-4">
    <!-- Section Titre -->
    <div class="p-col-12">
      <h3 class="text-lg font-bold text-gray-700 text-center">Répartition par Catégorie et par Service</h3>
    </div>

    <!-- Graphique en barres groupées -->
    <div class="p-col-12">
      <p-chart
        type="bar"
        [data]="groupedBarChartData"
        [options]="groupedBarChartOptions"
        pStyleClass="chart-container"
      ></p-chart>
    </div>
  </div>
</div>

