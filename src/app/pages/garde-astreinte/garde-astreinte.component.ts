import {Component, OnInit} from '@angular/core';
import {GardeAstreinteData, GardeAstreintePractitioner} from "../../core/models/garde-astreinte.model";
import {GardeAstreinteService} from "../../core/services/garde-astreinte.service";
import {TableModule} from "primeng/table";
import {DatePipe, NgIf} from "@angular/common";
import {ChartModule} from "primeng/chart";
import {AutoCompleteModule} from "primeng/autocomplete";
import {FormsModule} from "@angular/forms";
import {BreadcrumbComponent} from "../breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../core/models/breadcrumbItem";

@Component({
  selector: 'app-garde-astreinte',
  standalone: true,
    imports: [
        TableModule,
        DatePipe,
        ChartModule,
        AutoCompleteModule,
        FormsModule,
        NgIf,
        BreadcrumbComponent
    ],
  templateUrl: './garde-astreinte.component.html',
  styleUrl: './garde-astreinte.component.scss'
})
export class GardeAstreinteComponent implements OnInit{

  gardeAstreinteData: GardeAstreinteData | null = null; // Données de garde et astreinte
  isLoading = true; // Indique si les données sont en cours de chargement
  hasError = false; // Indique s'il y a eu une erreur lors du chargement

  barChartData: any; // Données pour le graphique en barres
  barChartOptions: any; // Options pour le graphique en barres

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Garde et Astreinte', url: '/garde-et-astreinte' },
    { label: 'Liste des Gardes et Astreintes par Praticien' }
  ];

  constructor(private gardeAstreinteService: GardeAstreinteService) {}

  ngOnInit(): void {
    this.loadGardeAstreinteData();
  }

  loadGardeAstreinteData(): void {
    this.gardeAstreinteService.getGardeAstreinteData().subscribe({
      next: (data) => {
        if (data) {
          this.gardeAstreinteData = data;
          this.initializeBarChart(); // Initialise le graphique
          this.initializeBarChartStacked();
          this.hasError = false;
        } else {
          this.hasError = true;
        }
        this.isLoading = false;
      },
      error: () => {
        this.hasError = true;
        this.isLoading = false;
      },
    });

    // Charger les données initiales si elles ne sont pas déjà chargées
    this.gardeAstreinteService.loadInitialData().subscribe({
      next: () => console.log('Données de garde et astreinte chargées avec succès.'),
      error: () => console.error('Erreur lors du chargement initial des données.'),
    });
  }

  initializeBarChart(): void {
    if (!this.gardeAstreinteData?.data) return;

    const labels = this.gardeAstreinteData.data.map((practitioner) => practitioner.praticienName);
    const datasets = [
      {
        label: 'Gardes',
        backgroundColor: '#42a5f5',
        data: this.gardeAstreinteData.data.map((practitioner) => {
          return practitioner.yearlyBreakdown.reduce((sum, year) => sum + year.onCalls, 0);
        }),
      },
      {
        label: 'Astreintes',
        backgroundColor: '#66bb6a',
        data: this.gardeAstreinteData.data.map((practitioner) => {
          return practitioner.yearlyBreakdown.reduce((sum, year) => sum + year.standbys, 0);
        }),
      },
    ];

    this.barChartData = {
      labels,
      datasets,
    };

    this.barChartOptions = {
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Praticiens',
          },
        },
        y: {
          title: {
            display: true,
            text: 'Nombre de Gardes et Astreintes',
          },
        },
      },
    };
  }

  initializeBarChartStacked(): void {
    if (!this.gardeAstreinteData?.data) return;

    const labels = this.gardeAstreinteData.data.map((practitioner) => practitioner.praticienName);
    const datasets = [
      {
        label: 'Gardes',
        backgroundColor: '#42a5f5',
        data: this.gardeAstreinteData.data.map((practitioner) => {
          return practitioner.yearlyBreakdown.reduce((sum, year) => sum + year.onCalls, 0);
        }),
      },
      {
        label: 'Astreintes',
        backgroundColor: '#66bb6a',
        data: this.gardeAstreinteData.data.map((practitioner) => {
          return practitioner.yearlyBreakdown.reduce((sum, year) => sum + year.standbys, 0);
        }),
      },
    ];

    this.barChartData = {
      labels,
      datasets,
    };

    this.barChartOptions = {
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
      },
      scales: {
        x: {
          stacked: true, // Activer l'empilement sur l'axe X
          title: {
            display: true,
            text: 'Praticiens',
          },
        },
        y: {
          stacked: true, // Activer l'empilement sur l'axe Y
          title: {
            display: true,
            text: 'Nombre de Gardes et Astreintes',
          },
        },
      },
    };
  }

  getTotalOnCalls(practitioner: GardeAstreintePractitioner): number {
    return practitioner.yearlyBreakdown.reduce((sum, year) => sum + year.onCalls, 0);
  }

  getTotalStandbys(practitioner: GardeAstreintePractitioner): number {
    return practitioner.yearlyBreakdown.reduce((sum, year) => sum + year.standbys, 0);
  }
  onInput(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }

  //**********************Comparaison des Gardes et Astreintes des 3 dernières années**************************/
  selectedPractitioner: GardeAstreintePractitioner | null = null;
  comparisonChartData: any;
  comparisonChartOptions: any;

  filteredPractitioners: GardeAstreintePractitioner[] = [];

  filterPractitioners(event: any): void {
    const query = event.query.toLowerCase();
    this.filteredPractitioners =
      this.gardeAstreinteData?.data?.filter((practitioner) =>
        practitioner.praticienName.toLowerCase().includes(query)
      ) || [];
  }


  initializeComparisonChart(): void {
    if (!this.gardeAstreinteData?.data || !this.selectedPractitioner) return;

    const selectedPractitionerData = this.gardeAstreinteData.data.find(
      (practitioner) => practitioner.praticienId === this.selectedPractitioner?.praticienId
    );

    if (!selectedPractitionerData) return;

    const years = selectedPractitionerData.yearlyBreakdown.map((yearData) => yearData.year);

    const datasets = [
      {
        label: `${selectedPractitionerData.praticienName} - Gardes`,
        data: selectedPractitionerData.yearlyBreakdown.map((yearData) => yearData.onCalls),
        borderColor: '#42a5f5',
        backgroundColor: 'rgba(66, 165, 245, 0.5)',
        fill: true,
      },
      {
        label: `${selectedPractitionerData.praticienName} - Astreintes`,
        data: selectedPractitionerData.yearlyBreakdown.map((yearData) => yearData.standbys),
        borderColor: '#66bb6a',
        backgroundColor: 'rgba(102, 187, 106, 0.5)',
        fill: true,
      },
      ...this.gardeAstreinteData.data
        .filter((practitioner) => practitioner.praticienId !== this.selectedPractitioner?.praticienId)
        .map((practitioner) => ({
          label: `${practitioner.praticienName} - Gardes`,
          data: practitioner.yearlyBreakdown.map((yearData) => yearData.onCalls),
          borderColor: '#ffa726',
          backgroundColor: 'rgba(255, 167, 38, 0.5)',
          fill: false,
          borderDash: [5, 5],
        })),
    ];

    this.comparisonChartData = {
      labels: years,
      datasets,
    };

    this.comparisonChartOptions = {
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Années',
          },
        },
        y: {
          title: {
            display: true,
            text: 'Nombre de Gardes et Astreintes',
          },
        },
      }
    }
  }


}
