<div class="relative z-[10003] flex-shrink-0 flex h-16 bg-white border-b border-gray-200 lg:border-none">
  <button
    (click)="toggleSidebar()"
    type="button"
    class="px-4 border-r border-gray-200 text-gray-400 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden"
  >
    <span class="sr-only">Open sidebar</span>
    <svg
      class="h-6 w-6"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      aria-hidden="true"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M4 6h16M4 12h8m-8 6h16"
      />
    </svg>
  </button>

  <div class="flex-1 px-4 flex items-center sm:px-6 lg:max-w-6xl lg:mx-auto lg:px-8">
    <!-- Zone de recherche ultra-pro - 75% de la largeur -->
    <div class="flex-1 max-w-none pr-6">
      <app-autocomplete-search
        placeholder="Rechercher un praticien, pôle, service..."
        [maxResults]="8"
        [showCategories]="true"
        [showStats]="true"
        (resultSelected)="onSearchResultSelected($event)"
        (searchChanged)="onSearchChanged($event)">
      </app-autocomplete-search>
    </div>

    <!-- Zone utilisateur - 25% de la largeur -->
    <div class="flex-shrink-0 flex items-center">
      <ng-container *ngIf="isAuthenticated(); else loginButton">
        <!-- Menu utilisateur -->
        <div class="ml-3 relative">
          <div>
            <button
              (click)="toggleDropdown()"
              type="button"
              class="max-w-xs bg-white rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 lg:p-2 lg:rounded-md lg:hover:bg-gray-50"
              aria-expanded="false"
              aria-haspopup="true"
            >
              <div class="h-7 w-8 mr-1">
                <div [innerHTML]="generateAvatar()"></div>
              </div>
              <span class="hidden ml-3 text-gray-700 text-sm font-medium lg:block">
                <span class="sr-only">Open user menu for </span>{{ getUserName() }}
                <span *ngIf="isEmailAuthUser()" class="ml-1 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                  Visiteur
                </span>
              </span>

              <svg
                class="hidden flex-shrink-0 ml-1 h-5 w-5 text-gray-400 lg:block"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
          <!-- Dropdown menu -->
          <div
            *ngIf="isDropdownOpen"
            class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
            role="menu"
            aria-orientation="vertical"
            aria-labelledby="user-menu-button"
            tabindex="-1"
          >
            <!-- Affichage de l'email de l'utilisateur -->
            <div *ngIf="getUserEmail()" class="px-4 py-2 text-xs text-gray-500 border-b border-gray-100">
              {{ getUserEmail() }}
              <span *ngIf="isEmailAuthUser()" class="ml-1 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                Visiteur
              </span>
            </div>
            <!-- Lien profil conditionnel selon le type d'utilisateur -->
            <ng-container *ngIf="!isEmailAuthUser(); else emailAuthUserProfile">
              <a routerLink="/agent-details" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1">Votre profil</a>
            </ng-container>
            <ng-template #emailAuthUserProfile>
              <div class="block px-4 py-2 text-sm text-gray-400 cursor-not-allowed" role="menuitem" tabindex="-1"
                   title="Veuillez utiliser la connexion Windows pour accéder à cette page">
                <span>Votre profil</span>
                <span class="ml-1 text-xs text-yellow-600">(Connexion Windows requise)</span>
              </div>
            </ng-template>
            <!-- Lien Administrateur -->
            <a
              *ngIf="hasAdminRole()"
              [href]="getAdminDashboardUrl()"
              target="_blank"
              class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              role="menuitem"
              tabindex="-1"
            >
              Administrateur
            </a>
            <a
              (click)="logout()"
              class="block px-4 py-2 text-sm text-gray-700 cursor-pointer"
              role="menuitem"
              tabindex="-1"
            >
              Déconnexion
            </a>
          </div>
        </div>
      </ng-container>

      <ng-template #loginButton>
        <!-- Bouton "Se connecter" -->
        <a
          routerLink="/auth/login"
          class="text-sm text-white px-4 py-2 bg-cyan-700 rounded hover:bg-cyan-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
        >
          Se connecter
        </a>
      </ng-template>
    </div>
  </div>
</div>

<!--// objectif affficher les message juste en bas du header-->
<!--<p-message *ngIf="message" severity="{{ message.severity }}" text="{{ message.text }}"></p-message>-->
