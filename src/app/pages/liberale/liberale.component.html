<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

<div class="p-4">
  <!-- Indicateur de chargement -->
  <div *ngIf="loading" class="flex justify-center items-center h-40">
    <div class="text-gray-500">Chargement des données...</div>
  </div>

  <!-- Erreur de chargement -->
  <div *ngIf="!loading && liberalData === null" class="text-red-500">
    Impossible de charger les données libérales. Veuillez réessayer plus tard.
  </div>

  <!-- Contenu principal -->
  <div *ngIf="liberalData && !loading">
    <!-- Date de dernière actualisation -->
    <p class="text-gray-600 mb-4">
      Dernière actualisation :
      <span class="font-semibold">{{ liberalData.lastUpdate | date: 'dd/MM/yyyy' }}</span>
    </p>

    <!-- Champ de recherche -->
    <div class="relative mt-2 rounded-md shadow-sm">
      <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
        <span class="text-gray-500 sm:text-sm mr-3">
          <i class="pi pi-search"></i>
        </span>
      </div>
      <input
        type="text"
        (input)="liberalTable.filterGlobal(onInput($event), 'contains')"
        class="block w-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm"
        placeholder="Filtrer par nom, service ou UF..."
      />
    </div>

    <!-- Tableau -->
    <p-table
      #liberalTable
      [value]="liberalData?.praticiens || []"
      [paginator]="true"
      [rows]="10"
      [globalFilterFields]="['nom', 'service', 'uf']"
      [responsiveLayout]="'scroll'"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="Affichage {first} à {last} sur {totalRecords}"
      [rowsPerPageOptions]="[5, 10, 20]"
    >
      <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="nom">
            Nom du Praticien
            <p-sortIcon field="nom"></p-sortIcon>
          </th>
          <th pSortableColumn="service">
            Service
            <p-sortIcon field="service"></p-sortIcon>
          </th>
          <th pSortableColumn="uf">
            UF
            <p-sortIcon field="uf"></p-sortIcon>
          </th>
          <th pSortableColumn="consultationsLiberales" class="text-center">
            Consultations Libérales
            <p-sortIcon field="consultationsLiberales"></p-sortIcon>
          </th>
          <th pSortableColumn="actesLiberaux" class="text-center">
            Actes Libéraux
            <p-sortIcon field="actesLiberaux"></p-sortIcon>
          </th>
          <th pSortableColumn="interventions" class="text-center">
            Interventions
            <p-sortIcon field="interventions"></p-sortIcon>
          </th>
          <th pSortableColumn="actesComplementaires" class="text-center">
            Actes Complémentaires
            <p-sortIcon field="actesComplementaires"></p-sortIcon>
          </th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-praticien>
        <tr>
          <td>{{ praticien.nom }}</td>
          <td>{{ praticien.service }}</td>
          <td>{{ praticien.uf }}</td>
          <td class="text-center">{{ praticien.consultationsLiberales }}</td>
          <td class="text-center">{{ praticien.actesLiberaux }}</td>
          <td class="text-center">{{ praticien.interventions }}</td>
          <td class="text-center">{{ praticien.actesComplementaires }}</td>
        </tr>
      </ng-template>
    </p-table>

    <!-- Graphique -->
    <div class="p-grid p-mt-4">
      <!-- Section Titre -->
      <div class="p-col-12">
        <h3 class="text-lg font-bold text-gray-700 text-center">
          Répartition des Activités Libérales par Praticien
        </h3>
      </div>

      <!-- Graphique en barres empilées -->
      <div class="p-col-12">
        <!--  Formule == (Nombre d'acitvites dans une categorie)/(total des activites pour ce praticien) -->
        <p-chart
          type="bar"
          [data]="stackedBarChartData"
          [options]="stackedBarChartOptions"
          pStyleClass="chart-container"
        ></p-chart>
      </div>
    </div>
  </div>
</div>
