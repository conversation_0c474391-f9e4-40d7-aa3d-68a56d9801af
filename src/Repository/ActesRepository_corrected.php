<?php

namespace App\Repository;

use App\Entity\Activite\Actes;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Repository pour les statistiques d'actes médicaux
 *
 * Contient les méthodes pour calculer les statistiques de répartition
 * des actes par UF et par Praticien sur différentes périodes.
 *
 * Utilise le champ 'nombre_de_realisation' pour sommer les réalisations
 * au lieu de simplement compter les lignes.
 *
 * Routes associées:
 * - GET /api/actes/{id}/uf-single-stats
 * - GET /api/actes/{id}/praticien-single-stats
 * - GET /api/actes/{id}/temporal-stats
 *
 * @extends ServiceEntityRepository<Actes>
 */
class ActesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Actes::class);
    }

    /**
     * Récupère les statistiques mensuelles pour un acte sur une période donnée
     *
     * @param string $acteCode Code de l'acte
     * @param string $start Date de début (format Y-m-d)
     * @param string $end Date de fin (format Y-m-d)
     * @return array Tableau associatif [year_month => count]
     */
    public function findMonthlyStatsByPeriod(string $acteCode, string $start, string $end): array
    {
        $sql = "
            SELECT 
                CONCAT(a.annee, '-', LPAD(a.mois, 2, '0')) AS year_month,
                SUM(a.nombre_de_realisation) AS count
            FROM actes a
            WHERE a.code = :code
                AND a.is_actif = true
                AND a.date_realisation BETWEEN :start AND :end
            GROUP BY year_month
            ORDER BY year_month ASC
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery([
            'code' => $acteCode,
            'start' => $start,
            'end' => $end
        ]);

        $stats = [];
        while ($row = $result->fetchAssociative()) {
            $stats[$row['year_month']] = (int) $row['count'];
        }

        return $stats;
    }

    /**
     * Somme les nombre_de_realisation pour un acte et une UF sur une période
     */
    public function sumRealisationsByActeAndUfForPeriod(
        string $acteCode,
        string $ufId,
        \DateTime $startDate,
        \DateTime $endDate
    ): int {
        $result = $this->createQueryBuilder('a')
            ->select('SUM(a.nombre_de_realisation)')
            ->where('a.code = :acteCode')
            ->andWhere('a.ufIntervention = :ufId')
            ->andWhere('a.date_realisation BETWEEN :startDate AND :endDate')
            ->andWhere('a.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->setParameter('ufId', $ufId)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (int) ($result ?? 0);
    }

    /**
     * Somme les nombre_de_realisation pour un acte et un Praticien sur une période
     */
    public function sumRealisationsByActeAndPraticienForPeriod(
        string $acteCode,
        string $praticienId,
        \DateTime $startDate,
        \DateTime $endDate
    ): int {
        $result = $this->createQueryBuilder('a')
            ->select('SUM(a.nombre_de_realisation)')
            ->where('a.code = :acteCode')
            ->andWhere('a.agent = :praticienId')
            ->andWhere('a.date_realisation BETWEEN :startDate AND :endDate')
            ->andWhere('a.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->setParameter('praticienId', $praticienId)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (int) ($result ?? 0);
    }

    /**
     * Récupère toutes les UF qui ont réalisé un acte spécifique
     */
    public function getUfsForActe(string $acteCode): array
    {
        return $this->createQueryBuilder('a')
            ->select('DISTINCT uf.id, uf.ufcode, uf.libelle')
            ->join('a.ufIntervention', 'uf')
            ->where('a.code = :acteCode')
            ->andWhere('a.isActif = true')
            ->andWhere('uf.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->getQuery()
            ->getResult();
    }

    /**
     * Récupère tous les Praticiens qui ont réalisé un acte spécifique
     */
    public function getPraticiensForActe(string $acteCode): array
    {
        return $this->createQueryBuilder('a')
            ->select('DISTINCT ag.id, ag.nom, ag.prenom, ag.titre')
            ->join('a.agent', 'ag')
            ->where('a.code = :acteCode')
            ->andWhere('a.isActif = true')
            ->andWhere('ag.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->getQuery()
            ->getResult();
    }

    /**
     * Calcule le total des réalisations pour un acte sur une période
     */
    public function getTotalRealisationsForActeInPeriod(
        string $acteCode,
        \DateTime $startDate,
        \DateTime $endDate
    ): int {
        $result = $this->createQueryBuilder('a')
            ->select('SUM(a.nombre_de_realisation)')
            ->where('a.code = :acteCode')
            ->andWhere('a.date_realisation BETWEEN :startDate AND :endDate')
            ->andWhere('a.isActif = true')
            ->setParameter('acteCode', $acteCode)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (int) ($result ?? 0);
    }
}
