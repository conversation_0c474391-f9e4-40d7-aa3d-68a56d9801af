import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HomeComponent } from './pages/home/<USER>';
import { NotFoundComponent } from './pages/not-found/not-found.component';
import {CoreModule} from "./core/core.module";
import {SharedModule} from "./shared/shared.module";
import {SidebarComponent} from "./core/components/sidebar/sidebar.component";
import {SidebarMobileComponent} from "./core/components/sidebar-mobile/sidebar-mobile.component";
import {OverviewComponent} from "./pages/overview/overview.component";
import {ResumeProfilsComponent} from "./pages/resume-profils/resume-profils.component";
import {BrowserAnimationsModule} from "@angular/platform-browser/animations";
import {FooterComponent} from "./core/components/footer/footer.component";
import { AuthInterceptor } from './core/interceptors/auth.interceptor';


@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    NotFoundComponent
  ],
    imports: [
        BrowserModule,
        AppRoutingModule,
        HttpClientModule,
        CoreModule,
        SharedModule,
        SidebarComponent,
        SidebarMobileComponent,
        OverviewComponent,
        ResumeProfilsComponent,
        BrowserAnimationsModule,
        FooterComponent,
// meme si sharedModule a ete importe dans CoreModule cela ne va pas double le taille du fichier
    ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    }
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
