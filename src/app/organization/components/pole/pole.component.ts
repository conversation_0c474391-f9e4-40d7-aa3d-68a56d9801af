import {Component, OnInit} from '@angular/core';
import {PoleService} from "../../../core/services/organization/PoleService";
import {Pole} from "../../../core/models/organization/Pole.model";
import {NgForOf} from "@angular/common";

@Component({
  selector: 'app-pole-list',
  standalone: true,
  imports: [
    NgForOf
  ],
  templateUrl: './pole.component.html',
  styleUrl: './pole.component.scss'
})
export class PoleComponent implements OnInit{
  poles: Pole[] = [];

  constructor(private poleService: PoleService) {}

  ngOnInit(): void {
    this.poleService.poles$.subscribe((data) => {
      this.poles = data;
    });
  }

}
