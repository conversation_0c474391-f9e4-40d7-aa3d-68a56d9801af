// <PERSON><PERSON><PERSON> Subheader Component Styles

.subheader-container {
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

// Ensure autocomplete dropdowns appear above other elements
:host ::ng-deep {
  .p-autocomplete-panel {
    z-index: 10050 !important;
  }

  .p-dropdown-panel {
    z-index: 10050 !important;
  }

  .p-multiselect-panel {
    z-index: 10050 !important;
  }

  .p-calendar-panel {
    z-index: 10050 !important;
  }

  .p-overlay {
    z-index: 10050 !important;
  }

  .p-tooltip {
    z-index: 10051 !important;
  }
}

// Filter input styling
.filter-input {
  transition: all 0.2s ease-in-out;

  &:focus {
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
  }
}

// Dropdown customizations
::ng-deep .p-dropdown {
  .p-dropdown-trigger {
    background-color: transparent;
  }

  .p-dropdown-label {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  &.p-focus {
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
  }
}

// Button styling
.filter-button {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// Search button styling
.search-button {
  transition: all 0.15s ease-in-out;

  &:hover {
    background-color: #0891b2;
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.clear-button {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
  }
}

// Active filters indicator
.active-filters-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

// Active filters badges
.filter-badge {
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: #0891b2;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .remove-filter-btn {
    transition: all 0.15s ease-in-out;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
      transform: scale(1.1);
    }
  }
}

.filter-badges-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Advanced filters section
.advanced-filters {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .subheader-container {
    padding: 0.75rem;
  }

  .filter-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .filter-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}

@media (max-width: 640px) {
  .main-filters {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-input-group {
    min-width: 100%;
  }
}
