-- Insertion dans `tenant`
INSERT INTO tenant (id, nom, description)
VALUES
    (gen_random_uuid(), 'GHT Nancy', 'Groupement Hospitalier de Territoire de Nancy');

-- Récupérer l'ID du tenant
SELECT id INTO TEMP tenant_id FROM tenant WHERE nom = 'GHT Nancy';

-- Insertion dans `hopital`
INSERT INTO hopital (id, nom, adresse, telephone, email, tenant_id)
VALUES
    (gen_random_uuid(), 'CHRU de Nancy', '1 Rue du CHRU, Nancy', '0329123456', '<EMAIL>', (SELECT * FROM tenant_id));

-- R<PERSON>cupérer l'ID de l'hôpital
SELECT id INTO TEMP hopital_id FROM hopital WHERE nom = 'CHRU de Nancy';

-- Insertion dans `poles`
INSERT INTO poles (id, nom, hopital_id)
VALUES
    (gen_random_uuid(), 'Pôle Cardiologie', (SELECT * FROM hopital_id)),
    (gen_random_uuid(), 'Pôle Neurologie', (SELECT * FROM hopital_id));

-- Récupérer l'ID du pôle Cardiologie
SELECT id INTO TEMP pole_id FROM poles WHERE nom = 'Pôle Cardiologie';

-- Insertion dans `services`
INSERT INTO services (id, nom, pole_id)
VALUES
    (gen_random_uuid(), 'Service Cardiologie 1', (SELECT * FROM pole_id)),
    (gen_random_uuid(), 'Service Cardiologie 2', (SELECT * FROM pole_id));

-- Récupérer l'ID du service Cardiologie 1
SELECT id INTO TEMP service_id FROM services WHERE nom = 'Service Cardiologie 1';

-- Insertion dans `ufs`
INSERT INTO ufs (id, nom, service_id)
VALUES
    (gen_random_uuid(), 'UF Cardiologie Interventionnelle', (SELECT * FROM service_id)),
    (gen_random_uuid(), 'UF Rythmologie', (SELECT * FROM service_id));

-- Récupérer l'ID de l'UF Cardiologie Interventionnelle
SELECT id INTO TEMP uf_id FROM ufs WHERE nom = 'UF Cardiologie Interventionnelle';

-- Insertion dans `praticiens`
INSERT INTO praticiens (id, nom, prenom, specialite, matricule, service_id)
VALUES
    (gen_random_uuid(), 'Dubois', 'Jean', 'Cardiologue', 'M12345', (SELECT * FROM service_id)),
    (gen_random_uuid(), 'Martin', 'Sophie', 'Cardiologue', 'M67890', (SELECT * FROM service_id));

-- Récupérer l'ID du praticien Jean Dubois
SELECT id INTO TEMP praticien_id FROM praticiens WHERE matricule = 'M12345';

-- Insertion dans `actes`
INSERT INTO actes (id, code, description, categorie, type_acte, date_realisation, uf_id, praticien_id)
VALUES
    (gen_random_uuid(), 'CCAM001', 'Pose de stent', 'Interventionnelle', 'CCAM', '2025-01-15', (SELECT * FROM uf_id), (SELECT * FROM praticien_id)),
    (gen_random_uuid(), 'CCAM002', 'IRM cardiaque', 'Imagerie', 'CCAM', '2025-01-16', (SELECT * FROM uf_id), (SELECT * FROM praticien_id));

-- Insertion dans `enseignements`
INSERT INTO enseignements (id, praticien_id, annee, nombre_heures, type_enseignement)
VALUES
    (gen_random_uuid(), (SELECT * FROM praticien_id), 2025, 40, 'Cours magistral');

-- Insertion dans `gardes_astreintes`
INSERT INTO gardes_astreintes (id, praticien_id, date_garde, type_garde, duree_heure)
VALUES
    (gen_random_uuid(), (SELECT * FROM praticien_id), '2025-01-20', 'Astreinte', 12);

-- Insertion dans `liberal`
INSERT INTO liberal (id, praticien_id, date_activite, montant_honoraires, type_activite)
VALUES
    (gen_random_uuid(), (SELECT * FROM praticien_id), '2025-01-10', 350.00, 'Consultation');

-- Insertion dans `sigaps`
INSERT INTO sigaps (id, praticien_id, annee, categorie, nombre_publications)
VALUES
    (gen_random_uuid(), (SELECT * FROM praticien_id), 2025, 'A', 10);


SELECT * from tenant;
SELECT * from hopital;
SELECT * from praticiens;

-------------------------------------  INSERTION TENANT 2 --------------------------------------

-- Insertion dans `tenant`
INSERT INTO tenant (id, nom, description)
VALUES
    (gen_random_uuid(), 'GHT Strasbourg', 'Groupement Hospitalier de Territoire de Strasbourg');

-- Récupérer l'ID du nouveau tenant
SELECT id INTO TEMP new_tenant_id FROM tenant WHERE nom = 'GHT Strasbourg';

-- Insertion dans `hopital`
INSERT INTO hopital (id, nom, adresse, telephone, email, tenant_id)
VALUES
    (gen_random_uuid(), 'CHU de Strasbourg', '1 Rue de l Hôpital, Strasbourg', '0388123456', '<EMAIL>', (SELECT * FROM new_tenant_id));

-- Récupérer l'ID de l'hôpital
SELECT id INTO TEMP new_hopital_id FROM hopital WHERE nom = 'CHU de Strasbourg';

-- Insertion dans `poles`
INSERT INTO poles (id, nom, hopital_id)
VALUES
    (gen_random_uuid(), 'Pôle Pédiatrie', (SELECT * FROM new_hopital_id)),
    (gen_random_uuid(), 'Pôle Oncologie', (SELECT * FROM new_hopital_id));

-- Récupérer l'ID du pôle Pédiatrie
SELECT id INTO TEMP new_pole_id FROM poles WHERE nom = 'Pôle Pédiatrie';

-- Insertion dans `services`
INSERT INTO services (id, nom, pole_id)
VALUES
    (gen_random_uuid(), 'Service Pédiatrie Générale', (SELECT * FROM new_pole_id)),
    (gen_random_uuid(), 'Service Néonatologie', (SELECT * FROM new_pole_id));

-- Récupérer l'ID du service Pédiatrie Générale
SELECT id INTO TEMP new_service_id FROM services WHERE nom = 'Service Pédiatrie Générale';

-- Insertion dans `ufs`
INSERT INTO ufs (id, nom, service_id)
VALUES
    (gen_random_uuid(), 'UF Soins Intensifs Pédiatriques', (SELECT * FROM new_service_id)),
    (gen_random_uuid(), 'UF Pédiatrie Ambulatoire', (SELECT * FROM new_service_id));

-- Récupérer l'ID de l'UF Soins Intensifs Pédiatriques
SELECT id INTO TEMP new_uf_id FROM ufs WHERE nom = 'UF Soins Intensifs Pédiatriques';

-- Insertion dans `praticiens`
INSERT INTO praticiens (id, nom, prenom, specialite, matricule, service_id)
VALUES
    (gen_random_uuid(), 'Lefèvre', 'Lucie', 'Pédiatre', 'M98765', (SELECT * FROM new_service_id)),
    (gen_random_uuid(), 'Girard', 'Thomas', 'Pédiatre', 'M54321', (SELECT * FROM new_service_id));

-- Récupérer l'ID du praticien Lucie Lefèvre
SELECT id INTO TEMP new_praticien_id FROM praticiens WHERE matricule = 'M98765';

-- Insertion dans `actes`
INSERT INTO actes (id, code, description, categorie, type_acte, date_realisation, uf_id, praticien_id)
VALUES
    (gen_random_uuid(), 'PED001', 'Consultation pédiatrique', 'Consultation', 'CCAM', '2025-01-15', (SELECT * FROM new_uf_id), (SELECT * FROM new_praticien_id)),
    (gen_random_uuid(), 'PED002', 'Vaccination', 'Prévention', 'CCAM', '2025-01-16', (SELECT * FROM new_uf_id), (SELECT * FROM new_praticien_id));

-- Insertion dans `enseignements`
INSERT INTO enseignements (id, praticien_id, annee, nombre_heures, type_enseignement)
VALUES
    (gen_random_uuid(), (SELECT * FROM new_praticien_id), 2025, 30, 'Atelier pratique');

-- Insertion dans `gardes_astreintes`
INSERT INTO gardes_astreintes (id, praticien_id, date_garde, type_garde, duree_heure)
VALUES
    (gen_random_uuid(), (SELECT * FROM new_praticien_id), '2025-01-20', 'Garde', 24);

-- Insertion dans `liberal`
INSERT INTO liberal (id, praticien_id, date_activite, montant_honoraires, type_activite)
VALUES
    (gen_random_uuid(), (SELECT * FROM new_praticien_id), '2025-01-10', 250.00, 'Consultation');

-- Insertion dans `sigaps`
INSERT INTO sigaps (id, praticien_id, annee, categorie, nombre_publications)
VALUES
    (gen_random_uuid(), (SELECT * FROM new_praticien_id), 2025, 'B', 5);
