import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { BaseStructureService, PaginatedResult } from './base-structure.service';
import { ServiceModel } from '../../models/structure/ServiceModel';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

interface ServiceResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': ServiceModel[];
}

@Injectable({
  providedIn: 'root'
})
export class ServiceStructureService extends BaseStructureService<ServiceModel, ServiceResponse> {
  // BehaviorSubject to store all services
  private allServices = new BehaviorSubject<ServiceModel[]>([]);

  // Observable to expose all services
  readonly allServices$ = this.allServices.asObservable();

  // Flag to track if initial data has been loaded
  private dataLoaded = false;

  constructor(http: HttpClient) {
    super(http, '/api/services');

    // Load all services when service is initialized
    this.loadAllServices();
  }

  /**
   * Extract ServiceModel array and total count from API response
   * @param response API response
   * @returns PaginatedResult containing ServiceModel array and total count
   */
  protected extractData(response: ServiceResponse): PaginatedResult<ServiceModel> {
    return {
      items: response.member,
      totalItems: response.totalItems
    };
  }

  /**
   * Load all services from the API
   * This is called once when the service is initialized
   */
  private loadAllServices(): void {
    // Only load if not already loaded
    if (this.dataLoaded) {
      return;
    }

    // Set a reasonable limit for the number of services to fetch
    // Adjust this based on your application's needs
    const params = new HttpParams()
      .set('itemsPerPage', '1000')
      .set('isActif', 'true');

    this.http.get<ServiceResponse>(`${environment.api_url}/api/services`, { params }).pipe(
      map(response => response.member || []),
      catchError(error => {
        console.error('Error loading all services:', error);
        return of([]);
      })
    ).subscribe(services => {
      this.allServices.next(services);
      this.dataLoaded = true;
      console.log(`Loaded ${services.length} services for client-side filtering`);
    });
  }

  /**
   * Filter services based on search query
   * @param services Array of services to filter
   * @param query Search query
   * @param limit Maximum number of results to return
   * @returns Filtered array of services
   */
  private filterServices(services: ServiceModel[], query: string, limit: number): ServiceModel[] {
    const lowerQuery = query.toLowerCase().trim();

    // Filter services based on various properties
    return services.filter(service => {
      // Check each property that might contain the search term
      return (
        (service.secode && service.secode.toLowerCase().includes(lowerQuery)) ||
        (service.libelle && service.libelle.toLowerCase().includes(lowerQuery))
      );
    }).slice(0, limit); // Limit the number of results
  }

  /**
   * Get all services (for autocomplete dropdowns)
   * @param limit Maximum number of results to return
   * @returns Observable of all services
   */
  getAllServices(limit: number = 1000): Observable<ServiceModel[]> {
    // Ensure data is loaded
    if (!this.dataLoaded) {
      // If data isn't loaded yet, load it and then return all services
      this.loadAllServices();
      return this.allServices$.pipe(
        map(services => services.slice(0, limit))
      );
    }

    // Return all services
    return of(this.allServices.getValue().slice(0, limit));
  }

  /**
   * Search for services by code or name
   * @param query The search query
   * @param limit Maximum number of results to return
   * @returns Observable of service search results
   */
  searchServices(query: string, limit: number = 10): Observable<ServiceModel[]> {
    // If query is empty or too short, return all services for dropdown
    if (!query || query.trim().length < 2) {
      return this.getAllServices(limit);
    }

    // Ensure data is loaded
    if (!this.dataLoaded) {
      // If data isn't loaded yet, load it and then perform the search
      this.loadAllServices();
      // Return the first set of filtered results when they're available
      return this.allServices$.pipe(
        map(services => this.filterServices(services, query, limit))
      );
    }

    // Filter services client-side
    return of(this.filterServices(this.allServices.getValue(), query, limit));
  }

  /**
   * Refresh the services data
   * This can be called to force a refresh of the data
   */
  refreshServices(): void {
    this.dataLoaded = false;
    this.loadAllServices();
  }
}
