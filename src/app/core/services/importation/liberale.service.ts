import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../auth/auth.service';
import { BaseImportService, ImportValidation } from './base-import.service';

export interface LiberaleImportResponse {
  success: boolean;
  processed: number;
  results?: any[];
  errors?: any[];
}

@Injectable({
  providedIn: 'root'
})
export class LiberaleService extends BaseImportService {

  constructor(
    protected override http: HttpClient,
    protected override authService: AuthService
  ) {
    super(http, authService);
  }

  /**
   * Construire l'URL d'import pour Libérale
   */
  protected buildImportUrl(): string {
    return `${environment.api_url}/api/liberal/batch`;
  }

  /**
   * Valider les données Libérale
   */
  protected validateData(data: any[]): ImportValidation {
    const errors: string[] = [];

    console.log('🔍 Validating Libérale data:', {
      dataCount: data.length,
      sampleData: data.slice(0, 2)
    });

    if (!data || data.length === 0) {
      errors.push('Aucune donnée à importer');
      return { isValid: false, errors };
    }

    // Validation basique des champs requis pour Libérale
    data.forEach((item, index) => {
      if (!item.dateRealisation) {
        errors.push(`Ligne ${index + 1}: dateRealisation requise`);
      }
      // if (!item.tarif && item.tarif !== 0) {
      //   errors.push(`Ligne ${index + 1}: tarif requis`);
      // }
      if (!item.typeActe) {
        errors.push(`Ligne ${index + 1}: typeActe requis`);
      }
      if (!item.agentHrU) {
        errors.push(`Ligne ${index + 1}: identifiant agent requis (agentHrU)`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
