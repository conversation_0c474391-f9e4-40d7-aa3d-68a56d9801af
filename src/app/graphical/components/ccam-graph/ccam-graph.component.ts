import {Component, OnInit} from '@angular/core';
import { Chart } from 'chart.js/auto';
import {OverviewComponent} from "../../../pages/overview/overview.component";
import {ResumeProfilsComponent} from "../../../pages/resume-profils/resume-profils.component";

@Component({
  selector: 'app-ccam-graph',
  standalone: true,
    imports: [
        OverviewComponent,
        ResumeProfilsComponent
    ],
  templateUrl: './ccam-graph.component.html',
  styleUrl: './ccam-graph.component.scss'
})
export class CcamGraphComponent implements OnInit{
  chart : any;
  ccamCombinedChart!: any;
  ccamLineChartByMonth!: any;
  ccamBarChartByDay!: any;

  constructor() {
  }

  ngOnInit():void {
    this.createChart();
    this.createCombinedChartCCAM();
    this.createCCAMLineChartByMonth();
    this.createCCAMBarChartByDay();
  }
  private createCCAMBarChartByDay(): void {
    this.ccamBarChartByDay = new Chart('ccamBarChartByDay', {
      type: 'bar',
      data: {
        labels: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'], // Jours de la semaine
        datasets: [
          {
            label: 'Nombre d\'actes CCAM 2023',
            data: [10, 15, 12, 14, 18, 20, 5], // Données fictives pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.7)', // Bleu
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1,
          },
          {
            label: 'Nombre d\'actes CCAM 2024',
            data: [12, 14, 13, 16, 19, 18, 6], // Données fictives pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.7)', // Rouge
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1,
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Nombre d\'actes CCAM'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Comparaison des actes CCAM par jours de la semaine (2023 vs 2024)'
          }
        }
      }
    });
  }

  private createCCAMLineChartByMonth(): void {
    this.ccamLineChartByMonth = new Chart('ccamLineChartByMonth', {
      type: 'line',
      data: {
        labels: ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
        datasets: [
          {
            label: 'Nombre d\'actes CCAM 2023',
            data: [100, 120, 130, 140, 150, 160, 170, 130, 140, 150, 160, 170], // Données fictives pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.2)', // Bleu
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2,
            fill: true,
            tension: 0.3, // Ajoute une légère courbe aux lignes
          },
          {
            label: 'Nombre d\'actes CCAM 2024',
            data: [90, 110, 140, 130, 160, 150, 180, 120, 130, 160, 170, 180], // Données fictives pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.2)', // Rouge
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 2,
            fill: true,
            tension: 0.3,
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Nombre d\'actes CCAM'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Comparaison des actes CCAM par mois (2023 vs 2024)'
          }
        }
      }
    });
  }

  createChart(): void {
    this.chart = new Chart('ccamChart', {
      type: 'bar', // Graphique en barres pour représenter le nombre d'actes médicaux
      data: {
        labels: ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin'], // Mois
        datasets: [
          {
            label: 'Actes Chirurgicaux',
            data: [120, 150, 180, 170, 200, 210], // Données fictives pour les actes chirurgicaux
            backgroundColor: 'rgba(75, 192, 192, 0.6)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1
          },
          {
            label: 'Actes Diagnostiques',
            data: [90, 100, 140, 130, 160, 180], // Données fictives pour les actes diagnostiques
            backgroundColor: 'rgba(255, 99, 132, 0.6)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1
          },
          {
            label: 'Actes Thérapeutiques',
            data: [60, 70, 80, 90, 100, 120], // Données fictives pour les actes thérapeutiques
            backgroundColor: 'rgba(153, 102, 255, 0.6)',
            borderColor: 'rgba(153, 102, 255, 1)',
            borderWidth: 1
          }
        ]
      },
      options: {
        scales: {
          y: {
            beginAtZero: true
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Actes Médicaux CCAM par Mois'
          }
        }
      }
    });
  }

  private createCombinedChartCCAM(): void {
    this.ccamCombinedChart = new Chart('ccamCombinedChart', {
      type: 'bar', // Commence avec un graphique en barres
      data: {
        labels: [
          'Acte chirurgical', 'Acte diagnostique', 'Acte thérapeutique',
          'Acte endoscopique', 'Acte radiologique', 'Acte anesthésique'
        ], // Types d'actes CCAM
        datasets: [
          {
            label: 'Nombre d\'actes CCAM mensuels 2023',
            data: [120, 110, 130, 150, 140, 160], // Données fictives mensuelles 2023
            type: 'line', // Ce dataset est en courbe
            borderColor: 'rgba(54, 162, 235, 1)', // Bleu pour 2023
            backgroundColor: 'rgba(54, 162, 235, 0.2)', // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            tension: 0.3,
            yAxisID: 'y', // Utilise le premier axe Y
          },
          {
            label: 'Nombre d\'actes CCAM mensuels 2024',
            data: [110, 120, 125, 135, 145, 155], // Données fictives mensuelles 2024
            type: 'line', // Ce dataset est en courbe
            borderColor: 'rgba(255, 99, 132, 1)', // Rouge pour 2024
            backgroundColor: 'rgba(255, 99, 132, 0.2)', // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            tension: 0.3,
            yAxisID: 'y', // Utilise le premier axe Y
          },
          {
            label: 'Nombre d\'actes CCAM par type 2023',
            data: [150, 130, 140, 110, 120, 100], // Données fictives par type 2023
            backgroundColor: 'rgba(54, 162, 235, 0.7)', // Bleu pour les barres 2023
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1,
            yAxisID: 'y1', // Utilise le deuxième axe Y
          },
          {
            label: 'Nombre d\'actes CCAM par type 2024',
            data: [140, 125, 135, 105, 115, 95], // Données fictives par type 2024
            backgroundColor: 'rgba(255, 99, 132, 0.7)', // Rouge pour les barres 2024
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1,
            yAxisID: 'y1', // Utilise le deuxième axe Y
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            position: 'left',
            title: {
              display: true,
              text: 'Nombre d\'actes CCAM mensuels'
            }
          },
          y1: {
            beginAtZero: true,
            position: 'right',
            title: {
              display: true,
              text: 'Nombre d\'actes CCAM par type'
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                let label = context.dataset.label || '';

                if (label) {
                  label += ': ';
                }
                if (context.parsed.y !== null) {
                  label += `${context.parsed.y} actes`;
                }
                return label;
              },
              afterLabel: function(context) {
                // Ajout de détails supplémentaires sur les types d'actes CCAM
                let typeActe = '';
                switch (context.label) {
                  case 'Acte chirurgical':
                    typeActe = 'Actes liés aux interventions chirurgicales';
                    break;
                  case 'Acte diagnostique':
                    typeActe = 'Actes de diagnostic réalisés';
                    break;
                  case 'Acte thérapeutique':
                    typeActe = 'Actes thérapeutiques';
                    break;
                  case 'Acte endoscopique':
                    typeActe = 'Actes d\'endoscopie';
                    break;
                  case 'Acte radiologique':
                    typeActe = 'Actes de radiologie';
                    break;
                  case 'Acte anesthésique':
                    typeActe = 'Actes d\'anesthésie';
                    break;
                  default:
                    typeActe = 'Actes divers';
                    break;
                }
                return typeActe;
              }
            }
          },
          title: {
            display: true,
            text: 'Comparaison des actes CCAM par type pour Dr Morel Olivier (2023 vs 2024)'
          }
        }
      }
    });
  }

}
