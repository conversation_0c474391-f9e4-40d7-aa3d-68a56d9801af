import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay, tap } from 'rxjs/operators';
import { BaseStructureService, PaginatedResult } from '../structure/base-structure.service';
import { AgentModel } from '../../models/praticien/AgentModel';
import { AuthService } from '../auth/auth.service';
import { environment } from '../../../../environments/environment';

interface AgentResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': AgentModel[];
  'view': {
    '@id': string;
    '@type': string;
    'first': string;
    'last': string;
    'next': string;
  };
  'search': any;
}

@Injectable({
  providedIn: 'root'
})
export class AgentService extends BaseStructureService<AgentModel, AgentResponse> {
  // Additional cache for frequently accessed data
  private agentsByEjCache = new Map<string, Observable<PaginatedResult<AgentModel>>>();

  constructor(
    http: HttpClient,
    private authService: AuthService
  ) {
    super(http, '/api/agents');
  }

  /**
   * Extract AgentModel array and total count from API response
   * @param response API response
   * @returns PaginatedResult containing AgentModel array and total count
   */
  protected extractData(response: AgentResponse): PaginatedResult<AgentModel> {
    return {
      items: response.member,
      totalItems: response.totalItems
    };
  }

  /**
   * Get active agents for the current user's hospital (using hopital.code filter + isActif=true)
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @param filters Additional filters (optional)
   * @returns Observable of PaginatedResult containing active AgentModel array and total count
   */
  getAgentsByCurrentEj(page?: number, limit?: number, filters?: any): Observable<PaginatedResult<AgentModel>> {
    const ejcode = this.getEjCodeFromToken();

    if (!ejcode) {
      console.error('❌ No ejcode found in token, cannot fetch agents');
      return of({ items: [], totalItems: 0 });
    }

    return this.getAgentsByEj(ejcode, page, limit, filters);
  }

  /**
   * Get active agents for a specific hospital using hopital.code filter + isActif=true
   * @param ejcode EJ code (used as hopital.code filter value)
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @param filters Additional filters (optional)
   * @returns Observable of PaginatedResult containing active AgentModel array and total count
   */
  getAgentsByEj(ejcode: string, page?: number, limit?: number, filters?: any): Observable<PaginatedResult<AgentModel>> {
    const cacheKey = `hopital_${ejcode}_${page || 1}_${limit || 10}_${JSON.stringify(filters || {})}`;

    if (this.agentsByEjCache.has(cacheKey)) {
      return this.agentsByEjCache.get(cacheKey) as Observable<PaginatedResult<AgentModel>>;
    }

    let params = new HttpParams();
    params = params.set('hopital.code', ejcode);
    params = params.set('isActif', 'true'); // Filtrer seulement les agents actifs

    if (page) params = params.set('page', page.toString());
    if (limit) params = params.set('itemsPerPage', limit.toString());

    // Add search filters
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
          params = params.set(key, filters[key]);
        }
      });
    }

    const request = this.http.get<AgentResponse>(
      `${environment.api_url}${this.apiPath}`,
      { params }
    ).pipe(
      map(response => this.extractData(response)),
      tap(result => {
        console.log(`🏥 Loaded ${result.items.length} active agents for hospital.code=${ejcode}`);
      }),
      shareReplay(1),
      catchError(this.handleError)
    );

    this.agentsByEjCache.set(cacheKey, request);

    // Clear cache after specified time
    setTimeout(() => {
      this.agentsByEjCache.delete(cacheKey);
    }, this.cacheTime);

    return request;
  }

  /**
   * Search active agents with text query (filtered by current user's hospital + isActif=true)
   * @param query Search query
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @returns Observable of PaginatedResult containing active AgentModel array and total count
   */
  searchAgents(query: string, page?: number, limit?: number): Observable<PaginatedResult<AgentModel>> {
    const ejcode = this.getEjCodeFromToken();

    if (!ejcode) {
      console.error('❌ No ejcode found in token, cannot search agents');
      return of({ items: [], totalItems: 0 });
    }

    const filters = {
      'nom': query,
      'prenom': query,
      'fullName': query,
      'matricule': query,
      'hrUser': query
    };

    return this.getAgentsByEj(ejcode, page, limit, filters);
  }

  /**
   * Get ejcode from user token
   * @returns ejcode string or null if not found
   */
  private getEjCodeFromToken(): string | null {
    try {
      const tokenPayload = this.authService.decodeToken();
      if (tokenPayload && tokenPayload.code_ej) {
        console.log('🏥 EJ Code from token:', tokenPayload.code_ej);
        return tokenPayload.code_ej;
      }

      console.warn('⚠️ No code_ej found in token payload:', tokenPayload);
      return null;
    } catch (error) {
      console.error('❌ Error extracting ejcode from token:', error);
      return null;
    }
  }

  /**
   * Clear all caches
   */
  override clearCache(): void {
    super.clearCache();
    this.agentsByEjCache.clear();
  }
}
