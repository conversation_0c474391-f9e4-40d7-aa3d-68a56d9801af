import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { AuthService } from '../../core/services/auth/auth.service';

export interface ActesFiltersResponse {
  poles: PoleWithCrsAndPractitioners[];
  totalActes: number;
  appliedFilters: any;
  appliedPeriods: any;
  generationTimeMs: number;
}

export interface Practitioner {
  '@id': string;
  nom: string;
  prenom: string;
  titre: string;
  displayName: string;
}

export interface Cr {
  '@id': string;
  crcode: string;
  libelle: string;
}

export interface CrWithPractitioners extends Cr {
  practitioners: Practitioner[];
}

export interface Pole {
  '@id': string;
  poleCode: string;
  libelle: string;
}

export interface PoleWithCrsAndPractitioners extends Pole {
  crs: CrWithPractitioners[];
}

export interface CascadeFilters {
  ejcode?: string;
  practitioner?: string;
  pole?: string;
  cr?: string;
  typeVenue?: number; // 1=Consultation, 2=Hospitalisation, 3=Urgence
  p1Start?: string;
  p1End?: string;
  p2Start?: string;
  p2End?: string;
  p3Start?: string;
  p3End?: string;
}

/**
 * Service pour gérer les filtres en cascade des actes médicaux.
 *
 * Utilise l'endpoint /api/cascade/actes/filters pour récupérer les listes
 * de praticiens, pôles et CRs disponibles selon les filtres appliqués.
 *
 * Logique de cascade intelligente :
 * - Praticien sélectionné → Propose seulement les pôles/CRs où ce praticien a posé des actes
 * - Pôle sélectionné → Propose seulement les praticiens qui ont posé des actes dans ce pôle
 * - CR sélectionné → Propose seulement les praticiens qui ont posé des actes dans ce service
 */
@Injectable({
  providedIn: 'root'
})
export class ActesFiltersService {
  private readonly baseUrl = `${environment.api_url}/api/cascade/actes/filters`;

  // Cache des dernières données récupérées
  private filtersDataSubject = new BehaviorSubject<ActesFiltersResponse | null>(null);
  public filtersData$ = this.filtersDataSubject.asObservable();

  // État du loading
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  // Cache persistant au niveau du service
  private cachedFiltersData: ActesFiltersResponse | null = null;
  private cachedPeriods: string = '';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {
    console.log('🔧 ActesFiltersService: Service initialized');
  }

  /**
   * Vérifie si le cache est valide pour les filtres donnés
   */
  isCacheValid(filters: CascadeFilters): boolean {
    const hasData = !!this.cachedFiltersData && !!this.cachedFiltersData.poles;
    const periodsMatch = !this.havePeriodsChanged(filters);
    const isValid = hasData && periodsMatch;

    console.log('🔍 Service: Cache validation:', {
      hasData,
      periodsMatch,
      isValid,
      cachedPolesCount: this.cachedFiltersData?.poles?.length || 0
    });

    return isValid;
  }

  /**
   * Génère une clé de cache basée sur les périodes
   */
  private generatePeriodsCacheKey(filters: CascadeFilters): string {
    const periods = [
      filters.p1Start,
      filters.p1End,
      filters.p2Start,
      filters.p2End,
      filters.p3Start,
      filters.p3End,
      filters.ejcode
    ].filter(Boolean).join('|');

    return periods;
  }

  /**
   * Vérifie si les périodes ont changé depuis le dernier cache
   */
  private havePeriodsChanged(filters: CascadeFilters): boolean {
    const currentPeriods = this.generatePeriodsCacheKey(filters);
    const hasChanged = this.cachedPeriods !== currentPeriods;

    if (hasChanged) {
      console.log('🔄 Service: Periods have changed:', {
        cached: this.cachedPeriods,
        current: currentPeriods,
        hasCachedData: !!this.cachedFiltersData
      });
    } else {
      console.log('✅ Service: Periods unchanged, using cache', {
        periodsKey: currentPeriods,
        hasCachedData: !!this.cachedFiltersData
      });
    }

    return hasChanged;
  }

  /**
   * Récupérer l'ejcode depuis le token de l'utilisateur connecté
   */
  private getEjCodeFromToken(): string | null {
    try {
      const tokenPayload = this.authService.decodeToken();
      if (tokenPayload && tokenPayload.code_ej) {
        console.log('🏥 EJ Code from token:', tokenPayload.code_ej);
        return tokenPayload.code_ej;
      }

      console.warn('⚠️ No code_ej found in token payload:', tokenPayload);
      return null;
    } catch (error) {
      console.error('❌ Error extracting ejcode from token:', error);
      return null;
    }
  }

  /**
   * Récupérer les filtres disponibles selon les paramètres avec cache intelligent
   */
  getFilters(filters: CascadeFilters): Observable<ActesFiltersResponse> {
    // Ajouter automatiquement l'ejcode depuis le token
    const ejcode = this.getEjCodeFromToken();
    const filtersWithEjcode = { ...filters, ejcode: ejcode || 'ej0001' };

    // Vérifier si on peut utiliser le cache
    if (this.isCacheValid(filtersWithEjcode)) {
      console.log('⚡ Service: Using cached filters data - NO LOADING STATE');
      this.loadingSubject.next(false);
      return new Observable<ActesFiltersResponse>(observer => {
        // Vérifier que les données en cache sont valides
        if (!this.cachedFiltersData || !this.cachedFiltersData.poles) {
          console.warn('⚠️ Service: Cached data is invalid, clearing cache');
          this.cachedFiltersData = null;
          this.cachedPeriods = '';
          // Relancer la méthode pour charger depuis le serveur
          this.getFilters(filters).subscribe(observer);
          return;
        }

        // Simuler un délai très court pour voir l'état
        setTimeout(() => {
          const cachedDataWithFlag = {
            ...this.cachedFiltersData!,
            fromCache: true,
            cacheTimestamp: new Date().toISOString()
          };
          observer.next(cachedDataWithFlag);
          observer.complete();
        }, 10);
      });
    }

    // Charger depuis le serveur si pas de cache ou périodes changées
    this.loadingSubject.next(true);

    let params = new HttpParams();

    // Désactiver le cache côté navigateur
    params = params.set('_t', Date.now().toString());

    if (ejcode) {
      params = params.set('ejcode', ejcode);
      console.log('🏥 Auto-added ejcode from token:', ejcode);
    } else {
      console.warn('⚠️ No ejcode found in token, API call may fail');
    }

    // Ajouter tous les paramètres non vides
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    console.log('🌐 Service: Loading fresh data from server (cache miss or periods changed):', {
      ...filters,
      ejcode: ejcode || 'NOT_FOUND'
    });

    return new Observable<ActesFiltersResponse>(observer => {
      this.http.get<ActesFiltersResponse>(this.baseUrl, { params }).subscribe({
        next: (data) => {
          console.log('✅ Service: Cascade filters loaded and cached:', {
            poles: data.poles.length,
            totalCrs: data.poles.reduce((sum: number, pole: any) => sum + pole.crs.length, 0),
            totalActes: data.totalActes,
            generationTime: data.generationTimeMs + 'ms'
          });

          // Mettre en cache les données et les périodes
          this.cachedFiltersData = data;
          this.cachedPeriods = this.generatePeriodsCacheKey(filtersWithEjcode);

          this.filtersDataSubject.next(data);
          this.loadingSubject.next(false);
          observer.next(data);
          observer.complete();
        },
        error: (error) => {
          console.error('❌ Error fetching cascade filters:', error);
          this.loadingSubject.next(false);
          observer.error(error);
        }
      });
    });
  }

  /**
   * Récupérer les filtres avec les périodes depuis le GlobalFilterService
   */
  getFiltersWithPeriods(additionalFilters: Partial<CascadeFilters> = {}): Observable<ActesFiltersResponse> {
    // Récupérer les périodes depuis localStorage (comme dans GlobalFilterService)
    const globalFilters = this.getGlobalFiltersFromStorage();

    const filters: CascadeFilters = {
      ...this.buildPeriodParams(globalFilters),
      ...additionalFilters
    };

    return this.getFilters(filters);
  }

  /**
   * Récupérer les praticiens disponibles selon les filtres
   * Extrait tous les praticiens de tous les CRs
   */
  getPractitioners(filters: Partial<CascadeFilters> = {}): Observable<Practitioner[]> {
    return new Observable<Practitioner[]>(observer => {
      this.getFiltersWithPeriods(filters).subscribe({
        next: (data) => {
          // Extraire tous les praticiens uniques de tous les CRs de tous les pôles
          const practitionersMap = new Map<string, Practitioner>();
          data.poles.forEach(pole => {
            pole.crs.forEach(cr => {
              cr.practitioners.forEach(practitioner => {
                practitionersMap.set(practitioner['@id'], practitioner);
              });
            });
          });
          observer.next(Array.from(practitionersMap.values()));
        },
        error: (error) => observer.error(error)
      });
    });
  }

  /**
   * Récupérer les pôles disponibles selon les filtres
   */
  getPoles(filters: Partial<CascadeFilters> = {}): Observable<PoleWithCrsAndPractitioners[]> {
    return new Observable<PoleWithCrsAndPractitioners[]>(observer => {
      this.getFiltersWithPeriods(filters).subscribe({
        next: (data) => observer.next(data.poles),
        error: (error) => observer.error(error)
      });
    });
  }

  /**
   * Récupérer les CRs disponibles selon les filtres
   * Extrait tous les CRs de tous les pôles
   */
  getCrs(filters: Partial<CascadeFilters> = {}): Observable<CrWithPractitioners[]> {
    return new Observable<CrWithPractitioners[]>(observer => {
      this.getFiltersWithPeriods(filters).subscribe({
        next: (data) => {
          // Extraire tous les CRs de tous les pôles
          const allCrs: CrWithPractitioners[] = [];
          data.poles.forEach(pole => {
            allCrs.push(...pole.crs);
          });
          observer.next(allCrs);
        },
        error: (error) => observer.error(error)
      });
    });
  }

  /**
   * Récupérer les filtres globaux depuis localStorage
   */
  private getGlobalFiltersFromStorage(): any {
    try {
      const stored = localStorage.getItem('global_filters');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.warn('Error parsing global_filters from localStorage:', error);
      return {};
    }
  }

  /**
   * Construire les paramètres de périodes depuis les filtres globaux
   */
  private buildPeriodParams(globalFilters: any): Partial<CascadeFilters> {
    const params: Partial<CascadeFilters> = {};

    if (globalFilters.p1?.dateDebut && globalFilters.p1?.dateFin) {
      params.p1Start = this.formatDateForAPI(globalFilters.p1.dateDebut);
      params.p1End = this.formatDateForAPI(globalFilters.p1.dateFin);
    }

    if (globalFilters.p2?.dateDebut && globalFilters.p2?.dateFin) {
      params.p2Start = this.formatDateForAPI(globalFilters.p2.dateDebut);
      params.p2End = this.formatDateForAPI(globalFilters.p2.dateFin);
    }

    if (globalFilters.p3?.dateDebut && globalFilters.p3?.dateFin) {
      params.p3Start = this.formatDateForAPI(globalFilters.p3.dateDebut);
      params.p3End = this.formatDateForAPI(globalFilters.p3.dateFin);
    }

    return params;
  }

  /**
   * Formater une date pour l'API (YYYY-MM-DD)
   */
  private formatDateForAPI(date: Date | string): string {
    if (!date) return '';

    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  /**
   * Vider le cache des filtres
   */
  clearCache(): void {
    console.log('🗑️ Service: Clearing filters cache');
    this.cachedFiltersData = null;
    this.cachedPeriods = '';
    this.filtersDataSubject.next(null);
  }

  /**
   * Obtenir les dernières données en cache
   */
  getCachedData(): ActesFiltersResponse | null {
    return this.filtersDataSubject.value;
  }
}
