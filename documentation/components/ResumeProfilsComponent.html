<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  ResumeProfilsComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/pages/resume-profils/resume-profils.component.ts</code>
</p>






<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-resume-profils</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                                <code><a href="../components/ProfilePictureComponent.html" target="_self" >ProfilePictureComponent</a></code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./resume-profils.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./resume-profils.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>









</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Component } from &#x27;@angular/core&#x27;;
import {ProfilePictureComponent} from &quot;../../graphical/components/profile-picture/profile-picture.component&quot;;

@Component({
  selector: &#x27;app-resume-profils&#x27;,
  standalone: true,
  imports: [
    ProfilePictureComponent
  ],
  templateUrl: &#x27;./resume-profils.component.html&#x27;,
  styleUrl: &#x27;./resume-profils.component.scss&#x27;
})
export class ResumeProfilsComponent {

}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;!--&lt;app-profile-picture--&gt;
&lt;!--  [nom]&#x3D;&quot;&#x27;Dupont&#x27;&quot;--&gt;
&lt;!--  [prenom]&#x3D;&quot;&#x27;Jean&#x27;&quot;--&gt;
&lt;!--  [status]&#x3D;&quot;&#x27;online&#x27;&quot;--&gt;
&lt;!--&amp;gt;&lt;/app-profile-picture&gt;--&gt;

&lt;!--&lt;app-profile-picture--&gt;
&lt;!--  [nom]&#x3D;&quot;&#x27;Kuban&#x27;&quot;--&gt;
&lt;!--  [prenom]&#x3D;&quot;&#x27;Eric&#x27;&quot;--&gt;
&lt;!--  [status]&#x3D;&quot;&#x27;away&#x27;&quot;--&gt;
&lt;!--&amp;gt;&lt;/app-profile-picture&gt;--&gt;

&lt;!--&lt;app-profile-picture--&gt;
&lt;!--  [nom]&#x3D;&quot;&#x27;Smith&#x27;&quot;--&gt;
&lt;!--  [prenom]&#x3D;&quot;&#x27;Alice&#x27;&quot;--&gt;
&lt;!--  [status]&#x3D;&quot;&#x27;offline&#x27;&quot;--&gt;
&lt;!--&amp;gt;&lt;/app-profile-picture&gt;--&gt;


&lt;div class&#x3D;&quot;py-6 md:flex md:items-center md:justify-between lg:border-gray-200&quot;&gt;
  &lt;div class&#x3D;&quot;flex-1 min-w-0&quot;&gt;
    &lt;!-- Profile --&gt;
    &lt;div class&#x3D;&quot;flex items-center&quot;&gt;
      &lt;!-- Visible uniquement sur les écrans lg et plus --&gt;
      &lt;div  class&#x3D;&quot;hidden lg:block&quot;&gt;
        &lt;app-profile-picture
          [nom]&#x3D;&quot;&#x27;Olivier&#x27;&quot;
          [prenom]&#x3D;&quot;&#x27;Morel&#x27;&quot;
          [status]&#x3D;&quot;&#x27;offline&#x27;&quot;
        &gt;&lt;/app-profile-picture&gt;
      &lt;/div&gt;




      &lt;div&gt;
        &lt;div class&#x3D;&quot;flex items-center&quot;&gt;
          &lt;!-- Visible uniquement sur les écrans sm --&gt;
          &lt;div class&#x3D;&quot;block lg:hidden&quot;&gt;
            &lt;app-profile-picture
              [nom]&#x3D;&quot;&#x27;Morel&#x27;&quot;
              [prenom]&#x3D;&quot;&#x27;Olivier&#x27;&quot;
              [status]&#x3D;&quot;&#x27;online&#x27;&quot;
            &gt;&lt;/app-profile-picture&gt;
          &lt;/div&gt;
          &lt;h1 class&#x3D;&quot;ml-3 text-2xl font-bold leading-7 text-gray-900 sm:leading-9 sm:truncate&quot;&gt;
            Dr Morel Olivier
          &lt;/h1&gt;
        &lt;/div&gt;
        &lt;dl class&#x3D;&quot;mt-6 flex flex-col sm:ml-3 sm:mt-1 sm:flex-row sm:flex-wrap&quot;&gt;
          &lt;dt class&#x3D;&quot;sr-only&quot;&gt;Pole&lt;/dt&gt;
          &lt;dd class&#x3D;&quot;flex items-center text-sm text-gray-500 font-medium capitalize sm:mr-6&quot;&gt;
            &lt;svg
              class&#x3D;&quot;flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400&quot;
              xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;
              viewBox&#x3D;&quot;0 0 20 20&quot;
              fill&#x3D;&quot;currentColor&quot;
              aria-hidden&#x3D;&quot;true&quot;
            &gt;
              &lt;path
                fill-rule&#x3D;&quot;evenodd&quot;
                d&#x3D;&quot;M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z&quot;
                clip-rule&#x3D;&quot;evenodd&quot;
              /&gt;
            &lt;/svg&gt;
            POLE : BLOCS OPERATOIRES
          &lt;/dd&gt;

          &lt;dt class&#x3D;&quot;sr-only&quot;&gt;Service&lt;/dt&gt;
          &lt;dd class&#x3D;&quot;flex items-center text-sm text-gray-500 font-medium capitalize sm:mr-6&quot;&gt;
            &lt;svg
              class&#x3D;&quot;flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400&quot;
              xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;
              viewBox&#x3D;&quot;0 0 20 20&quot;
              fill&#x3D;&quot;currentColor&quot;
              aria-hidden&#x3D;&quot;true&quot;
            &gt;
              &lt;path
                fill-rule&#x3D;&quot;evenodd&quot;
                d&#x3D;&quot;M6 3a1 1 0 00-1 1v2H3a1 1 0 00-1 1v9a1 1 0 001 1h14a1 1 0 001-1V7a1 1 0 00-1-1h-2V4a1 1 0 00-1-1H6zm0 2V4h8v1H6z&quot;
                clip-rule&#x3D;&quot;evenodd&quot;
              /&gt;
            &lt;/svg&gt;
            SERVICE : BLOC OPERATOIRE MAT
          &lt;/dd&gt;

          &lt;dt class&#x3D;&quot;sr-only&quot;&gt;Unite Fonctionnel&lt;/dt&gt;
          &lt;dd class&#x3D;&quot;mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize&quot;&gt;
            &lt;svg
              class&#x3D;&quot;flex-shrink-0 mr-1.5 h-5 w-5 text-green-400&quot;
              xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;
              viewBox&#x3D;&quot;0 0 20 20&quot;
              fill&#x3D;&quot;currentColor&quot;
              aria-hidden&#x3D;&quot;true&quot;
            &gt;
              &lt;path
                fill-rule&#x3D;&quot;evenodd&quot;
                d&#x3D;&quot;M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z&quot;
                clip-rule&#x3D;&quot;evenodd&quot;
              /&gt;
            &lt;/svg&gt;
            UF D&#x27;AFFECTATION : 6261 - CPDP
          &lt;/dd&gt;

          &lt;dt class&#x3D;&quot;sr-only&quot;&gt;Grade&lt;/dt&gt;
          &lt;dd class&#x3D;&quot;mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize&quot;&gt;
            &lt;!-- Heroicon name: academic-cap --&gt;
            &lt;svg  class&#x3D;&quot;flex-shrink-0 mr-1.5 h-5 w-5 text-green-400&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot; &gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-********* 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5&quot; /&gt;
            &lt;/svg&gt;

            Grade: PU/PH
          &lt;/dd&gt;

          &lt;dt class&#x3D;&quot;sr-only&quot;&gt;ETP statutaire&lt;/dt&gt;
          &lt;dd class&#x3D;&quot;mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize&quot;&gt;
            &lt;!-- Heroicon name: puzzle-piece --&gt;
            &lt;svg class&#x3D;&quot;flex-shrink-0 mr-1.5 h-5 w-5 text-green-400&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke-width&#x3D;&quot;1.5&quot; stroke&#x3D;&quot;currentColor&quot;&gt;
              &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; d&#x3D;&quot;M14.25 6.087c0-.355.186-.676.401-.959.221-.29.349-.634.349-1.003 0-1.036-1.007-1.875-2.25-1.875s-2.25.84-2.25 1.875c0 .369.128.713.349 1.003.215.283.401.604.401.959v0a.64.64 0 0 1-.657.643 48.39 48.39 0 0 1-4.163-.3c.186 1.613.293 3.25.315 4.907a.656.656 0 0 1-.658.663v0c-.355 0-.676-.186-.959-.401a1.647 1.647 0 0 0-1.003-.349c-1.036 0-1.875 1.007-1.875 2.25s.84 2.25 1.875 2.25c.369 0 .713-.128 1.003-.349.283-.215.604-.401.959-.401v0c.31 0 .555.26.532.57a48.039 48.039 0 0 1-.642 5.056c1.518.19 3.058.309 4.616.354a.64.64 0 0 0 .657-.643v0c0-.355-.186-.676-.401-.959a1.647 1.647 0 0 1-.349-1.003c0-1.035 1.008-1.875 2.25-1.875 1.243 0 2.25.84 2.25 1.875 0 .369-.128.713-.349 1.003-.215.283-.4.604-.4.959v0c0 .333.277.599.61.58a48.1 48.1 0 0 0 5.427-.63 48.05 48.05 0 0 0 .582-4.717.532.532 0 0 0-.533-.57v0c-.355 0-.676.186-.959.401-.29.221-.634.349-1.003.349-1.035 0-1.875-1.007-1.875-2.25s.84-2.25 1.875-2.25c.37 0 .713.128 1.003.349.283.215.604.401.96.401v0a.656.656 0 0 0 .658-.663 48.422 48.422 0 0 0-.37-5.36c-1.886.342-3.81.574-5.766.689a.578.578 0 0 1-.61-.58v0Z&quot; /&gt;
            &lt;/svg&gt;

            ETP statutaire : 2
          &lt;/dd&gt;
        &lt;/dl&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
  &lt;div class&#x3D;&quot;mt-6 flex space-x-3 md:mt-0 md:ml-4&quot;&gt;
    &lt;button
      type&#x3D;&quot;button&quot;
      class&#x3D;&quot;inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500&quot;
    &gt;
      Imprimer un rapport
    &lt;/button&gt;
  &lt;/div&gt;
&lt;/div&gt;


</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><!--<app-profile-picture--><!--  [nom]="\'Dupont\'"--><!--  [prenom]="\'Jean\'"--><!--  [status]="\'online\'"--><!--&gt;</app-profile-picture>--><!--<app-profile-picture--><!--  [nom]="\'Kuban\'"--><!--  [prenom]="\'Eric\'"--><!--  [status]="\'away\'"--><!--&gt;</app-profile-picture>--><!--<app-profile-picture--><!--  [nom]="\'Smith\'"--><!--  [prenom]="\'Alice\'"--><!--  [status]="\'offline\'"--><!--&gt;</app-profile-picture>--><div class="py-6 md:flex md:items-center md:justify-between lg:border-gray-200">  <div class="flex-1 min-w-0">    <!-- Profile -->    <div class="flex items-center">      <!-- Visible uniquement sur les écrans lg et plus -->      <div  class="hidden lg:block">        <app-profile-picture          [nom]="\'Olivier\'"          [prenom]="\'Morel\'"          [status]="\'offline\'"        ></app-profile-picture>      </div>      <div>        <div class="flex items-center">          <!-- Visible uniquement sur les écrans sm -->          <div class="block lg:hidden">            <app-profile-picture              [nom]="\'Morel\'"              [prenom]="\'Olivier\'"              [status]="\'online\'"            ></app-profile-picture>          </div>          <h1 class="ml-3 text-2xl font-bold leading-7 text-gray-900 sm:leading-9 sm:truncate">            Dr Morel Olivier          </h1>        </div>        <dl class="mt-6 flex flex-col sm:ml-3 sm:mt-1 sm:flex-row sm:flex-wrap">          <dt class="sr-only">Pole</dt>          <dd class="flex items-center text-sm text-gray-500 font-medium capitalize sm:mr-6">            <svg              class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"              xmlns="http://www.w3.org/2000/svg"              viewBox="0 0 20 20"              fill="currentColor"              aria-hidden="true"            >              <path                fill-rule="evenodd"                d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"                clip-rule="evenodd"              />            </svg>            POLE : BLOCS OPERATOIRES          </dd>          <dt class="sr-only">Service</dt>          <dd class="flex items-center text-sm text-gray-500 font-medium capitalize sm:mr-6">            <svg              class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"              xmlns="http://www.w3.org/2000/svg"              viewBox="0 0 20 20"              fill="currentColor"              aria-hidden="true"            >              <path                fill-rule="evenodd"                d="M6 3a1 1 0 00-1 1v2H3a1 1 0 00-1 1v9a1 1 0 001 1h14a1 1 0 001-1V7a1 1 0 00-1-1h-2V4a1 1 0 00-1-1H6zm0 2V4h8v1H6z"                clip-rule="evenodd"              />            </svg>            SERVICE : BLOC OPERATOIRE MAT          </dd>          <dt class="sr-only">Unite Fonctionnel</dt>          <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize">            <svg              class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400"              xmlns="http://www.w3.org/2000/svg"              viewBox="0 0 20 20"              fill="currentColor"              aria-hidden="true"            >              <path                fill-rule="evenodd"                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"                clip-rule="evenodd"              />            </svg>            UF D\'AFFECTATION : 6261 - CPDP          </dd>          <dt class="sr-only">Grade</dt>          <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize">            <!-- Heroicon name: academic-cap -->            <svg  class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >              <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-********* 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5" />            </svg>            Grade: PU/PH          </dd>          <dt class="sr-only">ETP statutaire</dt>          <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize">            <!-- Heroicon name: puzzle-piece -->            <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">              <path stroke-linecap="round" stroke-linejoin="round" d="M14.25 6.087c0-.355.186-.676.401-.959.221-.29.349-.634.349-1.003 0-1.036-1.007-1.875-2.25-1.875s-2.25.84-2.25 1.875c0 .369.128.713.349 1.003.215.283.401.604.401.959v0a.64.64 0 0 1-.657.643 48.39 48.39 0 0 1-4.163-.3c.186 1.613.293 3.25.315 4.907a.656.656 0 0 1-.658.663v0c-.355 0-.676-.186-.959-.401a1.647 1.647 0 0 0-1.003-.349c-1.036 0-1.875 1.007-1.875 2.25s.84 2.25 1.875 2.25c.369 0 .713-.128 1.003-.349.283-.215.604-.401.959-.401v0c.31 0 .555.26.532.57a48.039 48.039 0 0 1-.642 5.056c1.518.19 3.058.309 4.616.354a.64.64 0 0 0 .657-.643v0c0-.355-.186-.676-.401-.959a1.647 1.647 0 0 1-.349-1.003c0-1.035 1.008-1.875 2.25-1.875 1.243 0 2.25.84 2.25 1.875 0 .369-.128.713-.349 1.003-.215.283-.4.604-.4.959v0c0 .333.277.599.61.58a48.1 48.1 0 0 0 5.427-.63 48.05 48.05 0 0 0 .582-4.717.532.532 0 0 0-.533-.57v0c-.355 0-.676.186-.959.401-.29.221-.634.349-1.003.349-1.035 0-1.875-1.007-1.875-2.25s.84-2.25 1.875-2.25c.37 0 .713.128 1.003.349.283.215.604.401.96.401v0a.656.656 0 0 0 .658-.663 48.422 48.422 0 0 0-.37-5.36c-1.886.342-3.81.574-5.766.689a.578.578 0 0 1-.61-.58v0Z" />            </svg>            ETP statutaire : 2          </dd>        </dl>      </div>    </div>  </div>  <div class="mt-6 flex space-x-3 md:mt-0 md:ml-4">    <button      type="button"      class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"    >      Imprimer un rapport    </button>  </div></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'ResumeProfilsComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'ResumeProfilsComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
