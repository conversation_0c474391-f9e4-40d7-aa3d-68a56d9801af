import { Component, OnInit } from '@angular/core';
import { AgentService } from '../../services/agent/agent.service';
import { AuthService } from '../../services/auth/auth.service';
import { UfService, UfModel } from '../../services/uf';
import { AgentModel } from '../../models/activite/ActiviteModel';
import { catchError, finalize, switchMap, map } from 'rxjs/operators';
import { Observable, of, forkJoin } from 'rxjs';

@Component({
  selector: 'app-agent-details',
  templateUrl: './agent-details.component.html',
  styleUrls: ['./agent-details.component.scss']
})
export class AgentDetailsComponent implements OnInit {
  agent: AgentModel | null = null;
  loading = true;
  error: string | null = null;
  ufDetails: Map<string, UfModel> = new Map();
  loadingUfs = false;

  constructor(
    private agentService: AgentService,
    private authService: AuthService,
    private ufService: UfService
  ) {}

  ngOnInit(): void {
    this.loadAgentDetails();
  }

  loadAgentDetails(): void {
    const tokenPayload = this.authService.decodeToken();

    if (!tokenPayload || !tokenPayload.sub) {
      this.error = 'Impossible de récupérer les informations de l\'utilisateur.';
      this.loading = false;
      return;
    }

    this.agentService.getAgentById(tokenPayload.sub)
      .pipe(
        catchError(error => {
          console.error('Error loading agent details:', error);
          this.error = 'Erreur lors du chargement des détails de l\'agent.';
          return of(null);
        }),
        switchMap(agent => {
          if (agent && agent.affectations && agent.affectations.length > 0) {
            // Extract UF IDs from affectations
            const ufIds = agent.affectations
              .filter(aff => aff.ufs)
              .map(aff => aff.ufs);

            if (ufIds.length > 0) {
              this.loadingUfs = true;

              // Create an array of observables for each UF request
              const ufRequests = ufIds.map(ufId =>
                this.ufService.getUfById(ufId).pipe(
                  catchError(error => {
                    console.error(`Error loading UF details for ${ufId}:`, error);
                    return of(null);
                  })
                )
              );

              // Execute all UF requests in parallel
              return forkJoin(ufRequests).pipe(
                map(ufResults => {
                  // Store UF details in the map
                  ufResults.forEach((uf, index) => {
                    if (uf) {
                      this.ufDetails.set(ufIds[index], uf);
                    }
                  });
                  return agent;
                }),
                finalize(() => {
                  this.loadingUfs = false;
                })
              );
            }
          }
          return of(agent);
        }),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(agent => {
        this.agent = agent;
      });
  }

  /**
   * Get UF details for an affectation
   * @param ufId The UF ID from the affectation
   * @returns The UF details or null if not found
   */
  getUfDetails(ufId: string): UfModel | null {
    return this.ufDetails.get(ufId) || null;
  }

  // Helper method to format dates
  formatDate(dateString: string | null): string {
    if (!dateString) return 'Non spécifié';

    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  // Helper method to check if a value is null or undefined
  isValueMissing(value: any): boolean {
    return value === null || value === undefined;
  }
}
