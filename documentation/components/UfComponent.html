<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  UfComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/organization/components/uf/uf.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-uf</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>NgIf</code>
                            <code>NgForOf</code>
                                <code><a href="../components/BreadcrumbComponent.html" target="_self" >BreadcrumbComponent</a></code>
                                <code><a href="../components/InformationPanelComponent.html" target="_self" >InformationPanelComponent</a></code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./uf.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./uf.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#breadcrumbItems" >breadcrumbItems</a>
                            </li>
                            <li>
                                <a href="#currentUfId" >currentUfId</a>
                            </li>
                            <li>
                                <a href="#isLoading" >isLoading</a>
                            </li>
                            <li>
                                <a href="#ufDetails" >ufDetails</a>
                            </li>
                            <li>
                                <a href="#ufs" >ufs</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#loadUfData" >loadUfData</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(ufService: UFService, route: ActivatedRoute)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="33" class="link-to-prism">src/app/organization/components/uf/uf.component.ts:33</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>ufService</td>

                                                        <td>
                                                                    <code>UFService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>route</td>

                                                        <td>
                                                                    <code>ActivatedRoute</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadUfData"></a>
                    <span class="name">
                        <span ><b>loadUfData</b></span>
                        <a href="#loadUfData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadUfData(ufId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="58"
                                    class="link-to-prism">src/app/organization/components/uf/uf.component.ts:58</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">
                        <div class="io-description"><p>/</p>
</div>

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ufId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="37"
                                    class="link-to-prism">src/app/organization/components/uf/uf.component.ts:37</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="breadcrumbItems"></a>
                    <span class="name">
                        <span ><b>breadcrumbItems</b></span>
                        <a href="#breadcrumbItems"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>BreadcrumbItem[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
    { label: &#x27;Pôle Cardiologie&#x27;, url: &#x27;/chru-nancy/pole-anesthesie-reanimation&#x27; },
    { label: &#x27;Service de Cardiologie Médicale&#x27;, url: &#x27;/chru-nancy/pole-anesthesie-reanimation/departement-anesthesie&#x27; },
    { label: &#x27;UF 2171 - Cardiologie Hypertension&#x27; }
  ]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/app/organization/components/uf/uf.component.ts:29</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="currentUfId"></a>
                    <span class="name">
                        <span ><b>currentUfId</b></span>
                        <a href="#currentUfId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="24" class="link-to-prism">src/app/organization/components/uf/uf.component.ts:24</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isLoading"></a>
                    <span class="name">
                        <span ><b>isLoading</b></span>
                        <a href="#isLoading"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>true</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="25" class="link-to-prism">src/app/organization/components/uf/uf.component.ts:25</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ufDetails"></a>
                    <span class="name">
                        <span ><b>ufDetails</b></span>
                        <a href="#ufDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>UF | undefined</code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="26" class="link-to-prism">src/app/organization/components/uf/uf.component.ts:26</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ufs"></a>
                    <span class="name">
                        <span ><b>ufs</b></span>
                        <a href="#ufs"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>UF[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="23" class="link-to-prism">src/app/organization/components/uf/uf.component.ts:23</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import {UF} from &quot;../../../core/models/organization/UF.model&quot;;
import {UFService} from &quot;../../../core/services/organization/UFService&quot;;
import {NgForOf, NgIf} from &quot;@angular/common&quot;;
import {BreadcrumbComponent} from &quot;../../../pages/breadcrumb/breadcrumb.component&quot;;
import {BreadcrumbItem} from &quot;../../../core/models/breadcrumbItem&quot;;
import {ActivatedRoute} from &quot;@angular/router&quot;;
import {InformationPanelComponent} from &quot;../information-panel/information-panel.component&quot;;

@Component({
  selector: &#x27;app-uf&#x27;,
  standalone: true,
  imports: [
    NgIf,
    NgForOf,
    BreadcrumbComponent,
    InformationPanelComponent
  ],
  templateUrl: &#x27;./uf.component.html&#x27;,
  styleUrl: &#x27;./uf.component.scss&#x27;
})
export class UfComponent implements OnInit {
  ufs: UF[] &#x3D; [];
  currentUfId: string &#x3D; &#x27;&#x27;;
  isLoading &#x3D; true;
  ufDetails: UF | undefined;

  // fil d&#x27;ariane
  breadcrumbItems: BreadcrumbItem[] &#x3D; [
    { label: &#x27;Pôle Cardiologie&#x27;, url: &#x27;/chru-nancy/pole-anesthesie-reanimation&#x27; },
    { label: &#x27;Service de Cardiologie Médicale&#x27;, url: &#x27;/chru-nancy/pole-anesthesie-reanimation/departement-anesthesie&#x27; },
    { label: &#x27;UF 2171 - Cardiologie Hypertension&#x27; }
  ];

  constructor(private ufService: UFService,private route: ActivatedRoute) {}

  ngOnInit(): void {
    // // Récupération de l&#x27;ID de l&#x27;UF à partir de la route
    // this.route.paramMap.subscribe((params) &#x3D;&gt; {
    //   const ufId &#x3D; params.get(&#x27;id&#x27;) || &#x27;&#x27;;
    //   this.currentUfId &#x3D; ufId;
    //   if (ufId) {
    //     this.loadUfData(ufId);
    //   }
    // });
    this.currentUfId &#x3D; &#x27;4d1e8400-e29b-41d4-a716-776655440006&#x27;;
    this.ufService.ufs$.subscribe((data) &#x3D;&gt; {
      this.ufs &#x3D; data;
      if (this.currentUfId) {
        this.loadUfData(this.currentUfId);
      }
    });
  }

  /*********************************************************************/

  // Charger les données de l&#x27;UF
  loadUfData(ufId: string): void {
    this.isLoading &#x3D; true;
    this.ufService.getUFById(ufId).subscribe((uf) &#x3D;&gt; {
      if (uf) {
        this.ufDetails &#x3D; uf;
        this.breadcrumbItems[this.breadcrumbItems.length - 1] &#x3D; {
          label: uf.name,
        };
      } else {
        console.error(&#x27;UF introuvable pour l’ID:&#x27;, ufId);
      }
      this.isLoading &#x3D; false;
    });
  }


}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;!--&amp;lt;!&amp;ndash;LISTE DES UF &amp;ndash;&amp;gt;--&gt;
&lt;!--&lt;div class&#x3D;&quot;p-4 bg-gray-100 min-h-screen&quot;&gt;--&gt;
&lt;!--  &lt;h1 class&#x3D;&quot;text-2xl font-semibold text-gray-800 mb-4&quot;&gt;Liste des Unités Fonctionnelles (UF)&lt;/h1&gt;--&gt;

&lt;!--  &lt;div *ngIf&#x3D;&quot;ufs.length &#x3D;&#x3D;&#x3D; 0&quot; class&#x3D;&quot;text-gray-500 text-center p-4&quot;&gt;--&gt;
&lt;!--    Aucune unité fonctionnelle disponible.--&gt;
&lt;!--  &lt;/div&gt;--&gt;

&lt;!--  &lt;div *ngFor&#x3D;&quot;let uf of ufs&quot; class&#x3D;&quot;bg-white shadow rounded-lg p-4 mb-4&quot;&gt;--&gt;
&lt;!--    &lt;h2 class&#x3D;&quot;text-xl font-bold text-cyan-700&quot;&gt;{{ uf.name }}&lt;/h2&gt;--&gt;
&lt;!--    &lt;div class&#x3D;&quot;text-gray-500 text-sm mt-2&quot;&gt;--&gt;
&lt;!--      &lt;span class&#x3D;&quot;font-semibold&quot;&gt;Code:&lt;/span&gt; {{ uf.code }}--&gt;
&lt;!--    &lt;/div&gt;--&gt;
&lt;!--  &lt;/div&gt;--&gt;
&lt;!--&lt;/div&gt;--&gt;

&lt;!--POUR L&#x27;INSTANT CE COMPONENTE SE COMPORTE COMME UN SINGLE-UF--&gt;

&lt;div class&#x3D;&quot;p-4 bg-gray-100 min-h-screen&quot;&gt;
  &lt;app-breadcrumb [items]&#x3D;&quot;breadcrumbItems&quot;&gt;&lt;/app-breadcrumb&gt;

  &lt;app-information-panel [elementId]&#x3D;&quot;this.currentUfId&quot; elementType&#x3D;&quot;uf&quot;&gt;&lt;/app-information-panel&gt;

&lt;/div&gt;/
</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><!--&lt;!&ndash;LISTE DES UF &ndash;&gt;--><!--<div class="p-4 bg-gray-100 min-h-screen">--><!--  <h1 class="text-2xl font-semibold text-gray-800 mb-4">Liste des Unités Fonctionnelles (UF)</h1>--><!--  <div *ngIf="ufs.length === 0" class="text-gray-500 text-center p-4">--><!--    Aucune unité fonctionnelle disponible.--><!--  </div>--><!--  <div *ngFor="let uf of ufs" class="bg-white shadow rounded-lg p-4 mb-4">--><!--    <h2 class="text-xl font-bold text-cyan-700">{{ uf.name }}</h2>--><!--    <div class="text-gray-500 text-sm mt-2">--><!--      <span class="font-semibold">Code:</span> {{ uf.code }}--><!--    </div>--><!--  </div>--><!--</div>--><!--POUR L\'INSTANT CE COMPONENTE SE COMPORTE COMME UN SINGLE-UF--><div class="p-4 bg-gray-100 min-h-screen">  <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>  <app-information-panel [elementId]="this.currentUfId" elementType="uf"></app-information-panel></div>/</div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'UfComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'UfComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
