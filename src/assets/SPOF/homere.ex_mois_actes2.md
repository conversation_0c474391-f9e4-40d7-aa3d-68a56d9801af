## homere.EX_MOIS_ACTES2;

| #   | Nom de la colonne             | NULL ? | Type             | Description pour Supra                                  |
|-----|-------------------------------|--------|------------------|-------------------------------------------------------|
| 1   | ACTIVITE                     | Y      | VARCHAR2(1)      | Code de l'activité médicale                          |
| 2   | ACTIVITELIB                  | Y      | VARCHAR2(4000)   | Libellé détaillé de l'activité médicale              |
| 3   | ACTIVITEUFDEMANDE            | Y      | VARCHAR2(240)    | Activité de l'unité fonctionnelle ayant fait la demande |
| 4   | ACTIVITEUFINTERVENTION       | Y      | VARCHAR2(240)    | Activité de l'unité fonctionnelle d'intervention     |
| 5   | AGE                          | Y      | NUMBER(4)        | Âge du patient                                       |
| 6   | ANESTHESIE_I                 | Y      | VARCHAR2(2)      | Indique si une anesthésie a été réalisée            |
| 7   | ANNEENAISS                   | Y      | NUMBER(4)        | Année de naissance du patient                       |
| 8   | ASSOCIATION                  | Y      | VARCHAR2(1)      | Acte en association (oui/non)                       |
| 9   | COCODE                       | Y      | VARCHAR2(2)      | Code des modalités spécifiques                      |
| 10  | CODE_ACTE                    | Y      | VARCHAR2(13)     | Code unique de l'acte (NGAP, CCAM, etc.)            |
| 11  | CODEPOSTAL                   | Y      | VARCHAR2(5)      | Code postal du patient                              |
| 12  | COEFFICIENT                  | Y      | NUMBER           | Coefficient appliqué à l'acte                       |
| 13  | DEPARTEMENT                  | Y      | VARCHAR2(3)      | Code du département                                 |
| 14  | DEPARTEMENTLIB               | Y      | VARCHAR2(30)     | Libellé du département                              |
| 15  | ED                           | Y      | VARCHAR2(1)      | Acte en urgence (oui/non)                           |
| 16  | EXECUTANT2                   | Y      | VARCHAR2(80)     | Nom de l'exécutant secondaire                       |
| 17  | EXECUTANT3                   | Y      | VARCHAR2(80)     | Nom de l'exécutant tertiaire                        |
| 18  | EXECUTANT4                   | Y      | VARCHAR2(80)     | Nom de l'exécutant quaternaire                      |
| 19  | EXECUTANTPRINCIPAL           | Y      | VARCHAR2(80)     | Nom de l'exécutant principal                        |
| 20  | HIERARCHIE2                  | Y      | VARCHAR2(2)      | Code hiérarchique à 2 niveaux                       |
| 21  | HIERARCHIE2LIB               | Y      | VARCHAR2(4000)   | Libellé de la hiérarchie à 2 niveaux                |
| 22  | HIERARCHIE5                  | Y      | VARCHAR2(5)      | Code hiérarchique à 5 niveaux                       |
| 23  | HIERARCHIE5LIB               | Y      | VARCHAR2(4000)   | Libellé de la hiérarchie à 5 niveaux                |
| 24  | HIERARCHIE8                  | Y      | VARCHAR2(8)      | Code hiérarchique à 8 niveaux                       |
| 25  | HIERARCHIE8LIB               | Y      | VARCHAR2(4000)   | Libellé de la hiérarchie à 8 niveaux                |
| 26  | HIERARCHIE11                 | Y      | VARCHAR2(11)     | Code hiérarchique à 11 niveaux                      |
| 27  | HIERARCHIE11LIB              | Y      | VARCHAR2(4000)   | Libellé de la hiérarchie à 11 niveaux               |
| 28  | HIERARCHIE14                 | Y      | VARCHAR2(14)     | Code hiérarchique à 14 niveaux                      |
| 29  | HIERARCHIE14LIB              | Y      | VARCHAR2(4000)   | Libellé de la hiérarchie à 14 niveaux               |
| 30  | ICR_A                        | Y      | NUMBER           | Indice de classification pour l'acte                |
| 31  | ICR_I                        | Y      | NUMBER           | Indice de classification pour l'intervention        |
| 32  | ICRANESTHESIE_I              | Y      | NUMBER           | Indice de classification pour l'anesthésie          |
| 33  | LETTRECOEF                   | Y      | VARCHAR2(43)     | Coefficient en lettres                              |
| 34  | LIBELLEC_A                   | Y      | VARCHAR2(100)    | Libellé court de l'acte                             |
| 35  | LIBELLEL_A                   | Y      | VARCHAR2(4000)   | Libellé long de l'acte                              |
| 36  | LIBERALE_A                   | Y      | VARCHAR2(1)      | Indique si l'acte est libéral                       |
| 37  | LIBERALE_I                   | Y      | VARCHAR2(1)      | Indique si l'intervention est libérale              |
| 38  | LIBORIGINE                   | Y      | VARCHAR2(50)     | Libellé d'origine                                   |
| 39  | LIBPOLEEXECUTANT2            | Y      | VARCHAR2(50)     | Libellé du pôle exécutant secondaire                |
| 40  | LIBPOLEEXECUTANT3            | Y      | VARCHAR2(50)     | Libellé du pôle exécutant tertiaire                 |
| 41  | LIBPOLEEXECUTANT4            | Y      | VARCHAR2(50)     | Libellé du pôle exécutant quaternaire               |
| 42  | LIBPOLEPRINCIPAL             | Y      | VARCHAR2(50)     | Libellé du pôle principal                           |
| 43  | LIBPOLEUFDEMANDE             | Y      | VARCHAR2(50)     | Libellé du pôle de l'unité fonctionnelle demandeuse |
| 44  | LIBPOLEUFINTERVENTION        | Y      | VARCHAR2(50)     | Libellé du pôle de l'unité fonctionnelle d'intervention |
| 45  | LIBPRINCIPAL                 | Y      | VARCHAR2(50)     | Libellé principal                                   |
| 46  | LIBSAPRINCIPAL               | Y      | VARCHAR2(240)    | Libellé administratif principal                     |
| 47  | LIBSAUFDEMANDE               | Y      | VARCHAR2(240)    | Libellé administratif de la demande                 |
| 48  | LIBSAUFINTERVENTION          | Y      | VARCHAR2(240)    | Libellé administratif de l'intervention             |
| 49  | LIBSEEXECUTANT2              | Y      | VARCHAR2(50)     | Libellé de service exécutant secondaire             |
| 50  | LIBSEEXECUTANT3              | Y      | VARCHAR2(50)     | Libellé de service exécutant tertiaire              |
| 51  | LIBSEEXECUTANT4              | Y      | VARCHAR2(50)     | Libellé de service exécutant quaternaire            |
| 52  | LIBSEPRINCIPAL               | Y      | VARCHAR2(50)     | Libellé de service principal                        |
| 53  | LIBSEUFDEMANDE               | Y      | VARCHAR2(50)     | Libellé de service de l'unité fonctionnelle demandeuse |
| 54  | LIBSEUFINTERVENTION          | Y      | VARCHAR2(50)     | Libellé de service de l'unité fonctionnelle d'intervention |
| 55  | LIBTYPEVENUE                 | Y      | VARCHAR2(15)     | Libellé du type de venue                            |
| 56  | LIBUFDEMANDE                 | Y      | VARCHAR2(50)     | Libellé de l'unité fonctionnelle demandeuse         |
| 57  | LIBUFINTERVENTION            | Y      | VARCHAR2(50)     | Libellé de l'unité fonctionnelle d'intervention     |
| 58  | MODIFICATEUR1                | Y      | VARCHAR2(1)      | Modificateur 1                                      |
| 59  | MODIFICATEUR1LIB             | Y      | VARCHAR2(100)    | Libellé du modificateur 1                           |
| 60  | MODIFICATEUR2                | Y      | VARCHAR2(1)      | Modificateur 2                                      |
| 61  | MODIFICATEUR2LIB             | Y      | VARCHAR2(100)    | Libellé du modificateur 2                           |
| 62  | MODIFICATEUR3                | Y      | VARCHAR2(1)      | Modificateur 3                                      |
| 63  | MODIFICATEUR3LIB             | Y      | VARCHAR2(100)    | Libellé du modificateur 3                           |
| 64  | MODIFICATEUR4                | Y      | VARCHAR2(1)      | Modificateur 4                                      |
| 65  | MODIFICATEUR4LIB             | Y      | VARCHAR2(100)    | Libellé du modificateur 4                           |
| 66  | MONTANTTHEORIQUE             | Y      | NUMBER           | Montant théorique de l'acte                         |
| 67  | NUIT_A                       | Y      | VARCHAR2(1)      | Indique si l'acte a été réalisé la nuit             |
| 68  | ORIGINE_I                    | Y      | VARCHAR2(2)      | Code d'origine de l'intervention                    |
| 69  | PHASE                        | Y      | VARCHAR2(1)      | Phase de réalisation de l'acte                      |
| 70  | PHASELIB                     | Y      | VARCHAR2(100)    | Libellé de la phase                                 |

...
## Et

| #   | Nom de la colonne             | NULL ? | Type             | Description pour Supra                                  |
|-----|-------------------------------|--------|------------------|-------------------------------------------------------|
| 71  | POLEEXECUTANT2               | Y      | VARCHAR2(3)      | Pôle de l'exécutant secondaire                        |
| 72  | POLEEXECUTANT3               | Y      | VARCHAR2(3)      | Pôle de l'exécutant tertiaire                         |
| 73  | POLEEXECUTANT4               | Y      | VARCHAR2(3)      | Pôle de l'exécutant quaternaire                       |
| 74  | POLEPRINCIPAL                | Y      | VARCHAR2(3)      | Pôle principal                                        |
| 75  | POLEUFDEMANDE                | Y      | VARCHAR2(3)      | Pôle de l'unité fonctionnelle ayant fait la demande  |
| 76  | POLEUFINTERVENTION           | Y      | VARCHAR2(3)      | Pôle de l'unité fonctionnelle de l'intervention      |
| 77  | REMBOURSEMENTEXC             | Y      | VARCHAR2(1)      | Indique si le remboursement est excédentaire         |
| 78  | SAPRINCIPAL                  | Y      | VARCHAR2(10)     | Structure administrative principale associée          |
| 79  | SAUFDEMANDE                  | Y      | VARCHAR2(10)     | Structure administrative de la demande               |
| 80  | SAUFINTERVENTION             | Y      | VARCHAR2(10)     | Structure administrative de l'intervention           |
| 81  | SEEXECUTANT2                 | Y      | VARCHAR2(4)      | Service exécutant secondaire                         |
| 82  | SEEXECUTANT3                 | Y      | VARCHAR2(4)      | Service exécutant tertiaire                          |
| 83  | SEEXECUTANT4                 | Y      | VARCHAR2(4)      | Service exécutant quaternaire                        |
| 84  | SEPRINCIPAL                  | Y      | VARCHAR2(4)      | Service principal                                    |
| 85  | SEUFDEMANDE                  | Y      | VARCHAR2(4)      | Service de l'unité fonctionnelle de la demande       |
| 86  | SEUFINTERVENTION             | Y      | VARCHAR2(4)      | Service de l'unité fonctionnelle de l'intervention   |
| 87  | SEXE                         | Y      | VARCHAR2(1)      | Sexe du patient                                      |
| 88  | SEXELIB                      | Y      | VARCHAR2(8)      | Libellé du sexe du patient                           |
| 89  | TOPANESTHESIE_A              | Y      | VARCHAR2(2)      | Indique si l'acte inclut une anesthésie              |
| 90  | TOPPMSI_A                    | Y      | NUMBER           | Données liées au PMSI pour cet acte                  |
| 91  | TYPEACTE                     | Y      | VARCHAR2(4)      | Type d'acte (CCAM, NGAP, etc.)                       |
| 92  | TYPECCAM                     | Y      | VARCHAR2(100)    | Libellé CCAM pour l'acte                             |
| 93  | TYPEPRINCIPAL                | Y      | VARCHAR2(240)    | Libellé principal du type d'acte                     |
| 94  | TYPEVENUE                    | Y      | VARCHAR2(1)      | Type de venue                                        |
| 95  | UFDEMANDE                    | Y      | VARCHAR2(4)      | Code de l'unité fonctionnelle ayant fait la demande  |
| 96  | UFEXECUTANT2                 | Y      | VARCHAR2(30)     | Unité fonctionnelle exécutante secondaire            |
| 97  | UFEXECUTANT3                 | Y      | VARCHAR2(30)     | Unité fonctionnelle exécutante tertiaire             |
| 98  | UFEXECUTANT4                 | Y      | VARCHAR2(30)     | Unité fonctionnelle exécutante quaternaire           |
| 99  | UFINTERVENTION               | Y      | VARCHAR2(4)      | Unité fonctionnelle de l'intervention                |
| 100 | UFLIBEXECUTANT2              | Y      | VARCHAR2(30)     | Libellé de l'unité exécutante secondaire             |
| 101 | UFLIBEXECUTANT3              | Y      | VARCHAR2(30)     | Libellé de l'unité exécutante tertiaire              |
| 102 | UFLIBEXECUTANT4              | Y      | VARCHAR2(30)     | Libellé de l'unité exécutante quaternaire            |
| 103 | UFPRINCIPAL                  | Y      | VARCHAR2(5)      | Unité fonctionnelle principale                       |
| 104 | ANNEE_ACTE                   | Y      | VARCHAR2(4)      | Année de réalisation de l'acte                       |
| 105 | MOIS_ACTE                    | Y      | VARCHAR2(2)      | Mois de réalisation de l'acte                        |
| 106 | NB                           | Y      | NUMBER           | Nombre d'actes                                      |
| 107 | TARIF                        | Y      | NUMBER           | Tarif de l'acte                                     |
| 108 | REGROUPEMENT                 | Y      | VARCHAR2(110)    | Regroupement d'actes                                |
| 109 | CRUFDEMANDE                  | Y      | VARCHAR2(4)      | Code régional de l'unité fonctionnelle de demande   |
| 110 | LIBCRUFDEMANDE               | Y      | VARCHAR2(50)     | Libellé régional de l'unité fonctionnelle de demande|
| 111 | UMUFDEMANDE                  | Y      | VARCHAR2(4)      | Unité médico-universitaire de la demande            |
| 112 | LIBUMUFDEMANDE               | Y      | VARCHAR2(25)     | Libellé UMU de la demande                           |
| 113 | CRUFINTERVENTION             | Y      | VARCHAR2(4)      | Code régional de l'intervention                     |
| 114 | LIBCRUFINTERVENTION          | Y      | VARCHAR2(50)     | Libellé régional de l'intervention                  |
| 115 | UMUFINTERVENTION             | Y      | VARCHAR2(4)      | Unité médico-universitaire de l'intervention        |
| 116 | LIBUMUFINTERVENTION          | Y      | VARCHAR2(25)     | Libellé UMU de l'intervention                       |
| 117 | CRPRINCIPAL                  | Y      | VARCHAR2(4)      | Code régional principal                             |
| 118 | LIBCRPRINCIPAL               | Y      | VARCHAR2(50)     | Libellé régional principal                          |
| 119 | UMPRIMCIPAL                  | Y      | VARCHAR2(4)      | Unité médico-universitaire principale               |
| 120 | LIBUMPRINCIPAL               | Y      | VARCHAR2(25)     | Libellé UMU principal                               |
| 121 | CREXECUTANT2                 | Y      | VARCHAR2(4)      | Code régional de l'exécutant secondaire             |
| 122 | LIBCREXECUTANT2              | Y      | VARCHAR2(50)     | Libellé régional de l'exécutant secondaire          |
| 123 | UMEXECUTANT2                 | Y      | VARCHAR2(4)      | Unité médico-universitaire de l'exécutant secondaire|
| 124 | LIBUMEXECUTANT2              | Y      | VARCHAR2(25)     | Libellé UMU de l'exécutant secondaire               |
| 125 | CREXECUTANT3                 | Y      | VARCHAR2(4)      | Code régional de l'exécutant tertiaire              |
| 126 | LIBCREXECUTANT3              | Y      | VARCHAR2(50)     | Libellé régional de l'exécutant tertiaire           |
| 127 | UMEXECUTANT3                 | Y      | VARCHAR2(4)      | Unité médico-universitaire de l'exécutant tertiaire |
| 128 | LIBUMEXECUTANT3              | Y      | VARCHAR2(25)     | Libellé UMU de l'exécutant tertiaire                |
| 129 | CREXECUTANT4                 | Y      | VARCHAR2(4)      | Code régional de l'exécutant quaternaire            |
| 130 | LIBCREXECUTANT4              | Y      | VARCHAR2(50)     | Libellé régional de l'exécutant quaternaire         |
| 131 | UMEXECUTANT4                 | Y      | VARCHAR2(4)      | Unité médico-universitaire de l'exécutant quaternaire|
| 132 | LIBUMEXECUTANT4              | Y      | VARCHAR2(25)     | Libellé UMU de l'exécutant quaternaire              |
| 133 | URGENCEINTER                 | Y      | VARCHAR2(1)      | Indique si l'acte est une urgence                   |
| 134 | MATPAIEPRINC                 | Y      | VARCHAR2(7)      | Matricule de paiement principal                     |
