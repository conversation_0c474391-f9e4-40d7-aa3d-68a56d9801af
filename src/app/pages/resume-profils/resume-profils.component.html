<!--<app-profile-picture-->
<!--  [nom]="'Du<PERSON>'"-->
<!--  [prenom]="'<PERSON>'"-->
<!--  [status]="'online'"-->
<!--&gt;</app-profile-picture>-->

<!--<app-profile-picture-->
<!--  [nom]="'Kuban'"-->
<!--  [prenom]="'<PERSON>'"-->
<!--  [status]="'away'"-->
<!--&gt;</app-profile-picture>-->

<!--<app-profile-picture-->
<!--  [nom]="'<PERSON>'"-->
<!--  [prenom]="'Alice'"-->
<!--  [status]="'offline'"-->
<!--&gt;</app-profile-picture>-->


<div class="py-6 md:flex md:items-center md:justify-between lg:border-gray-200">
  <div class="flex-1 min-w-0">
    <!-- Profile -->
    <div class="flex items-center">
      <!-- Visible uniquement sur les écrans lg et plus -->
      <div  class="hidden lg:block">
        <app-profile-picture
          [nom]="'Olivier'"
          [prenom]="'Morel'"
          [status]="'offline'"
        ></app-profile-picture>
      </div>




      <div>
        <div class="flex items-center">
          <!-- Visible uniquement sur les écrans sm -->
          <div class="block lg:hidden">
            <app-profile-picture
              [nom]="'Morel'"
              [prenom]="'Olivier'"
              [status]="'online'"
            ></app-profile-picture>
          </div>
          <h1 class="ml-3 text-2xl font-bold leading-7 text-gray-900 sm:leading-9 sm:truncate">
            Dr Gabriel Lemoine
          </h1>
        </div>
        <dl class="mt-6 flex flex-col sm:ml-3 sm:mt-1 sm:flex-row sm:flex-wrap">
          <dt class="sr-only">Pole</dt>
          <dd class="flex items-center text-sm text-gray-500 font-medium capitalize sm:mr-6">
            <svg
              class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
                clip-rule="evenodd"
              />
            </svg>
            POLE : BLOCS OPERATOIRES
          </dd>

          <dt class="sr-only">Service</dt>
          <dd class="flex items-center text-sm text-gray-500 font-medium capitalize sm:mr-6">
            <svg
              class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M6 3a1 1 0 00-1 1v2H3a1 1 0 00-1 1v9a1 1 0 001 1h14a1 1 0 001-1V7a1 1 0 00-1-1h-2V4a1 1 0 00-1-1H6zm0 2V4h8v1H6z"
                clip-rule="evenodd"
              />
            </svg>
            SERVICE : BLOC OPERATOIRE MAT
          </dd>

          <dt class="sr-only">Unite Fonctionnel</dt>
          <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize">
            <svg
              class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              />
            </svg>
            UF D'AFFECTATION : 6261 - CPDP
          </dd>

          <dt class="sr-only">Grade</dt>
          <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize">
            <!-- Heroicon name: academic-cap -->
            <svg  class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >
              <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-********* 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5" />
            </svg>

            Grade: PU/PH
          </dd>

          <dt class="sr-only">ETP statutaire</dt>
          <dd class="mt-3 flex items-center text-sm text-gray-500 font-medium sm:mr-6 sm:mt-0 capitalize">
            <!-- Heroicon name: puzzle-piece -->
            <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M14.25 6.087c0-.355.186-.676.401-.959.221-.29.349-.634.349-1.003 0-1.036-1.007-1.875-2.25-1.875s-2.25.84-2.25 1.875c0 .369.128.713.349 1.003.215.283.401.604.401.959v0a.64.64 0 0 1-.657.643 48.39 48.39 0 0 1-4.163-.3c.186 1.613.293 3.25.315 4.907a.656.656 0 0 1-.658.663v0c-.355 0-.676-.186-.959-.401a1.647 1.647 0 0 0-1.003-.349c-1.036 0-1.875 1.007-1.875 2.25s.84 2.25 1.875 2.25c.369 0 .713-.128 1.003-.349.283-.215.604-.401.959-.401v0c.31 0 .555.26.532.57a48.039 48.039 0 0 1-.642 5.056c1.518.19 3.058.309 4.616.354a.64.64 0 0 0 .657-.643v0c0-.355-.186-.676-.401-.959a1.647 1.647 0 0 1-.349-1.003c0-1.035 1.008-1.875 2.25-1.875 1.243 0 2.25.84 2.25 1.875 0 .369-.128.713-.349 1.003-.215.283-.4.604-.4.959v0c0 .333.277.599.61.58a48.1 48.1 0 0 0 5.427-.63 48.05 48.05 0 0 0 .582-4.717.532.532 0 0 0-.533-.57v0c-.355 0-.676.186-.959.401-.29.221-.634.349-1.003.349-1.035 0-1.875-1.007-1.875-2.25s.84-2.25 1.875-2.25c.37 0 .713.128 1.003.349.283.215.604.401.96.401v0a.656.656 0 0 0 .658-.663 48.422 48.422 0 0 0-.37-5.36c-1.886.342-3.81.574-5.766.689a.578.578 0 0 1-.61-.58v0Z" />
            </svg>
            ETP : 1
          </dd>
        </dl>
      </div>
    </div>
  </div>
  <div class="mt-6 flex space-x-3 md:mt-0 md:ml-4">
    <button
      type="button"
      class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
      (click)="displayModal = true"
    >
      Imprimer un rapport
    </button>
  </div>
</div>




<!-- Modal PrimeNG -->
<p-dialog [(visible)]="displayModal" modal [closable]="true" [style]="{width: '450px'}" [draggable]="false" [dismissableMask]="true">
  <ng-template pTemplate="header">
    <div class="flex items-center space-x-2">
      <i class="pi pi-info-circle text-blue-600 text-xl"></i>
      <h3 class="text-lg font-semibold text-gray-800">Information</h3>
    </div>
  </ng-template>

  <div class="flex flex-col items-center text-center p-4">
    <i class="pi pi-file text-gray-400 text-6xl mb-3"></i>
    <p class="text-gray-700 text-md">
      Nous travaillons actuellement pour déterminer quelles données restent pertinentes pour une impression.
    </p>
  </div>

  <ng-template pTemplate="footer">
    <div class="flex justify-end w-full">
      <button pButton type="button" class="p-button-rounded p-button-sm p-button-secondary" (click)="displayModal = false">
        <i class="pi pi-times"></i> Fermer
      </button>
    </div>
  </ng-template>
</p-dialog>

