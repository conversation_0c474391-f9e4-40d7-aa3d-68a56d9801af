<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  ActeDetailsComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/pages/acte-details/acte-details.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-acte-details</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>ChartModule</code>
                            <code>StyleClassModule</code>
                            <code>PrimeTemplate</code>
                            <code>TableModule</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./acte-details.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./acte-details.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#acteParPraticienSummary" >acteParPraticienSummary</a>
                            </li>
                            <li>
                                <a href="#acteParUfSummary" >acteParUfSummary</a>
                            </li>
                            <li>
                                <a href="#acteTitle" >acteTitle</a>
                            </li>
                            <li>
                                <a href="#anneeN" >anneeN</a>
                            </li>
                            <li>
                                <a href="#anneeNmoinsUn" >anneeNmoinsUn</a>
                            </li>
                            <li>
                                <a href="#chartOptions" >chartOptions</a>
                            </li>
                            <li>
                                <a href="#groupedBarChartData" >groupedBarChartData</a>
                            </li>
                            <li>
                                <a href="#totalNbActePraticienAnneeN" >totalNbActePraticienAnneeN</a>
                            </li>
                            <li>
                                <a href="#totalNbActePraticienAnneeNmoinsUn" >totalNbActePraticienAnneeNmoinsUn</a>
                            </li>
                            <li>
                                <a href="#totalNbActeUfAnneeN" >totalNbActeUfAnneeN</a>
                            </li>
                            <li>
                                <a href="#totalNbActeUfAnneeNmoinsUn" >totalNbActeUfAnneeNmoinsUn</a>
                            </li>
                            <li>
                                <a href="#totalPartPraticienAnneeN" >totalPartPraticienAnneeN</a>
                            </li>
                            <li>
                                <a href="#totalPartPraticienAnneeNmoinsUn" >totalPartPraticienAnneeNmoinsUn</a>
                            </li>
                            <li>
                                <a href="#totalPartUfAnneeN" >totalPartUfAnneeN</a>
                            </li>
                            <li>
                                <a href="#totalPartUfAnneeNmoinsUn" >totalPartUfAnneeNmoinsUn</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#initGroupedGraph" >initGroupedGraph</a>
                            </li>
                            <li>
                                <a href="#loadActeData" >loadActeData</a>
                            </li>
                            <li>
                                <a href="#loadActeParPraticienSummary" >loadActeParPraticienSummary</a>
                            </li>
                            <li>
                                <a href="#loadActeParUfSummary" >loadActeParUfSummary</a>
                            </li>
                            <li>
                                <a href="#loadActeParUfSummary444" >loadActeParUfSummary444</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#onInput" >onInput</a>
                            </li>
                            <li>
                                <a href="#truncateActeDescription" >truncateActeDescription</a>
                            </li>
                            <li>
                                <a href="#viewPraticienDetails" >viewPraticienDetails</a>
                            </li>
                            <li>
                                <a href="#viewUfDetails" >viewUfDetails</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(acteOverviewService: ActeOverviewService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="41" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:41</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>acteOverviewService</td>

                                                        <td>
                                                                    <code>ActeOverviewService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initGroupedGraph"></a>
                    <span class="name">
                        <span ><b>initGroupedGraph</b></span>
                        <a href="#initGroupedGraph"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initGroupedGraph(ufs: any[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="99"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:99</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ufs</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadActeData"></a>
                    <span class="name">
                        <span ><b>loadActeData</b></span>
                        <a href="#loadActeData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadActeData()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="81"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:81</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadActeParPraticienSummary"></a>
                    <span class="name">
                        <span ><b>loadActeParPraticienSummary</b></span>
                        <a href="#loadActeParPraticienSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadActeParPraticienSummary(praticiens: any[])</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="200"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:200</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>praticiens</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadActeParUfSummary"></a>
                    <span class="name">
                        <span ><b>loadActeParUfSummary</b></span>
                        <a href="#loadActeParUfSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadActeParUfSummary(ufs: any[], actesDetails: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="145"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:145</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ufs</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>actesDetails</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadActeParUfSummary444"></a>
                    <span class="name">
                        <span ><b>loadActeParUfSummary444</b></span>
                        <a href="#loadActeParUfSummary444"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadActeParUfSummary444(ufs: any[], actesDetails: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="117"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:117</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>ufs</td>
                                            <td>
                                                        <code>any[]</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>actesDetails</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="51"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:51</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onInput"></a>
                    <span class="name">
                        <span ><b>onInput</b></span>
                        <a href="#onInput"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onInput(event: Event)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="246"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:246</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                        <code>Event</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="truncateActeDescription"></a>
                    <span class="name">
                        <span ><b>truncateActeDescription</b></span>
                        <a href="#truncateActeDescription"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>truncateActeDescription(acte: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="259"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:259</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>acte</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="viewPraticienDetails"></a>
                    <span class="name">
                        <span ><b>viewPraticienDetails</b></span>
                        <a href="#viewPraticienDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>viewPraticienDetails(praticien: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="255"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:255</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>praticien</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="viewUfDetails"></a>
                    <span class="name">
                        <span ><b>viewUfDetails</b></span>
                        <a href="#viewUfDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>viewUfDetails(uf: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="251"
                                    class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:251</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uf</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="acteParPraticienSummary"></a>
                    <span class="name">
                        <span ><b>acteParPraticienSummary</b></span>
                        <a href="#acteParPraticienSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="27" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:27</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="acteParUfSummary"></a>
                    <span class="name">
                        <span ><b>acteParUfSummary</b></span>
                        <a href="#acteParUfSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>any[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="26" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:26</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="acteTitle"></a>
                    <span class="name">
                        <span ><b>acteTitle</b></span>
                        <a href="#acteTitle"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="22" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:22</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="anneeN"></a>
                    <span class="name">
                        <span ><b>anneeN</b></span>
                        <a href="#anneeN"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:29</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="anneeNmoinsUn"></a>
                    <span class="name">
                        <span ><b>anneeNmoinsUn</b></span>
                        <a href="#anneeNmoinsUn"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="28" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:28</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chartOptions"></a>
                    <span class="name">
                        <span ><b>chartOptions</b></span>
                        <a href="#chartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="24" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:24</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="groupedBarChartData"></a>
                    <span class="name">
                        <span ><b>groupedBarChartData</b></span>
                        <a href="#groupedBarChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="23" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:23</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalNbActePraticienAnneeN"></a>
                    <span class="name">
                        <span ><b>totalNbActePraticienAnneeN</b></span>
                        <a href="#totalNbActePraticienAnneeN"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="41" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:41</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalNbActePraticienAnneeNmoinsUn"></a>
                    <span class="name">
                        <span ><b>totalNbActePraticienAnneeNmoinsUn</b></span>
                        <a href="#totalNbActePraticienAnneeNmoinsUn"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="40" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:40</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalNbActeUfAnneeN"></a>
                    <span class="name">
                        <span ><b>totalNbActeUfAnneeN</b></span>
                        <a href="#totalNbActeUfAnneeN"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="34" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:34</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalNbActeUfAnneeNmoinsUn"></a>
                    <span class="name">
                        <span ><b>totalNbActeUfAnneeNmoinsUn</b></span>
                        <a href="#totalNbActeUfAnneeNmoinsUn"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>0</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="33" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:33</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartPraticienAnneeN"></a>
                    <span class="name">
                        <span ><b>totalPartPraticienAnneeN</b></span>
                        <a href="#totalPartPraticienAnneeN"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;100%&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:39</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartPraticienAnneeNmoinsUn"></a>
                    <span class="name">
                        <span ><b>totalPartPraticienAnneeNmoinsUn</b></span>
                        <a href="#totalPartPraticienAnneeNmoinsUn"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;100%&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:38</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartUfAnneeN"></a>
                    <span class="name">
                        <span ><b>totalPartUfAnneeN</b></span>
                        <a href="#totalPartUfAnneeN"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;100%&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:36</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="totalPartUfAnneeNmoinsUn"></a>
                    <span class="name">
                        <span ><b>totalPartUfAnneeNmoinsUn</b></span>
                        <a href="#totalPartUfAnneeNmoinsUn"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;100%&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/app/pages/acte-details/acte-details.component.ts:35</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {StyleClassModule} from &quot;primeng/styleclass&quot;;
import {PrimeTemplate} from &quot;primeng/api&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {ActeOverviewService} from &quot;../../core/services/overview/acte-overview.service&quot;;
import {ActeSummary} from &quot;../../core/models/overview/acte-overview.model&quot;;

@Component({
  selector: &#x27;app-acte-details&#x27;,
  standalone: true,
  imports: [
    ChartModule,
    StyleClassModule,
    PrimeTemplate,
    TableModule
  ],
  templateUrl: &#x27;./acte-details.component.html&#x27;,
  styleUrl: &#x27;./acte-details.component.scss&#x27;
})
export class ActeDetailsComponent  implements OnInit{
  acteTitle: string &#x3D; &#x27;&#x27;; // Titre dynamique
  groupedBarChartData: any;
  chartOptions: any;

  acteParUfSummary: any[] &#x3D; []; // Données pour le tableau des UF
  acteParPraticienSummary: any[] &#x3D; []; // Données pour le tableau des praticiens
  anneeNmoinsUn: string &#x3D; &#x27;&#x27;; // Année N-1
  anneeN: string &#x3D; &#x27;&#x27;; // Année N


  //----
  totalNbActeUfAnneeNmoinsUn: number &#x3D; 0; // Total UF N-1
  totalNbActeUfAnneeN: number &#x3D; 0; // Total UF N
  totalPartUfAnneeNmoinsUn: string &#x3D; &#x27;100%&#x27;;
  totalPartUfAnneeN: string &#x3D; &#x27;100%&#x27;;

  totalPartPraticienAnneeNmoinsUn: string &#x3D; &#x27;100%&#x27;;
  totalPartPraticienAnneeN: string &#x3D; &#x27;100%&#x27;;
  totalNbActePraticienAnneeNmoinsUn: number &#x3D; 0; // Total praticien N-1
  totalNbActePraticienAnneeN: number &#x3D; 0; // Total praticien N

  //--


  constructor(
    private acteOverviewService:ActeOverviewService
  ) {
  }

  ngOnInit(): void {
    this.chartOptions &#x3D; {
      responsive: true,
      maintainAspectRatio: false,
      aspectRatio: 0.9,
      plugins: {
        legend: {
          position: &#x27;top&#x27;,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: &#x27;Unités Fonctionnelles (UF)&#x27;,
          },
        },
        y: {
          title: {
            display: true,
            text: &#x27;Nombre d\&#x27;actes&#x27;,
          },
          beginAtZero: true,
        },
      },
    };

    this.loadActeData();
  }

  loadActeData(): void {
    this.acteOverviewService.acteSummary$.subscribe((data: ActeSummary | null) &#x3D;&gt; {
      if (data) {
        // Titre et années
        this.acteTitle &#x3D; &#x60;${data.actesDetails.code} - ${data.actesDetails.description}&#x60;;
        this.anneeNmoinsUn &#x3D; data.anneeNmoinsUn;
        this.anneeN &#x3D; data.anneeN;

        // Données pour le graphique
        this.initGroupedGraph(data.ufs);

        // Données pour les tableaux
        this.loadActeParUfSummary(data.ufs, data.actesDetails);
        this.loadActeParPraticienSummary(data.acteParPraticienSummary);
      }
    });
  }

  initGroupedGraph(ufs: any[]): void {
    this.groupedBarChartData &#x3D; {
      labels: ufs.map((uf) &#x3D;&gt; uf.nomUF),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ufs.map((uf) &#x3D;&gt; uf.nbActesAnneeNmoinsUn),
          backgroundColor: &#x27;#42A5F5&#x27;,
        },
        {
          label: this.anneeN,
          data: ufs.map((uf) &#x3D;&gt; uf.nbActesAnneeN),
          backgroundColor: &#x27;#FFA726&#x27;,
        },
      ],
    };
  }

  loadActeParUfSummary444(ufs: any[], actesDetails: any): void {
    // Calcul des totaux UF pour N-1 et N
    this.totalNbActeUfAnneeNmoinsUn &#x3D; ufs.reduce((sum, uf) &#x3D;&gt; sum + uf.nbActesAnneeNmoinsUn, 0);
    this.totalNbActeUfAnneeN &#x3D; ufs.reduce((sum, uf) &#x3D;&gt; sum + uf.nbActesAnneeN, 0);

    //

    this.acteParUfSummary &#x3D; ufs.map((uf) &#x3D;&gt; ({
      nom: uf.nomUF,
      actesNmoinsUn: uf.nbActesAnneeNmoinsUn,
      partUfNmoinsUn: ((uf.nbActesAnneeNmoinsUn / this.totalNbActeUfAnneeNmoinsUn ) * 100).toFixed(2) + &#x27;%&#x27;,
      actesAnneeN: uf.nbActesAnneeN,
      partUfAnneeN: Math.min((uf.nbActesAnneeN / this.totalNbActeUfAnneeN) * 100).toFixed(2) + &#x27;%&#x27;,
    }));

    // Calcul des totaux des parts
    this.totalPartUfAnneeNmoinsUn &#x3D; Math.min(
      100,
      this.acteParUfSummary.reduce((sum, uf) &#x3D;&gt; sum + parseFloat(uf.partUfNmoinsUn), 0)
    ).toFixed(2) + &#x27;%&#x27;;

    this.totalPartUfAnneeN &#x3D; Math.min(
      100,
      this.acteParUfSummary.reduce((sum, uf) &#x3D;&gt; sum + parseFloat(uf.partUfAnneeN), 0)
    ).toFixed(2) + &#x27;%&#x27;;

  }

  loadActeParUfSummary(ufs: any[], actesDetails: any): void {
    // Calcul des totaux UF pour N-1 et N
    this.totalNbActeUfAnneeNmoinsUn &#x3D; ufs.reduce((sum, uf) &#x3D;&gt; sum + uf.nbActesAnneeNmoinsUn, 0);
    this.totalNbActeUfAnneeN &#x3D; ufs.reduce((sum, uf) &#x3D;&gt; sum + uf.nbActesAnneeN, 0);

    // Préparer les données sans arrondi pour ajuster la dernière valeur
    let partsNmoinsUn: number[] &#x3D; [];
    let partsAnneeN: number[] &#x3D; [];
    let totalPartsNmoinsUn &#x3D; 0;
    let totalPartsAnneeN &#x3D; 0;

    // Calcul initial des parts
    this.acteParUfSummary &#x3D; ufs.map((uf, index) &#x3D;&gt; {
      const partNmoinsUn &#x3D; (uf.nbActesAnneeNmoinsUn / this.totalNbActeUfAnneeNmoinsUn) * 100;
      const partAnneeN &#x3D; (uf.nbActesAnneeN / this.totalNbActeUfAnneeN) * 100;

      partsNmoinsUn.push(partNmoinsUn);
      partsAnneeN.push(partAnneeN);

      totalPartsNmoinsUn +&#x3D; partNmoinsUn;
      totalPartsAnneeN +&#x3D; partAnneeN;

      return {
        nom: uf.nomUF,
        actesNmoinsUn: uf.nbActesAnneeNmoinsUn,
        actesAnneeN: uf.nbActesAnneeN,
        partUfNmoinsUn: 0, // Placeholder
        partUfAnneeN: 0, // Placeholder
      };
    });

    // Ajuster la dernière valeur pour que le total soit exactement 100 %
    const diffNmoinsUn &#x3D; 100 - totalPartsNmoinsUn;
    const diffAnneeN &#x3D; 100 - totalPartsAnneeN;

    if (this.acteParUfSummary.length &gt; 0) {
      partsNmoinsUn[partsNmoinsUn.length - 1] +&#x3D; diffNmoinsUn;
      partsAnneeN[partsAnneeN.length - 1] +&#x3D; diffAnneeN;
    }

    // Appliquer les arrondis finaux
    this.acteParUfSummary &#x3D; this.acteParUfSummary.map((item, index) &#x3D;&gt; ({
      ...item,
      partUfNmoinsUn: partsNmoinsUn[index].toFixed(2) + &#x27;%&#x27;,
      partUfAnneeN: partsAnneeN[index].toFixed(2) + &#x27;%&#x27;,
    }));

    // Calcul des totaux des parts
    this.totalPartUfAnneeNmoinsUn &#x3D; &#x27;100.00%&#x27;;
    this.totalPartUfAnneeN &#x3D; &#x27;100.00%&#x27;;
  }




  loadActeParPraticienSummary(praticiens: any[]): void {
    // Calcul des totaux des actes pour N-1 et N
    this.totalNbActePraticienAnneeNmoinsUn &#x3D; praticiens.reduce(
      (sum, praticien) &#x3D;&gt; sum + praticien.nbActesAnneeNmoinsUn,
      0
    );
    this.totalNbActePraticienAnneeN &#x3D; praticiens.reduce(
      (sum, praticien) &#x3D;&gt; sum + praticien.nbActesAnneeN,
      0
    );

    // Calcul des parts pour chaque praticien
    this.acteParPraticienSummary &#x3D; praticiens.map((praticien) &#x3D;&gt; {
      const partAnneeNmoinsUn &#x3D; this.totalNbActePraticienAnneeNmoinsUn
        ? ((praticien.nbActesAnneeNmoinsUn / this.totalNbActePraticienAnneeNmoinsUn) * 100).toFixed(2) + &#x27;%&#x27;
        : &#x27;0%&#x27;;

      const partAnneeN &#x3D; this.totalNbActePraticienAnneeN
        ? ((praticien.nbActesAnneeN / this.totalNbActePraticienAnneeN) * 100).toFixed(2) + &#x27;%&#x27;
        : &#x27;0%&#x27;;

      return {
        ...praticien,
        partAnneeNmoinsUn,
        partAnneeN,
      };
    });

    // Calcul des totaux des parts (si nécessaire)
    this.totalPartPraticienAnneeNmoinsUn &#x3D; Math.min(
      100,
      this.acteParPraticienSummary.reduce(
        (sum, praticien) &#x3D;&gt; sum + parseFloat(praticien.partAnneeNmoinsUn.replace(&#x27;%&#x27;, &#x27;&#x27;)),
        0
      )
    ).toFixed(2) + &#x27;%&#x27;;

    this.totalPartPraticienAnneeN &#x3D; Math.min(
      100,
      this.acteParPraticienSummary.reduce(
        (sum, praticien) &#x3D;&gt; sum + parseFloat(praticien.partAnneeN.replace(&#x27;%&#x27;, &#x27;&#x27;)),
        0
      )
    ).toFixed(2) + &#x27;%&#x27;;
  }

  onInput(event: Event): string {
    console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }

  viewUfDetails(uf: any) {
    alert(&#x60;Voir les détails pour l&#x27;UF : ${uf.nom}&#x60;);
  }

  viewPraticienDetails(praticien: any): void {
    console.log(&#x27;Voir les détails pour le praticien :&#x27;, praticien.nom, praticien.prenom);
  }

  truncateActeDescription(acte: string): string {
    const words &#x3D; acte.split(&#x27; &#x27;);
    if (words.length &gt; 2) {
      return words.slice(0, 3).join(&#x27; &#x27;) + &#x27;...&#x27;;
    }
    return acte;
  }
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;section&gt;
  &lt;div class&#x3D;&quot;mb-6&quot;&gt;
    &lt;h2 class&#x3D;&quot;text-2xl font-bold text-cyan-700 text-center&quot;&gt;
      {{ acteTitle }}
    &lt;/h2&gt;
  &lt;/div&gt;
  &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg&quot;&gt;
    &lt;!-- Graphique en barres groupées --&gt;
    &lt;div class&#x3D;&quot;col-span-12 bg-white p-4 shadow rounded-lg&quot;&gt;
      &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
        Comparaison du nombre d&#x27;actes par UF pour la période 2023 et 2024
      &lt;/h5&gt;
      &lt;p-chart
        type&#x3D;&quot;bar&quot;
        [data]&#x3D;&quot;groupedBarChartData&quot;
        [options]&#x3D;&quot;chartOptions&quot;
        pStyleClass&#x3D;&quot;w-full h-96&quot;
      &gt;&lt;/p-chart&gt;
    &lt;/div&gt;
  &lt;/div&gt;

&lt;/section&gt;

&lt;!-- Détails des actes réalisés par UF pour la période 2023 et 2024--&gt;
&lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
  &lt;h2 class&#x3D;&quot;text-2xl font-bold text-cyan-700 mb-4&quot;&gt;
    Détails des actes réalisés par UF pour la période 2023 et 2024
  &lt;/h2&gt;
  &lt;div class&#x3D;&quot;p-card&quot;&gt;
    &lt;p-table
      #acteParUfTable
      [value]&#x3D;&quot;acteParUfSummary&quot;
      [paginator]&#x3D;&quot;true&quot;
      [rows]&#x3D;&quot;10&quot;
      [rowsPerPageOptions]&#x3D;&quot;[10, 25, 50]&quot;
      [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;nom&#x27;, &#x27;nbActesAnneeNmoinsUn&#x27;,&#x27;nbActesAnneeN&#x27;]&quot;
      class&#x3D;&quot;min-w-full bg-white border rounded-lg shadow&quot;
    &gt;
      &lt;!-- Caption with Title, Total, and Search Input --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;caption&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between items-center p-3 rounded-t-lg&quot;&gt;
          &lt;div class&#x3D;&quot;text-lg font-bold text-cyan-700 flex items-center&quot;&gt;
            &lt;!--             drop down hear if needed--&gt;
          &lt;/div&gt;
          &lt;div&gt;
            &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
              &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
          &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
            &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
          &lt;/span&gt;
              &lt;/div&gt;
              &lt;input
                type&#x3D;&quot;text&quot;
                name&#x3D;&quot;search&quot;
                (input)&#x3D;&quot;acteParUfTable.filterGlobal( onInput($event), &#x27;contains&#x27;)&quot;
                class&#x3D;&quot;block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm&quot;
                placeholder&#x3D;&quot;Filtrer par nom UF&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/ng-template&gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr&gt;
          &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            &lt;i
              class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
              title&#x3D;&quot;Voir les détails de l&#x27;acte&quot;
            &gt;&lt;/i&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nom&quot; class&#x3D;&quot;px-4 py-2 border-b text-left font-semibold&quot;&gt;
            UF
            &lt;p-sortIcon field&#x3D;&quot;nom&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;

          &lt;th pSortableColumn&#x3D;&quot;nbActesAnneeNmoinsUn&quot; class&#x3D;&quot;px-4 py-2 border text-center font-semibold&quot;&gt;
            {{ anneeNmoinsUn }}
            &lt;p-sortIcon field&#x3D;&quot;nbActesAnneeNmoinsUn&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;NB actes&lt;/span&gt;
              &lt;span&gt;Part de l&#x27;UF&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesAnneeN&quot; class&#x3D;&quot;px-4 py-2 border text-center font-semibold&quot;&gt;
            {{anneeN}}
            &lt;p-sortIcon field&#x3D;&quot;nbActesAnneeN&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;NB actes&lt;/span&gt;
              &lt;span&gt;Part de l&#x27;UF&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-uf&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                title&#x3D;&quot;Voir les détails de {{uf.nom}}&quot;
                (click)&#x3D;&quot;viewUfDetails(uf)&quot;
              &gt;&lt;/i&gt;
            &lt;/p&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2&quot;&gt;{{ uf.nom }}&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 border text-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ uf.actesNmoinsUn || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ uf.partUfNmoinsUn || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 border text-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ uf.actesAnneeN || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ uf.partUfAnneeN || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
        &lt;tr class&#x3D;&quot;font-semibold bg-gray-100&quot;&gt;
          &lt;td colspan&#x3D;&quot;2&quot; class&#x3D;&quot;px-4 py-2 text-left&quot;&gt;TOTAL&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalNbActeUfAnneeNmoinsUn || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartUfAnneeNmoinsUn || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalNbActeUfAnneeN || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartUfAnneeN || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;
  &lt;/div&gt;

&lt;/section&gt;

&lt;!-- Détails des actes réalisés par praticien pour la période 2023 et 2024 --&gt;
&lt;section class&#x3D;&quot;mb-8 mt-8&quot;&gt;
  &lt;h2 class&#x3D;&quot;text-2xl font-bold text-cyan-700 mb-4&quot;&gt;
    Détails des actes réalisés par Praticien pour la période 2023 et 2024
  &lt;/h2&gt;

  &lt;div class&#x3D;&quot;p-card&quot;&gt;
    &lt;p-table
      #acteParPraticien
      [value]&#x3D;&quot;acteParPraticienSummary&quot;
      [paginator]&#x3D;&quot;true&quot;
      [rows]&#x3D;&quot;10&quot;
      [rowsPerPageOptions]&#x3D;&quot;[10, 25, 50]&quot;
      [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;nom&#x27;, &#x27;prenom&#x27;, &#x27;nbActesAnneeNmoinsUn&#x27;,&#x27;nbActesAnneeN&#x27;]&quot;
      class&#x3D;&quot;min-w-full bg-white border rounded-lg shadow&quot;
    &gt;
      &lt;!-- Caption with Search Input --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;caption&quot;&gt;
        &lt;div class&#x3D;&quot;flex justify-between items-center p-3 rounded-t-lg&quot;&gt;
          &lt;div class&#x3D;&quot;text-lg font-bold text-cyan-700 flex items-center&quot;&gt;
            &lt;!-- Placeholder for dropdown or additional controls --&gt;
          &lt;/div&gt;
          &lt;div&gt;
            &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
              &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
                &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                  &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
                &lt;/span&gt;
              &lt;/div&gt;
              &lt;input
                type&#x3D;&quot;text&quot;
                name&#x3D;&quot;search&quot;
                (input)&#x3D;&quot;acteParPraticien.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
                class&#x3D;&quot;block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
                  ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset
                  focus:ring-indigo-600 sm:text-sm&quot;
                placeholder&#x3D;&quot;Filtrer par nom ou prénom&quot;
              /&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/ng-template&gt;

      &lt;!-- Table Header --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr&gt;
          &lt;th class&#x3D;&quot;px-4 py-2 border-b text-left text-gray-700 font-semibold&quot;&gt;
            &lt;i
              class&#x3D;&quot;pi pi-hashtag cursor-pointer ml-2&quot;
              title&#x3D;&quot;Voir les détails du praticien&quot;
            &gt;&lt;/i&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nom&quot; class&#x3D;&quot;px-4 py-2 border-b text-left font-semibold&quot;&gt;
            Nom
            &lt;p-sortIcon field&#x3D;&quot;nom&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;prenom&quot; class&#x3D;&quot;px-4 py-2 border-b text-left font-semibold&quot;&gt;
            Prénom
            &lt;p-sortIcon field&#x3D;&quot;prenom&quot;&gt;&lt;/p-sortIcon&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesAnneeNmoinsUn&quot; class&#x3D;&quot;px-4 py-2 border text-center font-semibold&quot;&gt;
            {{ anneeNmoinsUn }}
            &lt;p-sortIcon field&#x3D;&quot;nbActesAnneeNmoinsUn&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;NB actes&lt;/span&gt;
              &lt;span&gt;Part du praticien&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;nbActesAnneeN&quot; class&#x3D;&quot;px-4 py-2 border text-center font-semibold&quot;&gt;
            {{ anneeN }}
            &lt;p-sortIcon field&#x3D;&quot;nbActesAnneeN&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;NB actes&lt;/span&gt;
              &lt;span&gt;Part du praticien&lt;/span&gt;
            &lt;/div&gt;
          &lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;!-- Table Body --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-praticien&gt;
        &lt;tr class&#x3D;&quot;border-b hover:bg-gray-50&quot;&gt;
          &lt;td style&#x3D;&quot;padding: 0.75rem; text-align: left;&quot;&gt;
            &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
              &lt;i
                class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                title&#x3D;&quot;Voir les détails de {{ praticien.nom }} {{ praticien.prenom }}&quot;
                (click)&#x3D;&quot;viewPraticienDetails(praticien)&quot;
              &gt;&lt;/i&gt;
            &lt;/p&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2&quot;&gt;{{ praticien.nom }}&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2&quot;&gt;{{ praticien.prenom }}&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 border text-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ praticien.nbActesAnneeNmoinsUn || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ praticien.partAnneeNmoinsUn || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 border text-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ praticien.nbActesAnneeN || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ praticien.partAnneeN || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;

      &lt;!-- Footer --&gt;
      &lt;ng-template pTemplate&#x3D;&quot;footer&quot;&gt;
        &lt;tr class&#x3D;&quot;font-semibold bg-gray-100&quot;&gt;
          &lt;td colspan&#x3D;&quot;3&quot; class&#x3D;&quot;px-4 py-2 text-left&quot;&gt;TOTAL&lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalNbActePraticienAnneeNmoinsUn || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartPraticienAnneeNmoinsUn || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
          &lt;td class&#x3D;&quot;px-4 py-2 text-center border&quot;&gt;
            &lt;div class&#x3D;&quot;flex justify-center space-x-8&quot;&gt;
              &lt;span&gt;{{ totalNbActePraticienAnneeN || &quot;0&quot; }}&lt;/span&gt;
              &lt;span&gt;{{ totalPartPraticienAnneeN || &quot;0%&quot; }}&lt;/span&gt;
            &lt;/div&gt;
          &lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;
  &lt;/div&gt;
&lt;/section&gt;

</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><section>  <div class="mb-6">    <h2 class="text-2xl font-bold text-cyan-700 text-center">      {{ acteTitle }}    </h2>  </div>  <div class="grid grid-cols-12 gap-4 p-4 bg-gray-100 rounded-lg">    <!-- Graphique en barres groupées -->    <div class="col-span-12 bg-white p-4 shadow rounded-lg">      <h5 class="text-lg font-bold text-gray-700 mb-3">        Comparaison du nombre d\'actes par UF pour la période 2023 et 2024      </h5>      <p-chart        type="bar"        [data]="groupedBarChartData"        [options]="chartOptions"        pStyleClass="w-full h-96"      ></p-chart>    </div>  </div></section><!-- Détails des actes réalisés par UF pour la période 2023 et 2024--><section class="mb-8 mt-8">  <h2 class="text-2xl font-bold text-cyan-700 mb-4">    Détails des actes réalisés par UF pour la période 2023 et 2024  </h2>  <div class="p-card">    <p-table      #acteParUfTable      [value]="acteParUfSummary"      [paginator]="true"      [rows]="10"      [rowsPerPageOptions]="[10, 25, 50]"      [responsiveLayout]="\'scroll\'"      [globalFilterFields]="[\'nom\', \'nbActesAnneeNmoinsUn\',\'nbActesAnneeN\']"      class="min-w-full bg-white border rounded-lg shadow"    >      <!-- Caption with Title, Total, and Search Input -->      <ng-template pTemplate="caption">        <div class="flex justify-between items-center p-3 rounded-t-lg">          <div class="text-lg font-bold text-cyan-700 flex items-center">            <!--             drop down hear if needed-->          </div>          <div>            <div class="relative mt-2 rounded-md shadow-sm">              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">          <span class="text-gray-500 sm:text-sm mr-3">            <i class="pi pi-search"></i>          </span>              </div>              <input                type="text"                name="search"                (input)="acteParUfTable.filterGlobal( onInput($event), \'contains\')"                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm"                placeholder="Filtrer par nom UF"              />            </div>          </div>        </div>      </ng-template>      <ng-template pTemplate="header">        <tr>          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            <i              class="pi pi-hashtag cursor-pointer ml-2"              title="Voir les détails de l\'acte"            ></i>          </th>          <th pSortableColumn="nom" class="px-4 py-2 border-b text-left font-semibold">            UF            <p-sortIcon field="nom"></p-sortIcon>          </th>          <th pSortableColumn="nbActesAnneeNmoinsUn" class="px-4 py-2 border text-center font-semibold">            {{ anneeNmoinsUn }}            <p-sortIcon field="nbActesAnneeNmoinsUn"></p-sortIcon>            <div class="flex justify-center space-x-8">              <span>NB actes</span>              <span>Part de l\'UF</span>            </div>          </th>          <th pSortableColumn="nbActesAnneeN" class="px-4 py-2 border text-center font-semibold">            {{anneeN}}            <p-sortIcon field="nbActesAnneeN"></p-sortIcon>            <div class="flex justify-center space-x-8">              <span>NB actes</span>              <span>Part de l\'UF</span>            </div>          </th>        </tr>      </ng-template>      <ng-template pTemplate="body" let-uf>        <tr class="border-b hover:bg-gray-50">          <td style="padding: 0.75rem; text-align: left;">            <p class="text-sm font-bold text-gray-600 flex items-center">              <i                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                title="Voir les détails de {{uf.nom}}"                (click)="viewUfDetails(uf)"              ></i>            </p>          </td>          <td class="px-4 py-2">{{ uf.nom }}</td>          <td class="px-4 py-2 border text-center">            <div class="flex justify-center space-x-8">              <span>{{ uf.actesNmoinsUn || "0" }}</span>              <span>{{ uf.partUfNmoinsUn || "0%" }}</span>            </div>          </td>          <td class="px-4 py-2 border text-center">            <div class="flex justify-center space-x-8">              <span>{{ uf.actesAnneeN || "0" }}</span>              <span>{{ uf.partUfAnneeN || "0%" }}</span>            </div>          </td>        </tr>      </ng-template>      <ng-template pTemplate="footer">        <tr class="font-semibold bg-gray-100">          <td colspan="2" class="px-4 py-2 text-left">TOTAL</td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalNbActeUfAnneeNmoinsUn || "0" }}</span>              <span>{{ totalPartUfAnneeNmoinsUn || "0%" }}</span>            </div>          </td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalNbActeUfAnneeN || "0" }}</span>              <span>{{ totalPartUfAnneeN || "0%" }}</span>            </div>          </td>        </tr>      </ng-template>    </p-table>  </div></section><!-- Détails des actes réalisés par praticien pour la période 2023 et 2024 --><section class="mb-8 mt-8">  <h2 class="text-2xl font-bold text-cyan-700 mb-4">    Détails des actes réalisés par Praticien pour la période 2023 et 2024  </h2>  <div class="p-card">    <p-table      #acteParPraticien      [value]="acteParPraticienSummary"      [paginator]="true"      [rows]="10"      [rowsPerPageOptions]="[10, 25, 50]"      [responsiveLayout]="\'scroll\'"      [globalFilterFields]="[\'nom\', \'prenom\', \'nbActesAnneeNmoinsUn\',\'nbActesAnneeN\']"      class="min-w-full bg-white border rounded-lg shadow"    >      <!-- Caption with Search Input -->      <ng-template pTemplate="caption">        <div class="flex justify-between items-center p-3 rounded-t-lg">          <div class="text-lg font-bold text-cyan-700 flex items-center">            <!-- Placeholder for dropdown or additional controls -->          </div>          <div>            <div class="relative mt-2 rounded-md shadow-sm">              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">                <span class="text-gray-500 sm:text-sm mr-3">                  <i class="pi pi-search"></i>                </span>              </div>              <input                type="text"                name="search"                (input)="acteParPraticien.filterGlobal(onInput($event), \'contains\')"                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset                  ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset                  focus:ring-indigo-600 sm:text-sm"                placeholder="Filtrer par nom ou prénom"              />            </div>          </div>        </div>      </ng-template>      <!-- Table Header -->      <ng-template pTemplate="header">        <tr>          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">            <i              class="pi pi-hashtag cursor-pointer ml-2"              title="Voir les détails du praticien"            ></i>          </th>          <th pSortableColumn="nom" class="px-4 py-2 border-b text-left font-semibold">            Nom            <p-sortIcon field="nom"></p-sortIcon>          </th>          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left font-semibold">            Prénom            <p-sortIcon field="prenom"></p-sortIcon>          </th>          <th pSortableColumn="nbActesAnneeNmoinsUn" class="px-4 py-2 border text-center font-semibold">            {{ anneeNmoinsUn }}            <p-sortIcon field="nbActesAnneeNmoinsUn"></p-sortIcon>            <div class="flex justify-center space-x-8">              <span>NB actes</span>              <span>Part du praticien</span>            </div>          </th>          <th pSortableColumn="nbActesAnneeN" class="px-4 py-2 border text-center font-semibold">            {{ anneeN }}            <p-sortIcon field="nbActesAnneeN"></p-sortIcon>            <div class="flex justify-center space-x-8">              <span>NB actes</span>              <span>Part du praticien</span>            </div>          </th>        </tr>      </ng-template>      <!-- Table Body -->      <ng-template pTemplate="body" let-praticien>        <tr class="border-b hover:bg-gray-50">          <td style="padding: 0.75rem; text-align: left;">            <p class="text-sm font-bold text-gray-600 flex items-center">              <i                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                title="Voir les détails de {{ praticien.nom }} {{ praticien.prenom }}"                (click)="viewPraticienDetails(praticien)"              ></i>            </p>          </td>          <td class="px-4 py-2">{{ praticien.nom }}</td>          <td class="px-4 py-2">{{ praticien.prenom }}</td>          <td class="px-4 py-2 border text-center">            <div class="flex justify-center space-x-8">              <span>{{ praticien.nbActesAnneeNmoinsUn || "0" }}</span>              <span>{{ praticien.partAnneeNmoinsUn || "0%" }}</span>            </div>          </td>          <td class="px-4 py-2 border text-center">            <div class="flex justify-center space-x-8">              <span>{{ praticien.nbActesAnneeN || "0" }}</span>              <span>{{ praticien.partAnneeN || "0%" }}</span>            </div>          </td>        </tr>      </ng-template>      <!-- Footer -->      <ng-template pTemplate="footer">        <tr class="font-semibold bg-gray-100">          <td colspan="3" class="px-4 py-2 text-left">TOTAL</td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalNbActePraticienAnneeNmoinsUn || "0" }}</span>              <span>{{ totalPartPraticienAnneeNmoinsUn || "0%" }}</span>            </div>          </td>          <td class="px-4 py-2 text-center border">            <div class="flex justify-center space-x-8">              <span>{{ totalNbActePraticienAnneeN || "0" }}</span>              <span>{{ totalPartPraticienAnneeN || "0%" }}</span>            </div>          </td>        </tr>      </ng-template>    </p-table>  </div></section></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'ActeDetailsComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'ActeDetailsComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
