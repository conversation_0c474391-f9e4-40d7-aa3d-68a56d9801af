import {Component, OnInit} from '@angular/core';
import { Chart } from 'chart.js/auto';
import {OverviewComponent} from "../../../pages/overview/overview.component";
import {ResumeProfilsComponent} from "../../../pages/resume-profils/resume-profils.component";
import {BreadcrumbComponent} from "../../../pages/breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../../core/models/breadcrumbItem";
import {ChartModule} from "primeng/chart";
import {SinglePraticienService} from "../../../core/services/overview/single-praticien-overview.service";
import {StyleClassModule} from "primeng/styleclass";
import {NgClass, NgForOf, NgIf} from "@angular/common";
import {DialogModule} from "primeng/dialog";
import {FieldsetModule} from "primeng/fieldset";
import {AccordionModule} from "primeng/accordion";
import {AvatarModule} from "primeng/avatar";
import {BadgeModule} from "primeng/badge";
import {TableModule} from "primeng/table";
import {RepartitionUF, TableauComparatif} from "../../../core/models/overview/singlePraticien-overview";
import {StepsModule} from "primeng/steps";
import {ActivatedRoute, Router} from "@angular/router";


@Component({
  selector: 'app-affectation-praticien',
  standalone: true,
  imports: [
    OverviewComponent,
    ResumeProfilsComponent,
    BreadcrumbComponent,
    ChartModule,
    StyleClassModule,
    NgForOf,
    DialogModule,
    FieldsetModule,
    AccordionModule,
    AvatarModule,
    BadgeModule,
    TableModule,
    NgIf,
    NgClass,
    StepsModule
  ],
  templateUrl: './affectation-praticien.component.html',
  styleUrl: './affectation-praticien.component.scss'
})
export class AffectationPraticienComponent implements OnInit{
  praticienData: RepartitionUF[] = [];
  praticienInfo: any;
  currentPraticienId !: string;

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Praticiens', url: '/graphic/praticien-list' },
    { label: 'Récapitulatif des actes du praticien' }
  ];
  //ccam
  ccamLineChartData: any;
  ccamBarChartData: any;
  ccamMonthlyTableData: any[] = [];
  ccamWeeklyTableData: any[] = [];

  //ngap
  ngapLineChartData: any;
  ngapBarChartData: any;
  ngapMonthlyTableData: any[] = [];
  ngapWeeklyTableData: any[] = [];

  //labo
  laboLineChartData: any;
  laboBarChartData: any;
  laboMonthlyTableData: any[] = [];
  laboWeeklyTableData: any[] = [];


  chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio:0.8,
    // scales: {
    //   x: {
    //     title: {
    //       display: true,
    //       text: 'Jours de la semaine',
    //     },
    //     ticks: {
    //       autoSkip: false,
    //     },
    //   },
    //   y: {
    //     beginAtZero: true,
    //     title: {
    //       display: true,
    //       text: 'Nombre d\'actes CCAM'
    //     }
    //   }
    // },
    plugins: {
      legend: {
        position: 'top',
      },
    },
  };

  anneeNmoinsUn = "2023"
  anneeN = "2024"
  //
  ccamData: TableauComparatif[] = [];
  ngapData: TableauComparatif[] = [];
  laboData: TableauComparatif[] = [];

  constructor(
    private singlePraticienService: SinglePraticienService,
    private route: ActivatedRoute
    ) {}

  ngOnInit(): void {
    // this.currentPraticienId = this.router.snapshot.paramMap.get("uid") || '';
    this.currentPraticienId =  this.route.snapshot.paramMap.get('uid') || '';

    if (this.currentPraticienId) {
      this.singlePraticienService.loadPraticienData(this.currentPraticienId);
    }

    this.singlePraticienService.praticienData$.subscribe((data) => {
      if (data) {
        this.initNgapData(data.ngap);
        this.initCcamData(data.ccam);
        this.initLaboData(data.labo);
        //
        this.ccamData = data.ccam.tableauComparatif;
        this.ngapData = data.ngap.tableauComparatif;
        this.laboData = data.labo.tableauComparatif;
        //
        this.praticienData = data.praticien.repartitionUF; // Données pour le tableau
        this.praticienInfo = data.praticien; // Autres informations du praticien
      }
    });
  }

  private initCcamData(ccam: any): void {
    // Line Chart pour l'évolution mensuelle
    this.ccamLineChartData = {
      labels: ccam.evolutionMensuelle.map((item: any) => item.mois),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ccam.evolutionMensuelle.map((item: any) => item.anneeNmoinsUn),
          borderColor: '#66BB6A',
          fill: false,
        },
        {
          label: this.anneeN,
          data: ccam.evolutionMensuelle.map((item: any) => item.anneeN),
          borderColor: '#FFCA28',
          fill: false,
        },
      ],
    };

    // Bar Chart pour la répartition hebdomadaire
    this.ccamBarChartData = {
      labels: ccam.repartitionHebdomadaire.map((item: any) => item.semaine),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ccam.repartitionHebdomadaire.map((item: any) => item.anneeNmoinsUn),
          backgroundColor: '#66BB6A',
        },
        {
          label: this.anneeN,
          data: ccam.repartitionHebdomadaire.map((item: any) => item.anneeN),
          backgroundColor: '#FFCA28',
        },
      ],
    };

    // Tableaux pour les données mensuelles et hebdomadaires
    this.ccamMonthlyTableData = ccam.evolutionMensuelle;
    this.ccamWeeklyTableData = ccam.repartitionHebdomadaire;
  }


  private initNgapData(ngap: any): void {
    this.ngapLineChartData = {
      labels: ngap.evolutionMensuelle.map((item: any) => item.mois),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ngap.evolutionMensuelle.map((item: any) => item.anneeNmoinsUn),
          borderColor: '#42A5F5',
          fill: false,
        },
        {
          label: this.anneeN,
          data: ngap.evolutionMensuelle.map((item: any) => item.anneeN),
          borderColor: '#FFA726',
          fill: false,
        },
      ],
    };

    this.ngapBarChartData = {
      labels: ngap.repartitionHebdomadaire.map((item: any) => item.semaine),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: ngap.repartitionHebdomadaire.map((item: any) => item.anneeNmoinsUn),
          backgroundColor: '#42A5F5',
        },
        {
          label: this.anneeN,
          data: ngap.repartitionHebdomadaire.map((item: any) => item.anneeN),
          backgroundColor: '#FFA726',
        },
      ],
    };

    this.ngapMonthlyTableData = ngap.evolutionMensuelle;
    this.ngapWeeklyTableData = ngap.repartitionHebdomadaire;
  }


  private initLaboData(labo: any): void {
    // Line Chart pour l'évolution mensuelle
    this.laboLineChartData = {
      labels: labo.evolutionMensuelle.map((item: any) => item.mois),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: labo.evolutionMensuelle.map((item: any) => item.anneeNmoinsUn),
          borderColor: '#AB47BC', // Couleur pour LABO - Année N-1
          fill: false,
        },
        {
          label: this.anneeN,
          data: labo.evolutionMensuelle.map((item: any) => item.anneeN),
          borderColor: '#FF7043', // Couleur pour LABO - Année N
          fill: false,
        },
      ],
    };

    // Bar Chart pour la répartition hebdomadaire
    this.laboBarChartData = {
      labels: labo.repartitionHebdomadaire.map((item: any) => item.semaine),
      datasets: [
        {
          label: this.anneeNmoinsUn,
          data: labo.repartitionHebdomadaire.map((item: any) => item.anneeNmoinsUn),
          backgroundColor: '#AB47BC', // Couleur pour LABO - Année N-1
        },
        {
          label: this.anneeN,
          data: labo.repartitionHebdomadaire.map((item: any) => item.anneeN),
          backgroundColor: '#FF7043', // Couleur pour LABO - Année N
        },
      ],
    };

    // Tableaux pour les données mensuelles et hebdomadaires
    this.laboMonthlyTableData = labo.evolutionMensuelle;
    this.laboWeeklyTableData = labo.repartitionHebdomadaire;
  }

  getTotalForYearByTypeActe(year: string, typeActe: 'CCAM' | 'NGAP' | 'Labo'): number {
    let data: TableauComparatif[] = [];

    // Select the appropriate data based on the type of act
    switch (typeActe) {
      case 'CCAM':
        data = this.ccamData;
        break;
      case 'NGAP':
        data = this.ngapData;
        break;
      case 'Labo':
        data = this.laboData;
        break;
      default:
        return 0;
    }

    // Sum the total for the specified year
    let total = data.reduce((sum, item) => {
      return sum + (year === '2023' ? item.totalAnneeNmoinsUn : item.totalAnneeN);
    }, 0);

    return total;
  }


  dialogState = {
    evolutionMensuelle: false,
    repartitionHebdomadaire: false,
    evolutionMensuelleCCAM: false,
    repartitionHebdomadaireCCAM: false,
    evolutionMensuelleLABO: false, // Pour le LABO
    repartitionHebdomadaireLABO: false, // Pour le LABO
  };

  showDialog(dialogType: 'evolutionMensuelle' | 'repartitionHebdomadaire' | 'evolutionMensuelleCCAM' | 'repartitionHebdomadaireCCAM' | 'evolutionMensuelleLABO' | 'repartitionHebdomadaireLABO'): void {
    this.dialogState[dialogType] = true;
  }

  onInput(event: Event): string {
    //console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }

  viewActeDetails(acte: any) {
    alert(`${acte.name}: ${acte.code}`);
  }

  /************************************* STEPPER ************************************************************/
  activeTable: 'CCAM' | 'NGAP' | 'LABO' = 'CCAM';
  activeGraphe: 'CCAMGRAPHE' | 'NGAPGRAPHE' | 'LABOGRAPHE' = 'CCAMGRAPHE';

  showTable(tableType: 'CCAM' | 'NGAP' | 'LABO' ): void {

    this.activeTable = tableType;
  }
  showGraphe(grapheType: 'CCAMGRAPHE' | 'NGAPGRAPHE' | 'LABOGRAPHE' ): void {

    this.activeGraphe = grapheType;
  }
    /******************************************************************************************************/
}
