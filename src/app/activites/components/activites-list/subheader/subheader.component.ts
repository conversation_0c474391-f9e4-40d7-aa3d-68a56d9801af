import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { Subject, Subscription, Observable, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, catchError } from 'rxjs/operators';

import { ActiviteModel, AgentModel } from '../../../../core/models/activite/ActiviteModel';
import { PoleModel } from '../../../../core/models/structure/PoleModel';
import { ServiceModel } from '../../../../core/models/structure/ServiceModel';
import { CRModel } from '../../../../core/models/structure/CRModel';
import { GlobalFilterService, VenueType, GlobalFilterState } from '../../../../core/services/global-filter/global-filter.service';
import { PoleService } from '../../../../core/services/structure/pole.service';
import { CRService } from '../../../../core/services/structure/cr.service';
import { ActesFiltersService, Practitioner, Pole, Cr, PoleWithCrsAndPractitioners, CrWithPractitioners } from '../../../services';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../../environments/environment';

// Utilise directement le type Practitioner du serveur

@Component({
  selector: 'app-subheader',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AutoCompleteModule,
    DropdownModule,
    ButtonModule,
    TooltipModule
  ],
  templateUrl: './subheader.component.html',
  styleUrls: ['./subheader.component.scss']
})
export class SubheaderComponent implements OnInit, OnDestroy, OnChanges {

  // Input: données d'activités du composant parent (pour compatibilité)
  @Input() activites: ActiviteModel[] = [];

  // Input: utiliser les filtres côté serveur avec cascade intelligente
  @Input() useServerSideFilters: boolean = false;

  // Output: émission des changements de filtres
  @Output() filtersChanged = new EventEmitter<GlobalFilterState>();

  // Output: demander au tableau de changer de page pour la recherche
  @Output() requestPageChange = new EventEmitter<number>();

  // Selected values (compatibles avec les deux modes)
  selectedPractitioner: any = null; // AgentModel (client-side) ou Practitioner (server-side)
  selectedPole: any = null; // PoleModel (client-side) ou PoleWithCrsAndPractitioners (server-side)
  selectedCr: any = null; // CRModel (client-side) ou CrWithPractitioners (server-side)
  selectedVenueType: VenueType | null = null;

  // UI state
  showHelpSection: boolean = true;
  isSticky: boolean = false;



  // Search queries for autocomplete
  practitionerQuery = '';
  poleQuery = '';
  crQuery = '';

  // Autocomplete suggestions (types dynamiques selon le mode)
  practitionerSuggestions: any[] = [];
  poleSuggestions: any[] = [];
  crSuggestions: any[] = [];

  // Cache pour tous les pôles et CR (pour résolution d'URIs)
  private allPoles: PoleModel[] = [];
  private allCrs: CRModel[] = [];
  private polesAndCrsLoaded = false;

  // Loading states for autocomplete
  poleLoading = false;
  crLoading = false;

  // Intervals et timeouts pour éviter les race conditions
  private poleLoadingInterval: any = null;
  private crLoadingInterval: any = null;
  private poleLoadingTimeout: any = null;
  private crLoadingTimeout: any = null;

  // Toutes les activités chargées une seule fois depuis l'API
  private allActivitiesForFilters: ActiviteModel[] = [];
  private dataLoaded = false;

  // Propriétés pour les filtres côté serveur (nouvelle structure optimisée)
  serverSidePoles: PoleWithCrsAndPractitioners[] = [];
  serverSideLoading = false;

  // Données filtrées pour les autocompletes (selon la cascade)
  availablePractitioners: Practitioner[] = [];
  availablePoles: PoleWithCrsAndPractitioners[] = [];
  availableCrs: CrWithPractitioners[] = [];

  // Venue type options (générées depuis le tableau VENUE_TYPES)
  get venueTypeOptions() {
    const baseOptions = Object.values(this.VENUE_TYPES).map(venue => ({
      label: venue.libelle,
      value: venue.enum
    }));

    // Ajouter l'option "Tous les types"
    return [
      ...baseOptions,
      { label: 'Tous les types', value: VenueType.BOTH }
    ];
  }

  // Debounce search
  private practitionerSearch$ = new Subject<string>();
  private poleSearch$ = new Subject<string>();
  private crSearch$ = new Subject<string>();

  // Subscriptions management
  private subscriptions: Subscription[] = [];

  constructor(
    private globalFilterService: GlobalFilterService,
    private poleService: PoleService,
    private crService: CRService,
    private actesFiltersService: ActesFiltersService,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    console.log('🚀 SubHeader (activites-list) ngOnInit started', { useServerSideFilters: this.useServerSideFilters });

    if (this.useServerSideFilters) {
      // Mode filtres côté serveur avec cascade intelligente
      this.setupServerSideFilters();
    } else {
      // Mode client-side traditionnel
      // Pre-load CRs immediately for instant autocomplete
      this.preloadCrsForInstantDisplay();

      // Load all data once (activities, poles, services)
      this.loadAllData();

      // Setup autocomplete with debounce
      this.setupAutoComplete();
    }

    // Load current filters from service
    this.loadCurrentFilters();

    // Écouter les changements de périodes pour invalider le cache
    this.setupPeriodsListener();

    // Écouter le scroll pour masquer la section d'aide quand sticky
    this.setupScrollListener();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Plus besoin de gérer les changements d'activités car on charge tout une fois
    if (changes['activites'] && changes['activites'].currentValue) {
      console.log('📊 Activities data changed (but we load all data independently)');
    }
  }



  /**
   * Load all data once: activities, poles, and services
   */
  private loadAllData(): void {
    console.log('📊 Loading all data...');

    // Load all activities from API (no pagination)
    this.loadAllActivities();

    // Load reference data (poles and services)
    this.loadReferenceData();
  }

  /**
   * Load all activities from API in one call
   */
  private loadAllActivities(): void {
    console.log('📊 Loading all activities from API...');

    const url = `${environment.api_url}/api/actes`;
    console.log('📊 Request URL:', url);

    this.http.get<any>(url).subscribe({
      next: (response) => {
        console.log('📊 All activities response:', {
          totalItems: response.totalItems,
          memberLength: response.member?.length,
          hydraMemberLength: response['hydra:member']?.length
        });

        const activities = response.member || response['hydra:member'] || [];
        this.allActivitiesForFilters = activities;
        this.dataLoaded = true;

        console.log(`✅ Loaded ${this.allActivitiesForFilters.length} activities for filters`);

        // Update suggestions now that we have all data
        this.updateSuggestions();
      },
      error: (error) => {
        console.error('❌ Error loading all activities:', error);
        this.dataLoaded = true; // Set to true to prevent infinite loading
      }
    });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.practitionerSearch$.complete();
    this.poleSearch$.complete();
    this.crSearch$.complete();

    // Clean up intervals and timeouts
    if (this.poleLoadingInterval) {
      clearInterval(this.poleLoadingInterval);
    }
    if (this.crLoadingInterval) {
      clearInterval(this.crLoadingInterval);
    }
    if (this.poleLoadingTimeout) {
      clearTimeout(this.poleLoadingTimeout);
    }
    if (this.crLoadingTimeout) {
      clearTimeout(this.crLoadingTimeout);
    }
  }

  /**
   * Pre-load CRs immediately for instant autocomplete display
   * KISS: Simple pre-loading without complex state management
   */
  private preloadCrsForInstantDisplay(): void {
    console.log('⚡ Pre-loading CRs for instant autocomplete...');

    const crPreloadSub = this.crService.getAll().subscribe({
      next: (result) => {
        this.allCrs = result.items;
        console.log('⚡ CRs pre-loaded:', this.allCrs.length);

        // Pre-populate suggestions for immediate display
        this.crSuggestions = this.allCrs.slice(0, 20);
        console.log('⚡ CR suggestions pre-populated:', this.crSuggestions.length);
      },
      error: (error) => {
        console.error('❌ Error pre-loading CRs:', error);
      }
    });

    this.subscriptions.push(crPreloadSub);
  }

  /**
   * Load reference data (poles and CRs) for URI resolution with parallel loading
   */
  private loadReferenceData(): void {
    console.log('🚀 Loading reference data with parallel optimization...');

    // Load all poles in parallel
    const polesSub = this.poleService.getAllPoles(1000).subscribe({
      next: (poles) => {
        this.allPoles = poles;
        console.log('✅ Loaded poles for URI resolution:', this.allPoles.length);
        this.checkDataLoaded();
      },
      error: (error) => {
        console.error('❌ Error loading poles:', error);
        this.checkDataLoaded();
      }
    });
    this.subscriptions.push(polesSub);

    // Load all CRs in parallel with immediate availability for autocomplete
    const crsSub = this.crService.getAll().subscribe({
      next: (result) => {
        this.allCrs = result.items;
        console.log('🚀 CRs loaded instantly for autocomplete:', this.allCrs.length);

        // Make CRs immediately available for autocomplete
        this.crLoading = false;
        if (this.crSuggestions.length === 0) {
          this.crSuggestions = this.getAvailableCrs().slice(0, 20);
          console.log('⚡ CR suggestions pre-loaded:', this.crSuggestions.length);
        }

        this.checkDataLoaded();
      },
      error: (error) => {
        console.error('❌ Error loading CRs:', error);
        this.crLoading = false;
        this.checkDataLoaded();
      }
    });
    this.subscriptions.push(crsSub);
  }

  /**
   * Check if reference data is loaded and update suggestions
   */
  private checkDataLoaded(): void {
    if (this.allPoles.length > 0 && this.allCrs.length > 0) {
      this.dataLoaded = true;
      console.log('🎉 Reference data loaded, updating suggestions...', {
        poles: this.allPoles.length,
        crs: this.allCrs.length,
        activitiesForFilters: this.allActivitiesForFilters.length
      });
      this.updateSuggestions();
    } else {
      console.log('⏳ Reference data not complete yet:', {
        poles: this.allPoles.length,
        crs: this.allCrs.length
      });
    }
  }



  /**
   * Setup autocomplete with debounce
   */
  private setupAutoComplete(): void {
    console.log('Setting up autocomplete for activites-list...');

    // Set up practitioner search with debounce
    const practitionerSub = this.practitionerSearch$.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(query => {
      console.log(`🔍 Processing practitioner query: "${query}"`);

      // Simple logic: filter all loaded data
      this.practitionerSuggestions = this.getFilteredPractitioners(query);
      console.log(`✅ Found ${this.practitionerSuggestions.length} practitioner results`);
    });
    this.subscriptions.push(practitionerSub);

    // Set up pole search with debounce
    const poleSub = this.poleSearch$.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(query => {
      console.log('Processing pole query:', query);
      this.poleSuggestions = this.getFilteredPoles(query);
      console.log('Pole results:', this.poleSuggestions.length);
    });
    this.subscriptions.push(poleSub);

    // Set up CR search with debounce
    const crSub = this.crSearch$.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(query => {
      console.log('Processing CR query:', query);
      this.crSuggestions = this.getFilteredCrs(query);
      // Arrêter le loader seulement si les données sont disponibles
      if (this.allCrs.length > 0 && this.dataLoaded) {
        this.crLoading = false;
      }
      console.log('CR search results:', this.crSuggestions.length);
    });
    this.subscriptions.push(crSub);

    console.log('Autocomplete setup complete for activites-list');
  }

  /**
   * Load current filters from GlobalFilterService
   */
  private loadCurrentFilters(): void {
    const currentFilters = this.globalFilterService.getCurrentFilterState();

    console.log('🔄 Loading current filters from service:', currentFilters);

    this.selectedPractitioner = currentFilters.practitioner;
    this.selectedPole = currentFilters.pole;
    this.selectedCr = currentFilters.service; // CR utilise le champ service du filtre global
    this.selectedVenueType = currentFilters.venueType;

    // Fix displayName for autocomplete after localStorage reload
    if (this.selectedPractitioner && !(this.selectedPractitioner as any).displayName) {
      console.log('🔧 Fixing missing displayName for practitioner after localStorage reload');
      (this.selectedPractitioner as any).displayName = this.getPractitionerDisplayName(this.selectedPractitioner);
      console.log('🔧 Added displayName:', (this.selectedPractitioner as any).displayName);
    }

    if (this.selectedPole && !this.selectedPole.libelle) {
      console.log('🔧 Fixing missing libelle for pole after localStorage reload');
      // Try to find the pole in our loaded data to get the correct libelle
      const foundPole = this.allPoles.find(p => p['@id'] === this.selectedPole!['@id']);
      if (foundPole) {
        this.selectedPole = foundPole;
        console.log('🔧 Restored pole from loaded data:', foundPole.libelle);
      }
    }

    if (this.selectedCr && !this.selectedCr.libelle) {
      console.log('🔧 Fixing missing libelle for CR after localStorage reload');
      // Try to find the CR in our loaded data to get the correct libelle
      const foundCr = this.allCrs.find(cr => cr['@id'] === this.selectedCr!['@id']);
      if (foundCr) {
        this.selectedCr = foundCr;
        console.log('🔧 Restored CR from loaded data:', foundCr.libelle);
      }
    }

    console.log('🔄 Loaded filters:', {
      practitioner: this.selectedPractitioner,
      pole: this.selectedPole,
      cr: this.selectedCr,
      venueType: this.selectedVenueType
    });

    // Test display name immediately after loading
    if (this.selectedPractitioner) {
      console.log('🔄 Testing practitioner display name after load:', this.getPractitionerDisplayName(this.selectedPractitioner));
    }

    // Si on est en mode server-side et qu'on a des filtres, déclencher la cascade
    if (this.useServerSideFilters && (this.selectedPractitioner || this.selectedPole || this.selectedCr)) {
      console.log('🔄 Triggering cascade after loading filters in server-side mode');
      // Attendre que les données soient chargées avant d'appliquer la cascade
      setTimeout(() => {
        this.applyCumulativeFilters();
      }, 100);
    }
  }

  /**
   * Update suggestions based on current activites data
   */
  private updateSuggestions(): void {
    console.log('Updating suggestions based on activites data:', {
      activitesInput: this.activites.length,
      allActivitiesForFilters: this.allActivitiesForFilters.length,
      dataLoaded: this.dataLoaded
    });

    if (!this.dataLoaded) {
      console.log('Reference data not loaded yet, skipping suggestions update');
      return;
    }

    // Update suggestions with current data
    const practitioners = this.getAvailablePractitioners().slice(0, 20);
    this.practitionerSuggestions = practitioners.map(practitioner => ({
      ...practitioner,
      displayName: `${practitioner.titre || ''} ${practitioner.prenom || ''} ${practitioner.nom || ''}`.trim()
    })) as any[];
    this.poleSuggestions = this.getAvailablePoles().slice(0, 20);
    this.crSuggestions = this.getAvailableCrs().slice(0, 20);

    console.log('Updated suggestions:', {
      practitioners: this.practitionerSuggestions.length,
      poles: this.poleSuggestions.length,
      crs: this.crSuggestions.length
    });
  }

  // Search event handlers
  onPractitionerSearch(event: any): void {
    console.log('🔍 Practitioner search:', event.query);
    this.practitionerSearch$.next(event.query);
  }

  onPoleSearch(event: any): void {
    console.log('🔍 Pole search:', event.query);
    this.poleSearch$.next(event.query);
  }

  onCrSearch(event: any): void {
    console.log('🔍 CR search:', event.query);

    // Cohérence avec onCrDropdownClick : vérifier dataLoaded aussi
    if (this.allCrs.length === 0 || !this.dataLoaded) {
      console.log('⏳ CRs data not ready yet, showing loading');
      this.crLoading = true;
      this.crSuggestions = [];
    }

    this.crSearch$.next(event.query);
  }

  // Dropdown click handlers
  onPractitionerDropdownClick(): void {
    console.log('🔽 Practitioner dropdown clicked');
    const practitioners = this.getAvailablePractitioners().slice(0, 20);
    // Add displayName for autocomplete
    this.practitionerSuggestions = practitioners.map(practitioner => ({
      ...practitioner,
      displayName: `${practitioner.titre || ''} ${practitioner.prenom || ''} ${practitioner.nom || ''}`.trim()
    })) as any[];
    console.log('Practitioner suggestions set:', this.practitionerSuggestions.length);
  }

  onPoleDropdownClick(): void {
    console.log(' Pole dropdown clicked');

    // Nettoyer les anciens intervalles et timeouts pour éviter les race conditions
    if (this.poleLoadingInterval) {
      clearInterval(this.poleLoadingInterval);
      this.poleLoadingInterval = null;
    }
    if (this.poleLoadingTimeout) {
      clearTimeout(this.poleLoadingTimeout);
      this.poleLoadingTimeout = null;
    }

    // Show loader until poles are ready
    this.poleLoading = true;
    this.poleSuggestions = [];

    // Wait for data to be truly ready before showing results
    if (this.dataLoaded && this.allPoles.length > 0) {
      // Data already loaded, show brief loader for smooth UX
      this.poleLoadingTimeout = setTimeout(() => {
        if (this.poleLoading) { // Vérifier si toujours en cours de chargement
          this.poleSuggestions = this.getAvailablePoles().slice(0, 20);
          this.poleLoading = false;
          console.log('Pole suggestions set:', this.poleSuggestions.length);
        }
      }, 200); // Délai optimisé pour UX fluide
    } else {
      // Data not ready, wait for it
      console.log(' Poles data not ready, waiting...');
      this.poleLoadingInterval = setInterval(() => {
        if (this.dataLoaded && this.allPoles.length > 0) {
          this.poleSuggestions = this.getAvailablePoles().slice(0, 20);
          this.poleLoading = false;
          console.log('Pole suggestions ready:', this.poleSuggestions.length);
          clearInterval(this.poleLoadingInterval);
          this.poleLoadingInterval = null;
        }
      }, 50); // Intervalle rapide pour réactivité maximale

      // Safety timeout
      this.poleLoadingTimeout = setTimeout(() => {
        if (this.poleLoading && this.poleLoadingInterval) {
          console.log(' Timeout waiting for poles');
          this.poleLoading = false;
          clearInterval(this.poleLoadingInterval);
          this.poleLoadingInterval = null;
        }
      }, 10000); // Timeout généreux pour connexions lentes
    }
  }

  onCrDropdownClick(): void {
    console.log(' CR dropdown clicked');

    // Nettoyer les anciens intervalles et timeouts pour éviter les race conditions
    if (this.crLoadingInterval) {
      clearInterval(this.crLoadingInterval);
      this.crLoadingInterval = null;
    }
    if (this.crLoadingTimeout) {
      clearTimeout(this.crLoadingTimeout);
      this.crLoadingTimeout = null;
    }

    // Always show loader first for consistent UX
    this.crLoading = true;
    this.crSuggestions = [];

    // Wait for data to be truly ready before showing results
    if (this.allCrs.length > 0 && this.dataLoaded) {
      // Data already loaded, show brief loader for smooth UX
      this.crLoadingTimeout = setTimeout(() => {
        if (this.crLoading) { // Vérifier si toujours en cours de chargement
          this.crSuggestions = this.getAvailableCrs().slice(0, 20);
          this.crLoading = false;
          console.log('CR suggestions set:', this.crSuggestions.length);
        }
      }, 200); // Délai optimisé pour UX fluide
    } else {
      // CRs not loaded yet, wait for them
      console.log(' CRs data not ready, waiting...');
      this.crLoadingInterval = setInterval(() => {
        if (this.allCrs.length > 0 && this.dataLoaded) {
          console.log('⚡ CRs loaded, updating suggestions');
          this.crSuggestions = this.getAvailableCrs().slice(0, 20);
          this.crLoading = false;
          console.log('CR suggestions ready:', this.crSuggestions.length);
          clearInterval(this.crLoadingInterval);
          this.crLoadingInterval = null;
        }
      }, 50); // Intervalle rapide pour réactivité maximale

      // Safety timeout
      this.crLoadingTimeout = setTimeout(() => {
        if (this.crLoading && this.crLoadingInterval) {
          console.log(' Timeout waiting for CRs');
          this.crLoading = false;
          clearInterval(this.crLoadingInterval);
          this.crLoadingInterval = null;
        }
      }, 10000); // Timeout généreux pour connexions lentes
    }
  }

  // Selection handlers
  onPractitionerSelect(event: any): void {
    const practitioner = event.value ? event.value : event;
    console.log('👤 Practitioner selected:', {
      nom: practitioner.nom,
      prenom: practitioner.prenom,
      fullName: practitioner.fullName,
      displayName: practitioner.displayName,
      id: practitioner['@id']
    });

    this.selectedPractitioner = practitioner;
    this.globalFilterService.setPractitioner(practitioner);

    // Selection made, no need to reset search state anymore

    this.updateSuggestions(); // Update other suggestions based on selection
    this.emitFiltersChanged();
  }

  onPoleSelect(event: any): void {
    const pole = event.value ? event.value : event;
    this.selectedPole = pole;
    this.globalFilterService.setPole(pole);
    this.updateSuggestions(); // Update other suggestions based on selection
    this.emitFiltersChanged();
  }

  onCrSelect(event: any): void {
    const cr = event.value ? event.value : event;
    this.selectedCr = cr;
    this.globalFilterService.setService(cr); // Utilise setService car le backend attend 'service'
    this.updateSuggestions(); // Update other suggestions based on selection
    this.emitFiltersChanged();
  }

  onVenueTypeChange(): void {
    this.globalFilterService.setVenueType(this.selectedVenueType);
    this.updateSuggestions(); // Update suggestions based on venue type
    this.emitFiltersChanged();
  }

  clearFilters(): void {
    console.log('🗑️ Clearing selection filters (keeping periods and dates)');
    this.selectedPractitioner = null;
    this.selectedPole = null;
    this.selectedCr = null;
    this.selectedVenueType = null;
    this.practitionerQuery = '';
    this.poleQuery = '';
    this.crQuery = '';

    // Use clearSelectionFilters to preserve periods and dates
    this.globalFilterService.clearSelectionFilters();
    this.updateSuggestions();
    this.emitFiltersChanged();
  }

  /**
   * Clear individual filters
   */
  clearPractitioner(): void {
    console.log('🗑️ Clearing practitioner filter');
    this.selectedPractitioner = null;
    this.practitionerQuery = '';
    this.globalFilterService.setPractitioner(null);
    this.updateSuggestions();
    this.emitFiltersChanged();
  }

  clearPole(): void {
    console.log('🗑️ Clearing pole filter');
    this.selectedPole = null;
    this.poleQuery = '';
    this.globalFilterService.setPole(null);
    this.updateSuggestions();
    this.emitFiltersChanged();
  }

  clearCr(): void {
    console.log('🗑️ Clearing CR filter');
    this.selectedCr = null;
    this.crQuery = '';
    this.globalFilterService.setService(null); // Utilise setService car le backend attend 'service'
    this.updateSuggestions();
    this.emitFiltersChanged();
  }

  clearVenueType(): void {
    console.log('🗑️ Clearing venue type filter');
    this.selectedVenueType = null;
    this.globalFilterService.setVenueType(null);
    this.updateSuggestions();
    this.emitFiltersChanged();
  }

  closeHelpSection(): void {
    this.showHelpSection = false;
  }

  /**
   * Configure le listener de scroll pour masquer la section d'aide quand sticky
   */
  private setupScrollListener(): void {
    const scrollListener = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const wasSticky = this.isSticky;

      // Considérer comme sticky si on a scrollé plus de 50px
      this.isSticky = scrollTop > 50;

      // Masquer la section d'aide quand on devient sticky
      if (!wasSticky && this.isSticky && this.showHelpSection) {
        console.log('📌 Subheader is now sticky, hiding help section');
        this.showHelpSection = false;
      }

      // Optionnel : Réafficher la section d'aide quand on revient en haut
      // if (wasSticky && !this.isSticky) {
      //   console.log('📌 Subheader is no longer sticky, could show help section');
      //   this.showHelpSection = true;
      // }
    };

    // Ajouter le listener
    window.addEventListener('scroll', scrollListener, { passive: true });

    // Nettoyer le listener lors de la destruction du composant
    this.subscriptions.push({
      unsubscribe: () => window.removeEventListener('scroll', scrollListener)
    } as any);
  }



  /**
   * Écouter les changements de périodes pour invalider le cache
   */
  private setupPeriodsListener(): void {
    if (!this.useServerSideFilters) return;

    // Stocker les périodes initiales pour comparaison
    let previousPeriods = this.getCurrentPeriodsKey();

    const periodsSubscription = this.globalFilterService.getFilterState().subscribe((filters: GlobalFilterState) => {
      const currentPeriods = this.getCurrentPeriodsKey();

      // Ne recharger que si les périodes ont vraiment changé
      if (currentPeriods !== previousPeriods) {
        console.log('🔄 Periods actually changed, clearing service cache and reloading');
        console.log('Previous:', previousPeriods);
        console.log('Current:', currentPeriods);

        this.actesFiltersService.clearCache();

        // Recharger les données avec les nouvelles périodes
        const additionalFilters = {
          ejcode: 'ej0001' // Valeur par défaut
        };

        this.loadServerSideFilters(additionalFilters);
        previousPeriods = currentPeriods;
      } else {
        console.log('✅ Periods unchanged, no reload needed');
      }
    });

    this.subscriptions.push(periodsSubscription);
  }

  /**
   * Génère une clé unique basée sur les périodes actuelles
   */
  private getCurrentPeriodsKey(): string {
    const globalFilters = this.globalFilterService.getCurrentFilterState();
    const periods = [
      globalFilters.p1?.dateDebut?.toISOString(),
      globalFilters.p1?.dateFin?.toISOString(),
      globalFilters.p2?.dateDebut?.toISOString(),
      globalFilters.p2?.dateFin?.toISOString(),
      globalFilters.p3?.dateDebut?.toISOString(),
      globalFilters.p3?.dateFin?.toISOString()
    ].filter(Boolean).join('|');

    return periods;
  }

  /**
   * Emit filters changed event to parent component
   */
  private emitFiltersChanged(): void {
    // Récupérer les périodes actuelles depuis le service pour les préserver
    const currentFilters = this.globalFilterService.getCurrentFilterState();

    const filters: GlobalFilterState = {
      practitioner: this.selectedPractitioner,
      pole: this.selectedPole,
      service: this.selectedCr, // CR utilise le champ service du filtre global
      venueType: this.selectedVenueType,
      p1: currentFilters.p1, // Préserver les périodes existantes
      p2: currentFilters.p2,
      p3: currentFilters.p3,
      dateDebut: currentFilters.dateDebut,
      dateFin: currentFilters.dateFin
    };

    console.log('📤 Subheader emitting filters with preserved periods:', {
      hasP1: !!filters.p1,
      hasP2: !!filters.p2,
      hasP3: !!filters.p3,
      practitioner: !!filters.practitioner,
      pole: !!filters.pole,
      service: !!filters.service,
      venueType: filters.venueType
    });

    this.filtersChanged.emit(filters);
  }

  // ========================================
  // RÉSOLUTION D'URIS API PLATFORM
  // ========================================

  /**
   * Resolve pole URI to pole object
   */
  private resolveUriToPole(uri: string): PoleModel | null {
    return this.allPoles.find(pole => pole['@id'] === uri) || null;
  }

  /**
   * Resolve CR URI to CR object (using allCrs instead of allServices)
   */
  private resolveUriToCr(uri: string): CRModel | null {
    return this.allCrs.find(cr => cr['@id'] === uri) || null;
  }

  // ========================================
  // LOGIQUE DE CASCADE INTELLIGENTE
  // ========================================

  /**
   * Get available practitioners based on current selections
   * Uses real data from activites (including progressively loaded ones)
   */
  private getAvailablePractitioners(): AgentModel[] {
    console.log('Getting available practitioners from activites:', this.allActivitiesForFilters.length);

    // Filter activities based on current selections
    let filteredActivites = this.getFilteredActivites();

    // Extract unique practitioners from filtered activities
    const practitionersMap = new Map<string, AgentModel>();
    filteredActivites.forEach(activite => {
      if (activite.agent && activite.agent['@id']) {
        practitionersMap.set(activite.agent['@id'], activite.agent);
      }
    });

    const result = Array.from(practitionersMap.values());
    console.log('Available practitioners:', result.length);

    // Debug: Show all practitioner names on current page
    console.log('👥 Practitioners on current page:', result.map(p => `${p.prenom} ${p.nom}`));
    return result;
  }

  /**
   * Get available poles based on current selections
   * Uses real data from activites via ufIntervention with URI resolution
   */
  private getAvailablePoles(): PoleModel[] {
    console.log('Getting available poles from activites with URI resolution');

    if (!this.dataLoaded) {
      console.log('Reference data not loaded yet');
      return [];
    }

    // Filter activities based on current selections
    let filteredActivites = this.getFilteredActivites();

    // Extract unique pole URIs from filtered activities
    const poleUris = new Set<string>();
    filteredActivites.forEach((activite, index) => {
      if (activite.ufIntervention && (activite.ufIntervention as any).pole) {
        const poleUri = (activite.ufIntervention as any).pole;
        if (typeof poleUri === 'string') {
          poleUris.add(poleUri);
          console.log(`Found pole URI in activity ${index}:`, poleUri);
        } else {
          console.log(`Pole is not a string URI in activity ${index}:`, poleUri);
        }
      }
    });

    console.log('Unique pole URIs found:', Array.from(poleUris));

    // Resolve URIs to pole objects
    const resolvedPoles: PoleModel[] = [];
    poleUris.forEach(uri => {
      const pole = this.resolveUriToPole(uri);
      if (pole) {
        resolvedPoles.push(pole);
        console.log(`Resolved pole URI ${uri} to:`, pole.libelle);
      } else {
        console.log(`Could not resolve pole URI:`, uri);
      }
    });

    console.log('Available poles:', resolvedPoles.length);
    return resolvedPoles;
  }

  /**
   * Get available CRs based on current selections
   * Uses real data from activites via ufIntervention.cr with URI resolution
   * Optimized to work even if activities are not fully loaded yet
   */
  private getAvailableCrs(): CRModel[] {
    console.log('Getting available CRs with optimization');

    // If CRs are loaded but activities aren't, return all CRs for quick display
    if (this.allCrs.length > 0 && this.allActivitiesForFilters.length === 0) {
      console.log('⚡ Activities not loaded yet, returning all CRs for quick display');
      return this.allCrs.slice(0, 20);
    }

    // If no CRs loaded at all, return empty
    if (this.allCrs.length === 0) {
      console.log('No CRs loaded yet');
      return [];
    }

    // Filter activities based on current selections
    let filteredActivites = this.getFilteredActivites();

    // Extract unique CR URIs from filtered activities
    const crUris = new Set<string>();
    filteredActivites.forEach((activite, index) => {
      if (activite.ufIntervention && activite.ufIntervention.cr) {
        const crUri = activite.ufIntervention.cr;
        if (typeof crUri === 'string') {
          crUris.add(crUri);
          console.log(`Found CR URI in activity ${index}:`, crUri);
        } else {
          console.log(`CR is not a string URI in activity ${index}:`, crUri);
        }
      }
    });

    console.log('Unique CR URIs found:', Array.from(crUris));

    // Resolve URIs to CR objects
    const resolvedCrs: CRModel[] = [];
    crUris.forEach(uri => {
      const cr = this.resolveUriToCr(uri);
      if (cr) {
        resolvedCrs.push(cr);
        console.log(`Resolved CR URI ${uri} to:`, cr.libelle);
      } else {
        console.log(`Could not resolve CR URI:`, uri);
      }
    });

    console.log('Available CRs:', resolvedCrs.length);
    return resolvedCrs;
  }

  /**
   * Get filtered activities based on current selections
   * This is the core of the intelligent cascade logic
   */
  private getFilteredActivites(): ActiviteModel[] {
    let filteredActivites = [...this.allActivitiesForFilters];

    // Filter by venue type first
    if (this.selectedVenueType !== null && this.selectedVenueType !== undefined) {
      filteredActivites = filteredActivites.filter(activite => {
        // Si "Tous les types" est sélectionné, ne pas filtrer
        if (this.selectedVenueType === VenueType.BOTH) return true;

        // Utiliser le tableau VENUE_TYPES pour la correspondance
        const venueTypeCode = this.getVenueTypeCode(this.selectedVenueType);
        return venueTypeCode !== null ? activite.typeVenue === venueTypeCode : true;
      });
    }

    // Filter by practitioner (if selected)
    if (this.selectedPractitioner) {
      filteredActivites = filteredActivites.filter(activite =>
        activite.agent && activite.agent['@id'] === this.selectedPractitioner!['@id']
      );
    }

    // Filter by pole (if selected) - using URI comparison
    if (this.selectedPole) {
      filteredActivites = filteredActivites.filter(activite =>
        activite.ufIntervention &&
        (activite.ufIntervention as any).pole &&
        (activite.ufIntervention as any).pole === this.selectedPole!['@id']
      );
    }

    // Filter by CR (if selected) - using URI comparison
    if (this.selectedCr) {
      filteredActivites = filteredActivites.filter(activite =>
        activite.ufIntervention &&
        activite.ufIntervention.cr &&
        activite.ufIntervention.cr === this.selectedCr!['@id']
      );
    }

    return filteredActivites;
  }

  /**
   * Get filtered practitioners based on search query (simplified version)
   */
  private getFilteredPractitioners(query: string): any[] {
    console.log(`🔍 Searching practitioners for: "${query}"`);

    const availablePractitioners = this.getAvailablePractitioners();
    console.log(`🔍 Total practitioners available: ${availablePractitioners.length}`);

    if (!query || query.length < 2) {
      console.log('🔍 Query too short, returning first 20 practitioners');
      return availablePractitioners.slice(0, 20).map(practitioner => ({
        ...practitioner,
        displayName: `${practitioner.titre || ''} ${practitioner.prenom || ''} ${practitioner.nom || ''}`.trim()
      })) as any[];
    }

    const searchTerm = query.toLowerCase();
    console.log(`🔍 Searching for "${searchTerm}" in ${availablePractitioners.length} practitioners`);

    // Filter practitioners based on search term
    const filtered = availablePractitioners.filter(practitioner => {
      const nomMatch = practitioner.nom && practitioner.nom.toLowerCase().includes(searchTerm);
      const prenomMatch = practitioner.prenom && practitioner.prenom.toLowerCase().includes(searchTerm);
      const emailMatch = practitioner.email && practitioner.email.toLowerCase().includes(searchTerm);
      const fullNameMatch = practitioner.fullName && practitioner.fullName.toLowerCase().includes(searchTerm);

      return nomMatch || prenomMatch || emailMatch || fullNameMatch;
    });

    console.log(`✅ Found ${filtered.length} practitioners matching "${searchTerm}"`);

    // Add displayName and return results (limit to 20 for performance)
    const results = filtered.slice(0, 20).map(practitioner => ({
      ...practitioner,
      displayName: `${practitioner.titre || ''} ${practitioner.prenom || ''} ${practitioner.nom || ''}`.trim()
    })) as any[];

    console.log(`🎯 Returning ${results.length} practitioner suggestions`);
    return results;
  }

  /**
   * Get filtered poles based on search query with progressive loading
   */
  private getFilteredPoles(query: string): PoleModel[] {
    const availablePoles = this.getAvailablePoles();

    if (!query || query.length < 2) {
      return availablePoles.slice(0, 20);
    }

    const searchTerm = query.toLowerCase();
    const filtered = availablePoles.filter(pole =>
      (pole.polecode && pole.polecode.toLowerCase().includes(searchTerm)) ||
      (pole.libelle && pole.libelle.toLowerCase().includes(searchTerm))
    );

    // With complete dataset loaded, no need for progressive loading
    if (filtered.length === 0) {
      console.log(`❌ No poles found for "${query}" in complete dataset`);
    }

    return filtered.slice(0, 10);
  }

  /**
   * Get filtered CRs based on search query
   */
  private getFilteredCrs(query: string): CRModel[] {
    const availableCrs = this.getAvailableCrs();

    if (!query || query.length < 2) {
      return availableCrs.slice(0, 20);
    }

    const searchTerm = query.toLowerCase();
    const filtered = availableCrs.filter(cr =>
      (cr.crcode && cr.crcode.toLowerCase().includes(searchTerm)) ||
      (cr.libelle && cr.libelle.toLowerCase().includes(searchTerm))
    );

    // With complete dataset loaded, no need for progressive loading
    if (filtered.length === 0) {
      console.log(`❌ No CRs found for "${query}" in complete dataset`);
    }

    return filtered.slice(0, 10);
  }

  /**
   * Tableau clé-valeur pour les types de venue
   */
  readonly VENUE_TYPES = {
    1: { code: 1, libelle: 'Consultation', enum: VenueType.CONSULTATION },
    2: { code: 2, libelle: 'Hospitalisation', enum: VenueType.HOSPITALIZATION },
    3: { code: 3, libelle: 'Urgence', enum: VenueType.URGENCE }
  } as const;

  /**
   * Get venue type label from numeric code
   */
  getVenueTypeLabel(typeVenue: number): string {
    return this.VENUE_TYPES[typeVenue as keyof typeof this.VENUE_TYPES]?.libelle || 'Type inconnu';
  }

  /**
   * Get venue type label from enum (pour le template)
   */
  getVenueTypeLabelFromEnum(venueType: VenueType | null): string {
    if (!venueType) return '';
    if (venueType === VenueType.BOTH) return 'Tous les types';

    const entry = Object.values(this.VENUE_TYPES).find(v => v.enum === venueType);
    return entry?.libelle || 'Type inconnu';
  }

  /**
   * Get venue type code from enum
   */
  getVenueTypeCode(venueType: VenueType | null): number | null {
    if (!venueType || venueType === VenueType.BOTH) return null;

    const entry = Object.values(this.VENUE_TYPES).find(v => v.enum === venueType);
    return entry?.code || null;
  }

  /**
   * Convertit un Practitioner (server-side) en AgentModel pour la persistance
   */
  private convertPractitionerToAgentModel(practitioner: Practitioner): AgentModel {
    // Créer un nom complet à partir des champs disponibles
    const fullName = [practitioner.titre, practitioner.prenom, practitioner.nom]
      .filter(Boolean)
      .join(' ') || null;

    // Créer le displayName pour l'affichage dans l'autocomplete
    const displayName = fullName || 'Praticien inconnu';

    const agentModel = {
      '@id': practitioner['@id'],
      '@type': 'Agent',
      titre: practitioner.titre || null,
      prenom: practitioner.prenom || null,
      nom: practitioner.nom || null,
      fullName: fullName,
      displayName: displayName, // Ajouter la propriété displayName
      email: null,
      matricule: null,
      hrUser: null,
      categorie: null,
      dateArrivee: null,
      dateDepart: null,
      etablissement: null,
      dateVenue: null,
      dateMaj: null,
      createurFiche: null,
      modifFiche: null,
      pfuser: null,
      dmdacre: null,
      dmdamaj: null,
      validFrom: '',
      validTo: null,
      periodeType: '',
      source: null,
      isActif: true,
      dateCreation: ''
    } as unknown as AgentModel;

    // Ajouter explicitement la propriété displayName pour l'autocomplete
    (agentModel as any).displayName = displayName;

    return agentModel;
  }

  /**
   * Convertit un PoleWithCrsAndPractitioners (server-side) en PoleModel pour la persistance
   */
  private convertServerSidePoleToModel(pole: PoleWithCrsAndPractitioners): PoleModel {
    return {
      '@id': pole['@id'],
      '@type': 'Pole',
      libelle: pole.libelle,
      polecode: pole.poleCode || '',
      etab: '',
      datdeb: null,
      datfin: null,
      datclos: null,
      pfuser: null,
      dmdacre: null,
      dmdamaj: null,
      hopital: null,
      validFrom: '',
      validTo: null,
      periodeType: '',
      source: null,
      isActif: true,
      dateCreation: ''
    } as unknown as PoleModel;
  }

  /**
   * Convertit un CrWithPractitioners (server-side) en CRModel pour la persistance
   */
  private convertServerSideCrToModel(cr: CrWithPractitioners): CRModel {
    return {
      '@id': cr['@id'],
      '@type': 'CR',
      libelle: cr.libelle,
      crcode: cr.crcode || '',
      nomresp: null,
      polecode: '',
      pfuser: null,
      dmdacre: null,
      dmdamaj: null,
      etab: '',
      datdeb: null,
      datfin: null,
      validFrom: '',
      validTo: null,
      periodeType: '',
      source: null,
      isActif: true,
      dateCreation: ''
    } as unknown as CRModel;
  }

  /**
   * Get pole display name (safe method for localStorage objects)
   */
  getPoleDisplayName(pole: any): string {
    if (!pole) {
      return '';
    }

    // Gérer les différents types d'objets pôle (PoleModel ou PoleWithCrsAndPractitioners)
    return pole.libelle || pole.poleCode || pole.polecode || 'Pôle inconnu';
  }

  /**
   * Get CR display name (safe method for localStorage objects)
   */
  getCrDisplayName(cr: CRModel): string {
    console.log('🏷️ getCrDisplayName called with:', cr);

    if (!cr) {
      console.log('🏷️ No CR provided');
      return '';
    }

    // Debug: Check the structure of the CR object
    console.log('🏷️ CR object structure:', {
      type: typeof cr,
      keys: Object.keys(cr),
      libelle: cr.libelle,
      crcode: cr.crcode
    });

    return cr.libelle || cr.crcode || 'CR inconnu';
  }

  /**
   * Get practitioner display name (handles displayName or constructs from fields)
   * Compatible avec AgentModel (client-side) et Practitioner (server-side)
   */
  getPractitionerDisplayName(practitioner: any): string {
    console.log('🏷️ getPractitionerDisplayName called with:', practitioner);

    if (!practitioner) {
      console.log('🏷️ No practitioner provided');
      return '';
    }

    // Debug: Check the structure of the practitioner object
    console.log('🏷️ Practitioner object structure:', {
      type: typeof practitioner,
      keys: Object.keys(practitioner),
      titre: practitioner.titre,
      prenom: practitioner.prenom,
      nom: practitioner.nom,
      fullName: practitioner.fullName,
      displayName: (practitioner as any).displayName
    });

    // Try displayName first (server-side Practitioner type has this field)
    if (practitioner.displayName) {
      console.log('🏷️ Using displayName from server-side:', practitioner.displayName);
      return practitioner.displayName;
    }

    // Fallback for client-side AgentModel
    const displayName = (practitioner as any).displayName;
    if (displayName) {
      console.log('🏷️ Using displayName from client-side:', displayName);
      return displayName;
    }

    // Construct from individual fields (prenom + nom are more reliable than fullName)
    const parts = [
      practitioner.titre,
      practitioner.prenom,
      practitioner.nom
    ].filter(part => part && part.trim());

    if (parts.length > 0) {
      const result = parts.join(' ').trim();
      console.log('🏷️ Constructed name from parts:', result);
      return result;
    }

    // Fallback to fullName only if no individual fields
    if (practitioner.fullName && practitioner.fullName.trim()) {
      console.log('🏷️ Using fullName as fallback:', practitioner.fullName);
      return practitioner.fullName;
    }

    console.log('🏷️ Using fallback name');
    return practitioner.nom || 'Praticien inconnu';
  }





  /**
   * Configuration des filtres côté serveur avec cascade intelligente
   */
  private setupServerSideFilters(): void {
    console.log('🔧 Component: Setting up server-side cascade filters');
    console.log('🔧 Component: Current state before setup:', {
      serverSideLoading: this.serverSideLoading,
      serverSidePoles: this.serverSidePoles.length,
      availablePractitioners: this.availablePractitioners.length
    });

    // Récupérer les filtres globaux pour les périodes
    const globalFilters = this.globalFilterService.getCurrentFilterState();
    const additionalFilters = {
      ejcode: 'ej0001' // Valeur par défaut
    };

    // Charger immédiatement les données pour que PrimeNG les ait (avec cache intelligent du service)
    this.loadServerSideFilters(additionalFilters);

    this.setupServerSideAutoComplete();
  }

  /**
   * Vider complètement le cache
   */
  private clearAllCache(): void {
    console.log('🗑️ Clearing ALL cache');

    // Vider le cache du service
    this.actesFiltersService.clearCache();

    // Vider les données locales
    this.serverSidePoles = [];
    this.availablePractitioners = [];
    this.availablePoles = [];
    this.availableCrs = [];
    this.practitionerSuggestions = [];
    this.poleSuggestions = [];
    this.crSuggestions = [];
  }

  /**
   * Charger les filtres depuis le serveur (cache géré par le service)
   */
  private loadServerSideFilters(additionalFilters: any = {}): void {
    console.log('🔄 Component: Loading server-side filters...', {
      currentServerSideLoading: this.serverSideLoading,
      additionalFilters
    });

    this.serverSideLoading = true;
    console.log('🔄 Component: Set serverSideLoading = true');

    this.actesFiltersService.getFiltersWithPeriods(additionalFilters).subscribe({
      next: (data: any) => {
        console.log('✅ Component: Server-side filters received:', {
          timestamp: new Date().toISOString(),
          poles: data.poles.length,
          totalCrs: data.poles.reduce((sum: number, pole: any) => sum + pole.crs.length, 0),
          totalPractitioners: this.countTotalPractitioners(data.poles),
          totalActes: data.totalActes,
          generationTime: data.generationTimeMs + 'ms',
          wasFromCache: data.fromCache || false
        });

        this.serverSidePoles = data.poles;

        // Initialiser les données disponibles (toutes au début)
        if (this.useServerSideFilters) {
          this.initializeAvailableData();
        }

        this.serverSideLoading = false;
        console.log('✅ Component: Set serverSideLoading = false');
      },
      error: (error: any) => {
        console.error('❌ Component: Error loading server-side filters:', error);
        this.serverSideLoading = false;
        console.log('❌ Component: Set serverSideLoading = false (error)');
      }
    });
  }

  /**
   * Configuration de l'autocomplete côté serveur
   */
  private setupServerSideAutoComplete(): void {
    const practitionerSearchSubscription = this.practitionerSearch$
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe(query => console.log('🔍 Searching practitioners server-side:', query));

    this.subscriptions.push(practitionerSearchSubscription);
  }

  /**
   * Gestion de la sélection d'un praticien (mode serveur)
   */
  onServerSidePractitionerSelect(event: any): void {
    const practitioner: Practitioner = event.value;
    console.log('👤 Server-side practitioner selected:', practitioner);

    // Convertir en AgentModel pour la persistance
    const agentModel = this.convertPractitionerToAgentModel(practitioner);

    console.log('👤 Converted AgentModel:', {
      displayName: (agentModel as any).displayName,
      fullName: agentModel.fullName,
      nom: agentModel.nom,
      prenom: agentModel.prenom
    });

    // Sauvegarder dans le GlobalFilterService pour la persistance
    this.selectedPractitioner = agentModel;
    this.globalFilterService.setPractitioner(agentModel);

    // Déclencher la cascade cumulative côté frontend
    this.applyCumulativeFilters();

    // Émettre le changement de filtre
    this.emitFiltersChanged();
  }

  /**
   * Gestion de la sélection d'un pôle (mode serveur)
   */
  onServerSidePoleSelect(event: any): void {
    const pole: PoleWithCrsAndPractitioners = event.value;
    console.log('🏢 Server-side pole selected:', pole);

    // Convertir en PoleModel pour la persistance
    const poleModel = this.convertServerSidePoleToModel(pole);

    // Sauvegarder dans le GlobalFilterService pour la persistance
    this.selectedPole = poleModel;
    this.globalFilterService.setPole(poleModel);

    // Déclencher la cascade cumulative côté frontend
    this.applyCumulativeFilters();

    // Émettre le changement de filtre
    this.emitFiltersChanged();
  }

  /**
   * Gestion de la sélection d'un CR (mode serveur)
   */
  onServerSideCrSelect(event: any): void {
    const cr: CrWithPractitioners = event.value;
    console.log('🏥 Server-side CR selected:', cr);

    // Convertir en CRModel pour la persistance
    const crModel = this.convertServerSideCrToModel(cr);

    // Sauvegarder dans le GlobalFilterService pour la persistance
    this.selectedCr = crModel;
    this.globalFilterService.setService(crModel); // Utilise setService car le backend attend 'service'

    // Déclencher la cascade cumulative côté frontend
    this.applyCumulativeFilters();

    // Émettre le changement de filtre
    this.emitFiltersChanged();
  }

  /**
   * Recherche de praticiens côté serveur
   */
  onServerSidePractitionerSearch(event: any): void {
    if (!this.useServerSideFilters) return;

    console.log('🔍 Server-side practitioner search:', event.query);
    console.log('🔍 Available practitioners:', this.availablePractitioners.length);

    // Filtrer les praticiens disponibles selon la recherche
    const query = event.query.toLowerCase();
    this.practitionerSuggestions = this.availablePractitioners.filter(p =>
      p.displayName?.toLowerCase().includes(query) ||
      p.nom?.toLowerCase().includes(query) ||
      p.prenom?.toLowerCase().includes(query)
    );

    console.log('🔍 Filtered suggestions:', this.practitionerSuggestions.length);
  }

  /**
   * Recherche de pôles côté serveur
   */
  onServerSidePoleSearch(event: any): void {
    if (!this.useServerSideFilters) return;

    console.log('🔍 Server-side pole search:', event.query);
    console.log('🔍 Available poles:', this.availablePoles.length);

    // Filtrer les pôles disponibles selon la recherche
    const query = event.query.toLowerCase();
    this.poleSuggestions = this.availablePoles.filter(p =>
      p.libelle?.toLowerCase().includes(query) ||
      p.poleCode?.toLowerCase().includes(query)
    );

    console.log('🔍 Filtered pole suggestions:', this.poleSuggestions.length);
  }

  /**
   * Recherche de CRs côté serveur
   */
  onServerSideCrSearch(event: any): void {
    if (!this.useServerSideFilters) return;

    console.log('🔍 Server-side CR search:', event.query);
    console.log('🔍 Available CRs:', this.availableCrs.length);

    // Filtrer les CRs disponibles selon la recherche
    const query = event.query.toLowerCase();
    this.crSuggestions = this.availableCrs.filter(c =>
      c.libelle?.toLowerCase().includes(query) ||
      c.crcode?.toLowerCase().includes(query)
    );

    console.log('🔍 Filtered CR suggestions:', this.crSuggestions.length);
  }

  /**
   * Clic sur dropdown praticiens côté serveur
   */
  onServerSidePractitionerDropdownClick(): void {
    console.log('📋 Server-side practitioner dropdown clicked');
    console.log('📋 Available practitioners:', this.availablePractitioners.length);

    // Si pas de données, charger
    if (this.serverSidePoles.length === 0) {
      this.loadServerSideFilters();
    } else if (this.useServerSideFilters) {
      // Afficher toutes les suggestions disponibles (selon la cascade)
      this.practitionerSuggestions = [...this.availablePractitioners];

      // Forcer la détection de changement pour PrimeNG
      setTimeout(() => {
        this.practitionerSuggestions = [...this.availablePractitioners];
        console.log('🔄 Practitioner suggestions updated:', this.practitionerSuggestions.length);
      }, 0);
    }
  }

  /**
   * Clic sur dropdown pôles côté serveur
   */
  onServerSidePoleDropdownClick(): void {
    console.log('📋 Server-side pole dropdown clicked');
    console.log('📋 Available poles:', this.availablePoles.length);
    console.log('📋 Current pole suggestions:', this.poleSuggestions.length);

    // Si pas de données, charger
    if (this.serverSidePoles.length === 0) {
      this.loadServerSideFilters();
    } else if (this.useServerSideFilters) {
      // Vider d'abord les suggestions pour forcer le rafraîchissement
      this.poleSuggestions = [];

      // Puis remettre toutes les suggestions disponibles
      setTimeout(() => {
        this.poleSuggestions = [...this.availablePoles];
        console.log('🔄 Pole suggestions forcefully updated:', this.poleSuggestions.length);
        console.log('🔄 First pole suggestion:', this.poleSuggestions[0]?.libelle);
      }, 10);
    }
  }

  /**
   * Clic sur dropdown CRs côté serveur
   */
  onServerSideCrDropdownClick(): void {
    console.log('📋 Server-side CR dropdown clicked');
    console.log('📋 Available CRs:', this.availableCrs.length);
    console.log('📋 Current CR suggestions:', this.crSuggestions.length);

    // Si pas de données, charger
    if (this.serverSidePoles.length === 0) {
      this.loadServerSideFilters();
    } else if (this.useServerSideFilters) {
      // Vider d'abord les suggestions pour forcer le rafraîchissement
      this.crSuggestions = [];

      // Puis remettre toutes les suggestions disponibles
      setTimeout(() => {
        this.crSuggestions = [...this.availableCrs];
        console.log('🔄 CR suggestions forcefully updated:', this.crSuggestions.length);
        console.log('🔄 First CR suggestion:', this.crSuggestions[0]?.libelle);
      }, 10);
    }
  }

  /**
   * Compter le nombre total de praticiens uniques
   */
  private countTotalPractitioners(poles: PoleWithCrsAndPractitioners[]): number {
    const practitionersSet = new Set<string>();
    poles.forEach(pole => {
      pole.crs.forEach(cr => {
        cr.practitioners.forEach(practitioner => {
          practitionersSet.add(practitioner['@id']);
        });
      });
    });
    return practitionersSet.size;
  }

  /**
   * Initialiser les données disponibles (toutes au début)
   */
  private initializeAvailableData(): void {
    // Au début, tout est disponible
    this.availablePoles = [...this.serverSidePoles];

    // Extraire tous les CRs de tous les pôles
    this.availableCrs = this.extractAllCrs();

    // Extraire tous les praticiens de tous les CRs
    this.availablePractitioners = this.extractAllPractitioners();

    // Mettre à jour les suggestions pour PrimeNG
    this.practitionerSuggestions = [...this.availablePractitioners];
    this.poleSuggestions = [...this.availablePoles];
    this.crSuggestions = [...this.availableCrs];

    console.log('🔧 Initialized available data:', {
      practitioners: this.availablePractitioners.length,
      poles: this.availablePoles.length,
      crs: this.availableCrs.length
    });
  }

  /**
   * Extraire tous les CRs de tous les pôles
   */
  private extractAllCrs(): CrWithPractitioners[] {
    const allCrs: CrWithPractitioners[] = [];
    this.serverSidePoles.forEach(pole => {
      allCrs.push(...pole.crs);
    });
    return allCrs;
  }

  /**
   * Extraire tous les praticiens uniques de tous les CRs
   */
  private extractAllPractitioners(): Practitioner[] {
    const practitionersMap = new Map<string, Practitioner>();

    this.serverSidePoles.forEach(pole => {
      pole.crs.forEach(cr => {
        cr.practitioners.forEach(practitioner => {
          if (!practitionersMap.has(practitioner['@id'])) {
            practitionersMap.set(practitioner['@id'], practitioner);
          }
        });
      });
    });

    return Array.from(practitionersMap.values());
  }

  /**
   * 🎯 LOGIQUE DE CASCADE : Praticien sélectionné
   * → Filtre les pôles et CRs où ce praticien a posé des actes
   */
  private onPractitionerCascade(practitionerId: string): void {
    console.log('🔄 Cascade: Practitioner selected', practitionerId);

    // Trouver les pôles où ce praticien a posé des actes
    const practitionerPoles: PoleWithCrsAndPractitioners[] = [];

    this.serverSidePoles.forEach(pole => {
      // Filtrer les CRs du pôle où le praticien a posé des actes
      const practitionerCrsInPole = pole.crs.filter(cr =>
        cr.practitioners.some(p => p['@id'] === practitionerId)
      );

      // Si le praticien a posé des actes dans ce pôle, l'ajouter
      if (practitionerCrsInPole.length > 0) {
        practitionerPoles.push({
          ...pole,
          crs: practitionerCrsInPole
        });
      }
    });

    // Extraire tous les CRs où le praticien a posé des actes
    const practitionerCrs: CrWithPractitioners[] = [];
    practitionerPoles.forEach(pole => {
      practitionerCrs.push(...pole.crs);
    });

    // Mettre à jour les données disponibles
    this.availablePractitioners = [...this.extractAllPractitioners()]; // AJOUTÉ: Garder tous les praticiens
    this.availablePoles = practitionerPoles;
    this.availableCrs = practitionerCrs;

    // Mettre à jour les suggestions ET forcer le rafraîchissement
    this.practitionerSuggestions = [...this.availablePractitioners]; // AJOUTÉ: Mettre à jour les suggestions de praticiens
    this.poleSuggestions = [...this.availablePoles];
    this.crSuggestions = [...this.availableCrs];

    // Forcer la détection de changement pour PrimeNG
    setTimeout(() => {
      this.practitionerSuggestions = [...this.availablePractitioners]; // AJOUTÉ: Forcer la mise à jour des praticiens
      this.poleSuggestions = [...this.availablePoles];
      this.crSuggestions = [...this.availableCrs];
    }, 0);

    console.log('🔄 After practitioner cascade:', {
      availablePoles: this.availablePoles.length,
      availableCrs: this.availableCrs.length,
      poleSuggestions: this.poleSuggestions.length,
      crSuggestions: this.crSuggestions.length
    });
  }

  /**
   * 🎯 LOGIQUE DE CASCADE : Pôle sélectionné
   * → Filtre les praticiens et CRs de ce pôle
   */
  private onPoleCascade(poleId: string): void {
    console.log('🔄 Cascade: Pole selected', poleId);

    // Trouver le pôle sélectionné
    const selectedPole = this.serverSidePoles.find(p => p['@id'] === poleId);
    if (!selectedPole) return;

    // IMPORTANT: Garder tous les pôles disponibles pour permettre le changement de sélection
    this.availablePoles = [...this.serverSidePoles];

    // Les CRs disponibles sont ceux du pôle sélectionné
    this.availableCrs = [...selectedPole.crs];

    // Les praticiens disponibles sont ceux qui ont posé des actes dans ce pôle
    const polePractitioners = new Map<string, Practitioner>();
    selectedPole.crs.forEach(cr => {
      cr.practitioners.forEach(practitioner => {
        polePractitioners.set(practitioner['@id'], practitioner);
      });
    });
    this.availablePractitioners = Array.from(polePractitioners.values());

    // Mettre à jour les suggestions ET forcer le rafraîchissement
    this.practitionerSuggestions = [...this.availablePractitioners];
    this.poleSuggestions = [...this.availablePoles]; // AJOUTÉ: Mettre à jour les suggestions de pôles
    this.crSuggestions = [...this.availableCrs];

    // Forcer la détection de changement pour PrimeNG
    setTimeout(() => {
      this.practitionerSuggestions = [...this.availablePractitioners];
      this.poleSuggestions = [...this.availablePoles]; // AJOUTÉ: Forcer la mise à jour des pôles
      this.crSuggestions = [...this.availableCrs];
    }, 0);

    console.log('🔄 After pole cascade:', {
      availablePractitioners: this.availablePractitioners.length,
      availablePoles: this.availablePoles.length, // AJOUTÉ: Log des pôles disponibles
      availableCrs: this.availableCrs.length,
      practitionerSuggestions: this.practitionerSuggestions.length,
      poleSuggestions: this.poleSuggestions.length, // AJOUTÉ: Log des suggestions de pôles
      crSuggestions: this.crSuggestions.length
    });
  }

  /**
   * 🎯 LOGIQUE DE CASCADE : CR sélectionné
   * → Filtre le pôle parent et les praticiens de ce CR
   */
  private onCrCascade(crId: string): void {
    console.log('🔄 Cascade: CR selected', crId);

    // Trouver le pôle qui contient ce CR
    let selectedCr: CrWithPractitioners | null = null;
    let parentPole: PoleWithCrsAndPractitioners | null = null;

    for (const pole of this.serverSidePoles) {
      const foundCr = pole.crs.find(cr => cr['@id'] === crId);
      if (foundCr) {
        selectedCr = foundCr;
        parentPole = pole;
        break;
      }
    }

    if (!selectedCr || !parentPole) return;

    // Le pôle disponible est celui qui contient ce CR
    this.availablePoles = [parentPole];

    // IMPORTANT: Garder tous les CRs disponibles pour permettre le changement de sélection
    this.availableCrs = [...this.extractAllCrs()];

    // Les praticiens disponibles sont ceux du CR sélectionné
    this.availablePractitioners = [...selectedCr.practitioners];

    // Mettre à jour les suggestions ET forcer le rafraîchissement
    this.practitionerSuggestions = [...this.availablePractitioners];
    this.poleSuggestions = [...this.availablePoles];
    this.crSuggestions = [...this.availableCrs]; // AJOUTÉ: Mettre à jour les suggestions de CRs

    // Forcer la détection de changement pour PrimeNG
    setTimeout(() => {
      this.practitionerSuggestions = [...this.availablePractitioners];
      this.poleSuggestions = [...this.availablePoles];
      this.crSuggestions = [...this.availableCrs]; // AJOUTÉ: Forcer la mise à jour des CRs
    }, 0);

    console.log('🔄 After CR cascade:', {
      availablePractitioners: this.availablePractitioners.length,
      availablePoles: this.availablePoles.length,
      practitionerSuggestions: this.practitionerSuggestions.length,
      poleSuggestions: this.poleSuggestions.length,
      parentPole: parentPole.libelle
    });
  }

  /**
   * Reset de la cascade - remettre tous les éléments disponibles
   */
  resetCascade(): void {
    console.log('🔄 Resetting cascade to show all elements');

    // Vider les sélections
    this.selectedPractitioner = null;
    this.selectedPole = null;
    this.selectedCr = null;

    // Remettre toutes les données disponibles
    this.initializeAvailableData();

    console.log('✅ Cascade reset completed');
  }



  /**
   * 🎯 FILTRES CUMULATIFS : Applique tous les filtres actifs en même temps
   */
  private applyCumulativeFilters(): void {
    console.log('🔄 Applying cumulative filters:', {
      selectedPractitioner: this.selectedPractitioner ? this.getPractitionerDisplayName(this.selectedPractitioner) : 'none',
      selectedPole: this.selectedPole?.libelle || 'none',
      selectedCr: this.selectedCr?.libelle || 'none'
    });

    // Commencer avec toutes les données
    let filteredPoles = [...this.serverSidePoles];
    let filteredCrs = this.extractAllCrs();
    let filteredPractitioners = this.extractAllPractitioners();

    // 1. FILTRE PAR PRATICIEN sélectionné
    if (this.selectedPractitioner) {
      const practitionerId = this.selectedPractitioner['@id'];

      // Filtrer les pôles où ce praticien a posé des actes
      filteredPoles = this.serverSidePoles.filter(pole =>
        pole.crs.some(cr =>
          cr.practitioners.some(p => p['@id'] === practitionerId)
        )
      ).map(pole => ({
        ...pole,
        crs: pole.crs.filter(cr =>
          cr.practitioners.some(p => p['@id'] === practitionerId)
        )
      }));

      // Filtrer les CRs où ce praticien a posé des actes
      filteredCrs = filteredCrs.filter(cr =>
        cr.practitioners.some(p => p['@id'] === practitionerId)
      );
    }

    // 2. FILTRE PAR PÔLE sélectionné
    if (this.selectedPole) {
      const poleId = this.selectedPole['@id'];

      // IMPORTANT: Garder TOUS les pôles pour permettre le changement de sélection
      // Ne pas filtrer filteredPoles ici !

      // Filtrer les CRs du pôle sélectionné
      const selectedPoleData = this.serverSidePoles.find(p => p['@id'] === poleId);
      if (selectedPoleData) {
        filteredCrs = filteredCrs.filter(cr =>
          selectedPoleData.crs.some(poleCr => poleCr['@id'] === cr['@id'])
        );

        // Filtrer les praticiens du pôle sélectionné
        const polePractitioners = new Map<string, Practitioner>();
        selectedPoleData.crs.forEach(cr => {
          cr.practitioners.forEach(practitioner => {
            polePractitioners.set(practitioner['@id'], practitioner);
          });
        });
        filteredPractitioners = Array.from(polePractitioners.values());
      }
    }

    // 3. FILTRE PAR CR sélectionné
    if (this.selectedCr) {
      const crId = this.selectedCr['@id'];

      // IMPORTANT: Garder TOUS les CRs pour permettre le changement de sélection
      // Ne pas filtrer filteredCrs ici !

      // Trouver le pôle parent du CR
      let parentPole: PoleWithCrsAndPractitioners | null = null;
      for (const pole of this.serverSidePoles) {
        if (pole.crs.some(cr => cr['@id'] === crId)) {
          parentPole = pole;
          break;
        }
      }

      // Ne pas filtrer les pôles non plus, garder tous les pôles disponibles

      // Filtrer les praticiens du CR sélectionné
      const selectedCrData = this.extractAllCrs().find(cr => cr['@id'] === crId);
      if (selectedCrData) {
        filteredPractitioners = [...selectedCrData.practitioners];
      }
    }

    // Mettre à jour les données disponibles
    this.availablePoles = filteredPoles;
    this.availableCrs = filteredCrs;
    this.availablePractitioners = filteredPractitioners;

    // Mettre à jour les suggestions
    this.poleSuggestions = [...this.availablePoles];
    this.crSuggestions = [...this.availableCrs];
    this.practitionerSuggestions = [...this.availablePractitioners];

    // Forcer la détection de changement pour PrimeNG
    setTimeout(() => {
      this.poleSuggestions = [...this.availablePoles];
      this.crSuggestions = [...this.availableCrs];
      this.practitionerSuggestions = [...this.availablePractitioners];
    }, 0);

    console.log('✅ Cumulative filters applied:', {
      availablePoles: this.availablePoles.length,
      availableCrs: this.availableCrs.length,
      availablePractitioners: this.availablePractitioners.length
    });
  }

  /**
   * Supprimer le filtre praticien
   */
  removePractitionerFilter(): void {
    console.log('🗑️ Removing practitioner filter');
    this.selectedPractitioner = null;
    this.applyCumulativeFilters();
  }

  /**
   * Supprimer le filtre pôle
   */
  removePoleFilter(): void {
    console.log('🗑️ Removing pole filter');
    this.selectedPole = null;
    this.applyCumulativeFilters();
  }

  /**
   * Supprimer le filtre CR
   */
  removeCrFilter(): void {
    console.log('🗑️ Removing CR filter');
    this.selectedCr = null;
    this.applyCumulativeFilters();
  }
}
