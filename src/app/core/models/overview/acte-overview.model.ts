export interface ActeDetails {
  acteId: string;
  code: string;
  description: string;
  nbTotalAnneeNmoinsUn: number;
  nbTotalAnneeN: number;
}

export interface UFActeSummary {
  ufId: string;
  nomUF: string;
  nbActesAnneeNmoinsUn: number;
  nbActesAnneeN: number;
}

export interface PraticienActeSummary {
  praticienId: string;
  nom: string;
  prenom: string;
  nbActesAnneeNmoinsUn: number;
  partAnneeNmoinsUn: string;
  nbActesAnneeN: number;
  partAnneeN: string;
}

export interface ActeSummary {
  anneeNmoinsUn: string;
  anneeN: string;
  actesDetails: ActeDetails;
  ufs: UFActeSummary[];
  acteParPraticienSummary: PraticienActeSummary[];
}
