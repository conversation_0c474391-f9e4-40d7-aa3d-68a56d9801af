import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../auth/auth.service';
import { BaseImportService, ImportValidation } from './base-import.service';

export interface GardeAstreinteImportResponse {
  success: boolean;
  processed: number;
  results?: any[];
  errors?: any[];
}

@Injectable({
  providedIn: 'root'
})
export class GardeAstreinteService extends BaseImportService {

  constructor(
    protected override http: HttpClient,
    protected override authService: AuthService
  ) {
    super(http, authService);
  }

  /**
   * Construire l'URL d'import pour Garde et Astreinte
   */
  protected buildImportUrl(): string {
    return `${environment.api_url}/api/gardes-astreintes/batch`;
  }

  /**
   * Valider les données Garde et Astreinte
   */
  protected validateData(data: any[]): ImportValidation {
    const errors: string[] = [];

    console.log('🔍 Validating Garde et Astreinte data:', {
      dataCount: data.length,
      sampleData: data.slice(0, 2)
    });

    if (!data || data.length === 0) {
      errors.push('Aucune donnée à importer');
      return { isValid: false, errors };
    }

    // Validation basique des champs requis pour Garde et Astreinte
    data.forEach((item, index) => {
      if (!item.dateGarde && !item.dateAstreinte) {
        errors.push(`Ligne ${index + 1}: dateGarde ou dateAstreinte requise`);
      }
      // if (!item.typeGarde && !item.typeAstreinte) {
      //   errors.push(`Ligne ${index + 1}: typeGarde ou typeAstreinte requis`);
      // }
      if (  !item.agentHrU) {
        errors.push(`Ligne ${index + 1}: identifiant agent requis (  agentHrU)`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
