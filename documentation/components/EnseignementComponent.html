<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  EnseignementComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#styleData" class="nav-link"
                role="tab" id="styleData-tab" data-bs-toggle="tab" data-link="style">Styles</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/pages/enseignement/enseignement.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-enseignement</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>DatePipe</code>
                            <code>ChartModule</code>
                            <code>NgIf</code>
                            <code>NgForOf</code>
                            <code>StyleClassModule</code>
                                <code><a href="../components/BreadcrumbComponent.html" target="_self" >BreadcrumbComponent</a></code>
                            <code>TableModule</code>
                            <code>FormsModule</code>
                            <code>AutoCompleteModule</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-3">styleUrls</td>
                <td class="col-md-9"><code>./enseignement.component.scss</code></td>
            </tr>



            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./enseignement.component.html</code></td>
            </tr>








        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#barChartData" >barChartData</a>
                            </li>
                            <li>
                                <a href="#barChartOptions" >barChartOptions</a>
                            </li>
                            <li>
                                <a href="#breadcrumbItems" >breadcrumbItems</a>
                            </li>
                            <li>
                                <a href="#comparisonChartData" >comparisonChartData</a>
                            </li>
                            <li>
                                <a href="#comparisonChartOptions" >comparisonChartOptions</a>
                            </li>
                            <li>
                                <a href="#filteredPractitioners" >filteredPractitioners</a>
                            </li>
                            <li>
                                <a href="#hasError" >hasError</a>
                            </li>
                            <li>
                                <a href="#isLoading" >isLoading</a>
                            </li>
                            <li>
                                <a href="#selectedPractitioner" >selectedPractitioner</a>
                            </li>
                            <li>
                                <a href="#teachingData" >teachingData</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#filterPractitioners" >filterPractitioners</a>
                            </li>
                            <li>
                                <a href="#initializeBarChart" >initializeBarChart</a>
                            </li>
                            <li>
                                <a href="#initializeComparisonChart" >initializeComparisonChart</a>
                            </li>
                            <li>
                                <a href="#initializeStackedBarChart" >initializeStackedBarChart</a>
                            </li>
                            <li>
                                <a href="#loadTeachingData" >loadTeachingData</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#onInput" >onInput</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(enseignementService: EnseignementService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="44" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:44</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>enseignementService</td>

                                                        <td>
                                                                    <code>EnseignementService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filterPractitioners"></a>
                    <span class="name">
                        <span ><b>filterPractitioners</b></span>
                        <a href="#filterPractitioners"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>filterPractitioners(event: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="83"
                                    class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:83</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeBarChart"></a>
                    <span class="name">
                        <span ><b>initializeBarChart</b></span>
                        <a href="#initializeBarChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeBarChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="93"
                                    class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:93</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeComparisonChart"></a>
                    <span class="name">
                        <span ><b>initializeComparisonChart</b></span>
                        <a href="#initializeComparisonChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeComparisonChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="223"
                                    class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:223</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeStackedBarChart"></a>
                    <span class="name">
                        <span ><b>initializeStackedBarChart</b></span>
                        <a href="#initializeStackedBarChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeStackedBarChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="152"
                                    class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:152</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadTeachingData"></a>
                    <span class="name">
                        <span ><b>loadTeachingData</b></span>
                        <a href="#loadTeachingData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadTeachingData()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="53"
                                    class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:53</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="49"
                                    class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:49</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onInput"></a>
                    <span class="name">
                        <span ><b>onInput</b></span>
                        <a href="#onInput"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onInput(event: Event)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="285"
                                    class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:285</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                        <code>Event</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="barChartData"></a>
                    <span class="name">
                        <span ><b>barChartData</b></span>
                        <a href="#barChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="39" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:39</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="barChartOptions"></a>
                    <span class="name">
                        <span ><b>barChartOptions</b></span>
                        <a href="#barChartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="40" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:40</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="breadcrumbItems"></a>
                    <span class="name">
                        <span ><b>breadcrumbItems</b></span>
                        <a href="#breadcrumbItems"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>BreadcrumbItem[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
    { label: &#x27;Enseignement&#x27;, url: &#x27;/enseignement&#x27; },
    { label: &#x27;Heure d\&#x27;enseignement&#x27; }
  ]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:31</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="comparisonChartData"></a>
                    <span class="name">
                        <span ><b>comparisonChartData</b></span>
                        <a href="#comparisonChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="43" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:43</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="comparisonChartOptions"></a>
                    <span class="name">
                        <span ><b>comparisonChartOptions</b></span>
                        <a href="#comparisonChartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="44" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:44</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="filteredPractitioners"></a>
                    <span class="name">
                        <span ><b>filteredPractitioners</b></span>
                        <a href="#filteredPractitioners"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>TeachingPractitioner[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="81" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:81</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="hasError"></a>
                    <span class="name">
                        <span ><b>hasError</b></span>
                        <a href="#hasError"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>false</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:38</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isLoading"></a>
                    <span class="name">
                        <span ><b>isLoading</b></span>
                        <a href="#isLoading"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>true</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="37" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:37</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="selectedPractitioner"></a>
                    <span class="name">
                        <span ><b>selectedPractitioner</b></span>
                        <a href="#selectedPractitioner"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>TeachingPractitioner | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>null</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="42" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:42</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="teachingData"></a>
                    <span class="name">
                        <span ><b>teachingData</b></span>
                        <a href="#teachingData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>TeachingHours | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>null</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="36" class="link-to-prism">src/app/pages/enseignement/enseignement.component.ts:36</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Component, OnInit } from &#x27;@angular/core&#x27;;
import {DatePipe, NgForOf, NgIf} from &quot;@angular/common&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {StyleClassModule} from &quot;primeng/styleclass&quot;;
import {BreadcrumbComponent} from &quot;../breadcrumb/breadcrumb.component&quot;;
import {BreadcrumbItem} from &quot;../../core/models/breadcrumbItem&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {FormsModule} from &quot;@angular/forms&quot;;
import {EnseignementService} from &quot;../../core/services/enseignement.service&quot;;
import {TeachingHours, TeachingPractitioner} from &quot;../../core/models/enseignement.model&quot;;
import {AutoCompleteModule} from &quot;primeng/autocomplete&quot;;

@Component({
  selector: &#x27;app-enseignement&#x27;,
  templateUrl: &#x27;./enseignement.component.html&#x27;,
  styleUrls: [&#x27;./enseignement.component.scss&#x27;],
  imports: [
    DatePipe,
    ChartModule,
    NgIf,
    NgForOf,
    StyleClassModule,
    BreadcrumbComponent,
    TableModule,
    FormsModule,
    AutoCompleteModule
  ],
  standalone: true
})
export class EnseignementComponent implements OnInit {
  breadcrumbItems: BreadcrumbItem[] &#x3D; [
    { label: &#x27;Enseignement&#x27;, url: &#x27;/enseignement&#x27; },
    { label: &#x27;Heure d\&#x27;enseignement&#x27; }
  ];
  //
  teachingData: TeachingHours | null &#x3D; null;
  isLoading &#x3D; true;
  hasError &#x3D; false;
  barChartData: any;
  barChartOptions: any;
  //
  selectedPractitioner: TeachingPractitioner | null &#x3D; null; // ID du praticien sélectionné
  comparisonChartData: any; // Données du graphique comparatif
  comparisonChartOptions: any; // Options du graphique comparatif


  constructor(private enseignementService: EnseignementService) {}

  ngOnInit(): void {
    this.loadTeachingData();
  }

  loadTeachingData(): void {
    this.enseignementService.getTeachingData().subscribe({
      next: (data) &#x3D;&gt; {
        if (data) {
          this.teachingData &#x3D; data; // Stocke les données
          this.initializeBarChart(); // Initialise le graphique en barres
          this.initializeStackedBarChart();
          this.initializeComparisonChart();

          this.hasError &#x3D; false; // Indique qu&#x27;il n&#x27;y a pas d&#x27;erreur
        } else {
          this.hasError &#x3D; true; // Indique une erreur s&#x27;il n&#x27;y a pas de données
        }
        this.isLoading &#x3D; false; // Indique la fin du chargement
      },
      error: () &#x3D;&gt; {
        this.hasError &#x3D; true; // En cas d&#x27;erreur
        this.isLoading &#x3D; false;
      },
    });

    // Charge les données initiales si elles ne sont pas déjà chargées
    this.enseignementService.loadInitialData().subscribe({
      next: () &#x3D;&gt; console.log(&#x27;Teaching hours data loaded successfully&#x27;),
      error: () &#x3D;&gt; console.error(&#x27;Error loading initial teaching hours data&#x27;),
    });
  }
//
  filteredPractitioners: TeachingPractitioner[] &#x3D; []; // Typage correct

  filterPractitioners(event: any): void {
    const query &#x3D; event.query.toLowerCase();
    this.filteredPractitioners &#x3D;
      this.teachingData?.data?.filter((practitioner: TeachingPractitioner) &#x3D;&gt;
        practitioner.praticienName.toLowerCase().includes(query)
      ) || [];
  }


  //@todo choose juste one (simple bar char or Stacked bar char functions )
  initializeBarChart(): void {
    if (!this.teachingData?.data) return;

    const labels &#x3D; this.teachingData.data.map(
      (practitioner: TeachingPractitioner) &#x3D;&gt; practitioner.praticienName
    );
    const datasets &#x3D; [
      {
        label: &#x27;Cours magistraux&#x27;,
        backgroundColor: &#x27;#42a5f5&#x27;,
        data: this.teachingData.data.map(
          (practitioner: TeachingPractitioner) &#x3D;&gt; practitioner.teachingType.lectures
        ),
      },
      {
        label: &#x27;Enseignement clinique&#x27;,
        backgroundColor: &#x27;#66bb6a&#x27;,
        data: this.teachingData.data.map(
          (practitioner: TeachingPractitioner) &#x3D;&gt; practitioner.teachingType.clinicalTeaching
        ),
      },
      {
        label: &#x27;Encadrement de recherche&#x27;,
        backgroundColor: &#x27;#ffa726&#x27;,
        data: this.teachingData.data.map(
          (practitioner: TeachingPractitioner) &#x3D;&gt; practitioner.teachingType.researchGuidance
        ),
      },
    ];

    this.barChartData &#x3D; {
      labels,
      datasets,
    };

    this.barChartOptions &#x3D; {
      plugins: {
        legend: {
          display: true,
          position: &#x27;top&#x27;,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: &#x27;Praticiens&#x27;,
          },
        },
        y: {
          title: {
            display: true,
            text: &#x27;Heures d’enseignement&#x27;,
          },
        },
      },
    };
  }

  initializeStackedBarChart(): void {
    if (!this.teachingData?.data) return;

    const labels &#x3D; this.teachingData.data.map(
      (practitioner) &#x3D;&gt; practitioner.praticienName
    );

    const datasets &#x3D; [
      {
        label: &#x27;Cours magistraux&#x27;,
        backgroundColor: &#x27;#42a5f5&#x27;,
        data: this.teachingData.data.map(
          (practitioner) &#x3D;&gt;
            practitioner.yearlyBreakdown.reduce(
              (total, year) &#x3D;&gt; total + year.hours, // Addition des heures pour toutes les années
              0
            ) * practitioner.teachingType.lectures // Proportion spécifique au type
        ),
      },
      {
        label: &#x27;Enseignement clinique&#x27;,
        backgroundColor: &#x27;#66bb6a&#x27;,
        data: this.teachingData.data.map(
          (practitioner) &#x3D;&gt;
            practitioner.yearlyBreakdown.reduce((total, year) &#x3D;&gt; total + year.hours, 0) *
            practitioner.teachingType.clinicalTeaching
        ),
      },
      {
        label: &#x27;Encadrement de recherche&#x27;,
        backgroundColor: &#x27;#ffa726&#x27;,
        data: this.teachingData.data.map(
          (practitioner) &#x3D;&gt;
            practitioner.yearlyBreakdown.reduce((total, year) &#x3D;&gt; total + year.hours, 0) *
            practitioner.teachingType.researchGuidance
        ),
      },
    ];

    this.barChartData &#x3D; {
      labels,
      datasets,
    };

    this.barChartOptions &#x3D; {
      plugins: {
        legend: {
          display: true,
          position: &#x27;top&#x27;,
        },
      },
      responsive: true,
      scales: {
        x: {
          stacked: true, // Active l&#x27;empilement sur l&#x27;axe X
          title: {
            display: true,
            text: &#x27;Praticiens&#x27;,
          },
        },
        y: {
          stacked: true, // Active l&#x27;empilement sur l&#x27;axe Y
          title: {
            display: true,
            text: &#x27;Heures d’enseignement&#x27;,
          },
        },
      },
    };
  }
//********************** Comparaison annuelle des heures d&#x27;enseignement par praticien*****************************
   initializeComparisonChart(): void {
    if (!this.teachingData?.data || !this.selectedPractitioner) return;

    const selectedPractitionerData &#x3D; this.teachingData.data.find(
      (practitioner) &#x3D;&gt; practitioner.praticienId &#x3D;&#x3D;&#x3D; this.selectedPractitioner?.praticienId
    );

    if (!selectedPractitionerData) return;

    const years &#x3D; selectedPractitionerData.yearlyBreakdown.map((yearData) &#x3D;&gt; yearData.year);

    const datasets &#x3D; [
      {
        label: selectedPractitionerData.praticienName,
        data: selectedPractitionerData.yearlyBreakdown.map((yearData) &#x3D;&gt; yearData.hours),
        borderColor: &#x27;#42a5f5&#x27;,
        backgroundColor: &#x27;rgba(66, 165, 245, 0.5)&#x27;,
        fill: true,
      },
      ...this.teachingData.data
        .filter((practitioner) &#x3D;&gt; practitioner.praticienId !&#x3D;&#x3D; this.selectedPractitioner?.praticienId)
        .map((practitioner) &#x3D;&gt; ({
          label: practitioner.praticienName,
          data: practitioner.yearlyBreakdown.map((yearData) &#x3D;&gt; yearData.hours),
          borderColor: &#x27;#66bb6a&#x27;,
          backgroundColor: &#x27;rgba(102, 187, 106, 0.5)&#x27;,
          fill: false,
          borderDash: [5, 5],
        })),
    ];

    this.comparisonChartData &#x3D; {
      labels: years,
      datasets,
    };

    this.comparisonChartOptions &#x3D; {
      plugins: {
        legend: {
          display: true,
          position: &#x27;top&#x27;,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: &#x27;Années&#x27;,
          },
        },
        y: {
          title: {
            display: true,
            text: &#x27;Heures d’enseignement&#x27;,
          },
        },
      }
    };
  }
  //***********************************************************************************/


  onInput(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;app-breadcrumb [items]&#x3D;&quot;breadcrumbItems&quot;&gt;&lt;/app-breadcrumb&gt;



&lt;div class&#x3D;&quot;p-4&quot;&gt;
  &lt;!-- Loader --&gt;
  &lt;div *ngIf&#x3D;&quot;isLoading&quot; class&#x3D;&quot;text-center&quot;&gt;
    &lt;p&gt;Chargement des données d&#x27;enseignement...&lt;/p&gt;
  &lt;/div&gt;

  &lt;!-- Error Message --&gt;
  &lt;div *ngIf&#x3D;&quot;hasError&quot; class&#x3D;&quot;text-center text-red-500&quot;&gt;
    &lt;p&gt;Erreur lors du chargement des données. Veuillez réessayer plus tard.&lt;/p&gt;
  &lt;/div&gt;

  &lt;!-- Data Display --&gt;
  &lt;div *ngIf&#x3D;&quot;!isLoading &amp;&amp; !hasError&quot;&gt;
    &lt;!-- Last Updated --&gt;
    &lt;div class&#x3D;&quot;mb-4&quot;&gt;
      &lt;p&gt;&lt;strong&gt;Dernière actualisation :&lt;/strong&gt; {{ teachingData?.lastUpdated | date: &#x27;dd/MM/yyyy&#x27; }}&lt;/p&gt;
    &lt;/div&gt;

    &lt;!-- Data Table --&gt;
    &lt;div *ngIf&#x3D;&quot;teachingData &amp;&amp; !isLoading &amp;&amp; !hasError&quot;&gt;
      &lt;div&gt;
        &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
          &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
          &lt;/div&gt;
          &lt;input
            type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
            (input)&#x3D;&quot;teachingTable.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
            class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
            placeholder&#x3D;&quot;  Filtrer par praticien,service,uf ...&quot;
          /&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;p-table
      #teachingTable
      [value]&#x3D;&quot;teachingData?.data || []&quot;
      [paginator]&#x3D;&quot;true&quot;
      [rows]&#x3D;&quot;5&quot;
      [globalFilterFields]&#x3D;&quot;[&#x27;praticienName&#x27;, &#x27;service&#x27;, &#x27;uf&#x27;]&quot;
      [showCurrentPageReport]&#x3D;&quot;true&quot;
      [rowsPerPageOptions]&#x3D;&quot;[5, 10, 20]&quot;
      currentPageReportTemplate&#x3D;&quot;Affichage {first} à {last} sur {totalRecords}&quot;
      class&#x3D;&quot;mb-4&quot;
    &gt;
      &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
        &lt;tr&gt;
          &lt;th pSortableColumn&#x3D;&quot;praticienName&quot;&gt;Praticien &lt;p-sortIcon field&#x3D;&quot;praticienName&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;service&quot;&gt;Service &lt;p-sortIcon field&#x3D;&quot;service&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;uf&quot;&gt;UF &lt;p-sortIcon field&#x3D;&quot;uf&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;totalTeachingHours&quot;&gt;Total Heures &lt;p-sortIcon field&#x3D;&quot;totalTeachingHours&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;teachingType.lectures&quot;&gt;Cours magistraux &lt;p-sortIcon field&#x3D;&quot;teachingType.lectures&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;teachingType.clinicalTeaching&quot;&gt;Enseignement clinique &lt;p-sortIcon field&#x3D;&quot;teachingType.clinicalTeaching&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
          &lt;th pSortableColumn&#x3D;&quot;teachingType.researchGuidance&quot;&gt;Encadrement &lt;p-sortIcon field&#x3D;&quot;teachingType.researchGuidance&quot;&gt;&lt;/p-sortIcon&gt;&lt;/th&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
      &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-practitioner&gt;
        &lt;tr&gt;
          &lt;td&gt;{{ practitioner.praticienName }}&lt;/td&gt;
          &lt;td&gt;{{ practitioner.service }}&lt;/td&gt;
          &lt;td&gt;{{ practitioner.uf }}&lt;/td&gt;
          &lt;td&gt;{{ practitioner.totalTeachingHours }}&lt;/td&gt;
          &lt;td&gt;{{ practitioner.teachingType.lectures }}&lt;/td&gt;
          &lt;td&gt;{{ practitioner.teachingType.clinicalTeaching }}&lt;/td&gt;
          &lt;td&gt;{{ practitioner.teachingType.researchGuidance }}&lt;/td&gt;
        &lt;/tr&gt;
      &lt;/ng-template&gt;
    &lt;/p-table&gt;


    &lt;!-- Bar Chart --&gt;
    &lt;p-chart type&#x3D;&quot;bar&quot; [data]&#x3D;&quot;barChartData&quot; [options]&#x3D;&quot;barChartOptions&quot; class&#x3D;&quot;block&quot;&gt;&lt;/p-chart&gt;

      &lt;section class&#x3D;&quot;w-full mx-auto p-6 bg-white rounded-lg shadow-md&quot;&gt;
        &lt;h2 class&#x3D;&quot;text-xl font-semibold text-gray-800 mb-4&quot;&gt;
          Comparaison des Heures d’Enseignement des 3 dernières années
        &lt;/h2&gt;
        &lt;div class&#x3D;&quot;mb-6&quot;&gt;
          &lt;label for&#x3D;&quot;practitionerSelect&quot; class&#x3D;&quot;block text-sm font-medium text-gray-700 mb-2&quot;&gt;
            Sélectionnez un praticien :
          &lt;/label&gt;
          &lt;div class&#x3D;&quot;relative w-full&quot;&gt;
            &lt;p-autoComplete
              [(ngModel)]&#x3D;&quot;selectedPractitioner&quot;
              [dropdown]&#x3D;&quot;true&quot;
              [suggestions]&#x3D;&quot;filteredPractitioners&quot;
              optionLabel&#x3D;&quot;praticienName&quot;
              [forceSelection]&#x3D;&quot;true&quot;
              (completeMethod)&#x3D;&quot;filterPractitioners($event)&quot;
              (onSelect)&#x3D;&quot;initializeComparisonChart()&quot;
              [placeholder]&#x3D;&quot;&#x27;Filtrer par praticien...&#x27;&quot;
              id&#x3D;&quot;practitionerSelect&quot;
              class&#x3D;&quot;w-full p-3 text-gray-700 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm&quot;
            &gt;&lt;/p-autoComplete&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;!-- Graphique comparatif --&gt;
        &lt;p-chart
          type&#x3D;&quot;line&quot;
          [data]&#x3D;&quot;comparisonChartData&quot;
          [options]&#x3D;&quot;comparisonChartOptions&quot;
          *ngIf&#x3D;&quot;comparisonChartData&quot;
          class&#x3D;&quot;w-full&quot;
        &gt;&lt;/p-chart&gt;
      &lt;/section&gt;



    &lt;/div&gt;
&lt;/div&gt;
</code></pre>
    </div>

    <div class="tab-pane fade " id="styleData">
                <p class="comment">
                    <code>./enseignement.component.scss</code>
                </p>
                <pre class="line-numbers"><code class="language-scss">.chart-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}

h2 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
}


</code></pre>
    </div>

    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb><div class="p-4">  <!-- Loader -->  <div *ngIf="isLoading" class="text-center">    <p>Chargement des données d\'enseignement...</p>  </div>  <!-- Error Message -->  <div *ngIf="hasError" class="text-center text-red-500">    <p>Erreur lors du chargement des données. Veuillez réessayer plus tard.</p>  </div>  <!-- Data Display -->  <div *ngIf="!isLoading && !hasError">    <!-- Last Updated -->    <div class="mb-4">      <p><strong>Dernière actualisation :</strong> {{ teachingData?.lastUpdated | date: \'dd/MM/yyyy\' }}</p>    </div>    <!-- Data Table -->    <div *ngIf="teachingData && !isLoading && !hasError">      <div>        <div class="relative mt-2 rounded-md shadow-sm">          <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>          </div>          <input            type="text" name="price"            (input)="teachingTable.filterGlobal(onInput($event), \'contains\')"            class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "            placeholder="  Filtrer par praticien,service,uf ..."          />        </div>      </div>    <p-table      #teachingTable      [value]="teachingData?.data || []"      [paginator]="true"      [rows]="5"      [globalFilterFields]="[\'praticienName\', \'service\', \'uf\']"      [showCurrentPageReport]="true"      [rowsPerPageOptions]="[5, 10, 20]"      currentPageReportTemplate="Affichage {first} à {last} sur {totalRecords}"      class="mb-4"    >      <ng-template pTemplate="header">        <tr>          <th pSortableColumn="praticienName">Praticien <p-sortIcon field="praticienName"></p-sortIcon></th>          <th pSortableColumn="service">Service <p-sortIcon field="service"></p-sortIcon></th>          <th pSortableColumn="uf">UF <p-sortIcon field="uf"></p-sortIcon></th>          <th pSortableColumn="totalTeachingHours">Total Heures <p-sortIcon field="totalTeachingHours"></p-sortIcon></th>          <th pSortableColumn="teachingType.lectures">Cours magistraux <p-sortIcon field="teachingType.lectures"></p-sortIcon></th>          <th pSortableColumn="teachingType.clinicalTeaching">Enseignement clinique <p-sortIcon field="teachingType.clinicalTeaching"></p-sortIcon></th>          <th pSortableColumn="teachingType.researchGuidance">Encadrement <p-sortIcon field="teachingType.researchGuidance"></p-sortIcon></th>        </tr>      </ng-template>      <ng-template pTemplate="body" let-practitioner>        <tr>          <td>{{ practitioner.praticienName }}</td>          <td>{{ practitioner.service }}</td>          <td>{{ practitioner.uf }}</td>          <td>{{ practitioner.totalTeachingHours }}</td>          <td>{{ practitioner.teachingType.lectures }}</td>          <td>{{ practitioner.teachingType.clinicalTeaching }}</td>          <td>{{ practitioner.teachingType.researchGuidance }}</td>        </tr>      </ng-template>    </p-table>    <!-- Bar Chart -->    <p-chart type="bar" [data]="barChartData" [options]="barChartOptions" class="block"></p-chart>      <section class="w-full mx-auto p-6 bg-white rounded-lg shadow-md">        <h2 class="text-xl font-semibold text-gray-800 mb-4">          Comparaison des Heures d’Enseignement des 3 dernières années        </h2>        <div class="mb-6">          <label for="practitionerSelect" class="block text-sm font-medium text-gray-700 mb-2">            Sélectionnez un praticien :          </label>          <div class="relative w-full">            <p-autoComplete              [(ngModel)]="selectedPractitioner"              [dropdown]="true"              [suggestions]="filteredPractitioners"              optionLabel="praticienName"              [forceSelection]="true"              (completeMethod)="filterPractitioners($event)"              (onSelect)="initializeComparisonChart()"              [placeholder]="\'Filtrer par praticien...\'"              id="practitionerSelect"              class="w-full p-3 text-gray-700 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"            ></p-autoComplete>          </div>        </div>        <!-- Graphique comparatif -->        <p-chart          type="line"          [data]="comparisonChartData"          [options]="comparisonChartOptions"          *ngIf="comparisonChartData"          class="w-full"        ></p-chart>      </section>    </div></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'EnseignementComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'EnseignementComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
