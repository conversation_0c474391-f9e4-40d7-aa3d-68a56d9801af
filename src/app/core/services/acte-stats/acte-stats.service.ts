import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { DatePeriod } from '../global-filter/global-filter.service';
import { UfActeStats, UfActeStatsResponse } from '../../models/acte-stats/UfActeStats';
import { PraticienActeStats, PraticienActeStatsResponse } from '../../models/acte-stats/PraticienActeStats';
import { environment } from '../../../../environments/environment';

// Interface pour les données temporelles
export interface ActeTemporalData {
  mois: string;           // Format: "2024-01"
  annee: number;          // 2024
  moisNumero: number;     // 1-12
  p1Count: number;        // Nombre de réalisations P1
  p2Count: number;        // Nombre de réalisations P2
  p3Count: number;        // Nombre de réalisations P3
}

export interface ActeTemporalStatsResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  totalItems: number;
  member: ActeTemporalData[];
  view?: any;
}

@Injectable({
  providedIn: 'root'
})
export class ActeStatsService {
  private readonly API_BASE_URL = `${environment.api_url}/api/actes`;

  constructor(private http: HttpClient) {}

  /**
   * Get UF statistics for a specific acte across periods
   * @param acteId The acte ID
   * @param p1 Period 1 (current)
   * @param p2 Period 2 (previous year)
   * @param p3 Period 3 (two years ago)
   * @returns Observable of UF statistics
   */
  getUfStatsByActe(
    acteId: string,
    p1: DatePeriod | null,
    p2: DatePeriod | null,
    p3: DatePeriod | null
  ): Observable<UfActeStats[]> {
    const params = this.buildPeriodParams(p1, p2, p3);
    const url = `${this.API_BASE_URL}/${acteId}/uf-single-stats?${params}`;

    return this.http.get<UfActeStatsResponse>(url).pipe(
      map(response => response.member || []),
      catchError(error => {
        console.error('Error loading UF stats from API:', error);
        // Fallback to mock data in case of error
        return this.getMockUfStats(acteId);
      })
    );
  }

  /**
   * Get Praticien statistics for a specific acte across periods
   * @param acteId The acte ID
   * @param p1 Period 1 (current)
   * @param p2 Period 2 (previous year)
   * @param p3 Period 3 (two years ago)
   * @returns Observable of Praticien statistics
   */
  getPraticienStatsByActe(
    acteId: string,
    p1: DatePeriod | null,
    p2: DatePeriod | null,
    p3: DatePeriod | null
  ): Observable<PraticienActeStats[]> {
    const params = this.buildPeriodParams(p1, p2, p3);
    const url = `${this.API_BASE_URL}/${acteId}/praticien-single-stats?${params}`;

    return this.http.get<PraticienActeStatsResponse>(url).pipe(
      map(response => response.member || []),
      catchError(error => {
        console.error('Error loading Praticien stats from API:', error);
        // Fallback to mock data in case of error
        return this.getMockPraticienStats(acteId);
      })
    );
  }

  /**
   * Build URL parameters for periods
   */
  private buildPeriodParams(p1: DatePeriod | null, p2: DatePeriod | null, p3: DatePeriod | null): string {
    const params = new URLSearchParams();

    if (p1?.dateDebut && p1?.dateFin) {
      params.set('p1Start', this.formatDate(p1.dateDebut));
      params.set('p1End', this.formatDate(p1.dateFin));
    }

    if (p2?.dateDebut && p2?.dateFin) {
      params.set('p2Start', this.formatDate(p2.dateDebut));
      params.set('p2End', this.formatDate(p2.dateFin));
    }

    if (p3?.dateDebut && p3?.dateFin) {
      params.set('p3Start', this.formatDate(p3.dateDebut));
      params.set('p3End', this.formatDate(p3.dateFin));
    }

    return params.toString();
  }

  /**
   * Format date for API
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Handle Http operation that failed
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);
      return of(result as T);
    };
  }

  /**
   * Get mock UF statistics (temporary)
   */
  private getMockUfStats(acteId: string): Observable<UfActeStats[]> {
    return this.http.get<UfActeStats[]>('/assets/mock/uf-acte-stats.json').pipe(
      catchError(this.handleError<UfActeStats[]>('getMockUfStats', []))
    );
  }

  /**
   * Get temporal statistics for a specific acte across periods
   * @param acteCode The acte code (not ID)
   * @param p1 Period 1 (current)
   * @param p2 Period 2 (previous year)
   * @param p3 Period 3 (two years ago)
   * @returns Observable of temporal statistics
   */
  getTemporalStatsByActe(
    acteCode: string,
    p1: DatePeriod | null,
    p2: DatePeriod | null,
    p3: DatePeriod | null
  ): Observable<ActeTemporalData[]> {
    const params = this.buildPeriodParams(p1, p2, p3);
    const url = `${this.API_BASE_URL}/${acteCode}/nb-realisation-par-periode-par-mois-stats?${params}`;

    return this.http.get<ActeTemporalStatsResponse>(url).pipe(
      map(response => response.member || []),
      catchError(error => {
        console.error('Error loading temporal stats from API:', error);
        return of([]); // Retourner un tableau vide en cas d'erreur
      })
    );
  }

  /**
   * Get mock Praticien statistics (temporary)
   */
  private getMockPraticienStats(acteId: string): Observable<PraticienActeStats[]> {
    return this.http.get<PraticienActeStats[]>('/assets/mock/praticien-acte-stats.json').pipe(
      catchError(this.handleError<PraticienActeStats[]>('getMockPraticienStats', []))
    );
  }
}
