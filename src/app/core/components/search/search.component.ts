import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { SearchResponse } from '../../models/search-result.model';
import { SearchEntity } from '../../models/search-entity.model';
import { debounceTime, distinctUntilChanged, filter, of, Subject, switchMap } from 'rxjs';
import { FuseSearchService } from '../../services/fuse-search.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { catchError } from 'rxjs/operators';
import { AuthService } from '../../services/auth/auth.service';

@Component({
  selector: 'app-search',
  standalone: false,
  templateUrl: './search.component.html',
  styleUrl: './search.component.scss',
  animations: [
    trigger('fadeAnimation', [
      state('void', style({ opacity: 0 })),
      state('visible', style({ opacity: 1 })),
      transition(':enter', [animate('200ms ease-in')]),
      transition(':leave', [animate('200ms ease-out', style({ opacity: 0 }))])
    ])
  ]
})
export class SearchComponent implements OnInit {
  searchResults: { results: SearchEntity[], total_results: number } | null = null;
  private searchTerms = new Subject<string>(); // Pour gérer les termes de recherche
  isLoading = false; // Nouvelle propriété pour le chargement
  currentSearchTerm: string = ''; // Pour stocker le terme de recherche actuel

  // Pagination
  totalRecords = 0;
  rows = 5; // Nombre d'éléments par page
  first = 0; // Index de départ
  // Messages dynamiques
  noResultsMessage: string = 'Aucun résultat trouvé pour ces critères dans le système';
  errorMessage: string = 'Une erreur s\'est produite lors de la recherche. Veuillez réessayer plus tard.';

  constructor(
    private authService: AuthService,
    private router: Router,
    private fuseSearchService: FuseSearchService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.searchTerms
      .pipe(
        debounceTime(500), // Augmenter à 500ms pour réduire les appels
        distinctUntilChanged(),
        filter((term) => term.length >= 3), // Ne lancer que si ≥ 3 caractères
        switchMap((term: string) => {
          console.log('Recherche déclenchée pour :', term);
          this.currentSearchTerm = term; // Stocker le terme de recherche
          this.isLoading = true; // Activer le spinner
          return this.fuseSearchService.search(term, 10).pipe(
            catchError((error) => {
              console.error('Erreur API :', error);
              this.isLoading = false; // Désactiver le spinner en cas d'erreur

              // Créer une réponse d'erreur avec le terme de recherche
              const errorResponse = { results: [], total_results: 0 };

              // Gérer les erreurs réseau (pas de statut) et les erreurs HTTP
              if (error.status === undefined || !error.status) {
                // Erreur réseau (ex. ERR_CONNECTION_REFUSED)
                this.noResultsMessage = this.errorMessage;
              } else if (error.status === 400) {
                // Erreur spécifique 400
                this.noResultsMessage = 'Recherche trop courte ou invalide.';
              } else if (error.status !== 200) {
                // Autres erreurs HTTP
                this.noResultsMessage = this.errorMessage;
              }

              // Retourner la réponse d'erreur au lieu de null
              return of(errorResponse);
            })
          );
        })
      )
      .subscribe(
        (response: { results: SearchEntity[], total_results: number } | null) => {
          console.log('Résultats Recherche reçus :', response);

          if (response) {
            console.log('Nombre de résultats:', response.results.length);
            console.log('Terme de recherche actuel:', this.currentSearchTerm);

            this.searchResults = response;
            this.totalRecords = response.total_results || 0;
            this.isLoading = false;

            // TOUJOURS mettre à jour le message selon les résultats
            if (response.results.length === 0 && this.currentSearchTerm) {
              this.noResultsMessage = `Aucun résultat trouvé pour "${this.currentSearchTerm}" dans le système`;
              console.log('✅ Message personnalisé mis à jour:', this.noResultsMessage);
            } else {
              this.noResultsMessage = 'Aucun résultat trouvé pour ces critères dans le système';
              console.log('✅ Message par défaut mis à jour:', this.noResultsMessage);
            }

            // Forcer la détection de changement
            this.cdr.detectChanges();
          } else {
            console.log('❌ Response est null');
            this.isLoading = false;
          }
        }
      );
  }

  handleSearchInput(event: Event): void {
    console.log('Authentifié ?', this.authService.isAuthenticated());

    // decomente pour obliger l'utilisateur a etre authentifier
    // if (!this.authService.isAuthenticated()) {
    //   event.preventDefault();
    //   this.router.navigateByUrl('/auth/login');
    //   return;
    // }

    const input = event.target as HTMLInputElement;
    const query = input.value.trim();
    console.log('Query saisie :', query);
    if (query.length === 0) {
      this.searchResults = null; // Réinitialiser si la saisie est vide
      this.isLoading = false; // Désactiver le spinner si l'input est vide
      this.currentSearchTerm = ''; // Réinitialiser le terme de recherche
    } else {
      this.searchTerms.next(query);
    }
  }

  /**
   * Génère la route appropriée en fonction du type de résultat.
   * @param result - L'objet contenant les détails du résultat (type et id).
   * @returns La chaîne de route pour routerLink (ex. '/organisation/pole-list/:id').
   */
  getRouteForResult(result: any): string[] {
    switch (result.type) {
      case 'pole':
        return ['/organisation/pole-list', result.id];
      case 'service':
        return ['/organisation/service', result.id];
      case 'uf':
        return ['/organisation/ufs', result.id];
      case 'praticien':
        return ['/graphic/praticien', result.id];
      default:
        console.warn(`Type inconnu : ${result.type}, navigation par défaut`);
        return ['/']; // Route par défaut si type non reconnu
    }
  }

  /**
   * Déclenche la recherche lorsque l'utilisateur appuie sur la touche Entrée.
   * @param event - L'événement clavier généré.
   */
  handleEnter(event: Event): void {
    event.preventDefault(); // Empêche le comportement par défaut (ex. soumission de formulaire)
    const input = event.target as HTMLInputElement;
    const query = input.value.trim();
    if (query.length >= 3) {
      this.isLoading = true;
      this.searchTerms.next(query); // Déclenche la recherche via le Subject
    } else {
      this.searchResults = null; // Réinitialise si moins de 3 caractères
      this.isLoading = false;
      this.currentSearchTerm = ''; // Réinitialiser le terme de recherche
    }
  }

  /**
   * Ferme la liste déroulante après un clic sur un item.
   */
  closeDropdown(): void {
    this.searchResults = null; // Réinitialise les résultats pour fermer la liste
    this.isLoading = false;
    this.currentSearchTerm = ''; // Réinitialiser le terme de recherche
  }

  // Méthode appelée par le paginator pour changer de page
  onPageChange(event: any) {
    this.first = event.first;
    this.rows = event.rows;
    // Ici, tu peux appeler searchService.search avec des paramètres de pagination si ton backend le supporte
    // Exemple : this.searchService.search(query, { page: event.page + 1, limit: event.rows });
    console.log('Page changée :', event);
  }



  protected readonly KeyboardEvent = KeyboardEvent;
}
