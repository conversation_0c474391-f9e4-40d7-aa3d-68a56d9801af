/**
 * Interface pour les données d'organisation d'une UF
 * 
 * Représente la hiérarchie organisationnelle d'une UF :
 * UF → CR → POLE
 */
export interface UfOrganisation {
  ufcode: string;
  ufLibelle: string;
  poleCode: string;
  poleLibelle: string;
  crCode: string;
  crLibelle: string;
  dateDebut: string;
  dateFin: string;
  searchStart: string;
  searchEnd: string;
}

/**
 * Interface pour la réponse API Platform
 */
export interface UfOrganisationResponse {
  '@context': any;
  '@type': string;
  '@id': string;
  ufcode: string;
  ufLibelle: string;
  poleCode: string;
  poleLibelle: string;
  crCode: string;
  crLibelle: string;
  dateDebut: string;
  dateFin: string;
  searchStart: string;
  searchEnd: string;
}
