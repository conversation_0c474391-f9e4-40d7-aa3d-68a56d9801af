/* Supprimer la scrollbar sur Webkit (Chrome, Edge, Safari) */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Supprimer la scrollbar sur Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE et Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Assurer que le sélecteur de période reste fixe */
.period-selector {
  position: sticky;
  bottom: 0;
  background-color: #0e7490; /* bg-cyan-700 */
  z-index: 10;
  border-top: 1px solid #0891b2; /* border-cyan-600 */
}

/* Style pour le panel du calendrier rendu dans le body */
:host ::ng-deep .p-datepicker-panel.my-datepicker-panel,
:host ::ng-deep .p-datepicker.my-datepicker-panel,
:host ::ng-deep .my-datepicker-panel {
  min-width: 460px !important;    /* largeur mini (pour le confort même sur sidebar étroite) */
  width: 520px !important;        /* largeur principale desktop */
  max-width: 98vw !important;     /* jamais hors de l'écran */
  z-index: 1090 !important;       /* toujours devant */
  left: 0 !important;             /* positionne bien par rapport à l'input (important si appendTo=body) */
  border-radius: 10px !important;
  box-shadow: 0 8px 24px 0 rgba(0,0,0,0.08) !important;
}

@media (max-width: 800px) {
  :host ::ng-deep .p-datepicker-panel.my-datepicker-panel,
  :host ::ng-deep .p-datepicker.my-datepicker-panel,
  :host ::ng-deep .my-datepicker-panel {
    min-width: 320px !important;
    width: 95vw !important;
    max-width: 99vw !important;
  }
}

/* Styles améliorés pour les calendriers */
:host ::ng-deep .p-datepicker {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; /* Ombre subtile */
  border-radius: 8px !important; /* Coins arrondis */
  font-size: 14px !important; /* Taille de police plus lisible */
}

/* Amélioration des cellules du calendrier */
:host ::ng-deep .p-datepicker table td {
  padding: 8px !important; /* Plus d'espace dans les cellules */
}

/* Augmenter la taille des cellules pour une meilleure lisibilité */
:host ::ng-deep .p-datepicker table td > span {
  width: 40px !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 16px !important;
}

/* Style pour les boutons du calendrier */
:host ::ng-deep .p-datepicker .p-datepicker-header {
  padding: 16px !important; /* Plus d'espace dans l'en-tête */
  font-size: 18px !important; /* Texte plus grand */
}

:host ::ng-deep .p-datepicker .p-datepicker-header button {
  background-color: transparent !important;
  border-radius: 4px !important;
  transition: background-color 0.2s !important;
  width: 36px !important; /* Boutons plus grands */
  height: 36px !important;
}

:host ::ng-deep .p-datepicker .p-datepicker-header button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

:host ::ng-deep .p-datepicker .p-datepicker-title {
  font-size: 18px !important; /* Titre plus grand */
  font-weight: bold !important;
}

/* Style pour les inputs du calendrier */
:host ::ng-deep .p-calendar .p-inputtext {
  padding: 8px 12px !important; /* Plus d'espace dans l'input */
  border-radius: 6px !important; /* Coins plus arrondis */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; /* Ombre subtile */
  transition: all 0.2s ease !important; /* Animation douce */
}

:host ::ng-deep .p-calendar .p-inputtext:hover {
  background-color: #0c637a !important; /* Couleur légèrement plus claire au survol */
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important; /* Ombre plus prononcée au survol */
}

/* Amélioration de l'espacement entre les éléments */
.period-selector .flex.items-center {
  gap: 8px !important; /* Plus d'espace entre les calendriers */
}

/* Style pour le mois sélectionné */
:host ::ng-deep .p-datepicker table td.p-datepicker-today > span {
  background-color: rgba(255, 255, 255, 0.2) !important; /* Fond subtil pour aujourd'hui */
  border-radius: 4px !important;
}

:host ::ng-deep .p-datepicker table td > span.p-highlight {
  background-color: #0891b2 !important; /* Couleur cyan pour la sélection */
  color: white !important;
  border-radius: 4px !important;
}

/* ============================================== */
/* STYLES MODERNES POUR LA SIDEBAR COLLAPSE/EXPAND */
/* ============================================== */

/* Animation fluide pour la largeur de la sidebar */
.sidebar-container {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: width;
}

/* Styles spéciaux pour le header en mode collapsed */
.collapsed-header {
  background: linear-gradient(135deg, rgba(14, 116, 144, 0.8) 0%, rgba(8, 145, 178, 0.8) 100%);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin: 8px;
  padding: 16px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Effet de glow pour le logo en mode collapsed */
.collapsed-logo-container {
  position: relative;
}

.collapsed-logo-container::before {
  content: '';
  position: absolute;
  inset: -4px;
  background: linear-gradient(45deg, #67e8f9, #0891b2, #67e8f9);
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  animation: rotate-glow 3s linear infinite;
}

.collapsed-logo-container:hover::before {
  opacity: 0.3;
}

@keyframes rotate-glow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Bouton toggle amélioré en mode collapsed */
.collapsed-toggle-btn {
  position: relative;
  overflow: hidden;
}

.collapsed-toggle-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: conic-gradient(from 0deg, transparent, rgba(103, 232, 249, 0.3), transparent);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  animation: spin-slow 4s linear infinite;
}

.collapsed-toggle-btn:hover::before {
  opacity: 1;
}

@keyframes spin-slow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Animations pour les éléments de navigation */
.nav-item {
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.nav-item:hover::before {
  left: 100%;
}

/* Effet de glow sur les icônes au hover */
.nav-icon {
  filter: drop-shadow(0 0 0 transparent);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover .nav-icon {
  filter: drop-shadow(0 0 8px rgba(103, 232, 249, 0.5));
  transform: scale(1.1);
}

/* Animation pour le texte qui apparaît/disparaît */
.nav-text {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: left center;
}

.nav-text.collapsed {
  transform: scaleX(0);
  opacity: 0;
}

/* Effet de slide pour les sous-menus */
.submenu-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

.submenu-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
}

.submenu-item:hover {
  transform: translateX(4px);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Animation du bouton toggle */
.toggle-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.toggle-button:hover {
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.1);
}

.toggle-button:active {
  transform: scale(0.95);
}

/* Animation de rotation pour les flèches */
.arrow-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.arrow-icon.rotated {
  transform: rotate(180deg);
}

/* Effet de pulsation pour les éléments actifs */
.nav-item.active {
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(103, 232, 249, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(103, 232, 249, 0.1);
  }
}

/* Micro-interactions pour les tooltips */
.tooltip-trigger {
  position: relative;
}

.tooltip-trigger::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  margin-left: 8px;
}

.tooltip-trigger::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-right-color: rgba(0, 0, 0, 0.9);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  margin-left: 2px;
}

.tooltip-trigger:hover::after,
.tooltip-trigger:hover::before {
  opacity: 1;
  visibility: visible;
}

/* Animation pour la période d'analyse en mode collapsed */
.period-collapsed {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.period-collapsed .calendar-icon {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* Effet de gradient animé pour le background */
.sidebar-gradient {
  background: linear-gradient(135deg, #0e7490 0%, #0891b2 50%, #0e7490 100%);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Responsive design pour les animations */
@media (max-width: 1024px) {
  .nav-item::before {
    display: none; /* Désactiver l'effet de shine sur mobile */
  }

  .nav-icon {
    transition: transform 0.2s ease; /* Animations plus rapides sur mobile */
  }

  /* Ajuster la taille des éléments sur tablette */
  .nav-item {
    padding: 12px 16px !important;
  }

  .nav-icon {
    width: 24px !important;
    height: 24px !important;
  }
}

/* Optimisations spécifiques pour mobile et tablette */
@media (max-width: 1023px) {
  /* Sur tablette et mobile, la sidebar desktop est masquée */
  .sidebar-desktop {
    display: none !important;
  }

  /* Le contenu principal prend toute la largeur */
  .main-content {
    margin-left: 0 !important;
    width: 100% !important;
  }

  /* PROTECTION SIDEBAR MOBILE - Forcer l'affichage */
  .mobile-sidebar-overlay {
    display: flex !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    background: rgba(0, 0, 0, 0.5) !important;
  }
}

/* Optimisations spécifiques pour mobile */
@media (max-width: 767px) {
  /* Améliorer les zones de touch sur mobile */
  .nav-item, .toggle-button {
    min-height: 48px; /* Taille minimum recommandée pour le touch */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Espacer les éléments pour éviter les clics accidentels */
  .nav-item + .nav-item {
    margin-top: 4px;
  }

  /* Ajuster la période d'analyse pour mobile */
  .period-selector {
    padding: 12px !important;
  }

  /* Sidebar mobile plus large pour meilleure UX */
  .mobile-sidebar {
    max-width: 280px !important;
  }
}

/* Optimisations pour tablette */
@media (min-width: 768px) and (max-width: 1023px) {
  /* Assurer que le contenu ne déborde pas */
  .main-content {
    overflow-x: hidden;
  }

  /* Sidebar mobile adaptée pour tablette */
  .mobile-sidebar {
    max-width: 320px !important;
  }
}

/* Optimisations pour les très petits écrans */
@media (max-width: 480px) {
  .sidebar-container {
    width: 56px !important;
  }

  .nav-icon {
    width: 20px !important;
    height: 20px !important;
  }

  .toggle-button {
    padding: 6px !important;
  }
}

/* Mode paysage sur mobile */
@media (max-height: 600px) and (orientation: landscape) {
  .period-selector {
    position: static; /* Ne pas fixer en bas en mode paysage */
    margin-top: auto;
  }

  .nav-item {
    padding: 8px 12px !important; /* Réduire l'espacement vertical */
  }
}

/* Support pour les écrans haute densité */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .nav-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode support (si activé par l'utilisateur) */
@media (prefers-color-scheme: dark) {
  .tooltip-trigger::after {
    background: rgba(255, 255, 255, 0.9);
    color: black;
  }

  .tooltip-trigger::before {
    border-right-color: rgba(255, 255, 255, 0.9);
  }
}

/* ============================================== */
/* STYLES D'ACCESSIBILITÉ */
/* ============================================== */

/* Amélioration de l'accessibilité pour les animations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Classe pour masquer visuellement mais garder accessible aux lecteurs d'écran */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Focus visible amélioré pour l'accessibilité clavier */
.nav-item:focus-visible,
.toggle-button:focus-visible,
button:focus-visible,
a:focus-visible {
  outline: 2px solid #67e8f9 !important; /* cyan-300 */
  outline-offset: 2px !important;
  border-radius: 4px !important;
  box-shadow: 0 0 0 4px rgba(103, 232, 249, 0.3) !important;
}

/* Améliorer le contraste pour les utilisateurs avec des difficultés visuelles */
@media (prefers-contrast: high) {
  .nav-item {
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .nav-item:hover,
  .nav-item.active {
    border-color: #67e8f9;
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  .nav-icon {
    filter: contrast(1.2);
  }
}

/* Support pour les utilisateurs qui préfèrent moins de transparence */
@media (prefers-reduced-transparency: reduce) {
  .nav-item:hover {
    background-color: #0891b2 !important; /* Couleur solide au lieu de transparence */
  }

  .tooltip-trigger::after {
    background: #1f2937 !important; /* Fond solide pour les tooltips */
  }
}

/* Améliorer la lisibilité pour les petites tailles de police */
@media (max-width: 768px) {
  .nav-item {
    font-size: 16px !important; /* Taille minimum pour la lisibilité sur mobile */
  }
}

/* Styles pour les états de focus et d'activation */
.nav-item[aria-current="page"] {
  position: relative;
}

.nav-item[aria-current="page"]::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #67e8f9;
  border-radius: 0 2px 2px 0;
}

/* Améliorer la visibilité des éléments interactifs */
.nav-item,
.toggle-button {
  position: relative;
  cursor: pointer;
}

.nav-item:hover::before,
.toggle-button:hover::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: inherit;
  z-index: -1;
}

/* Support pour les modes de couleur personnalisés */
@media (prefers-color-scheme: dark) {
  .nav-item:focus-visible {
    outline-color: #a5f3fc !important; /* cyan-200 pour le mode sombre */
  }
}

/* Améliorer l'accessibilité des tooltips */
.tooltip-trigger[aria-describedby] {
  position: relative;
}

/* Styles pour les annonces aux lecteurs d'écran */
[aria-live] {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Améliorer la navigation au clavier */
.sidebar-container:focus-within {
  box-shadow: inset 0 0 0 2px rgba(103, 232, 249, 0.5);
}

/* Indicateur visuel pour les éléments avec sous-menus */
.nav-item[aria-expanded="true"] {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-item[aria-expanded="true"] .arrow-icon {
  color: #67e8f9 !important;
}

/* Améliorer l'accessibilité des calendriers */
:host ::ng-deep .p-calendar .p-inputtext:focus {
  outline: 2px solid #67e8f9 !important;
  outline-offset: 2px !important;
}

/* Support pour les utilisateurs avec des troubles vestibulaires */
@media (prefers-reduced-motion: reduce) {
  .nav-item:hover .nav-icon {
    transform: none !important; /* Pas d'animation de scale */
  }

  .submenu-item:hover {
    transform: none !important; /* Pas d'animation de translation */
  }
}
