/**
 * Interface pour les métadonnées des actes médicaux
 * Correspond au DTO ActesMetadataDto du backend
 */
export interface ActesMetadataModel {
  totalItems: number;
  totalByType: { [key: string]: number };
  dateRangeMin: string | null;
  dateRangeMax: string | null;
  lastUpdate: string | null;
  totalAgents: number;
  totalUfs: number;
  appliedPeriods: { [key: string]: string };
  generationTimeMs: number;
}

/**
 * Interface pour les statistiques par type d'acte
 */
export interface ActeTypeStats {
  CCAM: number;
  NGAP: number;
  LABO: number;
  [key: string]: number;
}

/**
 * Interface pour les périodes appliquées
 */
export interface AppliedPeriods {
  p1?: string;
  p2?: string;
  p3?: string;
  [key: string]: string | undefined;
}
