.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: -1;
  pointer-events: none;
  overflow: hidden;
  background:
    radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.95) 0%,
      rgba(241, 245, 249, 0.8) 50%,
      rgba(248, 250, 252, 0.95) 100%);
}

/* Particules flottantes */
.particle {
  position: absolute;
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
  opacity: 0.7;
  box-shadow:
    0 0 20px rgba(14, 165, 233, 0.2),
    0 0 40px rgba(14, 165, 233, 0.1),
    inset 0 0 10px rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(1px);
}

.particle-1 {
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #0ea5e9, #0284c7);
  top: 20%;
  left: 10%;
  animation: float 12s ease-in-out infinite, shimmer 4s ease-in-out infinite;
  animation-delay: 0s;
}

.particle-2 {
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #06b6d4, #0891b2);
  top: 60%;
  left: 80%;
  animation: float 15s ease-in-out infinite, pulse 6s ease-in-out infinite;
  animation-delay: 1s;
}

.particle-3 {
  width: 2px;
  height: 2px;
  background: radial-gradient(circle, #64748b, #475569);
  top: 80%;
  left: 20%;
  animation: float 8s ease-in-out infinite, twinkle 3s ease-in-out infinite;
  animation-delay: 2s;
}

.particle-4 {
  width: 10px;
  height: 10px;
  background: linear-gradient(45deg, #0ea5e9, #38bdf8); /* Bleu clair */
  top: 30%;
  left: 70%;
  animation-delay: 3s;
  animation-duration: 9s;
}

.particle-5 {
  width: 14px;
  height: 14px;
  background: linear-gradient(45deg, #0284c7, #0369a1); /* Bleu foncé */
  top: 10%;
  left: 50%;
  animation-delay: 4s;
  animation-duration: 11s;
}

.particle-6 {
  width: 7px;
  height: 7px;
  background: linear-gradient(45deg, #94a3b8, #cbd5e1); /* Gris argenté */
  top: 70%;
  left: 60%;
  animation-delay: 5s;
  animation-duration: 6s;
}

.particle-7 {
  width: 9px;
  height: 9px;
  background: linear-gradient(45deg, #0ea5e9, #38bdf8); /* Bleu ciel */
  top: 40%;
  left: 15%;
  animation-delay: 1.5s;
  animation-duration: 8.5s;
}

.particle-8 {
  width: 11px;
  height: 11px;
  background: linear-gradient(45deg, #06b6d4, #67e8f9); /* Cyan doux */
  top: 85%;
  left: 75%;
  animation-delay: 2.5s;
  animation-duration: 7.5s;
}

.particle-9 {
  width: 5px;
  height: 5px;
  background: linear-gradient(45deg, #64748b, #94a3b8); /* Gris-bleu */
  top: 15%;
  left: 85%;
  animation-delay: 3.5s;
  animation-duration: 9.5s;
}

.particle-10 {
  width: 13px;
  height: 13px;
  background: linear-gradient(45deg, #0284c7, #0ea5e9); /* Bleu médical */
  top: 55%;
  left: 35%;
  animation-delay: 4.5s;
  animation-duration: 10.5s;
}

/* Formes géométriques */
.shape {
  position: absolute;
  opacity: 0.2;
  filter: blur(0.5px);
}

.shape-circle-1 {
  width: 60px;
  height: 60px;
  border: 2px solid rgba(14, 165, 233, 0.3); /* Bleu médical */
  border-radius: 50%;
  top: 25%;
  left: 25%;
  animation: rotate 20s linear infinite;
}

.shape-circle-2 {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(6, 182, 212, 0.3); /* Cyan professionnel */
  border-radius: 50%;
  top: 65%;
  left: 65%;
  animation: rotate 15s linear infinite reverse;
}

.shape-triangle-1 {
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-bottom: 25px solid rgba(100, 116, 139, 0.3); /* Gris-bleu */
  top: 45%;
  left: 80%;
  animation: float 12s ease-in-out infinite;
  animation-delay: 2s;
}

.shape-triangle-2 {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 35px solid rgba(2, 132, 199, 0.3); /* Bleu foncé */
  top: 75%;
  left: 15%;
  animation: float 14s ease-in-out infinite;
  animation-delay: 4s;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
  }
  50% {
    transform: translateY(-40px) translateX(-10px) rotate(180deg);
  }
  75% {
    transform: translateY(-20px) translateX(15px) rotate(270deg);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Effet de pulsation pour certaines particules */
.particle-2, .particle-5, .particle-8 {
  animation: float 6s ease-in-out infinite, pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

@keyframes shimmer {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(14, 165, 233, 0.2),
      0 0 40px rgba(14, 165, 233, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(14, 165, 233, 0.4),
      0 0 60px rgba(14, 165, 233, 0.2);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .particle {
    transform: scale(0.8);
  }

  .shape-circle-1, .shape-circle-2 {
    transform: scale(0.7);
  }

  .shape-triangle-1, .shape-triangle-2 {
    transform: scale(0.6);
  }
}
