<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  HomeComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#styleData" class="nav-link"
                role="tab" id="styleData-tab" data-bs-toggle="tab" data-link="style">Styles</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/pages/home/<USER>/code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-home</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrls</td>
                <td class="col-md-9"><code>./home.component.scss</code></td>
            </tr>



            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./home.component.html</code></td>
            </tr>








        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>






    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="15"
                                    class="link-to-prism">src/app/pages/home/<USER>/a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Component, OnInit } from &#x27;@angular/core&#x27;;
import { Observable, of } from &#x27;rxjs&#x27;;
import { OlympicService } from &#x27;src/app/core/services/olympic.service&#x27;;

@Component({
  selector: &#x27;app-home&#x27;,
  templateUrl: &#x27;./home.component.html&#x27;,
  styleUrls: [&#x27;./home.component.scss&#x27;],
})
export class HomeComponent implements OnInit {
  // public olympics$: Observable&lt;any&gt; &#x3D; of(null);

  // constructor(private olympicService: OlympicService) {}

  ngOnInit(): void {
    // this.olympics$ &#x3D; this.olympicService.getOlympics();
  }
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;div class&#x3D;&quot;bg-gray-900 text-white&quot;&gt;
  &lt;!-- Header Section --&gt;
  &lt;header class&#x3D;&quot;relative bg-gradient-to-r from-blue-600 to-indigo-800&quot;&gt;
    &lt;div class&#x3D;&quot;max-w-7xl mx-auto px-6 lg:px-8 py-16&quot;&gt;
      &lt;div class&#x3D;&quot;flex flex-col lg:flex-row items-center justify-between&quot;&gt;
        &lt;div class&#x3D;&quot;lg:w-1/2&quot;&gt;
          &lt;h1 class&#x3D;&quot;text-5xl font-extrabold tracking-tight leading-tight&quot;&gt;
            Supra V2 &lt;br /&gt;
            &lt;span class&#x3D;&quot;text-blue-200&quot;&gt;Votre outils pour le suivi de l’activité médicale pour le CHRU de Nancy&lt;/span&gt;
          &lt;/h1&gt;
          &lt;p class&#x3D;&quot;mt-6 text-lg text-gray-300&quot;&gt;
            Une application interne modernisée pour optimiser les statistiques médicales, la gestion des praticiens, et garantir la conformité CNIL et RGPD. Conçue pour aujourd&#x27;hui, avec une vision pour le futur dans le GHT.
          &lt;/p&gt;
          &lt;div class&#x3D;&quot;mt-8 flex space-x-4&quot;&gt;
            &lt;a
              href&#x3D;&quot;#&quot;
              class&#x3D;&quot;px-8 py-3 bg-blue-500 text-white rounded-md font-medium text-lg hover:bg-blue-400&quot;
            &gt;Accéder à Supra&lt;/a
            &gt;
            &lt;a
              href&#x3D;&quot;#features&quot;
              class&#x3D;&quot;px-8 py-3 border border-blue-500 text-blue-200 rounded-md font-medium text-lg hover:bg-blue-500 hover:text-white&quot;
            &gt;Fonctionnalités&lt;/a
            &gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class&#x3D;&quot;lg:w-1/2 mt-10 lg:mt-0&quot;&gt;
          &lt;img
            src&#x3D;&quot;./assets/icon/statistiques.png&quot;
            alt&#x3D;&quot;Capture d&#x27;écran Supra V2&quot;
            class&#x3D;&quot;rounded-lg shadow-lg&quot;
          /&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/header&gt;

  &lt;!-- Mandataires Section --&gt;
  &lt;section class&#x3D;&quot;py-16 bg-gray-800&quot;&gt;
    &lt;div class&#x3D;&quot;max-w-7xl mx-auto px-6 lg:px-8 text-center&quot;&gt;
      &lt;h2 class&#x3D;&quot;text-4xl font-extrabold text-white&quot;&gt;Mandataires&lt;/h2&gt;
      &lt;p class&#x3D;&quot;mt-6 text-lg text-gray-400&quot;&gt;
        Supra V2 est porté par le CHRU de Nancy avec les mandataires suivants :
      &lt;/p&gt;
      &lt;div class&#x3D;&quot;mt-8 grid grid-cols-1 md:grid-cols-2 gap-8&quot;&gt;
        &lt;div class&#x3D;&quot;bg-gray-700 rounded-lg p-6 shadow-lg&quot;&gt;
          &lt;h3 class&#x3D;&quot;text-2xl font-bold text-blue-400&quot;&gt;DFAC&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-300&quot;&gt;
            Direction de la Facturation, responsable de l’exploitation des données d’actes médicaux pour optimiser la gestion financière.
          &lt;/p&gt;
        &lt;/div&gt;
        &lt;div class&#x3D;&quot;bg-gray-700 rounded-lg p-6 shadow-lg&quot;&gt;
          &lt;h3 class&#x3D;&quot;text-2xl font-bold text-blue-400&quot;&gt;CME&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-300&quot;&gt;
            Commission Médicale d&#x27;Établissement, superviseur des statistiques médicales et de l&#x27;activité des praticiens.
          &lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/section&gt;

  &lt;!-- Features Section --&gt;
  &lt;section id&#x3D;&quot;features&quot; class&#x3D;&quot;py-20 bg-gray-900&quot;&gt;
    &lt;div class&#x3D;&quot;max-w-7xl mx-auto px-6 lg:px-8&quot;&gt;
      &lt;h2 class&#x3D;&quot;text-4xl font-extrabold text-center text-white&quot;&gt;Fonctionnalités clés&lt;/h2&gt;
      &lt;p class&#x3D;&quot;mt-4 text-center text-gray-300&quot;&gt;
        SUPRA est un outil développé par le CHRU de Nancy, conçu pour le suivi précis de l&#x27;activité médicale des praticiens, tout en respectant les normes RGPD.
      &lt;/p&gt;
      &lt;div class&#x3D;&quot;mt-12 grid grid-cols-1 md:grid-cols-3 gap-8&quot;&gt;
        &lt;!-- Données hebdomadaires --&gt;
        &lt;div class&#x3D;&quot;text-center&quot;&gt;
          &lt;img
            src&#x3D;&quot;./assets/icon/collecte-de-donnees.png&quot;
            alt&#x3D;&quot;Icon Données Activité&quot;
            class&#x3D;&quot;mx-auto mb-4 w-20 h-20&quot;
          /&gt;
          &lt;h3 class&#x3D;&quot;text-2xl font-bold text-blue-400&quot;&gt;Données d&#x27;activité&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            Mise à jour hebdomadaire des données d&#x27;activité, incluant l&#x27;activité externe, hospitalisée, ou les deux, telles qu&#x27;elles sont tracées dans le système d&#x27;information du CHRU.
          &lt;/p&gt;
        &lt;/div&gt;
        &lt;!-- Données annuelles --&gt;
        &lt;div class&#x3D;&quot;text-center&quot;&gt;
          &lt;img
            src&#x3D;&quot;./assets/icon/transfert-de-donnees.png&quot;
            alt&#x3D;&quot;Icon Données Annuelles&quot;
            class&#x3D;&quot;mx-auto mb-4 w-20 h-20&quot;
          /&gt;
          &lt;h3 class&#x3D;&quot;text-2xl font-bold text-blue-400&quot;&gt;Données annuelles&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            Une fois par an, SUPRA fournit des données sur les heures d’enseignement, les SIGAPS, les gardes et astreintes rémunérées, ainsi que d&#x27;autres activités clés.
          &lt;/p&gt;
        &lt;/div&gt;
        &lt;!-- Fiabilisation des données --&gt;
        &lt;div class&#x3D;&quot;text-center&quot;&gt;
          &lt;img
            src&#x3D;&quot;./assets/icon/proteger.png&quot;
            alt&#x3D;&quot;Icon Fiabilisation&quot;
            class&#x3D;&quot;mx-auto mb-4 w-20 h-20&quot;
          /&gt;
          &lt;h3 class&#x3D;&quot;text-2xl font-bold text-blue-400&quot;&gt;Fiabilisation des données&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            SUPRA détecte les anomalies dans la saisie des actes et fiabilise les données pour une utilisation optimale dans l&#x27;appréciation de l&#x27;activité médicale.
          &lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;mt-12 grid grid-cols-1 md:grid-cols-2 gap-8&quot;&gt;
        &lt;!-- Confidentialité et RGPD --&gt;
        &lt;div class&#x3D;&quot;text-center&quot;&gt;
          &lt;img
            src&#x3D;&quot;./assets/icon/certification.png&quot;
            alt&#x3D;&quot;Icon RGPD&quot;
            class&#x3D;&quot;mx-auto mb-4 w-20 h-20&quot;
          /&gt;
          &lt;h3 class&#x3D;&quot;text-2xl font-bold text-blue-400&quot;&gt;Confidentialité et RGPD&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            Chaque praticien accède uniquement à ses propres données d&#x27;activité réalisées au CHRU, garantissant le respect des normes RGPD.
          &lt;/p&gt;
        &lt;/div&gt;
        &lt;!-- Données non intégrées --&gt;
        &lt;div class&#x3D;&quot;text-center&quot;&gt;
          &lt;img
            src&#x3D;&quot;./assets/icon/serveur.png&quot;
            alt&#x3D;&quot;Icon Données Exclues&quot;
            class&#x3D;&quot;mx-auto mb-4 w-20 h-20&quot;
          /&gt;
          &lt;h3 class&#x3D;&quot;text-2xl font-bold text-blue-400&quot;&gt;Données non intégrées&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            Certaines activités (RCP, visites dans les secteurs, Qualité, Management…) non enregistrées dans le système d&#x27;information ne sont pas incluses.
          &lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/section&gt;



  &lt;!-- Testimonials Section --&gt;
  &lt;section class&#x3D;&quot;py-20 bg-gray-800&quot;&gt;
    &lt;div class&#x3D;&quot;max-w-7xl mx-auto px-6 lg:px-8&quot;&gt;
      &lt;h2 class&#x3D;&quot;text-4xl font-extrabold text-center text-white&quot;&gt;Ce que disent les utilisateurs&lt;/h2&gt;
      &lt;div class&#x3D;&quot;mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8&quot;&gt;
        &lt;div class&#x3D;&quot;bg-gray-700 rounded-lg p-6 shadow-lg&quot;&gt;
          &lt;p class&#x3D;&quot;text-gray-300&quot;&gt;
            &quot;Supra V2 est une véritable avancée pour la gestion hospitalière. Les données sont plus fiables, et l&#x27;interface est un plaisir à utiliser.&quot;
          &lt;/p&gt;
          &lt;p class&#x3D;&quot;mt-4 font-bold text-blue-400&quot;&gt;- Dr. Marie Dupont, CME&lt;/p&gt;
        &lt;/div&gt;
        &lt;div class&#x3D;&quot;bg-gray-700 rounded-lg p-6 shadow-lg&quot;&gt;
          &lt;p class&#x3D;&quot;text-gray-300&quot;&gt;
            &quot;Les graphiques et statistiques nous permettent de mieux suivre les performances des services et des praticiens.&quot;
          &lt;/p&gt;
          &lt;p class&#x3D;&quot;mt-4 font-bold text-blue-400&quot;&gt;- Pr. Jacques Martin, Chef de service&lt;/p&gt;
        &lt;/div&gt;
        &lt;div class&#x3D;&quot;bg-gray-700 rounded-lg p-6 shadow-lg&quot;&gt;
          &lt;p class&#x3D;&quot;text-gray-300&quot;&gt;
            &quot;Une solution conforme aux réglementations CNIL et RGPD, tout en restant simple à administrer.&quot;
          &lt;/p&gt;
          &lt;p class&#x3D;&quot;mt-4 font-bold text-blue-400&quot;&gt;- Sophie Bernard, DFAC&lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/section&gt;



  &lt;!-- Glossary Section --&gt;
  &lt;section id&#x3D;&quot;glossary&quot; class&#x3D;&quot;py-20 bg-gray-900&quot;&gt;
    &lt;div class&#x3D;&quot;max-w-7xl mx-auto px-6 lg:px-8&quot;&gt;
      &lt;h2 class&#x3D;&quot;text-4xl font-extrabold text-center text-white&quot;&gt;Glossaire des termes clés&lt;/h2&gt;
      &lt;p class&#x3D;&quot;mt-4 text-center text-gray-400&quot;&gt;
        Familiarisez-vous avec les abréviations couramment utilisées dans l&#x27;application SUPRA.
      &lt;/p&gt;
      &lt;div class&#x3D;&quot;mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8&quot;&gt;
        &lt;!-- CCAM --&gt;
        &lt;div class&#x3D;&quot;text-center bg-gray-800 shadow-lg rounded-lg p-6&quot;&gt;
          &lt;h3 class&#x3D;&quot;text-xl font-bold text-blue-400&quot;&gt;CCAM&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            La &lt;strong&gt;Classification Commune des Actes Médicaux&lt;/strong&gt; est une nomenclature utilisée pour coder et suivre les actes techniques médicaux et chirurgicaux.
          &lt;/p&gt;
        &lt;/div&gt;
        &lt;!-- NGAP --&gt;
        &lt;div class&#x3D;&quot;text-center bg-gray-800 shadow-lg rounded-lg p-6&quot;&gt;
          &lt;h3 class&#x3D;&quot;text-xl font-bold text-blue-400&quot;&gt;NGAP&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            La &lt;strong&gt;Nomenclature Générale des Actes Professionnels&lt;/strong&gt; regroupe les actes cliniques et paramédicaux pour le suivi des activités des praticiens.
          &lt;/p&gt;
        &lt;/div&gt;
        &lt;!-- NABM --&gt;
        &lt;div class&#x3D;&quot;text-center bg-gray-800 shadow-lg rounded-lg p-6&quot;&gt;
          &lt;h3 class&#x3D;&quot;text-xl font-bold text-blue-400&quot;&gt;NABM&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            La &lt;strong&gt;Nomenclature des Actes de Biologie Médicale&lt;/strong&gt; est utilisée pour coder les examens biologiques réalisés par les laboratoires.
          &lt;/p&gt;
        &lt;/div&gt;
        &lt;!-- ETP --&gt;
        &lt;div class&#x3D;&quot;text-center bg-gray-800 shadow-lg rounded-lg p-6&quot;&gt;
          &lt;h3 class&#x3D;&quot;text-xl font-bold text-blue-400&quot;&gt;ETP&lt;/h3&gt;
          &lt;p class&#x3D;&quot;mt-4 text-gray-400&quot;&gt;
            L&#x27;&lt;strong&gt;Équivalent Temps Plein&lt;/strong&gt; mesure la charge de travail d’un praticien par rapport à un emploi à temps plein dans l’unité fonctionnelle (UF).
          &lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/section&gt;




  &lt;!-- Footer --&gt;
  &lt;footer class&#x3D;&quot;bg-gray-800 py-10&quot;&gt;
    &lt;div class&#x3D;&quot;max-w-7xl mx-auto px-6 lg:px-8 text-center text-gray-400&quot;&gt;
      &lt;p&gt;&amp;copy; 2024 Supra V2. Tous droits réservés - CHRU de Nancy.&lt;/p&gt;
      &lt;div class&#x3D;&quot;mt-4 flex justify-center space-x-4&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;hover:text-white&quot;&gt;Confidentialité&lt;/a&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;hover:text-white&quot;&gt;Conditions d&#x27;utilisation&lt;/a&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;hover:text-white&quot;&gt;Contactez-nous&lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/footer&gt;
&lt;/div&gt;
</code></pre>
    </div>

    <div class="tab-pane fade " id="styleData">
                <p class="comment">
                    <code>./home.component.scss</code>
                </p>
                <pre class="line-numbers"><code class="language-scss"></code></pre>
    </div>

    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><div class="bg-gray-900 text-white">  <!-- Header Section -->  <header class="relative bg-gradient-to-r from-blue-600 to-indigo-800">    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-16">      <div class="flex flex-col lg:flex-row items-center justify-between">        <div class="lg:w-1/2">          <h1 class="text-5xl font-extrabold tracking-tight leading-tight">            Supra V2 <br />            <span class="text-blue-200">Votre outils pour le suivi de l’activité médicale pour le CHRU de Nancy</span>          </h1>          <p class="mt-6 text-lg text-gray-300">            Une application interne modernisée pour optimiser les statistiques médicales, la gestion des praticiens, et garantir la conformité CNIL et RGPD. Conçue pour aujourd\'hui, avec une vision pour le futur dans le GHT.          </p>          <div class="mt-8 flex space-x-4">            <a              href="#"              class="px-8 py-3 bg-blue-500 text-white rounded-md font-medium text-lg hover:bg-blue-400"            >Accéder à Supra</a            >            <a              href="#features"              class="px-8 py-3 border border-blue-500 text-blue-200 rounded-md font-medium text-lg hover:bg-blue-500 hover:text-white"            >Fonctionnalités</a            >          </div>        </div>        <div class="lg:w-1/2 mt-10 lg:mt-0">          <img            src="./assets/icon/statistiques.png"            alt="Capture d\'écran Supra V2"            class="rounded-lg shadow-lg"          />        </div>      </div>    </div>  </header>  <!-- Mandataires Section -->  <section class="py-16 bg-gray-800">    <div class="max-w-7xl mx-auto px-6 lg:px-8 text-center">      <h2 class="text-4xl font-extrabold text-white">Mandataires</h2>      <p class="mt-6 text-lg text-gray-400">        Supra V2 est porté par le CHRU de Nancy avec les mandataires suivants :      </p>      <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">        <div class="bg-gray-700 rounded-lg p-6 shadow-lg">          <h3 class="text-2xl font-bold text-blue-400">DFAC</h3>          <p class="mt-4 text-gray-300">            Direction de la Facturation, responsable de l’exploitation des données d’actes médicaux pour optimiser la gestion financière.          </p>        </div>        <div class="bg-gray-700 rounded-lg p-6 shadow-lg">          <h3 class="text-2xl font-bold text-blue-400">CME</h3>          <p class="mt-4 text-gray-300">            Commission Médicale d\'Établissement, superviseur des statistiques médicales et de l\'activité des praticiens.          </p>        </div>      </div>    </div>  </section>  <!-- Features Section -->  <section id="features" class="py-20 bg-gray-900">    <div class="max-w-7xl mx-auto px-6 lg:px-8">      <h2 class="text-4xl font-extrabold text-center text-white">Fonctionnalités clés</h2>      <p class="mt-4 text-center text-gray-300">        SUPRA est un outil développé par le CHRU de Nancy, conçu pour le suivi précis de l\'activité médicale des praticiens, tout en respectant les normes RGPD.      </p>      <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">        <!-- Données hebdomadaires -->        <div class="text-center">          <img            src="./assets/icon/collecte-de-donnees.png"            alt="Icon Données Activité"            class="mx-auto mb-4 w-20 h-20"          />          <h3 class="text-2xl font-bold text-blue-400">Données d\'activité</h3>          <p class="mt-4 text-gray-400">            Mise à jour hebdomadaire des données d\'activité, incluant l\'activité externe, hospitalisée, ou les deux, telles qu\'elles sont tracées dans le système d\'information du CHRU.          </p>        </div>        <!-- Données annuelles -->        <div class="text-center">          <img            src="./assets/icon/transfert-de-donnees.png"            alt="Icon Données Annuelles"            class="mx-auto mb-4 w-20 h-20"          />          <h3 class="text-2xl font-bold text-blue-400">Données annuelles</h3>          <p class="mt-4 text-gray-400">            Une fois par an, SUPRA fournit des données sur les heures d’enseignement, les SIGAPS, les gardes et astreintes rémunérées, ainsi que d\'autres activités clés.          </p>        </div>        <!-- Fiabilisation des données -->        <div class="text-center">          <img            src="./assets/icon/proteger.png"            alt="Icon Fiabilisation"            class="mx-auto mb-4 w-20 h-20"          />          <h3 class="text-2xl font-bold text-blue-400">Fiabilisation des données</h3>          <p class="mt-4 text-gray-400">            SUPRA détecte les anomalies dans la saisie des actes et fiabilise les données pour une utilisation optimale dans l\'appréciation de l\'activité médicale.          </p>        </div>      </div>      <div class="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">        <!-- Confidentialité et RGPD -->        <div class="text-center">          <img            src="./assets/icon/certification.png"            alt="Icon RGPD"            class="mx-auto mb-4 w-20 h-20"          />          <h3 class="text-2xl font-bold text-blue-400">Confidentialité et RGPD</h3>          <p class="mt-4 text-gray-400">            Chaque praticien accède uniquement à ses propres données d\'activité réalisées au CHRU, garantissant le respect des normes RGPD.          </p>        </div>        <!-- Données non intégrées -->        <div class="text-center">          <img            src="./assets/icon/serveur.png"            alt="Icon Données Exclues"            class="mx-auto mb-4 w-20 h-20"          />          <h3 class="text-2xl font-bold text-blue-400">Données non intégrées</h3>          <p class="mt-4 text-gray-400">            Certaines activités (RCP, visites dans les secteurs, Qualité, Management…) non enregistrées dans le système d\'information ne sont pas incluses.          </p>        </div>      </div>    </div>  </section>  <!-- Testimonials Section -->  <section class="py-20 bg-gray-800">    <div class="max-w-7xl mx-auto px-6 lg:px-8">      <h2 class="text-4xl font-extrabold text-center text-white">Ce que disent les utilisateurs</h2>      <div class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">        <div class="bg-gray-700 rounded-lg p-6 shadow-lg">          <p class="text-gray-300">            "Supra V2 est une véritable avancée pour la gestion hospitalière. Les données sont plus fiables, et l\'interface est un plaisir à utiliser."          </p>          <p class="mt-4 font-bold text-blue-400">- Dr. Marie Dupont, CME</p>        </div>        <div class="bg-gray-700 rounded-lg p-6 shadow-lg">          <p class="text-gray-300">            "Les graphiques et statistiques nous permettent de mieux suivre les performances des services et des praticiens."          </p>          <p class="mt-4 font-bold text-blue-400">- Pr. Jacques Martin, Chef de service</p>        </div>        <div class="bg-gray-700 rounded-lg p-6 shadow-lg">          <p class="text-gray-300">            "Une solution conforme aux réglementations CNIL et RGPD, tout en restant simple à administrer."          </p>          <p class="mt-4 font-bold text-blue-400">- Sophie Bernard, DFAC</p>        </div>      </div>    </div>  </section>  <!-- Glossary Section -->  <section id="glossary" class="py-20 bg-gray-900">    <div class="max-w-7xl mx-auto px-6 lg:px-8">      <h2 class="text-4xl font-extrabold text-center text-white">Glossaire des termes clés</h2>      <p class="mt-4 text-center text-gray-400">        Familiarisez-vous avec les abréviations couramment utilisées dans l\'application SUPRA.      </p>      <div class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">        <!-- CCAM -->        <div class="text-center bg-gray-800 shadow-lg rounded-lg p-6">          <h3 class="text-xl font-bold text-blue-400">CCAM</h3>          <p class="mt-4 text-gray-400">            La <strong>Classification Commune des Actes Médicaux</strong> est une nomenclature utilisée pour coder et suivre les actes techniques médicaux et chirurgicaux.          </p>        </div>        <!-- NGAP -->        <div class="text-center bg-gray-800 shadow-lg rounded-lg p-6">          <h3 class="text-xl font-bold text-blue-400">NGAP</h3>          <p class="mt-4 text-gray-400">            La <strong>Nomenclature Générale des Actes Professionnels</strong> regroupe les actes cliniques et paramédicaux pour le suivi des activités des praticiens.          </p>        </div>        <!-- NABM -->        <div class="text-center bg-gray-800 shadow-lg rounded-lg p-6">          <h3 class="text-xl font-bold text-blue-400">NABM</h3>          <p class="mt-4 text-gray-400">            La <strong>Nomenclature des Actes de Biologie Médicale</strong> est utilisée pour coder les examens biologiques réalisés par les laboratoires.          </p>        </div>        <!-- ETP -->        <div class="text-center bg-gray-800 shadow-lg rounded-lg p-6">          <h3 class="text-xl font-bold text-blue-400">ETP</h3>          <p class="mt-4 text-gray-400">            L\'<strong>Équivalent Temps Plein</strong> mesure la charge de travail d’un praticien par rapport à un emploi à temps plein dans l’unité fonctionnelle (UF).          </p>        </div>      </div>    </div>  </section>  <!-- Footer -->  <footer class="bg-gray-800 py-10">    <div class="max-w-7xl mx-auto px-6 lg:px-8 text-center text-gray-400">      <p>&copy; 2024 Supra V2. Tous droits réservés - CHRU de Nancy.</p>      <div class="mt-4 flex justify-center space-x-4">        <a href="#" class="hover:text-white">Confidentialité</a>        <a href="#" class="hover:text-white">Conditions d\'utilisation</a>        <a href="#" class="hover:text-white">Contactez-nous</a>      </div>    </div>  </footer></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'HomeComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'HomeComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
