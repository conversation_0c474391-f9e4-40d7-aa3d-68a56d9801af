export interface GardeAstreinteData {
  lastUpdated: string;
  data: GardeAstreintePractitioner[];
}

export interface GardeAstreintePractitioner {
  praticienId: string;
  praticienName: string;
  service: string;
  uf: string;
  totalOnCalls: number; // Nombre total de gardes
  totalStandbys: number; // Nombre total d'astreintes
  yearlyBreakdown: YearlyGardeAstreinte[];
}

export interface YearlyGardeAstreinte {
  year: number; // Année
  onCalls: number; // Nombre de gardes dans l'année
  standbys: number; // Nombre d’astreintes dans l'année
}
