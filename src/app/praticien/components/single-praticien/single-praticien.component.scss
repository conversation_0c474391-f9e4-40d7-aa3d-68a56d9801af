// Single Praticien Component Styles

.single-praticien-container {
  min-height: 100vh;
  background-color: #f9fafb;
}

// Subheader styling
.subheader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .profile-avatar {
    background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

// Card styling
.content-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);

  .card-header {
    border-bottom: 1px solid #e5e7eb;

    .section-icon {
      font-size: 1.25rem;
    }
  }
}

// Table customizations
::ng-deep .p-datatable {
  .p-datatable-thead > tr > th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #374151;
    padding: 0.75rem 1rem;
  }

  .p-datatable-tbody > tr {
    &:hover {
      background-color: #f8fafc !important;
    }
  }

  .p-datatable-tbody > tr > td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
  }
}

// Status badges
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;

  &.active {
    background-color: #dcfce7;
    color: #166534;
  }

  &.inactive {
    background-color: #fee2e2;
    color: #991b1b;
  }
}

// Badge variants
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;

  &.blue {
    background-color: #dbeafe;
    color: #1d4ed8;
  }

  &.purple {
    background-color: #f3e8ff;
    color: #7c3aed;
  }

  &.green {
    background-color: #dcfce7;
    color: #166534;
  }

  &.gray {
    background-color: #f3f4f6;
    color: #374151;
  }

  &.orange {
    background-color: #fed7aa;
    color: #ea580c;
  }
}

// Loading spinner container
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16rem;
}

// Error message styling
.error-message {
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  color: #991b1b;
  padding: 1rem;
  border-radius: 0.375rem;
  margin: 1rem 0;
}

// Empty state styling
.empty-state {
  text-align: center;
  padding: 3rem 1rem;

  .empty-icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 1rem;
  }

  .empty-title {
    color: #6b7280;
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  .empty-subtitle {
    color: #9ca3af;
    font-size: 0.875rem;
  }
}

// Coming soon sections
.coming-soon {
  text-align: center;
  padding: 3rem 1rem;

  .coming-soon-icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 1rem;
  }

  .coming-soon-title {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  .coming-soon-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .single-praticien-container {
    padding: 0.5rem;
  }

  .subheader {
    .profile-section {
      flex-direction: column;
      text-align: center;
    }

    .profile-details {
      margin-top: 1rem;
      margin-left: 0;
    }
  }

  ::ng-deep .p-datatable {
    font-size: 0.875rem;

    .p-datatable-thead > tr > th,
    .p-datatable-tbody > tr > td {
      padding: 0.5rem;
    }
  }
}
