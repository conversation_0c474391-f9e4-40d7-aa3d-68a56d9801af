import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import {ActeSummary} from "../../models/overview/acte-overview.model";

@Injectable({
  providedIn: 'root',
})
export class ActeOverviewService {
  private acteSummarySubject = new BehaviorSubject<ActeSummary | null>(null);
  acteSummary$: Observable<ActeSummary | null> = this.acteSummarySubject.asObservable();

  constructor(private http: HttpClient) {
    // this.loadActeSummaryOld(); // Charger le mock dès l'initialisation
  }

  private loadActeSummaryOLD(): void {
    this.http.get<ActeSummary>('/assets/data/overview/acte-overview.json').subscribe(
      (data) => {
        this.acteSummarySubject.next(data);
      },
      (error) => {
        console.error('Erreur lors du chargement des données ActeSummary:', error);
      }
    );
  }

  /**
   * Charge les données spécifiques d'un acte en fonction du type et de l'ID.
   * @param type - Le type d'acte (ccam, ngap, labo).
   * @param acteId - L'ID de l'acte à charger.
   */

  loadActeSummary(type: string, acteId: string): void {
    // const url = `/assets/data/overview/${type}-acte-overview.json`; // URL dynamique basée sur le type
    const url = `/assets/data/overview/acte-overview.json`;
    this.http.get<ActeSummary>(url).subscribe(
      (data: ActeSummary) => {
        // Vérifie si l'ID de l'acte correspond

        //
        acteId = "1" //@todo supprime juste cette ligne pour garder l'aspect dynamique
        //
        if (data.actesDetails.acteId === acteId) {
          this.acteSummarySubject.next(data); // Charge les données dans le BehaviorSubject
        } else {
          console.error(`Aucun acte trouvé pour le type: ${type} et l'ID: ${acteId}`);
          this.acteSummarySubject.next(null); // Réinitialise si aucun acte n'est trouvé
        }
      },
      (error) => {
        console.error(`Erreur lors du chargement des données ${type} (ID: ${acteId}):`, error);
        this.acteSummarySubject.next(null); // Réinitialise en cas d'erreur
      }
    );
  }


  /**
   * Réinitialise les données de l'acte.
   */
  resetActeData(): void {
    this.acteSummarySubject.next(null); // Réinitialise les données à null
  }


  getActeDetailsById(acteId: string): Observable<ActeSummary | null> {
    return this.acteSummary$; // Retourner directement les données filtrées si nécessaire
  }
}
