# Documentation - Composant activites-list

## Vue d'ensemble

Le composant `activites-list` est responsable de l'affichage et de la gestion de la liste des activités médicales (actes CCAM, NGAP, LABO/NABM). Il implémente une pagination côté client avec filtrage en temps réel et gestion d'état global.

## Architecture des données

### Type de pagination : **Client-side (Front-end)**

Le composant utilise une approche de pagination côté client :
- **Chargement initial** : Toutes les activités sont récupérées en une seule requête API (`/api/actes`)
- **Pagination** : Gérée par PrimeNG Table côté client
- **Filtrage** : Appliqué en temps réel sur les données chargées en mémoire
- **Performance** : Optimisée pour des datasets de taille moyenne (quelques milliers d'enregistrements)

### Routes API utilisées

1. **Route principale** : `GET /api/actes`
   - Récupère toutes les activités **SANS filtrage temporel**
   - ⚠️ **PROBLÈME IDENTIFIÉ** : Ne prend pas en compte les périodes P1, P2, P3 du GlobalFilterService
   - Utilisée au chargement initial du composant
   - Format de réponse : Hydra/API Platform avec `member` et `totalItems`

2. **Routes de comptage par type** (méthodes dépréciées mais conservées) :
   - `GET /api/actes?typeActe=CCAM&itemsPerPage=1`
   - `GET /api/actes?typeActe=NGAP&itemsPerPage=1`
   - `GET /api/actes?typeActe[]=LABO&typeActe[]=NABM&itemsPerPage=1`

### ⚠️ Incohérence avec les autres services

**Autres services utilisent correctement les périodes :**
- `ActeStatsService` : `GET /api/actes/{acteId}/uf-single-stats?p1Start=...&p1End=...&p2Start=...&p2End=...&p3Start=...&p3End=...`
- `ActeMultiStatsService` : `GET /api/actes/multi-stats?p1Start=...&p1End=...&p2Start=...&p2End=...&p3Start=...&p3End=...`
- `DashboardService` : `GET /api/actes?p1Start=...&p1End=...&p2Start=...&p2End=...&p3Start=...&p3End=...`

**Le composant activites-list devrait utiliser :**
```
GET /api/actes?p1Start=2024-10-01&p1End=2025-03-31&p2Start=2023-10-01&p2End=2024-03-31&p3Start=2022-10-01&p3End=2023-03-31
```

## Structure des composants

### Composant principal : `ActivitesListComponent`

**Fichiers :**
- `activites-list.component.ts` (1127 lignes)
- `activites-list.component.html` (625 lignes)
- `activites-list.component.scss`

**Responsabilités :**
- Chargement et gestion des données d'activités
- Application des filtres globaux et locaux
- Gestion de la sélection multiple
- Export CSV des données sélectionnées
- Navigation vers les détails d'activités

### Sous-composant : `SubheaderComponent`

**Fichiers :**
- `subheader/subheader.component.ts` (1072 lignes)
- `subheader/subheader.component.html`
- `subheader/subheader.component.scss`

**Responsabilités :**
- Interface de filtrage avancé (praticien, pôle, CR, type de venue)
- Autocomplete intelligent basé sur les données chargées
- Communication avec le GlobalFilterService
- Émission des changements de filtres vers le composant parent

## Services utilisés

### 1. ActiviteService
- **Héritage** : Étend `BaseStructureService<ActiviteModel, ActiviteResponse>`
- **Cache** : Implémente un système de cache avec TTL (5 minutes)
- **Méthodes spécialisées** :
  - `getActivitiesByMonth(month, year)`
  - `getActivitiesByAgent(agentId)`

### 2. GlobalFilterService
- **Persistance** : Sauvegarde des filtres dans localStorage (clé: `global_filters`)
- **État global** : BehaviorSubject pour la réactivité
- **Gestion des périodes** : Calcul automatique des périodes P1, P2, P3
- **Filtres supportés** :
  - Praticien (AgentModel)
  - Pôle (PoleModel)
  - CR/Service (CRModel)
  - Type de venue (VenueType: HOSPITALIZATION, EXTERNAL, BOTH)
  - Périodes de dates (P1, P2, P3)

### 3. HttpClient
- **Usage direct** : Pour les requêtes API spécifiques
- **Endpoint principal** : `${environment.api_url}/api/actes`
- ⚠️ **MANQUE** : Paramètres de périodes P1, P2, P3 non inclus dans les requêtes

## Logique de filtrage

### ⚠️ Problème majeur : Filtrage temporel manquant

**Situation actuelle :**
- Le composant charge TOUTES les activités sans filtrage temporel
- Les périodes P1, P2, P3 du GlobalFilterService ne sont PAS utilisées dans les requêtes API
- Le filtrage temporel devrait être fait côté serveur, pas côté client

**Correction nécessaire :**
La méthode `loadAllActivites()` devrait construire l'URL avec les paramètres de périodes :
```typescript
// Au lieu de : GET /api/actes
// Devrait être : GET /api/actes?p1Start=...&p1End=...&p2Start=...&p2End=...&p3Start=...&p3End=...
```

### Filtres globaux (appliqués côté client uniquement)

**Logique ET/OU :**
- **Praticien ET (Pôle OU CR) ET Type de venue**
- Les filtres sont appliqués sur `ufIntervention` pour pôle/CR
- Support des URIs API Platform (string) et objets complets
- ⚠️ **LIMITATION** : Filtrage côté client sur un dataset potentiellement incomplet

**Implémentation :**
```typescript
// Méthode principale : applyGlobalFilters()
// Ligne 141-265 dans activites-list.component.ts
```

### Filtres locaux

1. **Filtre par type d'acte** :
   - ALL, CCAM, NGAP, LABO/NABM
   - Appliqué via `applyActivityTypeFilter()`

2. **Recherche textuelle** :
   - Champs recherchés : code, description, nom/prénom agent
   - Debounce de 500ms pour optimiser les performances
   - Minimum 2 caractères pour déclencher la recherche

## Gestion de l'état

### Propriétés principales

```typescript
// Données
activites: ActiviteModel[] = [];           // Données filtrées par type
allActivites: ActiviteModel[] = [];        // Toutes les données chargées
filteredActivites: ActiviteModel[] = [];   // Données après tous les filtres
selectedActivites: ActiviteModel[] = [];   // Sélection utilisateur

// Pagination (gérée par PrimeNG)
first = 0;                    // Index du premier élément affiché
pageSize = 10;               // Nombre d'éléments par page
totalActivites = 0;          // Total après filtrage

// Compteurs par type
totalCcamActivites = 0;
totalNgapActivites = 0;
totalLaboActivites = 0;
totalAllActivites = 0;
```

### Cycle de vie des données

1. **Initialisation** (`ngOnInit`) :
   - Appel `loadAllActivites()`
   - Souscription aux changements de filtres globaux

2. **Chargement** (`loadAllActivites`) :
   - ⚠️ **PROBLÈME** : Requête GET `/api/actes` sans paramètres de périodes
   - **DEVRAIT ÊTRE** : Requête avec `p1Start`, `p1End`, `p2Start`, `p2End`, `p3Start`, `p3End`
   - Stockage dans `allActivites` et `activites`
   - Calcul des compteurs par type
   - Application des filtres initiaux

3. **Filtrage en cascade** :
   - Type d'acte → `applyActivityTypeFilter()`
   - Filtres globaux → `applyGlobalFilters()` (côté client uniquement)
   - Recherche textuelle → `applySearchFilter()`

## Fonctionnalités avancées

### Sélection multiple et export
- Sélection individuelle et globale (checkbox)
- Export CSV avec colonnes dynamiques selon le type d'acte
- Encodage UTF-8 avec BOM pour Excel

### Navigation
- Détails d'activité unique : `/activites/activite/{id}`
- Détails multi-activités : `/activites/activites-multi-details?ids={id1,id2,...}`

### Optimisations
- Cache des données en mémoire
- Debounce sur la recherche textuelle
- Pagination côté client pour éviter les requêtes répétées
- Gestion des subscriptions pour éviter les fuites mémoire

## Interface utilisateur

### Header premium avec métriques
- Statut système en temps réel
- Graphiques de répartition par type (CCAM/NGAP/LABO)
- Affichage des périodes actives (P1, P2, P3)

### Filtres par type d'acte
- Interface à onglets avec compteurs
- Couleurs distinctives par type
- Indicateurs visuels de sélection

### Tableau PrimeNG
- Colonnes : Sélection, Détails, Code, Description, Type, Date, Praticien, UF, Détails
- Tri sur colonnes principales
- Scroll horizontal pour responsive
- Actions groupées (export, détails)

## Gestion des erreurs

- Try-catch sur les opérations localStorage
- Fallback sur les requêtes API échouées
- Logs détaillés pour le debugging
- États de chargement avec skeletons

## Points d'attention

1. **⚠️ CRITIQUE - Filtrage temporel manquant** :
   - Le composant ne respecte pas les périodes P1, P2, P3 du GlobalFilterService
   - Charge toutes les activités de la base sans filtrage temporel
   - Incohérent avec les autres services de l'application

2. **Performance** : Le chargement de toutes les activités peut être lourd pour de très gros datasets

3. **Mémoire** : Les données restent en mémoire pendant toute la session

4. **Synchronisation** : Les filtres globaux sont partagés entre composants

5. **Cache** : Pas de cache HTTP, rechargement à chaque visite du composant

## Recommandations de correction

### 1. Intégrer le filtrage temporel dans les requêtes API

Modifier la méthode `loadAllActivites()` pour inclure les paramètres de périodes :

```typescript
loadAllActivites(): void {
  this.loading = true;
  console.log('📊 Loading ALL activities from API...');

  // Construire l'URL avec les paramètres de périodes
  let url = `${environment.api_url}/api/actes`;

  if (this.globalFilters?.p1 && this.globalFilters?.p2 && this.globalFilters?.p3) {
    const params = this.buildPeriodParams(
      this.globalFilters.p1,
      this.globalFilters.p2,
      this.globalFilters.p3
    );
    url += `?${params}`;
  }

  const subscription = this.http.get<any>(url).subscribe({
    // ... reste du code
  });
}

private buildPeriodParams(p1: DatePeriod, p2: DatePeriod, p3: DatePeriod): string {
  const params = new URLSearchParams();

  if (p1?.dateDebut && p1?.dateFin) {
    params.set('p1Start', this.formatDate(p1.dateDebut));
    params.set('p1End', this.formatDate(p1.dateFin));
  }

  if (p2?.dateDebut && p2?.dateFin) {
    params.set('p2Start', this.formatDate(p2.dateDebut));
    params.set('p2End', this.formatDate(p2.dateFin));
  }

  if (p3?.dateDebut && p3?.dateFin) {
    params.set('p3Start', this.formatDate(p3.dateDebut));
    params.set('p3End', this.formatDate(p3.dateFin));
  }

  return params.toString();
}

private formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}
```

### 2. Recharger les données lors du changement de périodes

Modifier la souscription aux changements de filtres globaux :

```typescript
ngOnInit(): void {
  // Subscribe to global filter changes
  const filterSubscription = this.globalFilterService.getFilterState().subscribe(filters => {
    const previousFilters = this.globalFilters;
    this.globalFilters = filters;

    // Si les périodes ont changé, recharger les données
    if (this.periodsChanged(previousFilters, filters)) {
      this.loadAllActivites();
    } else if (this.activites.length > 0) {
      // Sinon, appliquer seulement les filtres côté client
      this.applyGlobalFilters();
    }
  });
}

private periodsChanged(previous: GlobalFilterState | null, current: GlobalFilterState | null): boolean {
  if (!previous || !current) return true;

  return (
    JSON.stringify(previous.p1) !== JSON.stringify(current.p1) ||
    JSON.stringify(previous.p2) !== JSON.stringify(current.p2) ||
    JSON.stringify(previous.p3) !== JSON.stringify(current.p3)
  );
}
```

## Compréhension de l'API Backend

### Architecture API Platform

L'API `/api/actes` est construite avec **API Platform** et suit une architecture moderne :

#### 1. **Entity** : `Actes` (`../supra-back/src/Entity/Activite/Actes.php`)
- **Table** : `actes` avec index sur `validFrom`, `validTo`, `periodeType`
- **Champs principaux** :
  - `code` : Code de l'acte médical
  - `description` : Description de l'acte
  - `typeActe` : Type (CCAM, NGAP, LABO)
  - `date_realisation` : Date de réalisation (champ avec underscore en DB)
  - `nombre_de_realisation` : Nombre de réalisations (champ avec underscore en DB)
  - Relations : `agent`, `ufPrincipal`, `ufDemande`, `ufIntervention`
- **Trait** : `HorodatageTrait` pour la gestion SCD Type 2 (`validFrom`, `validTo`, `periodeType`)

#### 2. **DTO** : `ActesDto` (`../supra-back/src/ApiResource/Activite/ActesDto.php`)
- **Configuration API Platform** :
  - `shortName: 'actes'` → URL `/api/actes`
  - `paginationEnabled: true` avec `paginationItemsPerPage: 10`
  - Cache headers : 1h côté client et proxy
  - Filtres : `MultiFieldSearchFilter` et `SearchFilter` sur `typeActe`
- **Provider** : `EntityToDtoStateProvider`
- **Processor** : `EntityClassDtoStateProcessor`

#### 3. **Mapper** : `ActesEntityToDtoMapper` (`../supra-back/src/Mapper/Actes/ActesEntityToDtoMapper.php`)
- Convertit `Actes` entity → `ActesDto`
- Utilise `MicroMapper` pour les relations (Agent, UFs)
- Gère la conversion des IRIs API Platform

#### 4. **Extension Doctrine** : `ActesPeriodesExtension` (`../supra-back/src/Domain/Extension/ActesPeriodesExtension.php`)
- **Fonction** : Filtre automatiquement les actes selon les périodes P1, P2, P3
- **Logique SCD Type 2** :
  ```sql
  WHERE (
    -- Période P1
    (validFrom <= p1End AND (validTo IS NULL OR validTo >= p1Start)
     AND date_realisation >= p1Start AND date_realisation <= p1End)
    OR
    -- Période P2
    (validFrom <= p2End AND (validTo IS NULL OR validTo >= p2Start)
     AND date_realisation >= p2Start AND date_realisation <= p2End)
    OR
    -- Période P3
    (validFrom <= p3End AND (validTo IS NULL OR validTo >= p3Start)
     AND date_realisation >= p3Start AND date_realisation <= p3End)
  )
  ```

### Fonctionnalités API disponibles

#### 1. **Pagination native API Platform**
- Paramètres : `page`, `itemsPerPage`
- Métadonnées Hydra : `totalItems`, `view.first`, `view.last`, `view.next`

#### 2. **Filtrage temporel automatique**
- Paramètres : `p1Start`, `p1End`, `p2Start`, `p2End`, `p3Start`, `p3End`
- Format dates : `YYYY-MM-DD`
- Exemple : `/api/actes?p1Start=2024-10-01&p1End=2025-03-31&p2Start=2023-10-01&p2End=2024-03-31&p3Start=2022-10-01&p3End=2023-03-31`

#### 3. **Filtres disponibles**
- `typeActe` : Filtre exact (CCAM, NGAP, LABO)
- `MultiFieldSearchFilter` : Recherche multi-champs

#### 4. **Cache HTTP**
- 1 heure côté client et proxy
- Headers `Vary` sur `Accept` et `Accept-Language`

### Repository et requêtes personnalisées

Le `ActesRepository` contient des méthodes optimisées :
- `sumRealisationsByActeAndUfForPeriod()` : Somme par acte/UF/période
- `sumRealisationsByActeAndPraticienForPeriod()` : Somme par acte/praticien/période

## Nouvelle logique proposée

### Objectifs d'amélioration

1. **Utiliser la pagination côté serveur** au lieu du chargement complet
2. **Exploiter l'extension de périodes** déjà implémentée
3. **Optimiser les performances** pour de gros datasets
4. **Maintenir la réactivité** des filtres utilisateur

### Architecture cible

#### 1. **Pagination hybride intelligente**

**Principe** : Combiner pagination serveur + cache intelligent côté client

```typescript
interface PaginationStrategy {
  mode: 'server' | 'client' | 'hybrid';
  serverPageSize: number;
  clientCacheSize: number;
  prefetchPages: number;
}

// Configuration adaptative selon le dataset
const strategy: PaginationStrategy = {
  mode: this.totalItems > 1000 ? 'server' : 'hybrid',
  serverPageSize: 50,
  clientCacheSize: 200, // 4 pages en cache
  prefetchPages: 2
};
```

#### 2. **Gestion des périodes côté serveur**

**Modification requise dans `loadAllActivites()`** :

```typescript
private buildApiUrl(): string {
  let url = `${environment.api_url}/api/actes`;
  const params = new URLSearchParams();

  // 1. Ajouter les paramètres de périodes (PRIORITAIRE)
  if (this.globalFilters?.p1?.dateDebut && this.globalFilters?.p1?.dateFin) {
    params.set('p1Start', this.formatDate(this.globalFilters.p1.dateDebut));
    params.set('p1End', this.formatDate(this.globalFilters.p1.dateFin));
  }

  if (this.globalFilters?.p2?.dateDebut && this.globalFilters?.p2?.dateFin) {
    params.set('p2Start', this.formatDate(this.globalFilters.p2.dateDebut));
    params.set('p2End', this.formatDate(this.globalFilters.p2.dateFin));
  }

  if (this.globalFilters?.p3?.dateDebut && this.globalFilters?.p3?.dateFin) {
    params.set('p3Start', this.formatDate(this.globalFilters.p3.dateDebut));
    params.set('p3End', this.formatDate(this.globalFilters.p3.dateFin));
  }

  // 2. Ajouter la pagination
  params.set('page', this.currentPage.toString());
  params.set('itemsPerPage', this.pageSize.toString());

  // 3. Ajouter les filtres de type
  if (this.selectedActivityType !== 'ALL') {
    params.set('typeActe', this.selectedActivityType);
  }

  return params.toString() ? `${url}?${params.toString()}` : url;
}

private formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}
```

#### 3. **Cache intelligent multi-niveaux**

```typescript
interface CacheStrategy {
  // Cache L1 : Page courante + pages adjacentes
  pageCache: Map<string, ActiviteModel[]>;

  // Cache L2 : Métadonnées et compteurs
  metaCache: {
    totalItems: number;
    totalByType: { [key: string]: number };
    lastUpdate: Date;
  };

  // Cache L3 : Filtres appliqués côté client
  filterCache: Map<string, ActiviteModel[]>;
}
```

#### 4. **Stratégie de chargement adaptatif**

```typescript
async loadActivitesAdaptive(): Promise<void> {
  const estimatedTotal = await this.getEstimatedTotal();

  if (estimatedTotal <= 500) {
    // Dataset petit : chargement complet + pagination client
    await this.loadAllActivitesClientSide();
  } else if (estimatedTotal <= 5000) {
    // Dataset moyen : pagination serveur + prefetch
    await this.loadActivitesServerSideWithPrefetch();
  } else {
    // Dataset large : pagination serveur pure
    await this.loadActivitesServerSideOnly();
  }
}

private async getEstimatedTotal(): Promise<number> {
  // Requête rapide pour obtenir le total
  const url = this.buildApiUrl() + '&itemsPerPage=1';
  const response = await this.http.get<any>(url).toPromise();
  return response.totalItems || 0;
}
```

### ✅ Améliorations implémentées côté API

#### 1. **✅ MultiFieldSearchFilter amélioré**
Le filtre de recherche a été optimisé avec :

**Nouvelles fonctionnalités :**
- **Recherche multi-mots** : Support des termes multiples avec logique ET
- **Phrases exactes** : Support des guillemets pour recherches précises
- **Champs étendus** : Code, description, agent (nom/prénom/titre/email), UF (libellé/code), type acte, numéro intervention
- **Jointures optimisées** : Évite les jointures multiples sur la même table
- **PostgreSQL optimisé** : Utilise LOWER() pour l'insensibilité à la casse

**Exemples d'utilisation :**
```
GET /api/actes?search=consultation cardiologie
GET /api/actes?search="Dr Martin" CCAM
GET /api/actes?search=46C2DXI8VA
```

#### 2. **✅ Endpoint de métadonnées ajouté**
Nouvel endpoint : `GET /api/actes/metadata`

**Fonctionnalités :**
- **Statistiques rapides** : Total, répartition par type, plage de dates
- **Support des périodes** : Compatible avec p1Start/p1End, etc.
- **Cache optimisé** : 30 minutes côté client/proxy
- **Métriques de performance** : Temps de génération inclus

**Exemple de réponse :**
```json
{
  "totalItems": 15420,
  "totalByType": {"CCAM": 8500, "NGAP": 4200, "LABO": 2720},
  "dateRangeMin": "2022-01-01",
  "dateRangeMax": "2024-12-31",
  "totalAgents": 245,
  "totalUfs": 89,
  "appliedPeriods": {"p1": "2024-01-01 to 2024-12-31"},
  "generationTimeMs": 45
}
```

#### 3. **✅ Index de base de données optimisés**
Index composites ajoutés dans l'entité Actes :

```sql
-- Index composites pour les requêtes fréquentes (PostgreSQL)
CREATE INDEX CONCURRENTLY idx_actes_type_date ON actes(type_acte, date_realisation);
CREATE INDEX CONCURRENTLY idx_actes_periods ON actes(validFrom, validTo, date_realisation);
CREATE INDEX CONCURRENTLY idx_actes_agent_date ON actes(agent_id, date_realisation);
CREATE INDEX CONCURRENTLY idx_actes_uf_date ON actes(uf_intervention_id, date_realisation);

-- Index pour les recherches fréquentes
CREATE INDEX CONCURRENTLY idx_actes_code ON actes(code);
CREATE INDEX CONCURRENTLY idx_actes_internum ON actes(internum);
CREATE INDEX CONCURRENTLY idx_actes_active ON actes(isActif);

-- Index partiel pour optimiser les requêtes sur actes actifs
CREATE INDEX CONCURRENTLY idx_actes_active_type_date ON actes(type_acte, date_realisation) WHERE isActif = true;
```

**Migration créée :** `Version20250824185212.php`
- Utilise `CREATE INDEX CONCURRENTLY` pour éviter le verrouillage
- Support du rollback avec `DROP INDEX CONCURRENTLY`

### Avantages de la nouvelle approche

1. **Performance** : Réduction drastique du temps de chargement initial
2. **Scalabilité** : Support de millions d'enregistrements
3. **Cohérence** : Utilisation des périodes P1, P2, P3 comme les autres composants
4. **UX** : Chargement progressif avec feedback utilisateur
5. **Cache** : Exploitation du cache HTTP API Platform (1h)
6. **Filtrage** : Combinaison optimale serveur/client selon le contexte
