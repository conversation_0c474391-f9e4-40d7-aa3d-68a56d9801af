\documentclass{beamer}

\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
\usepackage{lmodern}

\usepackage{tikz}
\usetikzlibrary{shapes.geometric, arrows}

\usepackage{pifont} % Pour les checkmarks et croix

\usepackage{graphicx}


\usetheme{Madrid}

\title[\textbf{Supra V2}]{
  \textbf{Supra V2 : Modernisation et Optimisation}\\
  \large Vers une gestion et un suivi optimisés des activités médicales
}
\subtitle{Évolutions, conformité RGPD et perspectives}
\author{SIRH (RPA/DEV)}
\institute[CHRU NANCY]{CHRU NANCY}
\date{6 février 2024}

% \date{\today}


\begin{document}

  \begin{frame}
    \titlepage
  \end{frame}

  \begin{frame}{Plan de la présentation}
    \tableofcontents
  \end{frame}

  % =======================
  % 1. CONTEXTE
  % =======================
  \section{Contexte}

  \begin{frame}{Pourquoi Supra V2 ?}
    \begin{itemize}
      \item \textbf{Supra V1 est un outil développé } par le CHRU de Nancy dédié au suivi de l’activité médicale par praticien.
      \item Cependant, plusieurs limites ont été identifiées :
      \begin{itemize}
        \item \textbf{Non-conformité RGPD} : accès trop large aux données des praticiens.
        \item \textbf{Interface vieillissante}, complexité d’utilisation.
        \item \textbf{Architecture monolithique}, difficile à faire évoluer.
        \item \textbf{Sécurité des accès insuffisante}.
      \end{itemize}
      \item \textbf{Supra V2 vise à corriger ces problèmes} en garantissant :
      \begin{itemize}
        \item \textbf{Conformité RGPD / NIS2} : accès sécurisé et journalisé.
        \item \textbf{Expérience utilisateur améliorée} avec une interface modernisée.
        \item \textbf{Architecture évolutive et sécurisée}.
      \end{itemize}
    \end{itemize}
  \end{frame}

  % =======================
  % 2. ENJEUX (PESTEL)
  % =======================
  \section{Enjeux dégagés}

  \begin{frame}{Enjeux légaux et technologiques}
    \begin{block}{Enjeu légal : RGPD, NIS2}
      \textbf{Problème en V1 :} Tout praticien pouvait consulter les données des autres praticiens.
      \textbf{Solution en V2 :} Cloisonnement des accès par rôles + audit des accès.
    \end{block}

    \begin{block}{Enjeu technologique}
      \textbf{Problème en V1 :} Architecture monolithique, faible modularité.
      \textbf{Solution en V2 :} Nouvelle architecture modulaire + intégration simplifiée avec le SI hospitalier.
    \end{block}
  \end{frame}

  % =======================
  % 3. CONCEPTS (EN THÉORIE)
  % =======================
  \section{Concepts}


  % 	\begin{frame}{Fonctionnalités clés de Supra V2}
  % 	\begin{itemize}
  % 		\item \ding{226} \textbf{Activités médicales} : CCAM, NGAP, gestion labo.
  % 		\item \ding{226} \textbf{Suivi des Services et UF} : hiérarchisation et filtrage avancé.
  % 		\item \ding{226} \textbf{Enseignement et SIGAPS} : structuration des données de recherche.
  % 		\item \ding{226} \textbf{Garde et astreinte} : suivi détaillé des astreintes par praticien.
  % 		\item \ding{226} \textbf{Activité libérale} : intégration spécifique des actes libéraux dans le suivi global.
  % 	\end{itemize}
  % 	\end{frame}

  \begin{frame}{Fonctionnalités clés de Supra V2}
    \begin{columns}
      \begin{column}{0.6\textwidth}
        \begin{itemize}
          \item \textbf{Activités médicales} : CCAM, NGAP, gestion labo.
          \item \textbf{Suivi des Services et UF} : hiérarchisation et filtrage avancé.
          \item \textbf{Enseignement \& SIGAPS} : structuration des données de recherche.
          \item \textbf{Garde \& astreinte} : suivi détaillé des astreintes par praticien.
          \item \textbf{Activité libérale} : intégration spécifique des actes libéraux dans le suivi global.
        \end{itemize}
      \end{column}
      \begin{column}{0.4\textwidth}
        \centering
        \includegraphics[width=\textwidth]{poc.png} % Vérifie que "poc.png" est bien dans le dossier du projet
      \end{column}
    \end{columns}
  \end{frame}




  \begin{frame}{Améliorations structurelles}
    \begin{itemize}
      \item \textbf{Sécurité des accès} : chaque praticien ne voit que ses propres données.
      \item \textbf{Architecture évolutive} : migration vers un système modulaire.
      \item \textbf{Intégration SI hospitalier} : interfaçage plus efficace avec les autres systèmes.
      \item \textbf{Nouvelles interfaces et ergonomie améliorée}.
    \end{itemize}
  \end{frame}


  \begin{frame}{Comment fonctionne Supra V2 ?}
    \centering
    \begin{tikzpicture}
      % Serveur central SaaS
      \node[draw, fill=blue!20, rounded corners, minimum width=6cm, minimum height=1cm, align=center]
      (saas) at (0, 2) {\textbf{Infra Supra V2 } \\ \small Base mutualisée et services communs};

      % Hôpitaux du GHT
      \node[draw, fill=green!20, rounded corners, minimum width=4cm, minimum height=1cm, align=center]
      (hopital1) at (-4, 0) {\textbf{CHRU Nancy} \\ \small Accès sécurisé};

      \node[draw, fill=green!20, rounded corners, minimum width=4cm, minimum height=1cm, align=center]
      (hopital2) at (0, 0) {\textbf{Hôpital X} \\ \small Accès sécurisé};

      \node[draw, fill=green!20, rounded corners, minimum width=4cm, minimum height=1cm, align=center]
      (hopital3) at (4, 0) {\textbf{Hôpital Y} \\ \small Accès sécurisé};

      % Connexions Serveur -> Hôpitaux
      \draw[->, thick] (saas) -- (hopital1) node[midway, left] {\footnotesize Accès};
      \draw[->, thick] (saas) -- (hopital2) node[midway, right] {\footnotesize Accès };
      \draw[->, thick] (saas) -- (hopital3) node[midway, right] {\footnotesize Accès };

      % Praticiens dans chaque hôpital
      \node[draw, fill=yellow!20, rounded corners, minimum width=2.5cm, minimum height=0.8cm]
      (praticien1) at (-4, -2) {\textbf{Praticien A}};

      \node[draw, fill=yellow!20, rounded corners, minimum width=2.5cm, minimum height=0.8cm]
      (praticien2) at (0, -2) {\textbf{Praticien B}};

      \node[draw, fill=yellow!20, rounded corners, minimum width=2.5cm, minimum height=0.8cm]
      (praticien3) at (4, -2) {\textbf{Praticien C}};

      % Connexions praticiens - hôpitaux
      \draw[->, dashed] (hopital1) -- (praticien1);
      \draw[->, dashed] (hopital2) -- (praticien2);
      \draw[->, dashed] (hopital3) -- (praticien3);
    \end{tikzpicture}

    \vspace{0.5cm}
    \textbf{Principe :} Supra V2 a été \textbf{pensé} pour être extensible aux autres hôpitaux du GHT.
    Chaque établissement \textbf{\textcolor{blue}{pourra disposer}} d’un accès sécurisé à son espace, tout en bénéficiant d’une infrastructure mutualisée.
  \end{frame}

  \begin{frame}{Contraintes et prérequis du mode mutualisé}
    \textbf{Conditions pour un déploiement efficace de Supra V2 en mode mutualisé}

    \begin{itemize}
      \item \textbf{Référent local} : gestion des accès et configurations.
      \item \textbf{Données spécifiques} : structure interne, actes, gardes, libéral, enseignement.
      \item \textbf{Connexion au SI} : accès aux fichiers pour l’automatisation des flux.
      \item \textbf{Sécurité} : cloisonnement strict des données par hôpital.
    \end{itemize}

    \vspace{0.4cm}
    \textbf{\textcolor{blue}{Chaque hôpital devra s'organiser en interne pour assurer son flux de données.}}
  \end{frame}




  % =======================
  % 4. BENCHMARK (EN PRATIQUE)
  % =======================
  \section{Benchmark}

  \begin{frame}{Benchmark}
    \textbf{Comparaison des évolutions entre Supra V1 et Supra V2}

    \vspace{0.5cm}

    \begin{columns}
      % Colonne Supra V1
      \column{0.45\textwidth}
      \textbf{\textcolor{red}{Supra V1}} \\[0.2cm]
      \begin{itemize}
        \item \textcolor{red}{\ding{55}} \textbf{Non conforme RGPD} : accès trop large
        \item \textcolor{red}{\ding{55}} \textbf{Accès utilisateurs} : tout praticien voit tout
        \item \textcolor{red}{\ding{55}} \textbf{Architecture} : monolithique rigide
        \item \textcolor{red}{\ding{55}} \textbf{Ergonomie} : interface vieillissante
        \item \textcolor{red}{\ding{55}} \textbf{Sécurité} : peu de restrictions
      \end{itemize}

      % Colonne Supra V2
      \column{0.45\textwidth}
      \textbf{\textcolor{green}{Supra V2}} \\[0.2cm]
      \begin{itemize}
        \item \textcolor{green}{\ding{51}} \textbf{Cloisonnement des accès} (RGPD conforme)
        \item \textcolor{green}{\ding{51}} \textbf{Rôles définis, logs d’accès}
        \item \textcolor{green}{\ding{51}} \textbf{Architecture modulaire et évolutive}
        \item \textcolor{green}{\ding{51}} \textbf{Design modernisé et responsive}
        \item \textcolor{green}{\ding{51}} \textbf{Journalisation des accès et sécurité renforcée}
      \end{itemize}
    \end{columns}
  \end{frame}




  % =======================
  % 5. RÉPONSE AUX ENJEUX
  % =======================
  \section{Réponse aux enjeux}

  \begin{frame}{Réponse aux enjeux légaux (RGPD et Confidentialité)}
    \begin{itemize}
      \item \textbf{Accès sécurisé et traçabilité} :
      \begin{itemize}
        \item \ding{51} Restriction des accès selon les rôles et les services.
        \item \ding{51} Journalisation des connexions pour un suivi renforcé.
      \end{itemize}

      \item \textbf{Conformité RGPD et confidentialité} :
      \begin{itemize}
        \item \ding{51} Cloisonnement strict des données par praticien et par service.
        \item \ding{51} Gestion fine des consentements et anonymisation possible.
      \end{itemize}
    \end{itemize}
  \end{frame}

  \begin{frame}{Réponse aux enjeux technologiques}
    \begin{itemize}
      \item \textbf{Interopérabilité et intégration} :
      \begin{itemize}
        \item \ding{51} Compatibilité avec les SI hospitaliers existants.
        \item \ding{51} Formats standards facilitant l’échange de données.
      \end{itemize}

      \item \textbf{Modernisation et évolutivité} :
      \begin{itemize}
        \item \ding{51} Architecture modulaire garantissant l’évolutivité.
        \item \ding{51} Support des nouvelles technologies pour une meilleure pérennité.
      \end{itemize}
    \end{itemize}
  \end{frame}


  %--
  %--	\begin{frame}{Récupération et intégration des données}
  %--	\begin{itemize}
  %--		\item \textbf{Qui fournit les données ?}
  %--	\begin{itemize}
  %--		\item \textbf{DFAC} : Données administratives et financières.
  %--		\item \textbf{DAM} : Référentiels médicaux, règles d'affectation.
  %--		\item \textbf{Services hospitaliers} : Activités médicales, gardes, enseignement.
  %--		\end{itemize}

  %--		\item \textbf{Sous quelle forme ?}
  %--		\begin{itemize}
  %--			\item \textbf{Automatisé} : API, exports JSON, fichiers CSV/XML.
  %--			\item \textbf{Manuel} : Interfaces de saisie et validation humaine.
  %--		\end{itemize}

  %--		\item \textbf{Prochaines actions} :
  %--		\begin{itemize}
  %--			\item Standardisation des formats d’échange.
  %--			\item Mise en place des règles de validation et d’intégration.
  %--		\end{itemize}
  %--	\end{itemize}
  %--	\end{frame}

  \begin{frame}{Scénarios utilisateurs et prochaines étapes}
    \begin{itemize}
      \item \textbf{Création des scénarios utilisateurs} :
      \begin{itemize}
        \item Validation des attentes métiers avec chaque acteur (DFAC, CME, Praticiens).
        \item Tests sur des \textbf{cas réels et workflows opérationnels}.
        \item \textbf{Exemple} : "En tant que \textbf{DFAC}, et pour un \textbf{service donné}, j’aimerais voir les \textbf{personnes non affectées par la DAM}."
      \end{itemize}

      \item \textbf{Prochaines étapes} :
      \begin{itemize}
        \item \textbf{Tests finaux} sur la gestion des accès et conformité.
        \item \textbf{Déploiement progressif} chez les différents mandataires.
        \item \textbf{Formation des utilisateurs} pour assurer une adoption efficace.
      \end{itemize}
    \end{itemize}

  \end{frame}

  \begin{frame}{Questions / Échanges}
    \centering
    \Large \textbf{Merci pour votre attention !} \\[0.5cm]

    \textbf{Une question, un retour ? Discutons ensemble !} \\[1cm]

    \textbf{\ding{43} Accès au Proof of Concept (POC) :} \\[0.3cm]
    \textcolor{blue}{\url{http://supra-v2-preview.chu-nancy.fr}} \\[1cm]

    \begin{tabular}{ll}
      \textbf{Référent:} & BALDE Ismaila \\
      \textbf{Email :} & \textcolor{blue}{<EMAIL>}
    \end{tabular}
  \end{frame}

  %--	\begin{frame}
  %--	\centering
  %--	\vfill
  %--{\Huge \textbf{Merci !}} \\[0.5cm]
  %--	{\Large \textit{Fin de la présentation}} \\[1cm]

  %--\textcolor{gray}{\small \textbf{Et après ?} \\ Prochaines étapes à venir dans les futures itérations.}

  %--\vfill
  %--\end{frame}


%------------------------------------------
%--- Scénarios Utilisateurs
%--- SLIDE à ne pas montrer
%------------------------------------------




\end{document}
