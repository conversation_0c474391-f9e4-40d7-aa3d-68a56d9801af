import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

// Components
import { PraticienListComponent } from './components/praticien-list/praticien-list.component';
import { SinglePraticienComponent } from './components/single-praticien/single-praticien.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'praticien-list',
    pathMatch: 'full'
  },
  {
    path: 'praticien-list',
    component: PraticienListComponent,
    data: { 
      title: 'Liste des Praticiens',
      breadcrumb: 'Liste des Praticiens'
    }
  },
  {
    path: 'praticien/:id',
    component: SinglePraticienComponent,
    data: { 
      title: 'Détails du Praticien',
      breadcrumb: 'Détails du Praticien'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PraticienRoutingModule { }
