# Structure Services

This directory contains services for interacting with the hospital structure API endpoints.

## Available Services

- `EjService`: For interacting with Entités Juridiques (Hospitals) - `/api/ejs`
- `PoleService`: For interacting with Poles - `/api/poles`
- `CRService`: For interacting with CRs - `/api/crs`
- `ServiceStructureService`: For interacting with Services - `/api/services`
- `UFService`: For interacting with UFs - `/api/ufs`

## Features

All services extend the `BaseStructureService` which provides:

- Efficient caching with automatic expiration (5 minutes by default)
- Pagination support
- Prefetching of adjacent pages for smoother pagination
- Client-side filtering to reduce API calls
- Individual item caching for faster retrieval
- Comprehensive error handling

## Usage Examples

### Import

```typescript
// Import individual services
import { EjService } from '@app/core/services/structure/ej.service';

// Or import multiple services using the barrel file
import { EjService, PoleService, UFService } from '@app/core/services/structure';
```

### Basic Usage

```typescript
// Get all items
this.ejService.getAll().subscribe(ejs => {
  console.log('All EJs:', ejs);
});

// Get a single item by ID
this.ejService.getById('1f0318d2-751e-65c8-900c-93323fa75586').subscribe(ej => {
  console.log('Single EJ:', ej);
});
```

### Pagination

```typescript
// Get a specific page of data
this.poleService.getAll(1, 10).subscribe(poles => {
  console.log('Page 1 of poles (10 per page):', poles);
});

// Get a page with automatic prefetching of adjacent pages
this.poleService.getPage(2, 10).subscribe(poles => {
  console.log('Page 2 of poles with prefetching:', poles);
});
```

### Filtering

```typescript
// Filter items by a property
this.ufService.filterBy('isActif', true).subscribe(activeUfs => {
  console.log('Active UFs:', activeUfs);
});
```

### Cache Management

```typescript
// Clear the cache for a specific service
this.crService.clearCache();
```

## Performance Considerations

- These services implement caching to reduce API calls for frequently accessed data
- When possible, client-side filtering is used to avoid unnecessary API calls
- Adjacent pages are prefetched for smoother pagination
- Individual items are cached separately for faster retrieval by ID

## Data Relationships

The hospital structure follows this hierarchy:

```
EntiteJuridique (Hopital) 1 — * Pole 1 — * CR 1 — * UF
EntiteJuridique (Hopital) 1 — * Service 1 — * UF
```

- EntiteJuridique (Hopital) has many Poles and Services
- Pole has many CRs
- CR has many UFs
- Service is related to UFs via SECODE
- UF is related to CR via CRCODE and to Service via SECODE
