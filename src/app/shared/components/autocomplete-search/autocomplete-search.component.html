<div class="autocomplete-search-container">
  <!-- Input de recherche avec icônes -->
  <div class="search-input-wrapper" [class.focused]="isOpen" [class.loading]="isLoading">
    <i class="pi pi-search search-icon"></i>

    <input
      #searchInput
      type="text"
      [formControl]="searchControl"
      [placeholder]="placeholder"
      class="search-input"
      autocomplete="off"
      spellcheck="false"
      (keydown)="onKeyDown($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
    />

    <!-- Spinner de chargement -->
    <div class="loading-spinner" *ngIf="isLoading">
      <i class="pi pi-spin pi-spinner"></i>
    </div>

    <!-- Bouton clear -->
    <button
      *ngIf="searchControl.value && !isLoading"
      class="clear-button"
      type="button"
      (click)="clear()"
      tabindex="-1"
    >
      <i class="pi pi-times"></i>
    </button>
  </div>

  <!-- Dropdown des résultats -->
  <div class="search-dropdown" *ngIf="isOpen" [@slideDown]>
    <!-- Header avec statistiques -->
    <div class="search-header" *ngIf="showStats && totalResults > 0">
      <div class="search-stats">
        <span class="results-count">
          <strong>{{ totalResults }}</strong> résultat{{ totalResults > 1 ? 's' : '' }}
        </span>
        <span class="search-time">en {{ searchTime.toFixed(0) }}ms</span>
      </div>
      <div class="powered-by">
        <i class="pi pi-search"></i>
        Moteur de recherche
      </div>
    </div>

    <!-- Résultats groupés par catégorie -->
    <div class="search-results" *ngIf="showCategories && getObjectKeys(groupedResults).length > 0">
      <div
        *ngFor="let type of getObjectKeys(groupedResults)"
        class="result-category"
      >
        <!-- En-tête de catégorie -->
        <div class="category-header">
          <i class="pi pi-{{ getEntityIcon(type) }}" [style.color]="getEntityColor(type)"></i>
          <span class="category-label">{{ getEntityLabel(type) }}</span>
          <span class="category-count">({{ groupedResults[type].length }})</span>
        </div>

        <!-- Résultats de la catégorie -->
        <div class="category-results">
          <div
            *ngFor="let result of groupedResults[type]; let i = index; trackBy: trackByFn"
            class="search-result-item"
            [class.highlighted]="highlightedIndex === searchResults.indexOf(result)"
            (click)="selectResult(result)"
            (mouseenter)="highlightedIndex = searchResults.indexOf(result)"
          >
            <!-- Icône du type -->
            <div class="result-icon">
              <i class="pi pi-{{ getEntityIcon(result.type) }}" [style.color]="getEntityColor(result.type)"></i>
            </div>

            <!-- Contenu principal -->
            <div class="result-content">
              <div class="result-title" [innerHTML]="highlightMatch(result.fullName || result.nom, searchControl.value || '')"></div>

              <!-- Informations secondaires -->
              <div class="result-details" *ngIf="result.type === 'praticien'">
                <span class="detail-item" *ngIf="result.matricule">
                  <i class="pi pi-id-card"></i>
                  {{ result.matricule }}
                </span>
                <span class="detail-item" *ngIf="result.specialite">
                  <i class="pi pi-bookmark"></i>
                  {{ result.specialite }}
                </span>
              </div>
            </div>

            <!-- Badge du type -->
            <div class="result-badge">
              <span class="badge" [style.background-color]="getEntityColor(result.type)">
                {{ getEntityLabel(result.type) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Résultats simples (sans catégories) -->
    <div class="search-results simple" *ngIf="!showCategories && searchResults.length > 0">
      <div
        *ngFor="let result of searchResults; let i = index; trackBy: trackByFn"
        class="search-result-item"
        [class.highlighted]="highlightedIndex === i"
        (click)="selectResult(result)"
        (mouseenter)="highlightedIndex = i"
      >
        <!-- Icône du type -->
        <div class="result-icon">
          <i class="pi pi-{{ getEntityIcon(result.type) }}" [style.color]="getEntityColor(result.type)"></i>
        </div>

        <!-- Contenu principal -->
        <div class="result-content">
          <div class="result-title" [innerHTML]="highlightMatch(result.fullName || result.nom, searchControl.value || '')"></div>

          <!-- Informations secondaires -->
          <div class="result-details" *ngIf="result.type === 'praticien'">
            <span class="detail-item" *ngIf="result.matricule">
              <i class="pi pi-id-card"></i>
              {{ result.matricule }}
            </span>
            <span class="detail-item" *ngIf="result.specialite">
              <i class="pi pi-bookmark"></i>
              {{ result.specialite }}
            </span>
          </div>
        </div>

        <!-- Badge du type -->
        <div class="result-badge">
          <span class="badge" [style.background-color]="getEntityColor(result.type)">
            {{ getEntityLabel(result.type) }}
          </span>
        </div>
      </div>
    </div>

    <!-- Message si aucun résultat -->
    <div class="no-results" *ngIf="!isLoading && searchControl.value && searchResults.length === 0">
      <div class="no-results-icon">
        <i class="pi pi-search"></i>
      </div>
      <div class="no-results-text">
        <div class="no-results-title">Aucun résultat trouvé</div>
        <div class="no-results-subtitle">
          {{ noResultsMessage }}
        </div>
      </div>
    </div>

    <!-- Footer avec raccourcis clavier -->
    <div class="search-footer" *ngIf="searchResults.length > 0">
      <div class="keyboard-shortcuts">
        <span class="shortcut">
          <kbd>↑</kbd><kbd>↓</kbd> naviguer
        </span>
        <span class="shortcut">
          <kbd>↵</kbd> sélectionner
        </span>
        <span class="shortcut">
          <kbd>Esc</kbd> fermer
        </span>
      </div>
    </div>
  </div>
</div>
