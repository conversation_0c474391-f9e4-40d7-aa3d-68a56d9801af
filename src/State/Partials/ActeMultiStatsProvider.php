<?php

namespace App\State\Partials;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\Activite\Partials\ActeMultiStatsDto;
use App\Repository\ActesRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;

/**
 * StateProvider pour les statistiques multi-actes
 *
 * Calcule la répartition agrégée de plusieurs activités par acte médical
 * sur 3 périodes temporelles avec cache Redis.
 *
 * Route: POST /api/actes/multi-stats
 * Paramètres: p1Start, p1End, p2Start, p2End, p3Start, p3End
 * Body: {"activiteIds": ["1", "2", "3", "4", "5"]}
 *
 * Exemple Postman:
 * POST http://localhost:8000/api/actes/multi-stats?p1Start=2024-01-01&p1End=2024-12-31&p2Start=2023-01-01&p2End=2023-12-31&p3Start=2022-01-01&p3End=2022-12-31
 * Body: {"activiteIds": ["1", "2", "3", "4", "5"]}
 */
class ActeMultiStatsProvider implements ProviderInterface
{
    public function __construct(
        private ActesRepository $actesRepository,
        private RequestStack $requestStack,
        private CacheInterface $cache,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 3600
    ) {}

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): object|array|null
    {
        $request = $this->requestStack->getCurrentRequest();
        if (!$request) {
            return [];
        }

        // Récupération des IDs d'activités depuis les query parameters
        $activiteIdsParam = $request->query->get('activiteIds');
        if (!$activiteIdsParam) {
            return [];
        }

        // Les IDs peuvent être passés comme "1,2,3,4,5" ou comme array
        if (is_string($activiteIdsParam)) {
            $activiteIds = explode(',', $activiteIdsParam);
        } else {
            $activiteIds = (array) $activiteIdsParam;
        }

        // Nettoyer et valider les IDs
        $activiteIds = array_filter(array_map('trim', $activiteIds));

        if (empty($activiteIds)) {
            return [];
        }

        // Récupération des paramètres de périodes depuis la query string
        $p1Start = $request->query->get('p1Start');
        $p1End = $request->query->get('p1End');
        $p2Start = $request->query->get('p2Start');
        $p2End = $request->query->get('p2End');
        $p3Start = $request->query->get('p3Start');
        $p3End = $request->query->get('p3End');

        // Création de la clé de cache unique
        $cacheKey = sprintf(
            'acte_multi_stats_%s_%s_%s_%s_%s_%s_%s',
            md5(implode(',', $activiteIds)),
            $p1Start ?? 'null',
            $p1End ?? 'null',
            $p2Start ?? 'null',
            $p2End ?? 'null',
            $p3Start ?? 'null',
            $p3End ?? 'null'
        );

        return $this->cache->get($cacheKey, function (ItemInterface $item) use (
            $activiteIds, $p1Start, $p1End, $p2Start, $p2End, $p3Start, $p3End
        ) {
            $item->expiresAfter($this->apiCacheTtl);

            // Conversion des dates string en objets DateTime
            $p1StartDate = $p1Start ? new \DateTime($p1Start) : null;
            $p1EndDate = $p1End ? new \DateTime($p1End) : null;
            $p2StartDate = $p2Start ? new \DateTime($p2Start) : null;
            $p2EndDate = $p2End ? new \DateTime($p2End) : null;
            $p3StartDate = $p3Start ? new \DateTime($p3Start) : null;
            $p3EndDate = $p3End ? new \DateTime($p3End) : null;

            // Récupération des statistiques agrégées par acte
            $acteStatsData = $this->actesRepository->getMultiActeStatsByActiviteIds(
                $activiteIds,
                $p1StartDate,
                $p1EndDate,
                $p2StartDate,
                $p2EndDate,
                $p3StartDate,
                $p3EndDate
            );

            $acteStats = [];

            foreach ($acteStatsData as $acteData) {
                $totalRealisations = $acteData['p1_count'] + $acteData['p2_count'] + $acteData['p3_count'];

                // Inclusion uniquement des actes qui ont au moins une réalisation
                if ($totalRealisations > 0) {
                    $acteStats[] = new ActeMultiStatsDto(
                        acteCode: $acteData['code'],
                        acteDescription: $acteData['description'] ?? 'Description non disponible',
                        p1Count: (int)$acteData['p1_count'],
                        p2Count: (int)$acteData['p2_count'],
                        p3Count: (int)$acteData['p3_count'],
                        totalActivites: (int)$acteData['total_activites'],
                        totalRealisations: $totalRealisations
                    );
                }
            }

            // Tri par total de réalisations décroissant
            usort($acteStats, fn($a, $b) => $b->totalRealisations <=> $a->totalRealisations);

            return $acteStats;
        });
    }

        // Récupération des IDs d'activités depuis les query parameters
        $activiteIdsParam = $request->query->get('activiteIds');
        if (!$activiteIdsParam) {
            return [];
        }

        // Les IDs peuvent être passés comme "1,2,3,4,5" ou comme array
        if (is_string($activiteIdsParam)) {
            $activiteIds = explode(',', $activiteIdsParam);
        } else {
            $activiteIds = (array) $activiteIdsParam;
        }

        // Nettoyer et valider les IDs
        $activiteIds = array_filter(array_map('trim', $activiteIds));

        if (empty($activiteIds)) {
            return [];
        }

        // Récupération des paramètres de périodes depuis la query string
        $p1Start = $request->query->get('p1Start');
        $p1End = $request->query->get('p1End');
        $p2Start = $request->query->get('p2Start');
        $p2End = $request->query->get('p2End');
        $p3Start = $request->query->get('p3Start');
        $p3End = $request->query->get('p3End');

        // Création de la clé de cache unique
        $cacheKey = sprintf(
            'acte_multi_stats_%s_%s_%s_%s_%s_%s_%s',
            md5(implode(',', $activiteIds)),
            $p1Start ?? 'null',
            $p1End ?? 'null',
            $p2Start ?? 'null',
            $p2End ?? 'null',
            $p3Start ?? 'null',
            $p3End ?? 'null'
        );

        return $this->cache->get($cacheKey, function (ItemInterface $item) use (
            $activiteIds, $p1Start, $p1End, $p2Start, $p2End, $p3Start, $p3End
        ) {
            $item->expiresAfter($this->apiCacheTtl);

            // Conversion des dates string en objets DateTime
            $p1StartDate = $p1Start ? new \DateTime($p1Start) : null;
            $p1EndDate = $p1End ? new \DateTime($p1End) : null;
            $p2StartDate = $p2Start ? new \DateTime($p2Start) : null;
            $p2EndDate = $p2End ? new \DateTime($p2End) : null;
            $p3StartDate = $p3Start ? new \DateTime($p3Start) : null;
            $p3EndDate = $p3End ? new \DateTime($p3End) : null;

            // Récupération des statistiques agrégées par acte
            $acteStatsData = $this->actesRepository->getMultiActeStatsByActiviteIds(
                $activiteIds,
                $p1StartDate,
                $p1EndDate,
                $p2StartDate,
                $p2EndDate,
                $p3StartDate,
                $p3EndDate
            );

            $acteStats = [];

            foreach ($acteStatsData as $acteData) {
                $totalRealisations = $acteData['p1_count'] + $acteData['p2_count'] + $acteData['p3_count'];

                // Inclusion uniquement des actes qui ont au moins une réalisation
                if ($totalRealisations > 0) {
                    $acteStats[] = new ActeMultiStatsDto(
                        acteCode: $acteData['code'],
                        acteDescription: $acteData['description'] ?? 'Description non disponible',
                        p1Count: (int)$acteData['p1_count'],
                        p2Count: (int)$acteData['p2_count'],
                        p3Count: (int)$acteData['p3_count'],
                        totalActivites: (int)$acteData['total_activites'],
                        totalRealisations: $totalRealisations
                    );
                }
            }

            // Tri par total de réalisations décroissant
            usort($acteStats, fn($a, $b) => $b->totalRealisations <=> $a->totalRealisations);

            return $acteStats;
        });
    }
}
