<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  OverviewComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/pages/overview/overview.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-overview</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>





            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./overview.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./overview.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#truncateActeDescription" >truncateActeDescription</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>






    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="12"
                                    class="link-to-prism">src/app/pages/overview/overview.component.ts:12</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="truncateActeDescription"></a>
                    <span class="name">
                        <span ><b>truncateActeDescription</b></span>
                        <a href="#truncateActeDescription"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>truncateActeDescription(acte: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="15"
                                    class="link-to-prism">src/app/pages/overview/overview.component.ts:15</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>acte</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;

@Component({
  selector: &#x27;app-overview&#x27;,
  standalone: true,
  imports: [],
  templateUrl: &#x27;./overview.component.html&#x27;,
  styleUrl: &#x27;./overview.component.scss&#x27;
})
export class OverviewComponent implements OnInit{

  ngOnInit(): void {
  }

  truncateActeDescription(acte: string): string {
    const words &#x3D; acte.split(&#x27; &#x27;);
    if (words.length &gt; 2) {
      return words.slice(0, 3).join(&#x27; &#x27;) + &#x27;...&#x27;;
    }
    return acte;
  }



}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;div class&#x3D;&quot;mt-8&quot;&gt;
  &lt;div class&#x3D;&quot;max-w-6xl mx-auto px-4 sm:px-6 lg:px-8&quot;&gt;
    &lt;h2 class&#x3D;&quot;text-lg leading-6 font-medium text-gray-900&quot;&gt;Aperçu&lt;/h2&gt;
    &lt;div class&#x3D;&quot;mt-2 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3&quot;&gt;
      &lt;!-- Card --&gt;

      &lt;div class&#x3D;&quot;bg-white overflow-hidden shadow rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;p-5&quot;&gt;
          &lt;div class&#x3D;&quot;flex items-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex-shrink-0&quot;&gt;
              &lt;!-- Heroicon name: outline/scale --&gt;
              &lt;svg class&#x3D;&quot;h-6 w-6 text-gray-400&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3&quot; /&gt;
              &lt;/svg&gt;
            &lt;/div&gt;
            &lt;div class&#x3D;&quot;ml-5 w-0 flex-1&quot;&gt;
              &lt;dl&gt;
                &lt;dt class&#x3D;&quot;text-sm font-medium text-gray-500 truncate&quot;&gt;Actes CCAM&lt;/dt&gt;
                &lt;dd&gt;
                  &lt;div class&#x3D;&quot;text-lg font-medium text-gray-900&quot;&gt;120 Total&lt;/div&gt;
                  &lt;div class&#x3D;&quot;text-sm text-gray-500&quot;&gt;
                    Acte le plus fréquent :  &lt;span class&#x3D;&quot; shadow-md text-sm font-medium text-gray-500&quot;&gt;
                    {{ truncateActeDescription(&#x27;JNQM001 - Echographie non morphologique de la grossesse avant 11 semaines d\&#x27;amenorrhee&#x27;) }}
                     &lt;/span&gt;
                  &lt;/div&gt;
                &lt;/dd&gt;
              &lt;/dl&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class&#x3D;&quot;bg-gray-50 px-5 py-3&quot;&gt;
          &lt;div class&#x3D;&quot;text-sm&quot;&gt;
            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;font-medium text-cyan-700 hover:text-cyan-900&quot;&gt; Voir le détail complet &lt;/a&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;

      &lt;div class&#x3D;&quot;bg-white overflow-hidden shadow rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;p-5&quot;&gt;
          &lt;div class&#x3D;&quot;flex items-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex-shrink-0&quot;&gt;
              &lt;!-- Heroicon name: outline/scale --&gt;
              &lt;svg class&#x3D;&quot;h-6 w-6 text-gray-400&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3&quot; /&gt;
              &lt;/svg&gt;
            &lt;/div&gt;
            &lt;div class&#x3D;&quot;ml-5 w-0 flex-1&quot;&gt;
              &lt;dl&gt;
                &lt;dt class&#x3D;&quot;text-sm font-medium text-gray-500 truncate&quot;&gt;Actes NGAP&lt;/dt&gt;
                &lt;dd&gt;
                  &lt;div class&#x3D;&quot;text-lg font-medium text-gray-900&quot;&gt;95 Total&lt;/div&gt;
                  &lt;div class&#x3D;&quot;text-sm text-gray-500&quot;&gt;
                    Acte le plus fréquent : &lt;span class&#x3D;&quot; shadow-md text-sm font-medium text-gray-500&quot;&gt;
                    {{truncateActeDescription(&#x27;Consultation specialiste&#x27;)}}
                  &lt;/span&gt;
                  &lt;/div&gt;
                &lt;/dd&gt;
              &lt;/dl&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class&#x3D;&quot;bg-gray-50 px-5 py-3&quot;&gt;
          &lt;div class&#x3D;&quot;text-sm&quot;&gt;
            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;font-medium text-cyan-700 hover:text-cyan-900&quot;&gt; Voir le détail complet &lt;/a&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;

      &lt;div class&#x3D;&quot;bg-white overflow-hidden shadow rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;p-5&quot;&gt;
          &lt;div class&#x3D;&quot;flex items-center&quot;&gt;
            &lt;div class&#x3D;&quot;flex-shrink-0&quot;&gt;
              &lt;!-- Heroicon name: outline/scale --&gt;
              &lt;svg class&#x3D;&quot;h-6 w-6 text-gray-400&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot; fill&#x3D;&quot;none&quot; viewBox&#x3D;&quot;0 0 24 24&quot; stroke&#x3D;&quot;currentColor&quot; aria-hidden&#x3D;&quot;true&quot;&gt;
                &lt;path stroke-linecap&#x3D;&quot;round&quot; stroke-linejoin&#x3D;&quot;round&quot; stroke-width&#x3D;&quot;2&quot; d&#x3D;&quot;M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3&quot; /&gt;
              &lt;/svg&gt;
            &lt;/div&gt;
            &lt;div class&#x3D;&quot;ml-5 w-0 flex-1&quot;&gt;
              &lt;dl&gt;
                &lt;dt class&#x3D;&quot;text-sm font-medium text-gray-500 truncate&quot;&gt;Examens de Laboratoire&lt;/dt&gt;
                &lt;dd&gt;
                  &lt;div class&#x3D;&quot;text-lg font-medium text-gray-900&quot;&gt;78 Total&lt;/div&gt;
                  &lt;div class&#x3D;&quot;text-sm text-gray-500&quot;&gt;
                    Examen le plus fréquent : &lt;span class&#x3D;&quot; shadow-md text-sm font-medium text-gray-500 &quot;&gt;
                  {{truncateActeDescription(&#x27; Tests biochimiques&#x27;)}}
                  &lt;/span&gt;
                  &lt;/div&gt;
                &lt;/dd&gt;
              &lt;/dl&gt;
            &lt;/div&gt;
          &lt;/div&gt;
        &lt;/div&gt;
        &lt;div class&#x3D;&quot;bg-gray-50 px-5 py-3&quot;&gt;
          &lt;div class&#x3D;&quot;text-sm&quot;&gt;
            &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;font-medium text-cyan-700 hover:text-cyan-900&quot;&gt;  Voir le détail complet&lt;/a&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;

    &lt;/div&gt;
  &lt;/div&gt;

  &lt;h2 class&#x3D;&quot;max-w-6xl mx-auto mt-8 px-4 text-lg leading-6 font-medium text-gray-900 sm:px-6 lg:px-8&quot;&gt;TODO&lt;/h2&gt;
&lt;/div&gt;
</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><div class="mt-8">  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">    <h2 class="text-lg leading-6 font-medium text-gray-900">Aperçu</h2>    <div class="mt-2 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">      <!-- Card -->      <div class="bg-white overflow-hidden shadow rounded-lg">        <div class="p-5">          <div class="flex items-center">            <div class="flex-shrink-0">              <!-- Heroicon name: outline/scale -->              <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />              </svg>            </div>            <div class="ml-5 w-0 flex-1">              <dl>                <dt class="text-sm font-medium text-gray-500 truncate">Actes CCAM</dt>                <dd>                  <div class="text-lg font-medium text-gray-900">120 Total</div>                  <div class="text-sm text-gray-500">                    Acte le plus fréquent :  <span class=" shadow-md text-sm font-medium text-gray-500">                    {{ truncateActeDescription(\'JNQM001 - Echographie non morphologique de la grossesse avant 11 semaines d\\'amenorrhee\') }}                     </span>                  </div>                </dd>              </dl>            </div>          </div>        </div>        <div class="bg-gray-50 px-5 py-3">          <div class="text-sm">            <a href="#" class="font-medium text-cyan-700 hover:text-cyan-900"> Voir le détail complet </a>          </div>        </div>      </div>      <div class="bg-white overflow-hidden shadow rounded-lg">        <div class="p-5">          <div class="flex items-center">            <div class="flex-shrink-0">              <!-- Heroicon name: outline/scale -->              <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />              </svg>            </div>            <div class="ml-5 w-0 flex-1">              <dl>                <dt class="text-sm font-medium text-gray-500 truncate">Actes NGAP</dt>                <dd>                  <div class="text-lg font-medium text-gray-900">95 Total</div>                  <div class="text-sm text-gray-500">                    Acte le plus fréquent : <span class=" shadow-md text-sm font-medium text-gray-500">                    {{truncateActeDescription(\'Consultation specialiste\')}}                  </span>                  </div>                </dd>              </dl>            </div>          </div>        </div>        <div class="bg-gray-50 px-5 py-3">          <div class="text-sm">            <a href="#" class="font-medium text-cyan-700 hover:text-cyan-900"> Voir le détail complet </a>          </div>        </div>      </div>      <div class="bg-white overflow-hidden shadow rounded-lg">        <div class="p-5">          <div class="flex items-center">            <div class="flex-shrink-0">              <!-- Heroicon name: outline/scale -->              <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />              </svg>            </div>            <div class="ml-5 w-0 flex-1">              <dl>                <dt class="text-sm font-medium text-gray-500 truncate">Examens de Laboratoire</dt>                <dd>                  <div class="text-lg font-medium text-gray-900">78 Total</div>                  <div class="text-sm text-gray-500">                    Examen le plus fréquent : <span class=" shadow-md text-sm font-medium text-gray-500 ">                  {{truncateActeDescription(\' Tests biochimiques\')}}                  </span>                  </div>                </dd>              </dl>            </div>          </div>        </div>        <div class="bg-gray-50 px-5 py-3">          <div class="text-sm">            <a href="#" class="font-medium text-cyan-700 hover:text-cyan-900">  Voir le détail complet</a>          </div>        </div>      </div>    </div>  </div>  <h2 class="max-w-6xl mx-auto mt-8 px-4 text-lg leading-6 font-medium text-gray-900 sm:px-6 lg:px-8">TODO</h2></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'OverviewComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'OverviewComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
