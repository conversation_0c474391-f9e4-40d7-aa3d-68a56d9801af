/* Styles pour les fonctionnalités désactivées */

.feature-disabled {
  position: relative;
  opacity: 0.5 !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.05);
    border-radius: inherit;
    z-index: 1;
  }
}

.feature-blurred {
  position: relative;
  filter: blur(1px) !important;
  opacity: 0.6 !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: inherit;
    z-index: 1;
  }
}

.feature-coming-soon-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 10 !important;
  border-radius: 0.5rem !important;
  
  i {
    color: #6b7280 !important;
    font-size: 1.2rem !important;
    animation: pulse 2s infinite;
  }
}

/* Animation pour l'icône "bientôt disponible" */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Styles pour les éléments de menu désactivés */
.sidebar-item-disabled {
  .feature-coming-soon-overlay {
    border-radius: 0.375rem !important;
    
    i {
      font-size: 1rem !important;
      color: #94a3b8 !important;
    }
  }
}

/* Styles pour les boutons désactivés */
.button-disabled {
  background-color: #e5e7eb !important;
  color: #9ca3af !important;
  border-color: #d1d5db !important;
  cursor: not-allowed !important;
  
  &:hover {
    background-color: #e5e7eb !important;
    color: #9ca3af !important;
  }
}

/* Styles pour les cartes désactivées */
.card-disabled {
  background-color: #f9fafb !important;
  border-color: #e5e7eb !important;
  
  .feature-coming-soon-overlay {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(2px);
    
    i {
      color: #9ca3af !important;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .feature-coming-soon-overlay {
    i {
      font-size: 0.9rem !important;
    }
  }
}

/* Styles pour le composant feature-disabled */
.feature-disabled-container {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  min-height: 100vh;
}

.feature-disabled-card {
  background: white;
  border: 1px solid #e2e8f0;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

/* Animation d'entrée pour le composant */
.feature-disabled-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Styles pour les tooltips des features désactivées */
.feature-tooltip {
  position: relative;
  
  &:hover::after {
    content: attr(data-feature-message);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 50;
    margin-bottom: 0.25rem;
  }
  
  &:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #1f2937;
    z-index: 50;
  }
}
