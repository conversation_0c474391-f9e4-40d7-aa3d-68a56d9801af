import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink, RouterLinkActive, Router } from '@angular/router';
import { SidebarService } from '../../services/sidebar.service';
import { AuthService } from '../../services/auth/auth.service';
import { FeatureFlagDirective } from '../../directives/feature-flag.directive';

@Component({
  selector: 'app-sidebar-mobile',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    RouterLinkActive,
    FeatureFlagDirective
  ],
  templateUrl: './sidebar-mobile.component.html',
  styleUrls: ['./sidebar-mobile.component.scss']
})
export class SidebarMobileComponent implements OnInit {
  isSidebarOpen = false;

  constructor(
    private sidebarService: SidebarService,
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Écouter les changements d'état de la sidebar
    this.sidebarService.sidebarOpen$.subscribe(isOpen => {
      this.isSidebarOpen = isOpen;
      console.log('Sidebar mobile state changed:', isOpen);
    });
  }

  /**
   * Fermer la sidebar mobile
   */
  closeSidebar(): void {
    this.sidebarService.closeSidebar();
  }

  /**
   * Vérifier si l'utilisateur a le rôle admin
   */
  hasAdminRole(): boolean {
    return this.authService.hasAdminRole();
  }
}
