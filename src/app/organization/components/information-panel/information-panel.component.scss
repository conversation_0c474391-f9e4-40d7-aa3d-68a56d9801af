.custom-table {
  border-radius: 8px; /* Arrondir les coins */
  overflow: hidden; /* Assurer que le contenu respecte le radius */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Légère ombre */
}

.p-datatable-gridlines td,
.p-datatable-gridlines th {
  border: 1px solid #e5e7eb; /* Couleur de la bordure */
}

.p-datatable th {
  background-color: #e5f2ff; /* Couleur de fond pour les en-têtes */
  color: #333;
  font-weight: bold;
}

.p-datatable td {
  background-color: #f9f9f9; /* Couleur de fond pour les cellules */
  padding: 10px; /* Espace dans chaque cellule */
}

.p-datatable-striped .p-datatable-tbody > tr:nth-child(even) {
  background-color: #f3f4f6; /* Couleur de fond des lignes paires */
}

.p-datatable .bg-blue-50 {
  background-color: #eff6ff; /* Couleur de fond des en-têtes avec léger bleu */
}

.hover\:bg-blue-50:hover {
  background-color: #e0f2fe; /* Couleur de fond au survol */
}

.rounded-lg {
  border-radius: 8px;
}

// OVERLAY SUR LE BOUTON VOIR
.custom-overlay .p-overlaypanel {
  position: relative;
  padding-top: 8px;
  border-radius: 6px;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
}

.custom-overlay .p-overlaypanel::before {
  content: "";
  position: absolute;
  top: -8px;
  right: 15px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white; /* Couleur de fond de l'overlay */
}
