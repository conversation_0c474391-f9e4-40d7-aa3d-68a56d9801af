import {Component, Input, OnInit} from '@angular/core';

import * as ace from 'ace-builds'; // Import Ace Editor

// Import requis pour Ace
import 'ace-builds/src-noconflict/mode-json';
import 'ace-builds/src-noconflict/theme-monokai';

@Component({
  selector: 'app-json-editor',
  standalone: true,
  imports: [],
  templateUrl: './json-editor.component.html',
  styleUrl: './json-editor.component.scss'
})
export class JsonEditorComponent implements OnInit {
  @Input() jsonData: any; // JSON à afficher dans l'éditeur

  editor!: ace.Ace.Editor;

  ngOnInit(): void {
    this.setAceBasePath();
    this.initializeEditor();
  }

  private setAceBasePath(): void {
    // ace.config.set('basePath', 'https://cdnjs.cloudflare.com/ajax/libs/ace/1.4.14/');
    ace.config.set('basePath', './assets/ace/');
  }

  private initializeEditor(): void {
    // Initialisation de l'éditeur Ace
    this.editor = ace.edit('jsonEditor', {
      mode: 'ace/mode/json',
      theme: 'ace/theme/monokai',
      value: JSON.stringify(this.jsonData, null, 2),
      readOnly: false, // Éditeur en lecture seule
      fontSize: 14
    });
  }
}
