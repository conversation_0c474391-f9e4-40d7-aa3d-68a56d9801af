/* src/app/header/header.component.scss */
.list-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f9fafb;
  }

  .icon {
    margin-right: 0.75rem;
  }

  .info {
    flex: 1;
    display: flex;
    flex-direction: column;

    .name {
      font-weight: 500;
      color: #1f2937;
    }

    .details {
      font-size: 0.75rem;
      color: #6b7280;
    }
  }
}

.no-results {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #6b7280;
  font-style: italic;

  svg {
    margin-right: 0.75rem;
  }
}


// Custom paginator

/* src/app/header/header.component.scss */
/* src/app/header/header.component.scss */
:host {
  ul {
    max-height: 14rem;
    overflow-y: auto;
  }

  .list-item {
    padding: 0.5rem 1rem;
    min-height: 3rem;
    display: flex;
    align-items: center;
  }

  .custom-paginator {
    display: flex;
    justify-content: center;
    padding: 0.5rem;

    ::ng-deep .p-paginator {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.5rem;
      height: 2.5rem;
    }

    ::ng-deep .p-paginator-pages {
      display: flex;
      gap: 0.25rem;
    }

    ::ng-deep .p-paginator-page {
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.875rem;
    }

    ::ng-deep .p-paginator-first,
    ::ng-deep .p-paginator-prev,
    ::ng-deep .p-paginator-next,
    ::ng-deep .p-paginator-last {
      padding: 0.25rem;
    }
  }

  /* Style pour l'overlay (facultatif, peut être transparent) */
  .fixed.inset-0 {
    background-color: rgba(0, 0, 0, 0.01); /* Légère opacité pour feedback visuel */
  }
}
.message-info {
  padding: 0.5rem 1rem;
  font-size: 0.7rem; /* Réduit la taille du texte si nécessaire */
}
