import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError, of, forkJoin } from 'rxjs';
import { catchError, map, shareReplay, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

export interface PaginatedResult<T> {
  items: T[];
  totalItems: number;
}

@Injectable()
export abstract class BaseStructureService<T, R> {
  protected cache = new Map<string, Observable<PaginatedResult<T>>>();
  protected itemCache = new Map<string, T>();
  protected cacheTime = 5 * 60 * 1000; // 5 minutes cache
  protected allDataCache: PaginatedResult<T> | null = null;

  constructor(
    protected http: HttpClient,
    protected apiPath: string
  ) {}

  /**
   * Get all items with optional pagination and sorting
   * @param page Page number (optional)
   * @param limit Items per page (optional)
   * @param sortField Field to sort by (optional)
   * @param sortOrder Sort order (1 for ascending, -1 for descending) (optional)
   * @returns Observable of PaginatedResult containing items array and total count
   */
  getAll(page?: number, limit?: number, sortField?: string, sortOrder?: number): Observable<PaginatedResult<T>> {
    const cacheKey = `${this.apiPath}_${page || 'all'}_${limit || 'all'}_${sortField || 'none'}_${sortOrder || 'none'}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey) as Observable<PaginatedResult<T>>;
    }

    let params = new HttpParams();
    if (page !== undefined) {
      params = params.set('page', page.toString());
    }
    if (limit !== undefined) {
      params = params.set('itemsPerPage', limit.toString());
    }
    if (sortField !== undefined && sortField !== '') {
      params = params.set('order[' + sortField + ']', sortOrder !== undefined ? sortOrder.toString() : '1');
    }

    const request = this.http.get<R>(`${environment.api_url}${this.apiPath}`, { params }).pipe(
      map(response => this.extractData(response)),
      tap(paginatedResult => {
        // Cache individual items for getById
        paginatedResult.items.forEach(item => {
          const id = this.getItemId(item);
          if (id) {
            this.itemCache.set(id, item);
          }
        });

        // If we're getting all data without pagination, cache it
        if (page === undefined && limit === undefined) {
          this.allDataCache = paginatedResult;
        }
      }),
      shareReplay(1),
      catchError(this.handleError)
    );

    this.cache.set(cacheKey, request);

    // Clear cache after specified time
    setTimeout(() => {
      this.cache.delete(cacheKey);
    }, this.cacheTime);

    return request;
  }

  /**
   * Get a single item by ID
   * @param id Item ID
   * @returns Observable of single item
   */
  getById(id: string): Observable<T> {
    // Check if item is in cache
    if (this.itemCache.has(id)) {
      return of(this.itemCache.get(id) as T);
    }

    return this.http.get<T>(`${environment.api_url}${this.apiPath}/${id}`).pipe(
      tap(item => {
        // Cache the item
        this.itemCache.set(id, item);
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Get data with pagination and prefetch adjacent pages
   * @param page Current page number
   * @param limit Items per page
   * @returns Observable of PaginatedResult containing items array and total count
   */
  getPage(page: number, limit: number): Observable<PaginatedResult<T>> {
    // Get current page
    const currentPage$ = this.getAll(page, limit);

    // Prefetch next page in background
    this.prefetchPage(page + 1, limit);

    // If not first page, prefetch previous page
    if (page > 1) {
      this.prefetchPage(page - 1, limit);
    }

    return currentPage$;
  }

  /**
   * Prefetch a page and store in cache
   * @param page Page number to prefetch
   * @param limit Items per page
   */
  private prefetchPage(page: number, limit: number): void {
    const cacheKey = `${this.apiPath}_${page}_${limit}`;

    // Only prefetch if not already in cache
    if (!this.cache.has(cacheKey)) {
      this.getAll(page, limit).subscribe();
    }
  }

  /**
   * Filter items by property value
   * @param property Property name to filter by
   * @param value Value to match
   * @returns Observable of PaginatedResult containing filtered items
   */
  filterBy(property: keyof T, value: any): Observable<PaginatedResult<T>> {
    // If we have all data cached, filter locally
    if (this.allDataCache) {
      const filtered = this.allDataCache.items.filter(item =>
        (item[property] as any) === value
      );
      return of({
        items: filtered,
        totalItems: filtered.length
      });
    }

    // Otherwise get all data and filter
    return this.getAll().pipe(
      map(paginatedResult => {
        const filtered = paginatedResult.items.filter(item =>
          (item[property] as any) === value
        );
        return {
          items: filtered,
          totalItems: filtered.length
        };
      })
    );
  }

  /**
   * Extract data from API response
   * @param response API response
   * @returns PaginatedResult containing items array and total count
   */
  protected abstract extractData(response: R): PaginatedResult<T>;

  /**
   * Get unique ID from item
   * Default implementation tries to get @id property
   * Override in derived classes if needed
   * @param item Item to get ID from
   * @returns ID string or null if not found
   */
  protected getItemId(item: T): string | null {
    return (item as any)['@id'] || null;
  }

  /**
   * Handle HTTP errors
   * @param error HTTP error
   * @returns Observable with error
   */
  protected handleError(error: any): Observable<never> {
    console.error('API error:', error);
    return throwError(() => new Error(`API error: ${error.message}`));
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this.cache.clear();
    this.itemCache.clear();
    this.allDataCache = null;
  }
}
