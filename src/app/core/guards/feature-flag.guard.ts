import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { FeatureFlagService } from '../services/feature-flag.service';

@Injectable({
  providedIn: 'root'
})
export class FeatureFlagGuard implements CanActivate {

  constructor(
    private featureFlagService: FeatureFlagService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    const requestedRoute = state.url;

    // Vérifier si la route est activée
    if (this.featureFlagService.isRouteEnabled(requestedRoute)) {
      return true;
    }

    // Si la route n'est pas activée, rediriger vers le dashboard avec un message
    console.warn(`Route bloquée par feature flag: ${requestedRoute}`);

    // Optionnel: Ajouter un message d'information
    // Vous pouvez utiliser un service de notification ici

    // Rediriger vers le dashboard
    this.router.navigate(['/'], {
      queryParams: {
        message: 'feature-disabled',
        feature: this.extractFeatureFromRoute(requestedRoute)
      }
    });

    return false;
  }

  /**
   * Extrait le nom de la feature à partir de la route
   */
  private extractFeatureFromRoute(route: string): string {
    // Routes spécifiques du module activités
    if (route.startsWith('/activites/activite/') || route.includes('/activite/')) return 'activites-single';
    if (route.startsWith('/activites/activites-multi-details')) return 'activites-multi-details';
    if (route.startsWith('/activites/metadata-test')) return 'activites-metadata';
    if (route.startsWith('/activites/activites-list') || route === '/activites' || route === '/activites/') return 'activites-list';

    // Routes spécifiques pour le sprint actuel
    if (route.startsWith('/referent')) return 'import';

    // Routes des prochains sprints
    if (route.startsWith('/praticien')) return 'praticiens';
    if (route.startsWith('/graphic/praticien') || route.startsWith('/graphic')) return 'praticiens';
    if (route.startsWith('/structure')) return 'structure';
    if (route.startsWith('/sigaps')) return 'sigaps';
    if (route.startsWith('/garde-et-astreinte')) return 'garde-astreinte';
    if (route.startsWith('/liberale')) return 'liberale';
    if (route.startsWith('/acte')) return 'actes';
    if (route.startsWith('/agent-details')) return 'agent-details';
    if (route.startsWith('/particule')) return 'particule';

    return 'unknown';
  }
}
