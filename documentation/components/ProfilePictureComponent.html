<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  ProfilePictureComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/graphical/components/profile-picture/profile-picture.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
                <code>OnChanges</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-profile-picture</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>NgClass</code>
                            <code>NgStyle</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./profile-picture.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./profile-picture.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#dynamicBackground" >dynamicBackground</a>
                            </li>
                            <li>
                                <a href="#initials" >initials</a>
                            </li>
                            <li>
                                <a href="#statusTooltip" >statusTooltip</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#generateBackground" >generateBackground</a>
                            </li>
                            <li>
                                <a href="#getBadgeColor" >getBadgeColor</a>
                            </li>
                            <li>
                                <a href="#getInitials" >getInitials</a>
                            </li>
                            <li>
                                <a href="#ngOnChanges" >ngOnChanges</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Inputs</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#nom" >nom</a>
                            </li>
                            <li>
                                <a href="#prenom" >prenom</a>
                            </li>
                            <li>
                                <a href="#status" >status</a>
                            </li>
                        </ul>
                    </td>
                </tr>




                    <tr>
                        <td class="col-md-4">
                            <h6><b>Accessors</b></h6>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <ul class="index-list">
                                <li>
                                    <a href="#statusColor" >statusColor</a>
                                </li>
                            </ul>
                        </td>
                    </tr>
        </tbody>
    </table>
</section>


    <section data-compodoc="block-inputs">
    <h3 id="inputs">Inputs</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="nom"></a>
                        <b>nom</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="15" class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:15</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="prenom"></a>
                        <b>prenom</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="16" class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:16</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="status"></a>
                        <b>status</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>&quot;online&quot; | &quot;away&quot; | &quot;offline&quot;</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;offline&#x27;</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="17" class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:17</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>



    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="generateBackground"></a>
                    <span class="name">
                        <span ><b>generateBackground</b></span>
                        <a href="#generateBackground"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>generateBackground()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="44"
                                    class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:44</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getBadgeColor"></a>
                    <span class="name">
                        <span ><b>getBadgeColor</b></span>
                        <a href="#getBadgeColor"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getBadgeColor()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="73"
                                    class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:73</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getInitials"></a>
                    <span class="name">
                        <span ><b>getInitials</b></span>
                        <a href="#getInitials"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getInitials()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="38"
                                    class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:38</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnChanges"></a>
                    <span class="name">
                        <span ><b>ngOnChanges</b></span>
                        <a href="#ngOnChanges"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnChanges()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="33"
                                    class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:33</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="22"
                                    class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:22</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="dynamicBackground"></a>
                    <span class="name">
                        <span ><b>dynamicBackground</b></span>
                        <a href="#dynamicBackground"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="20" class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:20</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initials"></a>
                    <span class="name">
                        <span ><b>initials</b></span>
                        <a href="#initials"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:18</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="statusTooltip"></a>
                    <span class="name">
                        <span ><b>statusTooltip</b></span>
                        <a href="#statusTooltip"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="19" class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:19</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

    <section data-compodoc="block-accessors">
    <h3 id="accessors">
        Accessors
    </h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="statusColor"></a>
                        <span class="name"><b>statusColor</b><a href="#statusColor"><span class="icon ion-ios-link"></span></a></span>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <span class="accessor"><b>get</b><code>statusColor()</code></span>
                    </td>
                </tr>
                            <tr>
                                <td class="col-md-4">
                                    <div class="io-line">Defined in <a href="" data-line="61" class="link-to-prism">src/app/graphical/components/profile-picture/profile-picture.component.ts:61</a></div>
                                </td>
                            </tr>

            </tbody>
        </table>
</section>
</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, Input, OnChanges, OnInit} from &#x27;@angular/core&#x27;;
import {NgClass, NgStyle} from &quot;@angular/common&quot;;

@Component({
  selector: &#x27;app-profile-picture&#x27;,
  standalone: true,
  imports: [
    NgClass,
    NgStyle
  ],
  templateUrl: &#x27;./profile-picture.component.html&#x27;,
  styleUrl: &#x27;./profile-picture.component.scss&#x27;
})
export class ProfilePictureComponent implements OnInit, OnChanges{
  @Input() nom: string &#x3D; &#x27;&#x27;;
  @Input() prenom: string &#x3D; &#x27;&#x27;;
  @Input() status: &#x27;online&#x27; | &#x27;away&#x27; | &#x27;offline&#x27; &#x3D; &#x27;offline&#x27;;
  initials: string &#x3D; &#x27;&#x27;;
  statusTooltip: string &#x3D; &#x27;&#x27;;
  dynamicBackground: string &#x3D; &#x27;&#x27;;

  ngOnInit(): void {
    if (this.statusColor &#x3D;&#x3D;&#x3D; &#x27;bg-red-500&#x27;) {
      this.statusTooltip &#x3D; &#x27;Praticien parti du CHRU&#x27;;
    } else if (this.statusColor &#x3D;&#x3D;&#x3D; &#x27;bg-green-500&#x27;) {
      this.statusTooltip &#x3D; &#x27;Praticien encore au CHRU&#x27;;
    } else if (this.statusColor &#x3D;&#x3D;&#x3D; &#x27;bg-yellow-500&#x27;) {
      this.statusTooltip &#x3D; &#x27;Statut indéterminé pour le moment&#x27;;
    } else {
      this.statusTooltip &#x3D; &#x27;Statut inconnu&#x27;;
    }
  }
  ngOnChanges(): void {
    this.initials &#x3D; this.getInitials();
    this.dynamicBackground &#x3D; this.generateBackground();
  }

  getInitials(): string {
    const nomInitial &#x3D; this.nom.charAt(0).toUpperCase();
    const prenomInitial &#x3D; this.prenom.charAt(0).toUpperCase();
    return &#x60;${prenomInitial}${nomInitial}&#x60;;
  }

  generateBackground(): string {
    const colors &#x3D; [
      &#x27;#FF6F61&#x27;, // Coral
      &#x27;#6B5B95&#x27;, // Purple
      &#x27;#88B04B&#x27;, // Green
      &#x27;#F7CAC9&#x27;, // Pink
      &#x27;#92A8D1&#x27;, // Light Blue
      &#x27;#955251&#x27;, // Rose Brown
      &#x27;#B565A7&#x27;, // Purple Orchid
    ];

    const color1 &#x3D; colors[this.nom.charCodeAt(0) % colors.length];
    const color2 &#x3D; colors[this.prenom.charCodeAt(0) % colors.length];

    return &#x60;linear-gradient(135deg, ${color1}, ${color2})&#x60;;
  }

  get statusColor(): string {
    switch (this.status) {
      case &#x27;online&#x27;:
        return &#x27;bg-green-500&#x27;;
      case &#x27;away&#x27;:
        return &#x27;bg-yellow-500&#x27;;
      case &#x27;offline&#x27;:
      default:
        return &#x27;bg-red-500&#x27;;
    }
  }
  // Retourner la couleur du badge en fonction du statut
  getBadgeColor(): string {
    switch (this.statusColor) {
      case &#x27;bg-green-500&#x27;:
        return &#x27;#10B981&#x27;; // Vert
      case &#x27;bg-red-500&#x27;:
        return &#x27;#EF4444&#x27;; // Rouge
      case &#x27;bg-yellow-500&#x27;:
        return &#x27;#F59E0B&#x27;; // Jaune
      default:
        return &#x27;#6B7280&#x27;; // Gris
    }
  }
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;!--&lt;div--&gt;
&lt;!--  class&#x3D;&quot;relative w-12 h-12 flex items-center justify-center rounded-full text-white font-bold text-lg&quot;--&gt;
&lt;!--  [ngStyle]&#x3D;&quot;{ background: dynamicBackground }&quot;--&gt;
&lt;!--&amp;gt;--&gt;
&lt;!--  &amp;lt;!&amp;ndash; Initiales &amp;ndash;&amp;gt;--&gt;
&lt;!--  &lt;span&gt;{{ initials }}&lt;/span&gt;--&gt;

&lt;!--  &amp;lt;!&amp;ndash; Statut &amp;ndash;&amp;gt;--&gt;
&lt;!--  &lt;span--&gt;
&lt;!--    class&#x3D;&quot;absolute bottom-0 right-0 w-3 h-3 border-2 border-white rounded-full&quot;--&gt;
&lt;!--    [ngClass]&#x3D;&quot;statusColor&quot;--&gt;
&lt;!--  &gt;&lt;/span&gt;--&gt;
&lt;!--&lt;/div&gt;--&gt;

&lt;div
  class&#x3D;&quot;relative w-12 h-12 flex items-center justify-center rounded-full text-white font-bold text-lg group&quot;
  [ngStyle]&#x3D;&quot;{ background: dynamicBackground }&quot;
&gt;
  &lt;!-- Initiales --&gt;
  &lt;span&gt;{{ initials }}&lt;/span&gt;

  &lt;!-- Statut --&gt;
  &lt;span
    class&#x3D;&quot;absolute bottom-0 right-0 w-3 h-3 border-2 border-white rounded-full&quot;
    [ngClass]&#x3D;&quot;statusColor&quot;
  &gt;&lt;/span&gt;

  &lt;!-- Tooltip avec Badge --&gt;
  &lt;div
    class&#x3D;&quot;absolute bottom-14 left-1/2 transform -translate-x-1/2 px-2 py-1 text-xs text-indigo-950 bg-gray-100 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 flex items-center space-x-2&quot;
  &gt;
    &lt;i
      [class.pi]&#x3D;&quot;true&quot;
      [class.pi-check-circle]&#x3D;&quot;statusColor &#x3D;&#x3D;&#x3D; &#x27;bg-green-500&#x27;&quot;
      [class.pi-times-circle]&#x3D;&quot;statusColor &#x3D;&#x3D;&#x3D; &#x27;bg-red-500&#x27;&quot;
      [class.pi-info-circle]&#x3D;&quot;statusColor &#x3D;&#x3D;&#x3D; &#x27;bg-yellow-500&#x27;&quot;
      [class.pi-question-circle]&#x3D;&quot;statusColor !&#x3D;&#x3D; &#x27;bg-green-500&#x27; &amp;&amp; statusColor !&#x3D;&#x3D; &#x27;bg-red-500&#x27; &amp;&amp; statusColor !&#x3D;&#x3D; &#x27;bg-yellow-500&#x27;&quot;
      class&#x3D;&quot;p-text-secondary&quot;
      [ngStyle]&#x3D;&quot;{ fontSize: &#x27;1rem&#x27;, color: getBadgeColor() }&quot;
    &gt;&lt;/i&gt;
    &lt;span [innerHTML]&#x3D;&quot;statusTooltip&quot;&gt;&lt;/span&gt;
  &lt;/div&gt;
&lt;/div&gt;
</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><!--<div--><!--  class="relative w-12 h-12 flex items-center justify-center rounded-full text-white font-bold text-lg"--><!--  [ngStyle]="{ background: dynamicBackground }"--><!--&gt;--><!--  &lt;!&ndash; Initiales &ndash;&gt;--><!--  <span>{{ initials }}</span>--><!--  &lt;!&ndash; Statut &ndash;&gt;--><!--  <span--><!--    class="absolute bottom-0 right-0 w-3 h-3 border-2 border-white rounded-full"--><!--    [ngClass]="statusColor"--><!--  ></span>--><!--</div>--><div  class="relative w-12 h-12 flex items-center justify-center rounded-full text-white font-bold text-lg group"  [ngStyle]="{ background: dynamicBackground }">  <!-- Initiales -->  <span>{{ initials }}</span>  <!-- Statut -->  <span    class="absolute bottom-0 right-0 w-3 h-3 border-2 border-white rounded-full"    [ngClass]="statusColor"  ></span>  <!-- Tooltip avec Badge -->  <div    class="absolute bottom-14 left-1/2 transform -translate-x-1/2 px-2 py-1 text-xs text-indigo-950 bg-gray-100 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 flex items-center space-x-2"  >    <i      [class.pi]="true"      [class.pi-check-circle]="statusColor === \'bg-green-500\'"      [class.pi-times-circle]="statusColor === \'bg-red-500\'"      [class.pi-info-circle]="statusColor === \'bg-yellow-500\'"      [class.pi-question-circle]="statusColor !== \'bg-green-500\' && statusColor !== \'bg-red-500\' && statusColor !== \'bg-yellow-500\'"      class="p-text-secondary"      [ngStyle]="{ fontSize: \'1rem\', color: getBadgeColor() }"    ></i>    <span [innerHTML]="statusTooltip"></span>  </div></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'ProfilePictureComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'ProfilePictureComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
