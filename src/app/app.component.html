<div *ngIf="isInMaintenance">
  <app-maintenance></app-maintenance>
</div>
<div *ngIf="!isInMaintenance">
  <div class="flex min-h-screen">
    <!-- Sidebar DESKTOP avec largeur dynamique -->
    <app-sidebar
      class="hidden lg:block h-screen fixed left-0 top-0 z-40 transition-all duration-300 ease-in-out"
      [ngClass]="{
        'w-64': !isSidebarCollapsed,
        'w-16': isSidebarCollapsed
      }">
    </app-sidebar>

    <!-- Sidebar MOBILE (composant séparé) -->
    <app-sidebar-mobile></app-sidebar-mobile>

    <!-- Main Content avec margin dynamique -->
    <div
      class="flex flex-col flex-1 min-h-0 transition-all duration-300 ease-in-out"
      [ngClass]="{
        'lg:ml-64': !isSidebarCollapsed,
        'lg:ml-16': isSidebarCollapsed
      }">
      <app-header></app-header>
      <main class="flex-1">
        <router-outlet></router-outlet>
      </main>
      <app-footer class="mt-auto"></app-footer>
    </div>
  </div>
</div>
