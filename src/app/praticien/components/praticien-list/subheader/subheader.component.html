<!-- Subheader with filters -->
<div class="bg-white border-b border-gray-200 shadow-sm">
  <div class="px-4 py-3">
    <!-- Main filters row -->
    <div class="flex flex-wrap items-center gap-4 mb-3">
      <!-- Nom filter -->
      <div class="flex-1 min-w-[200px]">
        <label class="block text-xs font-medium text-gray-700 mb-1">Nom</label>
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="searchFilters.nom"
            (keydown)="onEnterKeyPress($event)"
            placeholder="Nom (appuyez sur Entrée)..."
            class="w-full px-3 py-2 pr-20 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
          />
          <button
            type="button"
            (click)="triggerImmediateSearch()"
            class="search-button absolute right-1 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors flex items-center gap-1"
            title="Appuyez sur Entrée ou cliquez pour rechercher"
          >
            <i class="pi pi-search text-xs"></i>
            <span class="hidden sm:inline">Entrée</span>
          </button>
        </div>
      </div>

      <!-- Prénom filter -->
      <div class="flex-1 min-w-[200px]">
        <label class="block text-xs font-medium text-gray-700 mb-1">Prénom</label>
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="searchFilters.prenom"
            (keydown)="onEnterKeyPress($event)"
            placeholder="Prénom (appuyez sur Entrée)..."
            class="w-full px-3 py-2 pr-20 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
          />
          <button
            type="button"
            (click)="triggerImmediateSearch()"
            class="absolute right-1 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors flex items-center gap-1"
            title="Appuyez sur Entrée ou cliquez pour rechercher"
          >
            <i class="pi pi-search text-xs"></i>
            <span class="hidden sm:inline">Entrée</span>
          </button>
        </div>
      </div>

      <!-- Catégorie filter -->
      <div class="flex-1 min-w-[180px]">
        <label class="block text-xs font-medium text-gray-700 mb-1">Catégorie</label>
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="searchFilters.categorie"
            (keydown)="onEnterKeyPress($event)"
            placeholder="M413 (Entrée)..."
            class="w-full px-3 py-2 pr-20 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
          />
          <button
            type="button"
            (click)="triggerImmediateSearch()"
            class="absolute right-1 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors flex items-center gap-1"
            title="Appuyez sur Entrée ou cliquez pour rechercher"
          >
            <i class="pi pi-search text-xs"></i>
            <span class="hidden sm:inline">Entrée</span>
          </button>
        </div>
      </div>

      <!-- HR User filter -->
      <div class="flex-1 min-w-[150px]">
        <label class="block text-xs font-medium text-gray-700 mb-1">Identifiant RH</label>
        <div class="relative">
          <input
            type="text"
            [(ngModel)]="searchFilters.hrUser"
            (keydown)="onEnterKeyPress($event)"
            placeholder="U074000 (Entrée)..."
            class="w-full px-3 py-2 pr-20 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
          />
          <button
            type="button"
            (click)="triggerImmediateSearch()"
            class="absolute right-1 top-1/2 transform -translate-y-1/2 px-3 py-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors flex items-center gap-1"
            title="Appuyez sur Entrée ou cliquez pour rechercher"
          >
            <i class="pi pi-search text-xs"></i>
            <span class="hidden sm:inline">Entrée</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Advanced filters toggle and actions -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <!-- Advanced filters toggle -->
        <button
          type="button"
          (click)="toggleAdvancedFilters()"
          class="flex items-center gap-2 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
        >
          <i class="pi" [ngClass]="showAdvancedFilters ? 'pi-chevron-up' : 'pi-chevron-down'"></i>
          Filtres avancés
        </button>

        <!-- Active filters indicator -->
        <span
          *ngIf="hasActiveFilters()"
          class="px-2 py-1 bg-cyan-100 text-cyan-800 rounded-full text-xs font-medium"
        >
          {{ getActiveFiltersCount() }} filtre(s) actif(s)
        </span>
      </div>

      <!-- Clear filters button -->
      <button
        *ngIf="hasActiveFilters()"
        type="button"
        (click)="clearAllFilters()"
        class="flex items-center gap-2 px-3 py-1 text-sm text-red-600 hover:text-red-800 border border-red-300 rounded-md hover:bg-red-50 transition-colors"
      >
        <i class="pi pi-times"></i>
        Effacer tous les filtres
      </button>
    </div>

    <!-- Advanced filters section -->
    <div *ngIf="showAdvancedFilters" class="mt-4 pt-4 border-t border-gray-200">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">


        <!-- Email filter -->
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">Email</label>
          <div class="relative">
            <input
              type="email"
              [(ngModel)]="searchFilters.email"
              (keydown)="onEnterKeyPress($event)"
              placeholder="Email..."
              class="w-full px-3 py-2 pr-16 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
            />
            <button
              type="button"
              (click)="triggerImmediateSearch()"
              class="absolute right-1 top-1/2 transform -translate-y-1/2 px-2 py-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors"
              title="Appuyez sur Entrée ou cliquez pour rechercher"
            >
              <i class="pi pi-search text-xs"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- AgentUfs related filters -->
      <div class="border-t border-gray-100 pt-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <i class="pi pi-link text-cyan-600 mr-2"></i>
          Filtres d'affectation Agent ↔ UF
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <!-- RGT filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">RGT</label>
            <div class="relative">
              <input
                type="text"
                [(ngModel)]="searchFilters['agentUfs.rgt']"
                (keydown)="onEnterKeyPress($event)"
                placeholder="INTERNES..."
                class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
              />
              <button
                type="button"
                (click)="triggerImmediateSearch()"
                class="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors"
                title="Entrée"
              >
                <i class="pi pi-search text-xs"></i>
              </button>
            </div>
          </div>

          <!-- Type Grade filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Type de Grade</label>
            <div class="relative">
              <input
                type="text"
                [(ngModel)]="searchFilters['agentUfs.typeGrade']"
                (keydown)="onEnterKeyPress($event)"
                placeholder="M/A..."
                class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
              />
              <button
                type="button"
                (click)="triggerImmediateSearch()"
                class="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors"
                title="Entrée"
              >
                <i class="pi pi-search text-xs"></i>
              </button>
            </div>
          </div>

          <!-- Libellé Grade filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Libellé Grade</label>
            <div class="relative">
              <input
                type="text"
                [(ngModel)]="searchFilters['agentUfs.libelleGrade']"
                (keydown)="onEnterKeyPress($event)"
                placeholder="Etudiant en odontologie..."
                class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
              />
              <button
                type="button"
                (click)="triggerImmediateSearch()"
                class="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors"
                title="Entrée"
              >
                <i class="pi pi-search text-xs"></i>
              </button>
            </div>
          </div>

          <!-- ETP filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">ETP</label>
            <div class="relative">
              <input
                type="number"
                step="0.1"
                [(ngModel)]="searchFilters['agentUfs.etp']"
                (keydown)="onEnterKeyPress($event)"
                placeholder="ETP..."
                class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
              />
              <button
                type="button"
                (click)="triggerImmediateSearch()"
                class="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors"
                title="Entrée"
              >
                <i class="pi pi-search text-xs"></i>
              </button>
            </div>
          </div>

          <!-- UF Code filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Code UF</label>
            <div class="relative">
              <input
                type="text"
                [(ngModel)]="searchFilters['agentUfs.ufs.ufcode']"
                (keydown)="onEnterKeyPress($event)"
                placeholder="Code UF..."
                class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
              />
              <button
                type="button"
                (click)="triggerImmediateSearch()"
                class="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors"
                title="Entrée"
              >
                <i class="pi pi-search text-xs"></i>
              </button>
            </div>
          </div>

          <!-- CR Code filter -->
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Code CR</label>
            <div class="relative">
              <input
                type="text"
                [(ngModel)]="searchFilters['agentUfs.ufs.cr.crcode']"
                (keydown)="onEnterKeyPress($event)"
                placeholder="Code CR..."
                class="w-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:ring-2 focus:ring-cyan-500 focus:border-transparent text-sm"
              />
              <button
                type="button"
                (click)="triggerImmediateSearch()"
                class="absolute right-1 top-1/2 transform -translate-y-1/2 p-1 bg-cyan-600 text-white text-xs rounded hover:bg-cyan-700 transition-colors"
                title="Entrée"
              >
                <i class="pi pi-search text-xs"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
