/* Supprimer la scrollbar sur Webkit (Chrome, Edge, Safari) */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Supprimer la scrollbar sur Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE et Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Assurer que le sélecteur de période reste fixe */
.period-selector {
  position: sticky;
  bottom: 0;
  background-color: #0e7490; /* bg-cyan-700 */
  z-index: 10;
  border-top: 1px solid #0891b2; /* border-cyan-600 */
}

/* Style pour le panel du calendrier rendu dans le body */
:host ::ng-deep .my-datepicker-panel {
  min-width: 460px !important;    /* largeur mini (pour le confort même sur sidebar étroite) */
  width: 520px !important;        /* largeur principale desktop */
  max-width: 98vw !important;     /* jamais hors de l'écran */
  z-index: 1090 !important;       /* toujours devant */
  left: 0 !important;             /* positionne bien par rapport à l'input (important si appendTo=body) */
  border-radius: 10px !important;
  box-shadow: 0 8px 24px 0 rgba(0,0,0,0.08);
}

@media (max-width: 800px) {
  :host ::ng-deep .my-datepicker-panel {
    min-width: 320px !important;
    width: 95vw !important;
    max-width: 99vw !important;
  }
}

/* Styles améliorés pour les calendriers */
:host ::ng-deep .p-datepicker {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; /* Ombre subtile */
  border-radius: 8px !important; /* Coins arrondis */
  font-size: 14px !important; /* Taille de police plus lisible */
}

/* Amélioration des cellules du calendrier */
:host ::ng-deep .p-datepicker table td {
  padding: 8px !important; /* Plus d'espace dans les cellules */
}

/* Augmenter la taille des cellules pour une meilleure lisibilité */
:host ::ng-deep .p-datepicker table td > span {
  width: 40px !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 16px !important;
}

/* Style pour les boutons du calendrier */
:host ::ng-deep .p-datepicker .p-datepicker-header {
  padding: 16px !important; /* Plus d'espace dans l'en-tête */
  font-size: 18px !important; /* Texte plus grand */
}

:host ::ng-deep .p-datepicker .p-datepicker-header button {
  background-color: transparent !important;
  border-radius: 4px !important;
  transition: background-color 0.2s !important;
  width: 36px !important; /* Boutons plus grands */
  height: 36px !important;
}

:host ::ng-deep .p-datepicker .p-datepicker-header button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

:host ::ng-deep .p-datepicker .p-datepicker-title {
  font-size: 18px !important; /* Titre plus grand */
  font-weight: bold !important;
}

/* Style pour les inputs du calendrier */
:host ::ng-deep .p-calendar .p-inputtext {
  padding: 8px 12px !important; /* Plus d'espace dans l'input */
  border-radius: 6px !important; /* Coins plus arrondis */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; /* Ombre subtile */
  transition: all 0.2s ease !important; /* Animation douce */
}

:host ::ng-deep .p-calendar .p-inputtext:hover {
  background-color: #0c637a !important; /* Couleur légèrement plus claire au survol */
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important; /* Ombre plus prononcée au survol */
}

/* Amélioration de l'espacement entre les éléments */
.period-selector .flex.items-center {
  gap: 8px !important; /* Plus d'espace entre les calendriers */
}

/* Style pour le mois sélectionné */
:host ::ng-deep .p-datepicker table td.p-datepicker-today > span {
  background-color: rgba(255, 255, 255, 0.2) !important; /* Fond subtil pour aujourd'hui */
  border-radius: 4px !important;
}

:host ::ng-deep .p-datepicker table td > span.p-highlight {
  background-color: #0891b2 !important; /* Couleur cyan pour la sélection */
  color: white !important;
  border-radius: 4px !important;
}
