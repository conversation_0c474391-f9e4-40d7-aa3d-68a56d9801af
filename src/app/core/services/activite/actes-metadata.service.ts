import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map, shareReplay, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { ActesMetadataModel } from '../../models/activite/ActesMetadataModel';
import { DatePeriod } from '../global-filter/global-filter.service';

/**
 * Service pour récupérer les métadonnées des actes médicaux
 *
 * Utilise l'endpoint GET /api/actes/metadata avec support des périodes P1, P2, P3
 * pour obtenir rapidement des statistiques sur le dataset des actes.
 */
@Injectable({
  providedIn: 'root'
})
export class ActesMetadataService {
  private readonly API_URL = `${environment.api_url}/api/actes/metadata`;
  private cache = new Map<string, Observable<ActesMetadataModel>>();
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutes (même que le cache backend)

  constructor(private http: HttpClient) {}

  /**
   * Récupère les métadonnées des actes avec filtrage par périodes
   *
   * @param p1 Période P1 (actuelle)
   * @param p2 Période P2 (année précédente)
   * @param p3 Période P3 (il y a deux ans)
   * @returns Observable<ActesMetadataModel>
   */
  getMetadata(
    p1: DatePeriod | null = null,
    p2: DatePeriod | null = null,
    p3: DatePeriod | null = null
  ): Observable<ActesMetadataModel> {

    // Vérifier si le cache est activé
    if (environment.enableFrontendCache) {
      // Construire la clé de cache basée sur les périodes
      const cacheKey = this.buildCacheKey(p1, p2, p3);

      // Vérifier le cache
      if (this.cache.has(cacheKey)) {
        console.log('📦 Cache HIT pour métadonnées:', cacheKey);
        return this.cache.get(cacheKey)!;
      }
    }

    // Construire les paramètres de requête
    const params = this.buildHttpParams(p1, p2, p3);

    console.log('🌐 Requête API métadonnées (cache:', environment.enableFrontendCache ? 'activé' : 'désactivé', ')');

    // Effectuer la requête HTTP
    let request$ = this.http.get<ActesMetadataModel>(this.API_URL, { params })
      .pipe(
        map(response => this.processResponse(response)),
        catchError(error => this.handleError(error))
      );

    // Ajouter shareReplay seulement si le cache est activé
    if (environment.enableFrontendCache) {
      request$ = request$.pipe(shareReplay(1));
    }

    // Mettre en cache seulement si activé
    if (environment.enableFrontendCache) {
      const cacheKey = this.buildCacheKey(p1, p2, p3);
      this.cache.set(cacheKey, request$);
      console.log('📦 Cache SET pour métadonnées:', cacheKey);

      // Nettoyer le cache après le TTL
      setTimeout(() => {
        this.cache.delete(cacheKey);
        console.log('🗑️ Cache expiré pour métadonnées:', cacheKey);
      }, this.CACHE_TTL);
    }

    return request$;
  }

  /**
   * Récupère les métadonnées sans filtrage temporel
   * Utile pour obtenir des statistiques globales
   */
  getGlobalMetadata(): Observable<ActesMetadataModel> {
    return this.getMetadata(null, null, null);
  }

  /**
   * Vide le cache des métadonnées
   * Utile après des modifications de données
   */
  clearCache(): void {
    if (environment.enableFrontendCache) {
      this.cache.clear();
      console.log('🗑️ Cache des métadonnées vidé manuellement');
    } else {
      console.log('ℹ️ Cache désactivé - aucune action nécessaire');
    }
  }

  /**
   * Construit les paramètres HTTP pour les périodes
   */
  private buildHttpParams(
    p1: DatePeriod | null,
    p2: DatePeriod | null,
    p3: DatePeriod | null
  ): HttpParams {
    let params = new HttpParams();

    // Ajouter les paramètres P1
    if (p1?.dateDebut && p1?.dateFin) {
      params = params.set('p1Start', this.formatDate(p1.dateDebut));
      params = params.set('p1End', this.formatDate(p1.dateFin));
    }

    // Ajouter les paramètres P2
    if (p2?.dateDebut && p2?.dateFin) {
      params = params.set('p2Start', this.formatDate(p2.dateDebut));
      params = params.set('p2End', this.formatDate(p2.dateFin));
    }

    // Ajouter les paramètres P3
    if (p3?.dateDebut && p3?.dateFin) {
      params = params.set('p3Start', this.formatDate(p3.dateDebut));
      params = params.set('p3End', this.formatDate(p3.dateFin));
    }

    return params;
  }

  /**
   * Construit une clé de cache unique basée sur les périodes
   */
  private buildCacheKey(
    p1: DatePeriod | null,
    p2: DatePeriod | null,
    p3: DatePeriod | null
  ): string {
    const p1Key = p1 && p1.dateDebut && p1.dateFin ?
      `${this.formatDate(p1.dateDebut)}_${this.formatDate(p1.dateFin)}` : 'null';
    const p2Key = p2 && p2.dateDebut && p2.dateFin ?
      `${this.formatDate(p2.dateDebut)}_${this.formatDate(p2.dateFin)}` : 'null';
    const p3Key = p3 && p3.dateDebut && p3.dateFin ?
      `${this.formatDate(p3.dateDebut)}_${this.formatDate(p3.dateFin)}` : 'null';

    return `metadata_${p1Key}_${p2Key}_${p3Key}`;
  }

  /**
   * Formate une date au format YYYY-MM-DD
   */
  private formatDate(date: Date | null): string {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  }

  /**
   * Traite la réponse de l'API
   */
  private processResponse(response: ActesMetadataModel): ActesMetadataModel {
    // Ajouter des logs pour le debugging
    console.log('📊 Métadonnées des actes reçues:', {
      totalItems: response.totalItems,
      totalByType: response.totalByType,
      dateRange: `${response.dateRangeMin} → ${response.dateRangeMax}`,
      generationTime: `${response.generationTimeMs}ms`,
      appliedPeriods: response.appliedPeriods
    });

    return response;
  }

  /**
   * Gère les erreurs de l'API
   */
  private handleError(error: any): Observable<ActesMetadataModel> {
    console.error('❌ Erreur lors de la récupération des métadonnées:', error);

    // Retourner des métadonnées par défaut en cas d'erreur
    const fallbackMetadata: ActesMetadataModel = {
      totalItems: 0,
      totalByType: { CCAM: 0, NGAP: 0, LABO: 0 },
      dateRangeMin: null,
      dateRangeMax: null,
      lastUpdate: null,
      totalAgents: 0,
      totalUfs: 0,
      appliedPeriods: {},
      generationTimeMs: 0
    };

    return of(fallbackMetadata);
  }

  /**
   * Utilitaire pour extraire les statistiques par type
   */
  getTypeStats(metadata: ActesMetadataModel): { type: string; count: number; percentage: number }[] {
    const total = metadata.totalItems;
    if (total === 0) return [];

    return Object.entries(metadata.totalByType).map(([type, count]) => ({
      type,
      count,
      percentage: Math.round((count / total) * 100)
    }));
  }

  /**
   * Utilitaire pour vérifier si des données sont disponibles
   */
  hasData(metadata: ActesMetadataModel): boolean {
    return metadata.totalItems > 0;
  }
}
