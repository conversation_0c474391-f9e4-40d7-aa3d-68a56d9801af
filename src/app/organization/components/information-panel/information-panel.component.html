

<h2 class="text-xl font-bold text-cyan-700 mb-4">Informations sur le {{ elementType | titlecase }}</h2>

<!-- Début Overview -->
<div class="flex space-x-4 p-4 bg-gray-800 rounded-lg">
  <!-- CCAM Card -->
  <p-card class="bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative">
    <div class="absolute top-2 right-2 flex items-center space-x-1">
      <p-button (onClick)="ccamOverlay.toggle($event)" icon="pi pi-eye"
                class="p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg">
      </p-button>
      <p-overlayPanel #ccamOverlay class="custom-overlay">
        <div class="p-4">
          <h5 class="text-lg font-bold mb-3">
             Analyse Comparative des {{totalCCAM}} Actes CCAM ({{startYear}}-{{ endYear }})
          </h5>
          <div>
            <div class="relative mt-2 rounded-md shadow-sm">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
              </div>
              <input
                type="text" name="price"
                (input)="ccamTableParActeParAnnee.filterGlobal(onInput($event), 'contains')"
                class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
                placeholder="  Filtrer par ccam"
              />
            </div>
          </div>
          <p-table
            #ccamTableParActeParAnnee
            [value]="filteredActesCCAMSummary"
            [scrollable]="true"
            scrollHeight="400px"
            [tableStyle]="{'min-width': '50rem'}"
            [globalFilterFields]="['code', 'description','totalAnneeNMoins1','totalAnneeN']"
          >
            <ng-template pTemplate="header">
              <tr class="border-b hover:bg-gray-50">
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
                  <i
                    class="pi pi-hashtag cursor-pointer ml-2"
                    title="Voir les détails de l'acte"
                  ></i>
                </th>
                <th pSortableColumn="code">
                  Code
                  <p-sortIcon field="code"></p-sortIcon>
                </th>
                <th pSortableColumn="description">
                  Description
                  <p-sortIcon field="description"></p-sortIcon>
                </th>
                <th  pSortableColumn="totalAnneeNMoins1" >
                  Total actes {{ startYear }}
                  <p-sortIcon field="totalAnneeNMoins1"></p-sortIcon>
                </th>
                <th  pSortableColumn="totalAnneeN" >
                  Total actes {{ endYear }}
                  <p-sortIcon field="totalAnneeN"></p-sortIcon>
                </th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-acte>
              <tr class="border-b hover:bg-gray-50">
                <td style="padding: 0.75rem; text-align: left;">
                  <p class="text-sm font-bold text-gray-600 flex items-center">
                    <i
                      class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                      title="Voir les détails de {{acte.code }} {{acte.description}}"
                      (click)="viewActeDetails(acte)"
                    ></i>
                  </p>
                </td>
                <td>{{acte.code}}</td>
                <td>{{acte.description}}</td>
                <td> {{acte.totalAnneeNMoins1}}  </td>
                <td> {{acte.totalAnneeN}}  </td>
              </tr>
            </ng-template>
            <!-- Ligne de total -->
            <ng-template pTemplate="footer">
              <tr  >
                <td colspan="3" class="font-bold text-left">TOTAL</td>
                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(startYear,'CCAM')" severity="info" styleClass="ml-2"></p-badge></td>
                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(endYear,'CCAM')" severity="info" styleClass="ml-3"></p-badge></td>
                <td></td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </p-overlayPanel>
    </div>
    <div class="flex items-center justify-between p-4">
      <div class="w-1/2">
        <p class="text-sm uppercase font-semibold text-gray-400">Total CCAM</p>
        <div class="flex items-center space-x-2 mt-2">
          <p-badge [value]="'↘ 0.6%'" severity="danger" class="mr-2"></p-badge>
          <span class="text-2xl font-bold">{{ totalCCAM }}</span>
        </div>
      </div>
      <div class="w-1/2 flex justify-end">
        <p-chart type="line" [data]="ccamData" [options]="chartOptions" class="w-full h-16"></p-chart>
      </div>
    </div>
  </p-card>

  <!-- NGAP Card -->
  <p-card class="bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative">
    <div class="absolute top-2 right-2 flex items-center space-x-1">
      <p-button (onClick)="ngapOverlay.toggle($event)" icon="pi pi-eye"
                class="p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg">
      </p-button>
      <p-overlayPanel #ngapOverlay class="custom-overlay">
        <div class="p-4">
          <h5 class="text-lg font-bold mb-3">
            Analyse Comparative des {{totalNGAP}} Actes NGAP ({{startYear}}-{{ endYear }})
          </h5>
          <div>
            <div class="relative mt-2 rounded-md shadow-sm">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
              </div>
              <input
                type="text" name="price"
                (input)="ngapTableParActeParAnnee.filterGlobal(onInput($event), 'contains')"
                class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
                placeholder="  Filtrer par ccam"
              />
            </div>
          </div>
          <p-table
            #ngapTableParActeParAnnee
            [value]="filteredActesNGAPSummary"
            [scrollable]="true"
            scrollHeight="400px"
            [tableStyle]="{'min-width': '50rem'}"
            [globalFilterFields]="['code', 'description','totalAnneeNMoins1','totalAnneeN']"
          >
            <ng-template pTemplate="header">
              <tr  class="border-b hover:bg-gray-50">
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
                  <i
                    class="pi pi-hashtag cursor-pointer ml-2"
                    title="Voir les détails de l'acte"
                  ></i>
                </th>
                <th pSortableColumn="code">
                  Code
                  <p-sortIcon field="code"></p-sortIcon>
                </th>
                <th pSortableColumn="description">
                  Description
                  <p-sortIcon field="description"></p-sortIcon>
                </th>
                <th  pSortableColumn="totalAnneeNMoins1" >
                  Total actes {{ startYear }}
                  <p-sortIcon field="totalAnneeNMoins1"></p-sortIcon>
                </th>
                <th  pSortableColumn="totalAnneeN" >
                  Total actes {{ endYear }}
                  <p-sortIcon field="totalAnneeN"></p-sortIcon>
                </th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-acte>
              <tr  class="border-b hover:bg-gray-50">
                <td style="padding: 0.75rem; text-align: left;">
                  <p class="text-sm font-bold text-gray-600 flex items-center">
                    <i
                      class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                      title="Voir les détails de {{acte.code }} {{acte.description}}"
                      (click)="viewActeDetails(acte)"
                    ></i>
                  </p>
                </td>
                <td>{{acte.code}}</td>
                <td>{{acte.description}}</td>
                <td> {{acte.totalAnneeNMoins1}}  </td>
                <td> {{acte.totalAnneeN}}  </td>

              </tr>
            </ng-template>
            <!-- Ligne de total -->
            <ng-template pTemplate="footer">
              <tr>
                <td colspan="3" class="font-bold text-left">TOTAL</td>
                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(startYear,'NGAP')" severity="info" styleClass="ml-2"></p-badge></td>
                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(endYear,'NGAP')" severity="info" styleClass="ml-3"></p-badge></td>
                <td></td>
               </tr>
            </ng-template>
          </p-table>
        </div>
      </p-overlayPanel>
    </div>
    <div class="flex items-center justify-between p-4">
      <div class="w-1/2">
        <p class="text-sm uppercase font-semibold text-gray-400">Total NGAP</p>
        <div class="flex items-center space-x-2 mt-2">
          <p-badge [value]="'↑ 4.2%'" severity="success" class="mr-2"></p-badge>
          <span class="text-2xl font-bold">{{ totalNGAP }}</span>
        </div>
      </div>
      <div class="w-1/2 flex justify-end">
        <p-chart type="line" [data]="ngapData" [options]="chartOptions" class="w-full h-16"></p-chart>
      </div>
    </div>
  </p-card>

  <!-- LABO Card -->
  <p-card class="bg-gray-900 text-white flex-1 rounded-lg shadow-lg relative">
    <div class="absolute top-2 right-2 flex items-center space-x-1">
      <p-button (onClick)="laboOverlay.toggle($event)" icon="pi pi-eye"
                class="p-button-rounded p-button-text p-button-secondary bg-sidebar text-white rounded-lg">
      </p-button>
      <p-overlayPanel #laboOverlay class="custom-overlay">
        <div class="p-4">
          <h5 class="text-lg font-bold mb-3">
            Analyse Comparative des {{totalLABO}} Actes CCAM ({{startYear}}-{{ endYear }})
          </h5>
          <div>
            <div class="relative mt-2 rounded-md shadow-sm">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
              </div>
              <input
                type="text" name="price"
                (input)="laboTableParActeParAnnee.filterGlobal(onInput($event), 'contains')"
                class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
                placeholder="  Filtrer par acte labo"
              />
            </div>
          </div>
          <p-table
            #laboTableParActeParAnnee
            [value]="filteredActesLABOSummary"
            [scrollable]="true"
            scrollHeight="400px"
            [tableStyle]="{'min-width': '50rem'}"
            [globalFilterFields]="['code', 'description','totalAnneeNMoins1','totalAnneeN']"
          >
            <ng-template pTemplate="header">
              <tr class="border-b hover:bg-gray-50">
                <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
                  <i
                    class="pi pi-hashtag cursor-pointer ml-2"
                    title="Voir les détails de l'acte"
                  ></i>
                </th>
                <th pSortableColumn="code">
                  Code
                  <p-sortIcon field="code"></p-sortIcon>
                </th>
                <th pSortableColumn="description">
                  Description
                  <p-sortIcon field="description"></p-sortIcon>
                </th>
                <th  pSortableColumn="totalAnneeNMoins1" >
                  Total actes {{ startYear }}
                  <p-sortIcon field="totalAnneeNMoins1"></p-sortIcon>
                </th>
                <th  pSortableColumn="totalAnneeN" >
                  Total actes {{ endYear }}
                  <p-sortIcon field="totalAnneeN"></p-sortIcon>
                </th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-acte>
              <tr class="border-b hover:bg-gray-50">
                <td style="padding: 0.75rem; text-align: left;">
                  <p class="text-sm font-bold text-gray-600 flex items-center">
                    <i
                      class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                      title="Voir les détails de {{acte.code }} {{acte.description}}"
                      (click)="viewActeDetails(acte)"
                    ></i>
                  </p>
                </td>
                <td>{{acte.code}}</td>
                <td>{{acte.description}}</td>
                <td> {{acte.totalAnneeNMoins1}}  </td>
                <td> {{acte.totalAnneeN}}  </td>
              </tr>
            </ng-template>
            <!-- Ligne de total -->
            <ng-template pTemplate="footer">
              <tr>
                <td colspan="3" class="font-bold text-right">TOTAL</td>
                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(startYear,'LABO')" severity="info" styleClass="ml-2"></p-badge></td>
                <td class="font-bold"><p-badge [value]="getTotalForYearByTypeActe(endYear,'LABO')" severity="info" styleClass="ml-3"></p-badge></td>
                <td></td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </p-overlayPanel>
    </div>
    <div class="flex items-center justify-between p-4">
      <div class="w-1/2">
        <p class="text-sm uppercase font-semibold text-gray-400">Total LABO</p>
        <div class="flex items-center space-x-2 mt-2">
          <p-badge [value]="'→ 2.1%'" severity="info" class="mr-2"></p-badge>
          <span class="text-2xl font-bold">{{ totalLABO }}</span>
        </div>
      </div>
      <div class="w-1/2 flex justify-end">
        <p-chart type="line" [data]="laboData" [options]="chartOptions" class="w-full h-16"></p-chart>
      </div>
    </div>
  </p-card>
</div>



<!-- TABLEAU D'ETP PAR GRADE-->
<p-card class="mb-4">
  <h3 class="text-lg font-semibold text-cyan-700 mb-2">Nombre d'ETP par Grade</h3>
  <p-table [value]="grades"
           class="p-datatable-gridlines p-datatable-striped custom-table rounded-lg overflow-hidden shadow"
  >
    <ng-template pTemplate="header">
      <tr>
        <th>Grade</th>
        <th>INTERNE DE SPECIALITE</th>
        <th>INT MEDECINE</th>
        <th>Docteur junior médecine</th>
        <th>Interne Spéc. Médecine Gen</th>
        <th>FFI MED</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-row>
      <tr>
        <td class="font-semibold">Nombre d'ETP</td>
        <td>{{ row.specialite }}</td>
        <td>{{ row.medecine }}</td>
        <td>{{ row.juniorMedecine }}</td>
        <td>{{ row.specMedecineGen }}</td>
        <td>{{ row.ffiMed }}</td>
      </tr>
    </ng-template>
  </p-table>
</p-card>

<!--TABLEAU REPARTITION DES ACTE PAR PRATICIEN-->
<section  class="mb-8 mt-8">
  <h2 class="text-2xl font-bold text-cyan-700 mb-4">
    Répartition des Actes par Praticien
  </h2>

  <div class="p-card">
    <p-table
      #repartitionDesActeParPraticien
      [value]="filterePractitionerActeSummary"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [responsiveLayout]="'scroll'"
      [globalFilterFields]="['nomUsuel', 'nomPatronymique','prenom','etp']"
      class="min-w-full bg-white border rounded-lg shadow"
    >
      <!-- Caption with Title, Total, and Search Input -->
      <ng-template pTemplate="caption">
        <div class="flex justify-between items-center p-3 rounded-t-lg">
          <div class="text-lg font-bold text-cyan-700 flex items-center">

              <p-dropdown
                [options]="statutOptions"
                [(ngModel)]="selectedStatut"
                (onChange)="onStatutChange($event)"
                placeholder="Filtrer par statut"
              />


          </div>
          <div>
            <div class="relative mt-2 rounded-md shadow-sm">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <span class="text-gray-500 sm:text-sm mr-3">
            <i class="pi pi-search"></i>
          </span>
              </div>
              <input
                type="text"
                name="search"
                (input)="repartitionDesActeParPraticien.filterGlobal( onInput($event), 'contains')"
                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm"
                placeholder="Filtrer par nom, prénom ou etp"
              />
            </div>
          </div>
        </div>
      </ng-template>


      <!-- Column Headers -->
      <ng-template pTemplate="header">
        <tr>
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>
          <th pSortableColumn="nomUsuel" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Nom usuel
            <p-sortIcon field="nomUsuel"></p-sortIcon>
          </th>
          <th pSortableColumn="nomPatronymique" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Nom patronymique
            <p-sortIcon field="nomPatronymique"></p-sortIcon>
          </th>
          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Prénom
            <p-sortIcon field="prenom"></p-sortIcon>
          </th>
          <th pSortableColumn="etp" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            (ETP praticien)/(ETP total)
            <p-sortIcon field="etp"></p-sortIcon>
          </th>
          <th pSortableColumn="nbActesCCAM" class="px-4 py-2 border text-center text-gray-700 font-semibold">
            CCAM
            <p-sortIcon field="nbActesCCAM"></p-sortIcon>
            <div class="flex justify-center space-x-8">
            <span>
              NB actes
            </span>
              <span>Part du praticien</span>
            </div>
          </th>
          <th pSortableColumn="nbActesNGAP" class="px-4 py-2 border text-center text-gray-700 font-semibold">
            NGAP
            <p-sortIcon field="nbActesNGAP"></p-sortIcon>
            <div class="flex justify-center space-x-8">
              <span>NB actes</span>
              <span>Part du praticien</span>
            </div>
          </th>
        </tr>
      </ng-template>

      <!-- Table Body with Centered Values -->
      <ng-template pTemplate="body" let-acte>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                title="Voir les détails de {{acte.nomUsuel }} {{acte.prenom}}"
                (click)="viewPratitionnerDetails(acte)"
              ></i>
            </p>
          </td>
          <td style="padding: 0.75rem; text-align: left;">{{ acte.nomUsuel || 'nom' }}</td>
          <td style="padding: 0.75rem; text-align: left;">{{ acte.nomPatronymique || 'nom' }}</td>
          <td style="padding: 0.75rem; text-align: left;">{{ acte.prenom || 'prenom' }}</td>
          <td style="padding: 0.75rem; text-align: left;">
            {{ acte.etp +'%' || '0' }}
            <div *ngIf="acte.praticienDateDepart">Arrivé(e) le {{ acte.praticienDateDepart | date: 'dd/MM/yyyy' }}</div>
            <div *ngIf="acte.praticienDateArrivee">Parti(e) le {{ acte.praticienDateArrivee | date: 'dd/MM/yyyy' }}</div>
          </td>
          <td style="padding: 0.75rem; text-align: center;" class="border">
            <div class="flex justify-center space-x-8 ">
              <span>{{ acte.nbActesCCAM || '0' }}</span>
              <span>{{ acte.partPraticienCCAM || '0' }}</span>
            </div>
          </td>
          <td style="padding: 0.75rem; text-align: center;">
            <div class="flex justify-center space-x-8">
              <span>{{ acte.nbActesNGAP || '0' }}</span>
              <span>{{ acte.partPraticienNGAP || '0' }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Footer for Totals -->
      <ng-template pTemplate="footer">
        <tr class="font-semibold bg-gray-100">
          <td colspan="4" class="px-4 py-2 text-left">TOTAL</td>
          <td class="px-4 py-2 text-left">{{ totalEtp + '%' || '0' }}</td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalCCAM || '0' }}</span>
              <span>{{ totalPartCCAM || '0' }}</span>
            </div>
          </td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalNGAP || '0' }}</span>
              <span>{{ totalPartNGAP || '0' }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty Message -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="text-center p-4">Aucun praticien </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</section>


<!-- PERSONNES NON AFFECTER AU SERVICE AYANT POSER AU MOINS UN ACTES-->
<section  class="mb-8 mt-8">
  <h2 class="text-2xl font-bold text-cyan-700 mb-4">
    Personnes qui ont fait des actes dans ce service sans y être affectées
  </h2>

  <div class="p-card">
    <p-table
      #repartitionDesActeParPraticienNonAffectes
      [value]="externalPractitionerSummary"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [responsiveLayout]="'scroll'"
      [globalFilterFields]="['nomUsuel', 'nomPatronymique','prenom','etp']"
      class="min-w-full bg-white border rounded-lg shadow"
    >
      <!-- Caption with Title, Total, and Search Input -->
      <ng-template pTemplate="caption">
        <div class="flex justify-between items-center p-3 rounded-t-lg">
          <div class="text-lg font-bold text-cyan-700 flex items-center">
<!--             drop down hear if needed-->
          </div>
          <div>
            <div class="relative mt-2 rounded-md shadow-sm">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <span class="text-gray-500 sm:text-sm mr-3">
            <i class="pi pi-search"></i>
          </span>
              </div>
              <input
                type="text"
                name="search"
                (input)="repartitionDesActeParPraticienNonAffectes.filterGlobal( onInput($event), 'contains')"
                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm"
                placeholder="Filtrer par nom, prénom ou etp"
              />
            </div>
          </div>
        </div>
      </ng-template>


      <!-- Column Headers -->
      <ng-template pTemplate="header">
        <tr>
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>
          <th pSortableColumn="nomUsuel" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Nom usuel
            <p-sortIcon field="nomUsuel"></p-sortIcon>
          </th>
          <th pSortableColumn="nomPatronymique" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Nom patronymique
            <p-sortIcon field="nomPatronymique"></p-sortIcon>
          </th>
          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Prénom
            <p-sortIcon field="prenom"></p-sortIcon>
          </th>
          <th pSortableColumn="etp" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            (ETP praticien)/(ETP total)
            <p-sortIcon field="etp"></p-sortIcon>
          </th>
          <th pSortableColumn="nbActesCCAM" class="px-4 py-2 border text-center text-gray-700 font-semibold">
            CCAM
            <p-sortIcon field="nbActesCCAM"></p-sortIcon>
            <div class="flex justify-center space-x-8">
            <span>
              NB actes
            </span>
              <span>Part du praticien</span>
            </div>
          </th>
          <th pSortableColumn="nbActesNGAP" class="px-4 py-2 border text-center text-gray-700 font-semibold">
            NGAP
            <p-sortIcon field="nbActesNGAP"></p-sortIcon>
            <div class="flex justify-center space-x-8">
              <span>NB actes</span>
              <span>Part du praticien</span>
            </div>
          </th>
        </tr>
      </ng-template>

      <!-- Table Body with Centered Values -->
      <ng-template pTemplate="body" let-acte>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                title="Voir les détails de {{acte.nomUsuel }} {{acte.prenom}}"
                (click)="viewPratitionnerDetails(acte)"
              ></i>
            </p>
          </td>
          <td style="padding: 0.75rem; text-align: left;">{{ acte.nomUsuel || 'nom' }}</td>
          <td style="padding: 0.75rem; text-align: left;">{{ acte.nomPatronymique || 'nom' }}</td>
          <td style="padding: 0.75rem; text-align: left;">{{ acte.prenom || 'prenom' }}</td>
          <td style="padding: 0.75rem; text-align: left;">
            {{ acte.etp +'%' || '0' }}
            <div *ngIf="acte.praticienDateDepart">Arrivé(e) le {{ acte.praticienDateDepart | date: 'dd/MM/yyyy' }}</div>
            <div *ngIf="acte.praticienDateArrivee">Parti(e) le {{ acte.praticienDateArrivee | date: 'dd/MM/yyyy' }}</div>

          </td>
          <td style="padding: 0.75rem; text-align: center;" class="border">
            <div class="flex justify-center space-x-8 ">
              <span>{{ acte.nbActesCCAM || '0' }}</span>
              <span>{{ acte.partPraticienCCAM || '0' }}</span>
            </div>
          </td>
          <td style="padding: 0.75rem; text-align: center;">
            <div class="flex justify-center space-x-8">
              <span>{{ acte.nbActesNGAP || '0' }}</span>
              <span>{{ acte.partPraticienNGAP || '0' }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Footer for Totals -->
      <ng-template pTemplate="footer">
        <tr class="font-semibold bg-gray-100">
          <td colspan="4" class="px-4 py-2 text-left">TOTAL</td>
          <td class="px-4 py-2 text-left">{{ totalEtpExternal  + '%' || '0' }}</td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalCCAMExternal  || '0' }}</span>
              <span>{{ totalPartCCAMExternal  || '0' }}</span>
            </div>
          </td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalNGAPExternal  || '0' }}</span>
              <span>{{ totalPartNGAPExternal  || '0' }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <!-- Empty Message -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="text-center p-4">Aucun praticien externe avec des actes</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</section>

<!-- PERSONNES NON AFFECTEES PAR LA DAM-->
<section class="mb-8 mt-8">
  <h2 class="text-2xl font-bold text-cyan-700 mb-4">
    Personnes non affectées par la DAM
  </h2>

  <div class="p-card">
    <p-table
      #nonAffectesDAMTable
      [value]="nonAffectesDAMSummary"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [responsiveLayout]="'scroll'"
      [globalFilterFields]="['nomUsuel', 'nomPatronymique','prenom']"
      class="min-w-full bg-white border rounded-lg shadow"
    >
      <!-- Caption with Title, Total, and Search Input -->
      <ng-template pTemplate="caption">
        <div class="flex justify-between items-center p-3 rounded-t-lg">
          <div class="text-lg font-bold text-cyan-700 flex items-center">
            <!--             drop down hear if needed-->
          </div>
          <div>
            <div class="relative mt-2 rounded-md shadow-sm">
              <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <span class="text-gray-500 sm:text-sm mr-3">
            <i class="pi pi-search"></i>
          </span>
              </div>
              <input
                type="text"
                name="search"
                (input)="nonAffectesDAMTable.filterGlobal( onInput($event), 'contains')"
                class="block w-80 h-full pl-8 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm"
                placeholder="Filtrer par nom, prénom ou etp"
              />
            </div>
          </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>
          <th pSortableColumn="nomUsuel" class="px-4 py-2 border-b text-left font-semibold">
            Nom usuel
            <p-sortIcon field="nomUsuel"></p-sortIcon>
          </th>
          <th pSortableColumn="nomPatronymique" class="px-4 py-2 border-b text-left font-semibold">
            Nom patronymique
            <p-sortIcon field="nomPatronymique"></p-sortIcon>
          </th>
          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left font-semibold">
            Prénom
            <p-sortIcon field="prenom"></p-sortIcon>
          </th>
          <th pSortableColumn="nbActesCCAM" class="px-4 py-2 border text-center font-semibold">
            CCAM
            <p-sortIcon field="nbActesCCAM"></p-sortIcon>
            <div class="flex justify-center space-x-8">
              <span>NB actes</span>
              <span>Part du praticien</span>
            </div>
          </th>
          <th pSortableColumn="nbActesNGAP" class="px-4 py-2 border text-center font-semibold">
            NGAP
            <p-sortIcon field="nbActesNGAP"></p-sortIcon>
            <div class="flex justify-center space-x-8">
              <span>NB actes</span>
              <span>Part du praticien</span>
            </div>
          </th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-praticien>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                title="Voir les détails de {{praticien.nomUsuel }} {{praticien.prenom}}"
                (click)="viewPratitionnerDetails(praticien)"
              ></i>
            </p>
          </td>
          <td class="px-4 py-2  ">{{ praticien.nomUsuel }}</td>
          <td class="px-4 py-2  ">{{ praticien.nomPatronymique }}</td>
          <td class="px-4 py-2  ">{{ praticien.prenom }}</td>
          <td class="px-4 py-2 border text-center">
            <div class="flex justify-center space-x-8">
              <span>{{ praticien.nbActesCCAM || "0" }}</span>
              <span>{{ praticien.partPraticienCCAM || "0%" }}</span>
            </div>
          </td>
          <td class="px-4 py-2 border text-center">
            <div class="flex justify-center space-x-8">
              <span>{{ praticien.nbActesNGAP || "0" }}</span>
              <span>{{ praticien.partPraticienNGAP || "0%" }}</span>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="footer">
        <tr class="font-semibold bg-gray-100">
          <td colspan="4" class="px-4 py-2 text-left">TOTAL</td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalCCAMNonAffectes || "0" }}</span>
              <span>{{ totalPartCCAMNonAffectes || "0%" }}</span>
            </div>
          </td>
          <td class="px-4 py-2 text-center border">
            <div class="flex justify-center space-x-8">
              <span>{{ totalNGAPNonAffectes || "0" }}</span>
              <span>{{ totalPartNGAPNonAffectes || "0%" }}</span>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</section>

<!--DEBUT DES TABLEAU DE DISTRIBUTION AVEC CCAM | NGAP-->
<div class="p-card">
  <p-table
    #ccamTable
    [value]="filteredActesCCAM"
    [paginator]="true"
    [rows]="10"
    [rowsPerPageOptions]="[10, 25, 50]"
    [responsiveLayout]="'scroll'"
    [globalFilterFields]="['code', 'description']"
    class="min-w-full bg-white border rounded-lg shadow"
  >
    <!-- Caption with Title, Total, and Search Input -->
    <ng-template pTemplate="caption">
      <div class="flex justify-between items-center  p-3 rounded-t-lg">
        <div class="text-lg font-bold text-cyan-700 flex items-center">
          <span>Répartition des Actes CCAM par Praticien</span>
          <span class="ml-2 text-gray-500 text-base font-semibold bg-gray-100 px-2 py-1 rounded-md">
            Nombre total : {{ totalCCAM }}
          </span>
        </div>
        <div>
          <div class="relative mt-2 rounded-md shadow-sm">
            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
            </div>
            <input
              type="text" name="price"
              (input)="ccamTable.filterGlobal(onInput($event), 'contains')"
              class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
              placeholder="  Filtrer par ccam"
            />
          </div>
        </div>
      </div>
    </ng-template>

    <!-- Column Headers -->
    <ng-template pTemplate="header">
      <tr >
        <th pSortableColumn="code" style="width: 15%" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Code
          <p-sortIcon field="code"></p-sortIcon>

        </th>
        <th pSortableColumn="description" style="width: 45%" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Description
          <p-sortIcon field="description"></p-sortIcon>
        </th>
        <th  style="width: 40%" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Praticiens  <p-badge  [value]="'Fréquence'" severity="info" styleClass="ml-2" />
        </th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template pTemplate="body" let-acte>
      <tr class="border-b hover:bg-gray-50">
        <td style="padding: 0.75rem;">{{ acte.code }}</td>
        <td style="padding: 0.75rem;">{{ acte.description }}</td>
        <td style="padding: 0.75rem;">
          <ul class="list-disc list-inside space-y-1">
            <li *ngFor="let praticien of getPraticiensParActe(acte.id, 'CCAM')">
              {{ praticien.nom }} - {{ praticien.prenom }}
              <p-badge  [value]="getCount(acte.id, praticien.id, 'CCAM')" severity="info" styleClass="ml-2" />
              <!--              ({{ getCount(acte.id, praticien.id, 'CCAM') }} fois)-->
            </li>
          </ul>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="3">Aucun acte CCAM réalisé</td>
      </tr>
    </ng-template>
  </p-table>
</div>

<div class="card mb-4">
  <p-table
    #ngapTable
    [value]="filteredActesNGAP"
    [paginator]="true"
    [rows]="10"
    [rowsPerPageOptions]="[10, 25, 50]"
    [responsiveLayout]="'scroll'"
    [globalFilterFields]="['code', 'description']"
    class=" p-card  min-w-full bg-white border border-gray-200 rounded-lg shadow"
  >
    <!-- Caption with a Global Search Input -->
    <ng-template pTemplate="caption">
      <div class="flex justify-between items-center p-3 rounded-t-lg">
        <div class="text-lg  font-bold text-cyan-700 flex items-center">
          <span>Répartition des Actes NGAP par Praticien</span>
          <span class="ml-2 text-gray-500 text-base font-semibold bg-gray-100 px-2 py-1 rounded-md">
          Nombre total : {{ totalNGAP }}
         </span>
        </div>
        <div>
          <div class="relative mt-2 rounded-md shadow-sm">
            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"  ></i>
              </span>
            </div>
            <input
              type="text" name="price"
              (input)="ngapTable.filterGlobal(onInput($event), 'contains')"
              class="block w-full h-full pl-8 pr-5 py-2   rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
              placeholder="  Filtrer par ngap"
            />
          </div>
        </div>
      </div>
    </ng-template>

    <!-- Column Headers -->
    <ng-template pTemplate="header">
      <tr>
        <th pSortableColumn="code" style="width: 15%">
          Code
          <p-sortIcon field="code"></p-sortIcon>

        </th>
        <th pSortableColumn="description" style="width: 45%">
          Description
          <p-sortIcon field="description"></p-sortIcon>
        </th>
        <th  style="width: 40%">
          Praticiens  <p-badge  [value]="'Fréquence'" severity="info" styleClass="ml-2" />
        </th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template pTemplate="body" let-acte>
      <tr class="border-b hover:bg-gray-50">
        <td style="padding: 0.75rem;">{{ acte.code }}</td>
        <td style="padding: 0.75rem;">{{ acte.description }}</td>
        <td style="padding: 0.75rem;">
          <ul class="list-disc list-inside space-y-1">
            <li *ngFor="let praticien of getPraticiensParActe(acte.id, 'NGAP')">
              {{ praticien.nom }} - {{ praticien.prenom }}
              <p-badge  [value]="getCount(acte.id, praticien.id, 'NGAP')" severity="info" styleClass="ml-2" />
            </li>
          </ul>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="3">Aucun acte NGPA réalisé</td>
      </tr>
    </ng-template>
  </p-table>
</div>







