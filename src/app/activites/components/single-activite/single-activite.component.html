<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

<!-- Loading state -->
<div *ngIf="loading" class="p-4">
  <div class="flex justify-center items-center p-8">
    <i class="pi pi-spin pi-spinner text-cyan-700 text-2xl mr-2"></i>
    <span>Chargement des données...</span>
  </div>
</div>

<!-- Error state -->
<div *ngIf="!loading && error" class="p-4">
  <div class="p-4 bg-red-100 text-red-800 rounded-lg">
    <div class="flex items-center">
      <i class="pi pi-exclamation-triangle text-red-600 text-xl mr-2"></i>
      <span>{{ error }}</span>
    </div>
    <div class="mt-4">
      <button pButton label="Retour à la liste" icon="pi pi-arrow-left" (click)="goBack()" class="p-button-secondary"></button>
    </div>
  </div>
</div>

<!-- Content when data is loaded -->
<div *ngIf="!loading && !error && activite" class="p-4">
  <!-- Header with actions -->
  <div class="flex justify-between items-center mb-4">
    <div class="flex items-center">
      <i class="pi pi-calendar text-cyan-700 text-2xl mr-2"></i>
      <h1 class="text-xl font-bold text-gray-700">Activité {{ activite.code }}</h1>
    </div>
    <div>
      <button pButton label="Retour à la liste" icon="pi pi-arrow-left" (click)="goBack()" class="p-button-secondary mr-2"></button>
    </div>
  </div>

  <!-- Main content -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
    <!-- Left column: Basic info -->
    <div class="col-span-1 md:col-span-2">
      <p-card styleClass="h-full">
        <ng-template pTemplate="header">
          <div class="p-3 bg-gray-50 border-b">
            <h2 class="text-lg font-semibold text-gray-700">Informations générales</h2>
          </div>
        </ng-template>

        <!-- Header avec badges principaux -->
        <div class="mb-6 pb-4 border-b border-gray-200">
          <div class="flex flex-wrap items-center gap-3 mb-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-cyan-100 text-cyan-800">
              <i class="pi pi-tag mr-2"></i>
              {{ activite.code }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              <i class="pi pi-bookmark mr-2"></i>
              {{ activite.typeActe }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
              <i class="pi pi-folder mr-2"></i>
              {{ activite.activiteLib }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
              <i class="pi pi-calendar mr-2"></i>
              {{ formatDateMonthYear(activite.dateRealisation) }}
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
              <i class="pi pi-chart-bar mr-2"></i>
              {{ activite.nombreDeRealisation }} réalisation(s)
            </span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-tag text-cyan-600 mr-1"></i>
              Code de l'acte
            </label>
            <div class="text-base font-semibold text-gray-900">{{ activite.code }}</div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-bookmark text-blue-600 mr-1"></i>
              Type d'acte
            </label>
            <div class="text-base">{{ activite.typeActe }}</div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-folder text-purple-600 mr-1"></i>
              Activité
            </label>
            <div class="text-base">
              <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-lg font-semibold">{{ activite.activite }}</span>
            </div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-sitemap text-indigo-600 mr-1"></i>
              Libellé activité
            </label>
            <div class="text-base">
              <span class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm">{{ activite.activiteLib }}</span>
            </div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-calendar text-green-600 mr-1"></i>
              Date de réalisation
            </label>
            <div class="text-base">
              <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">{{ formatDateMonthYear(activite.dateRealisation) }}</span>
            </div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-chart-bar text-orange-600 mr-1"></i>
              Nombre de réalisations
            </label>
            <div class="text-base">
              <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-lg text-lg font-semibold">{{ activite.nombreDeRealisation }}</span>
            </div>
          </div>



          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-clock text-purple-600 mr-1"></i>
              Semaine ISO
            </label>
            <div class="text-base">
              <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">{{ activite.semaineIso }}</span>
            </div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-hashtag text-gray-600 mr-1"></i>
              Numéro d'intervention
            </label>
            <div class="text-base">
              <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded font-mono">{{ activite.internum }}</span>
            </div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-users text-cyan-600 mr-1"></i>
              Type de venue
            </label>
            <div class="text-base">
              <span class="px-2 py-1 bg-cyan-100 text-cyan-800 rounded">{{ activite.libTypeVenue }}</span>
              <span class="ml-2 text-sm text-gray-500">({{ activite.typeVenue }})</span>
            </div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-star text-yellow-600 mr-1"></i>
              ICR A
            </label>
            <div class="text-base">
              <span *ngIf="activite.icrA" class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded font-medium">{{ activite.icrA }}</span>
              <span *ngIf="!activite.icrA" class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic">AUCUN(e)</span>
            </div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-percentage text-red-600 mr-1"></i>
              Coefficient
            </label>
            <div class="text-base">
              <span *ngIf="activite.coefficient" class="px-2 py-1 bg-red-100 text-red-800 rounded font-medium">{{ activite.coefficient }}</span>
              <span *ngIf="!activite.coefficient" class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic">AUCUN(e)</span>
            </div>
          </div>

          <div class="p-field">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-sort-alpha-down text-pink-600 mr-1"></i>
              Lettre coefficient
            </label>
            <div class="text-base">
              <span *ngIf="activite.lettreCoef" class="px-2 py-1 bg-pink-100 text-pink-800 rounded font-medium">{{ activite.lettreCoef }}</span>
              <span *ngIf="!activite.lettreCoef" class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic">AUCUN(e)</span>
            </div>
          </div>

          <div class="p-field col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              <i class="pi pi-file-edit text-gray-600 mr-1"></i>
              Description de l'acte
            </label>
            <div class="p-3 bg-gray-50 rounded-lg border border-gray-200">
              <p class="text-base text-gray-800 leading-relaxed">{{ activite.description }}</p>
            </div>
          </div>
        </div>
      </p-card>
    </div>

    <!-- Right column: Agent info and status -->
    <div class="col-span-1">
      <div class="space-y-4">
        <!-- Agent info -->
        <p-card styleClass="mb-4">
          <ng-template pTemplate="header">
            <div class="p-3 bg-gray-50 border-b flex justify-between items-center">
              <h2 class="text-lg font-semibold text-gray-700 flex items-center">
                <i class="pi pi-user text-blue-600 mr-2"></i>
                Agent
              </h2>
              <button
                pButton
                icon="pi pi-external-link"
                label="Voir fiche"
                class="p-button-sm p-button-outlined p-button-secondary"
                [routerLink]="['/graphic/praticien', extractIdFromApiUrl(activite.agent['@id'])]"
                pTooltip="Voir la fiche complète de cet agent"
                tooltipPosition="left">
              </button>
            </div>
          </ng-template>

          <!-- Header avec badge principal -->
          <div class="mb-4 pb-3 border-b border-gray-200">
            <div class="flex items-center gap-3 mb-3">
              <span class="inline-flex items-center px-3 py-2 rounded-lg text-base font-semibold bg-blue-100 text-blue-800">
                <i class="pi pi-user mr-2"></i>
                {{ activite.agent.titre }} {{ activite.agent.prenom }} {{ activite.agent.nom }}
              </span>
            </div>
            <div class="flex flex-wrap gap-2">
              <span class="inline-flex items-center px-2 py-1 rounded text-sm bg-gray-100 text-gray-800">
                <i class="pi pi-id-card mr-1"></i>
                {{ activite.agent.matricule }}
              </span>
              <span class="inline-flex items-center px-2 py-1 rounded text-sm bg-purple-100 text-purple-800">
                <i class="pi pi-bookmark mr-1"></i>
                {{ activite.agent.categorie }}
              </span>
            </div>
          </div>

          <div class="space-y-3">
            <div class="p-field">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="pi pi-envelope text-green-600 mr-1"></i>
                Email
              </label>
              <div class="text-base">
                <a [href]="'mailto:' + activite.agent.email" class="text-green-600 hover:text-green-800 underline">
                  {{ activite.agent.email }}
                </a>
              </div>
            </div>

            <div class="p-field">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="pi pi-building text-cyan-600 mr-1"></i>
                Établissement
              </label>
              <div class="text-base">
                <span class="px-2 py-1 bg-cyan-100 text-cyan-800 rounded">{{ activite.agent.etablissement }}</span>
              </div>
            </div>

            <!-- Dates de venue et départ -->
            <div class="p-field">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="pi pi-calendar-plus text-green-600 mr-1"></i>
                Date de venue
              </label>
              <div class="text-base">
                <span *ngIf="activite.agent.dateVenue" class="px-2 py-1 bg-green-100 text-green-800 rounded">
                  {{ formatDate(activite.agent.dateVenue) }}
                </span>
                <span *ngIf="!activite.agent.dateVenue" class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic">
                  Pas connu de supra
                </span>
              </div>
            </div>

            <div class="p-field">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="pi pi-calendar-minus text-red-600 mr-1"></i>
                Date de départ
              </label>
              <div class="text-base">
                <span *ngIf="activite.agent.dateDepart" class="px-2 py-1 bg-red-100 text-red-800 rounded">
                  {{ formatDate(activite.agent.dateDepart) }}
                </span>
                <span *ngIf="!activite.agent.dateDepart" class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic">
                  Pas connu de SUPRA
                </span>
              </div>
            </div>
          </div>
        </p-card>

        <!-- UF info -->
        <p-card styleClass="mb-4">
          <ng-template pTemplate="header">
            <div class="p-3 bg-gray-50 border-b">
              <h2 class="text-lg font-semibold text-gray-700 flex items-center">
                <i class="pi pi-sitemap text-purple-600 mr-2"></i>
                Unités fonctionnelles
              </h2>
            </div>
          </ng-template>

          <p-tabView>
            <p-tabPanel header="UF Principal">
              <!-- Header avec badges -->
              <div class="mb-4 pb-3 border-b border-gray-200">
                <div class="flex items-center gap-3 mb-3">
                  <span class="inline-flex items-center px-3 py-2 rounded-lg text-base font-semibold bg-purple-100 text-purple-800">
                    <i class="pi pi-star mr-2"></i>
                    {{ activite.ufPrincipal.ufcode }}
                  </span>
                  <button
                    pButton
                    icon="pi pi-external-link"
                    label="Voir UF"
                    class="p-button-sm p-button-outlined p-button-secondary"
                    [routerLink]="['/structure/uf', extractIdFromApiUrl(activite.ufPrincipal['@id'])]"
                    pTooltip="Voir la fiche complète de cette UF"
                    tooltipPosition="left">
                  </button>
                </div>
              </div>

              <div class="space-y-3">
                <div class="p-field">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="pi pi-tag text-purple-600 mr-1"></i>
                    Code UF
                  </label>
                  <div class="text-base">
                    <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded font-medium">{{ activite.ufPrincipal.ufcode }}</span>
                  </div>
                </div>

                <div class="p-field">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="pi pi-bookmark text-blue-600 mr-1"></i>
                    Libellé
                  </label>
                  <div class="text-base">{{ activite.ufPrincipal.libelle }}</div>
                </div>

                <div class="p-field">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="pi pi-sitemap text-indigo-600 mr-1"></i>
                    Organisation
                    <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium" *ngIf="periods.p1">
                      P1: {{ formatDateMonthYear(periods.p1.dateDebut) }} - {{ formatDateMonthYear(periods.p1.dateFin) }}
                    </span>
                  </label>
                  <div class="text-base" *ngIf="ufPrincipalOrganisation">
                    <div class="flex items-center gap-2 mb-2">
                      <span class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm font-medium">
                        CR: {{ ufPrincipalOrganisation.crCode }}
                      </span>
                      <span class="text-gray-600">{{ ufPrincipalOrganisation.crLibelle }}</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">
                        PÔLE: {{ ufPrincipalOrganisation.poleCode }}
                      </span>
                      <span class="text-gray-600">{{ ufPrincipalOrganisation.poleLibelle }}</span>
                    </div>
                  </div>
                  <div class="text-base" *ngIf="!ufPrincipalOrganisation">
                    <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic text-sm">
                      Organisation non disponible
                    </span>
                  </div>
                </div>
              </div>
            </p-tabPanel>

            <p-tabPanel header="UF Demande">
              <!-- Cas où ufDemande est null -->
              <div *ngIf="!activite.ufDemande" class="p-6 text-center">
                <div class="flex flex-col items-center">
                  <i class="pi pi-exclamation-triangle text-red-500 text-3xl mb-3"></i>
                  <span class="text-red-600 text-lg font-medium mb-2">Données UF Demande indisponibles</span>
                  <span class="text-gray-500 text-sm">La récupération de l'UF Demande a échoué (contacter l'équipe technique)</span>
                </div>
              </div>

              <!-- Cas où ufDemande existe -->
              <div *ngIf="activite.ufDemande">
                <!-- Header avec badges -->
                <div class="mb-4 pb-3 border-b border-gray-200">
                  <div class="flex items-center gap-3 mb-3">
                    <span class="inline-flex items-center px-3 py-2 rounded-lg text-base font-semibold bg-orange-100 text-orange-800">
                      <i class="pi pi-send mr-2"></i>
                      {{ activite.ufDemande.ufcode }}
                    </span>
                    <button
                      pButton
                      icon="pi pi-external-link"
                      label="Voir UF"
                      class="p-button-sm p-button-outlined p-button-secondary"
                      [routerLink]="['/structure/uf', extractIdFromApiUrl(activite.ufDemande['@id'])]"
                      pTooltip="Voir la fiche complète de cette UF"
                      tooltipPosition="left">
                    </button>
                  </div>
                </div>

                <div class="space-y-3">
                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-tag text-orange-600 mr-1"></i>
                      Code UF
                    </label>
                    <div class="text-base">
                      <span class="px-2 py-1 bg-orange-100 text-orange-800 rounded font-medium">{{ activite.ufDemande.ufcode }}</span>
                    </div>
                  </div>

                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-bookmark text-blue-600 mr-1"></i>
                      Libellé
                    </label>
                    <div class="text-base">{{ activite.ufDemande.libelle }}</div>
                  </div>

                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-sitemap text-indigo-600 mr-1"></i>
                      Organisation
                      <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium" *ngIf="periods.p1">
                        P1: {{ formatDateMonthYear(periods.p1.dateDebut) }} - {{ formatDateMonthYear(periods.p1.dateFin) }}
                      </span>
                    </label>
                    <div class="text-base" *ngIf="ufDemandeOrganisation">
                      <div class="flex items-center gap-2 mb-2">
                        <span class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm font-medium">
                          CR: {{ ufDemandeOrganisation.crCode }}
                        </span>
                        <span class="text-gray-600">{{ ufDemandeOrganisation.crLibelle }}</span>
                      </div>
                      <div class="flex items-center gap-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">
                          PÔLE: {{ ufDemandeOrganisation.poleCode }}
                        </span>
                        <span class="text-gray-600">{{ ufDemandeOrganisation.poleLibelle }}</span>
                      </div>
                    </div>
                    <div class="text-base" *ngIf="!ufDemandeOrganisation">
                      <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic text-sm">
                        Organisation non disponible
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </p-tabPanel>

            <p-tabPanel header="UF Intervention">
              <!-- Cas où ufIntervention est null -->
              <div *ngIf="!activite.ufIntervention" class="p-6 text-center">
                <div class="flex flex-col items-center">
                  <i class="pi pi-exclamation-triangle text-red-500 text-3xl mb-3"></i>
                  <span class="text-red-600 text-lg font-medium mb-2">Données UF Intervention indisponibles</span>
                  <span class="text-gray-500 text-sm">La récupération de l'UF Intervention a échoué (contacter l'équipe technique)</span>
                </div>
              </div>

              <!-- Cas où ufIntervention existe -->
              <div *ngIf="activite.ufIntervention">
                <!-- Header avec badges -->
                <div class="mb-4 pb-3 border-b border-gray-200">
                  <div class="flex items-center gap-3 mb-3">
                    <span class="inline-flex items-center px-3 py-2 rounded-lg text-base font-semibold bg-green-100 text-green-800">
                      <i class="pi pi-cog mr-2"></i>
                      {{ activite.ufIntervention.ufcode }}
                    </span>
                    <button
                      pButton
                      icon="pi pi-external-link"
                      label="Voir UF"
                      class="p-button-sm p-button-outlined p-button-secondary"
                      [routerLink]="['/structure/uf', extractIdFromApiUrl(activite.ufIntervention['@id'])]"
                      pTooltip="Voir la fiche complète de cette UF"
                      tooltipPosition="left">
                    </button>
                  </div>
                </div>

                <div class="space-y-3">
                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-tag text-green-600 mr-1"></i>
                      Code UF
                    </label>
                    <div class="text-base">
                      <span class="px-2 py-1 bg-green-100 text-green-800 rounded font-medium">{{ activite.ufIntervention.ufcode }}</span>
                    </div>
                  </div>

                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-bookmark text-blue-600 mr-1"></i>
                      Libellé
                    </label>
                    <div class="text-base">{{ activite.ufIntervention.libelle }}</div>
                  </div>

                  <div class="p-field">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                      <i class="pi pi-sitemap text-indigo-600 mr-1"></i>
                      Organisation
                      <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium" *ngIf="periods.p1">
                        P1: {{ formatDateObject(periods.p1.dateDebut) }} - {{ formatDateObject(periods.p1.dateFin) }}
                      </span>
                    </label>
                    <div class="text-base" *ngIf="ufInterventionOrganisation">
                      <div class="flex items-center gap-2 mb-2">
                        <span class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm font-medium">
                          CR: {{ ufInterventionOrganisation.crCode }}
                        </span>
                        <span class="text-gray-600">{{ ufInterventionOrganisation.crLibelle }}</span>
                      </div>
                      <div class="flex items-center gap-2">
                        <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">
                          PÔLE: {{ ufInterventionOrganisation.poleCode }}
                        </span>
                        <span class="text-gray-600">{{ ufInterventionOrganisation.poleLibelle }}</span>
                      </div>
                    </div>
                    <div class="text-base" *ngIf="!ufInterventionOrganisation">
                      <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded italic text-sm">
                        Organisation non disponible
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </p-tabPanel>
          </p-tabView>
        </p-card>
      </div>
    </div>
  </div>

  <!-- Chart Section -->
  <div class="mt-6">
    <p-card>
      <ng-template pTemplate="header">
        <div class="p-3 bg-gray-50 border-b">
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-700">
              <i class="pi pi-chart-line mr-2 text-cyan-700"></i>
              Évolution temporelle des réalisations
            </h2>
            <div class="flex items-center space-x-2">
              <div *ngIf="loadingChartData" class="flex items-center">
                <i class="pi pi-spin pi-spinner text-cyan-700 mr-2"></i>
                <span class="text-sm text-gray-600">Chargement du graphique...</span>
              </div>
            </div>
          </div>
        </div>
      </ng-template>

      <!-- Chart content -->
      <div class="p-4">
        <div class="mb-4">
          <p class="text-gray-600 text-sm">
            <i class="pi pi-info-circle mr-1"></i>
            Comparaison du nombre de réalisations cumulées de l'acte <strong>{{ activite?.code }}</strong>
            entre les trois périodes sélectionnées. Chaque ligne représente une période différente.
          </p>
        </div>

        <!-- Loading state -->
        <div *ngIf="loadingChartData" class="flex justify-center items-center h-64">
          <div class="text-center">
            <i class="pi pi-spin pi-spinner text-cyan-700 text-2xl mb-2"></i>
            <p class="text-gray-600">Génération du graphique en cours...</p>
          </div>
        </div>

        <!-- Chart -->
        <div *ngIf="!loadingChartData" class="w-full" style="height: 500px; min-height: 500px;">
          <div *ngIf="chartData?.labels?.length > 0" class="w-full h-full">
            <p-chart
              type="line"
              [data]="chartData"
              [options]="chartOptions"
              width="100%"
              height="500px"
              styleClass="w-full h-full">
            </p-chart>
          </div>

          <!-- Message quand aucune donnée (cas très rare maintenant) -->
          <div *ngIf="!chartData?.labels?.length"
               class="flex justify-center items-center h-full">
            <div class="text-center">
              <i class="pi pi-chart-line text-gray-400 text-4xl mb-4"></i>
              <h3 class="text-lg font-medium text-gray-600 mb-2">Erreur de chargement des données</h3>
              <p class="text-gray-500 text-sm">
                Impossible de générer le graphique pour les périodes sélectionnées.
              </p>
            </div>
          </div>
        </div>

        <!-- Period legend -->
        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4" *ngIf="!loadingChartData">
          <div class="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div class="w-4 h-4 bg-blue-500 rounded mr-3"></div>
            <div>
              <span class="font-medium text-blue-800">P1 - {{ getPeriodLabel(periods.p1) }}</span>
              <p class="text-sm text-blue-600" *ngIf="periods.p1">
                {{ formatPeriod(periods.p1) }}
              </p>
            </div>
          </div>
          <div class="flex items-center p-3 bg-teal-50 rounded-lg border border-teal-200">
            <div class="w-4 h-4 bg-teal-500 rounded mr-3"></div>
            <div>
              <span class="font-medium text-teal-800">P2 - {{ getPeriodLabel(periods.p2) }}</span>
              <p class="text-sm text-teal-600" *ngIf="periods.p2">
                {{ formatPeriod(periods.p2) }}
              </p>
            </div>
          </div>
          <div class="flex items-center p-3 bg-purple-50 rounded-lg border border-purple-200">
            <div class="w-4 h-4 bg-purple-500 rounded mr-3"></div>
            <div>
              <span class="font-medium text-purple-800">P3 - {{ getPeriodLabel(periods.p3) }}</span>
              <p class="text-sm text-purple-600" *ngIf="periods.p3">
                {{ formatPeriod(periods.p3) }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </p-card>
  </div>

  <!-- Statistics Section -->
  <div class="mt-6">
    <!-- Period badges -->
    <div class="mb-4" *ngIf="periods.p1 || periods.p2 || periods.p3">
      <h3 class="text-lg font-semibold text-gray-700 mb-2">Périodes d'analyse</h3>
      <div class="flex flex-wrap gap-2">
        <span *ngIf="periods.p1" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
          <i class="pi pi-calendar mr-1"></i>
          P1: {{ formatPeriod(periods.p1) }}
        </span>
        <span *ngIf="periods.p2" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
          <i class="pi pi-calendar mr-1"></i>
          P2: {{ formatPeriod(periods.p2) }}
        </span>
        <span *ngIf="periods.p3" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
          <i class="pi pi-calendar mr-1"></i>
          P3: {{ formatPeriod(periods.p3) }}
        </span>
      </div>
    </div>

    <!-- UF Statistics Table -->
    <div class="mb-6">
      <p-card>
        <ng-template pTemplate="header">
          <div class="p-3 bg-gray-50 border-b">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold text-gray-700">
                <i class="pi pi-building mr-2 text-cyan-700"></i>
                Répartition par Unités Fonctionnelles pour l'acte {{ activite?.code }}
              </h2>
              <div class="flex items-center space-x-2">
                <button
                  *ngIf="!loadingUfStats && ufStats.length > 0"
                  pButton
                  type="button"
                  icon="pi pi-download"
                  label="Export CSV"
                  class="p-button-sm p-button-outlined p-button-secondary"
                  (click)="exportUfStatsToCSV()"
                  pTooltip="Exporter les données UF en CSV">
                </button>
                <div *ngIf="loadingUfStats" class="flex items-center">
                  <i class="pi pi-spin pi-spinner text-cyan-700 mr-2"></i>
                  <span class="text-sm text-gray-600">Chargement...</span>
                </div>
              </div>
            </div>
          </div>
        </ng-template>

        <p-table
          [value]="ufStats"
          [paginator]="ufStats.length > 0"
          [rows]="10"
          [rowsPerPageOptions]="[5, 10, 20]"
          styleClass="p-datatable-sm"
          [loading]="loadingUfStats"
          dataKey="ufCode">

          <ng-template pTemplate="header">
            <tr>
              <th pSortableColumn="ufCode">Code UF <p-sortIcon field="ufCode"></p-sortIcon></th>
              <th pSortableColumn="ufLibelle">Libellé <p-sortIcon field="ufLibelle"></p-sortIcon></th>
              <th pSortableColumn="p1Count" class="text-center">P1 (2024) <p-sortIcon field="p1Count"></p-sortIcon></th>
              <th class="text-center">Freq. P1</th>
              <th pSortableColumn="p2Count" class="text-center">P2 (2023) <p-sortIcon field="p2Count"></p-sortIcon></th>
              <th class="text-center">Freq. P2</th>
              <th pSortableColumn="p3Count" class="text-center">P3 (2022) <p-sortIcon field="p3Count"></p-sortIcon></th>
              <th class="text-center">Freq. P3</th>
              <th pSortableColumn="totalCount" class="text-center">Total <p-sortIcon field="totalCount"></p-sortIcon></th>
              <th pSortableColumn="globalFrequency" class="text-center">Freq. Globale <p-sortIcon field="globalFrequency"></p-sortIcon></th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-uf>
            <tr>
              <td>
                <a
                  class="font-semibold text-cyan-700 hover:text-cyan-900 hover:underline cursor-pointer transition-colors duration-200"
                  (click)="navigateToUF(uf.ufId)"
                  pTooltip="Voir les détails de l'UF {{ uf.ufLibelle }}">
                  {{ uf.ufCode }}
                </a>
              </td>
              <td>{{ uf.ufLibelle }}</td>
              <td class="text-center">
                <span class="font-medium">{{ uf.p1Count }}</span>
              </td>
              <td class="text-center">
                <p-tag
                  [value]="uf.p1Frequency + '%'"
                  [severity]="getFrequencySeverity(uf.p1Frequency)"
                  styleClass="text-xs">
                </p-tag>
              </td>
              <td class="text-center">
                <span class="font-medium">{{ uf.p2Count }}</span>
              </td>
              <td class="text-center">
                <p-tag
                  [value]="uf.p2Frequency + '%'"
                  [severity]="getFrequencySeverity(uf.p2Frequency)"
                  styleClass="text-xs">
                </p-tag>
              </td>
              <td class="text-center">
                <span class="font-medium">{{ uf.p3Count }}</span>
              </td>
              <td class="text-center">
                <p-tag
                  [value]="uf.p3Frequency + '%'"
                  [severity]="getFrequencySeverity(uf.p3Frequency)"
                  styleClass="text-xs">
                </p-tag>
              </td>
              <td class="text-center">
                <span class="font-bold text-gray-800">{{ uf.totalCount }}</span>
              </td>
              <td class="text-center">
                <div class="flex items-center justify-center">
                  <p-tag
                    [value]="uf.globalFrequency + '%'"
                    [severity]="getFrequencySeverity(uf.globalFrequency)"
                    styleClass="text-sm font-medium">
                  </p-tag>
                  <div class="ml-2 w-16">
                    <p-progressBar
                      [value]="uf.globalFrequency"
                      [showValue]="false"
                      styleClass="h-2">
                    </p-progressBar>
                  </div>
                </div>
              </td>
            </tr>
          </ng-template>

          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="10" class="text-center py-8">
                <div class="flex flex-col items-center">
                  <i class="pi pi-info-circle text-gray-400 text-3xl mb-3"></i>
                  <span class="text-gray-600 text-lg font-medium mb-2">Aucune donnée statistique disponible</span>
                  <span class="text-gray-500 text-sm">Aucune réalisation de cet acte n'a été trouvée pour les périodes sélectionnées dans les unités fonctionnelles.</span>
                </div>
              </td>
            </tr>
          </ng-template>

          <!-- Footer with totals -->
          <ng-template pTemplate="footer">
            <tr class="bg-gray-100 font-bold border-t-2 border-gray-300" *ngIf="ufStats.length > 0">
              <td class="px-4 py-3 text-gray-800">
                <i class="pi pi-calculator mr-2 text-cyan-700"></i>
                <span class="font-bold">TOTAL</span>
              </td>
              <td class="px-4 py-3 text-gray-600">{{ ufStats.length }} UF(s)</td>
              <td class="text-center px-4 py-3">
                <span class="font-bold text-blue-700 text-lg">{{ getTotalP1Count() }}</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium">100%</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="font-bold text-green-700 text-lg">{{ getTotalP2Count() }}</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-sm font-medium">100%</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="font-bold text-purple-700 text-lg">{{ getTotalP3Count() }}</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">100%</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="font-bold text-gray-800 text-xl">{{ getTotalGlobalCount() }}</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="px-2 py-1 bg-gray-200 text-gray-800 rounded text-sm font-medium">100%</span>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </p-card>
    </div>

    <!-- Praticiens Statistics Table -->
    <div class="mb-6">
      <p-card>
        <ng-template pTemplate="header">
          <div class="p-3 bg-gray-50 border-b">
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold text-gray-700">
                <i class="pi pi-users mr-2 text-cyan-700"></i>
                Répartition par Praticiens pour l'acte {{ activite?.code }}
              </h2>
              <div class="flex items-center space-x-2">
                <button
                  *ngIf="!loadingPraticienStats && praticienStats.length > 0"
                  pButton
                  type="button"
                  icon="pi pi-download"
                  label="Export CSV"
                  class="p-button-sm p-button-outlined p-button-secondary"
                  (click)="exportPraticienStatsToCSV()"
                  pTooltip="Exporter les données Praticiens en CSV">
                </button>
                <div *ngIf="loadingPraticienStats" class="flex items-center">
                  <i class="pi pi-spin pi-spinner text-cyan-700 mr-2"></i>
                  <span class="text-sm text-gray-600">Chargement...</span>
                </div>
              </div>
            </div>
          </div>
        </ng-template>

        <p-table
          [value]="praticienStats"
          [paginator]="praticienStats.length > 0"
          [rows]="10"
          [rowsPerPageOptions]="[5, 10, 20]"
          styleClass="p-datatable-sm"
          [loading]="loadingPraticienStats"
          dataKey="praticienId">

          <ng-template pTemplate="header">
            <tr>
              <th pSortableColumn="praticienFullName">Praticien <p-sortIcon field="praticienFullName"></p-sortIcon></th>
              <th pSortableColumn="p1Count" class="text-center">P1 (2024) <p-sortIcon field="p1Count"></p-sortIcon></th>
              <th class="text-center">Freq. P1</th>
              <th pSortableColumn="p2Count" class="text-center">P2 (2023) <p-sortIcon field="p2Count"></p-sortIcon></th>
              <th class="text-center">Freq. P2</th>
              <th pSortableColumn="p3Count" class="text-center">P3 (2022) <p-sortIcon field="p3Count"></p-sortIcon></th>
              <th class="text-center">Freq. P3</th>
              <th pSortableColumn="totalCount" class="text-center">Total <p-sortIcon field="totalCount"></p-sortIcon></th>
              <th pSortableColumn="globalFrequency" class="text-center">Freq. Globale <p-sortIcon field="globalFrequency"></p-sortIcon></th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-praticien>
            <tr>
              <td>
                <div class="flex items-center">
                  <i class="pi pi-user text-gray-400 mr-2"></i>
                  <a
                    class="font-semibold text-cyan-700 hover:text-cyan-900 hover:underline cursor-pointer transition-colors duration-200"
                    (click)="navigateToPraticien(praticien.praticienId)"
                    pTooltip="Voir les détails du praticien {{ praticien.praticienFullName }}">
                    {{ praticien.praticienFullName }}
                  </a>
                </div>
              </td>
              <td class="text-center">
                <span class="font-medium">{{ praticien.p1Count }}</span>
              </td>
              <td class="text-center">
                <p-tag
                  [value]="praticien.p1Frequency + '%'"
                  [severity]="getFrequencySeverity(praticien.p1Frequency)"
                  styleClass="text-xs">
                </p-tag>
              </td>
              <td class="text-center">
                <span class="font-medium">{{ praticien.p2Count }}</span>
              </td>
              <td class="text-center">
                <p-tag
                  [value]="praticien.p2Frequency + '%'"
                  [severity]="getFrequencySeverity(praticien.p2Frequency)"
                  styleClass="text-xs">
                </p-tag>
              </td>
              <td class="text-center">
                <span class="font-medium">{{ praticien.p3Count }}</span>
              </td>
              <td class="text-center">
                <p-tag
                  [value]="praticien.p3Frequency + '%'"
                  [severity]="getFrequencySeverity(praticien.p3Frequency)"
                  styleClass="text-xs">
                </p-tag>
              </td>
              <td class="text-center">
                <span class="font-bold text-gray-800"> {{ praticien.totalCount }}</span>
              </td>
              <td class="text-center">
                <div class="flex items-center justify-center">
                  <p-tag
                    [value]="praticien.globalFrequency + '%'"
                    [severity]="getFrequencySeverity(praticien.globalFrequency)"
                    styleClass="text-sm font-medium">
                  </p-tag>
                  <div class="ml-2 w-16">
                    <p-progressBar
                      [value]="praticien.globalFrequency"
                      [showValue]="false"
                      styleClass="h-2">
                    </p-progressBar>
                  </div>
                </div>
              </td>
            </tr>
          </ng-template>

          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="9" class="text-center py-8">
                <div class="flex flex-col items-center">
                  <i class="pi pi-info-circle text-gray-400 text-3xl mb-3"></i>
                  <span class="text-gray-600 text-lg font-medium mb-2">Aucune donnée statistique disponible</span>
                  <span class="text-gray-500 text-sm">Aucune réalisation de cet acte n'a été trouvée pour les périodes sélectionnées par les praticiens.</span>
                </div>
              </td>
            </tr>
          </ng-template>

          <!-- Footer with totals -->
          <ng-template pTemplate="footer">
            <tr class="bg-gray-100 font-bold border-t-2 border-gray-300" *ngIf="praticienStats.length > 0">
              <td class="px-4 py-3 text-gray-800">
                <div class="flex items-center">
                  <i class="pi pi-calculator mr-2 text-cyan-700"></i>
                  <span class="font-bold">TOTAL</span>
                  <span class="ml-2 text-sm text-gray-600">({{ praticienStats.length }} praticien(s))</span>
                </div>
              </td>
              <td class="text-center px-4 py-3">
                <span class="font-bold text-blue-700 text-lg">{{ getTotalP1CountPraticiens() }}</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium">100%</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="font-bold text-green-700 text-lg">{{ getTotalP2CountPraticiens() }}</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-sm font-medium">100%</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="font-bold text-purple-700 text-lg">{{ getTotalP3CountPraticiens() }}</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm font-medium">100%</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="font-bold text-gray-800 text-xl">{{ getTotalGlobalCountPraticiens() }}</span>
              </td>
              <td class="text-center px-4 py-3">
                <span class="px-2 py-1 bg-gray-200 text-gray-800 rounded text-sm font-medium">100%</span>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </p-card>
    </div>
  </div>
</div>
