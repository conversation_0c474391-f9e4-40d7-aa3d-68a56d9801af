import {NgModule} from "@angular/core";
import {CommonModule} from "@angular/common";
import {HeaderComponent} from "./components/header/header.component";
import {SearchComponent} from "./components/search/search.component";
import {AgentDetailsComponent} from "./components/agent-details/agent-details.component";
import {SharedModule} from "../shared/shared.module";
import {ProfilePictureComponent} from "../graphical/components/profile-picture/profile-picture.component";
import {RouterModule} from "@angular/router";
import {MessageModule} from "primeng/message";
import {ProgressSpinnerModule} from "primeng/progressspinner";
import {PaginatorModule} from "primeng/paginator";


@NgModule({
  declarations: [
    HeaderComponent,
    SearchComponent,
    AgentDetailsComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    ProfilePictureComponent,
    RouterModule,
    MessageModule,
    ProgressSpinnerModule,
    PaginatorModule
  ],
  exports :[
    HeaderComponent,
    AgentDetailsComponent
  ]
})
export class CoreModule { }
