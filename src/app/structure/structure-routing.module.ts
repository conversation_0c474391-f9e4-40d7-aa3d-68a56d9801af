import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { PoleListComponent } from "./components/pole-list/pole-list.component";
import { CRListComponent } from "./components/cr-list/cr-list.component";
import { ServiceListComponent as StructureServiceListComponent } from "./components/service-list/service-list.component";
import { UFListComponent as StructureUFListComponent } from "./components/uf-list/uf-list.component";
import { DepartementComponent } from "../organization/components/departement/departement.component";
import { UfComponent } from "../organization/components/uf/uf.component";
import { InformationPanelComponent } from "../organization/components/information-panel/information-panel.component";
import { ServiceHospitalierComponent } from "../organization/components/service-hospitalier/service-hospitalier.component";
import { ServiceListComponent } from "../organization/components/service-list/service-list.component";
import { UfListComponent } from "../organization/components/uf-list/uf-list.component";

const routes: Routes = [
  {path: "pole-list", component: PoleListComponent},
  {path: "cr-list", component: CRListComponent},
  {path: "service-list", component: StructureServiceListComponent},
  {path: "uf-list", component: StructureUFListComponent},
  {path: "departement", component: DepartementComponent},
  {path: "service/:uid", component: ServiceHospitalierComponent},
  {path: "org-service-list", component: ServiceListComponent},
  {path: "org-uf-list", component: UfListComponent},
  {path: "uf/:uid", component: UfComponent},
  {path: "details", component: InformationPanelComponent}
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StructureRoutingModule {}
