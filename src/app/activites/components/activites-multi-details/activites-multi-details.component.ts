import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { TabViewModule } from 'primeng/tabview';
import { TooltipModule } from 'primeng/tooltip';
import { SkeletonModule } from 'primeng/skeleton';
import { TableModule } from 'primeng/table';
import { AccordionModule } from 'primeng/accordion';
import { TagModule } from 'primeng/tag';
import { ChartModule } from 'primeng/chart';
import { CheckboxModule } from 'primeng/checkbox';
import { RouterLink } from '@angular/router';
import { BreadcrumbItem } from '../../../core/models/breadcrumbItem';
import { BreadcrumbComponent } from '../../../pages/breadcrumb/breadcrumb.component';
import { ActiviteModel } from '../../../core/models/activite';
import { ActiviteService } from '../../../core/services/activite';
import { UfOrganisationService } from '../../../core/services/structure/uf-organisation.service';
import { UfOrganisation } from '../../../core/models/structure/UfOrganisation';
import { GlobalFilterService, DatePeriod } from '../../../core/services/global-filter/global-filter.service';
import { firstValueFrom } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ActeMultiStatsService, ActeMultiStatsData } from '../../../core/services/acte-multi-stats/acte-multi-stats.service';

// Les interfaces sont maintenant importées du service

@Component({
  selector: 'app-activites-multi-details',
  templateUrl: './activites-multi-details.component.html',
  styleUrls: ['./activites-multi-details.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    CardModule,
    TabViewModule,
    TooltipModule,
    SkeletonModule,
    TableModule,
    AccordionModule,
    TagModule,
    ChartModule,
    CheckboxModule,
    FormsModule,
    RouterLink,
    BreadcrumbComponent
  ]
})
export class ActivitesMultiDetailsComponent implements OnInit {
  // Breadcrumb configuration
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Accueil', url: '/' },
    { label: 'Activités', url: '/activites/activites-list' },
    { label: 'Détails des activités sélectionnées', url: '' }
  ];

  // Data properties
  activiteIds: string[] = [];
  activites: ActiviteModel[] = [];

  // Organisation data - Map par activité ID et type UF
  organisationData: Map<string, {
    principal?: UfOrganisation;
    demande?: UfOrganisation;
    intervention?: UfOrganisation;
  }> = new Map();

  // Loading states for organisations - Map par activité ID et type UF
  organisationLoading: Map<string, {
    principal?: boolean;
    demande?: boolean;
    intervention?: boolean;
  }> = new Map();

  // Periods from global filter
  periods: { p1: DatePeriod | null; p2: DatePeriod | null; p3: DatePeriod | null } = {
    p1: null,
    p2: null,
    p3: null
  };

  // UI state
  loading: boolean = true;
  error: string = '';
  activeIndex: number = 0;

  // Chart data
  chartData: ActeMultiStatsData[] = [];
  loadingChartData: boolean = false;
  chartOptions: any = {};
  barChartData: any = {};

  // Chart filtering
  selectedActes: Set<string> = new Set();
  showActeSelector: boolean = false;

  // Table expansion
  expandedRows: any = {};

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private activiteService: ActiviteService,
    private ufOrganisationService: UfOrganisationService,
    private globalFilterService: GlobalFilterService,
    private http: HttpClient,
    private acteMultiStatsService: ActeMultiStatsService
  ) {}

  ngOnInit(): void {
    // Load periods from global filter
    this.globalFilterService.getFilterState().subscribe(filterState => {
      this.periods = {
        p1: filterState.p1,
        p2: filterState.p2,
        p3: filterState.p3
      };
    });

    this.route.queryParamMap.subscribe(params => {
      const ids = params.get('ids');
      if (ids) {
        this.activiteIds = ids.split(',');
        this.loadActivites();
      } else {
        this.error = 'Aucun ID d\'activité spécifié';
        this.loading = false;
      }
    });
  }

  /**
   * Load multiple activites details from the service
   */
  loadActivites(): void {
    this.loading = true;
    this.activites = [];

    // Create an array of promises for each activite request
    const requests = this.activiteIds.map(id =>
      firstValueFrom(this.activiteService.getById(id))
    );

    // Wait for all requests to complete
    Promise.all(requests)
      .then(results => {
        this.activites = results.filter(result => result !== null && result !== undefined) as ActiviteModel[];

        // Expand first row by default
        if (this.activites.length > 0) {
          this.expandedRows[this.activites[0]['@id']] = true;
        }

        // Load organisations and chart data in parallel
        this.loadUfOrganisations();
        this.loadChartData(); // Chargement en parallèle

        this.loading = false;
      })
      .catch(error => {
        console.error('Error loading activites:', error);
        this.error = 'Erreur lors du chargement des détails des activités';
        this.loading = false;
      });
  }

  /**
   * Format date for display
   * @param dateString Date string
   * @returns Formatted date string
   */
  formatDate(dateString: string | null): string {
    if (!dateString) return 'Non spécifié';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  }

  /**
   * Navigate back to the activites list
   */
  goBack(): void {
    this.router.navigate(['/activites/activites-list']);
  }

  /**
   * Get badge class based on activity type
   */
  getActivityTypeClass(type: string): string {
    switch (type) {
      case 'CCAM':
        return 'bg-blue-100 text-blue-800';
      case 'NGAP':
        return 'bg-green-100 text-green-800';
      case 'LABO':
      case 'NABM':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  /**
   * Get status class based on isActif property
   */
  getStatusClass(isActif: boolean): string {
    return isActif ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  }

  /**
   * Check if any activity has the specified type
   */
  hasActivityType(type: string): boolean {
    return this.activites.some(a => a.typeActe === type);
  }

  /**
   * Count activities with the specified type
   */
  countActivitiesByType(type: string): number {
    return this.activites.filter(a => a.typeActe === type).length;
  }

  /**
   * Count active activities
   */
  countActiveActivities(): number {
    return this.activites.filter(a => a.isActif).length;
  }

  /**
   * Count inactive activities
   */
  countInactiveActivities(): number {
    return this.activites.filter(a => !a.isActif).length;
  }

  /**
   * Check if there are any inactive activities
   */
  hasInactiveActivities(): boolean {
    return this.activites.some(a => !a.isActif);
  }

  /**
   * Extract ID from API URL
   * @param apiUrl API URL like "/api/structure/ufs/123"
   * @returns ID string
   */
  extractIdFromApiUrl(apiUrl: string): string {
    if (!apiUrl) return '';
    const parts = apiUrl.split('/');
    return parts[parts.length - 1];
  }

  /**
   * Navigate to single activite details
   */
  navigateToSingleActivite(activiteId: string): void {
    const id = this.extractIdFromApiUrl(activiteId);
    this.router.navigate(['/activites/activite', id]);
  }

  /**
   * Format date for display with fallback
   * @param date Date object or string
   * @returns Formatted date string
   */
  formatDateObject(date: Date | string | null): string {
    if (!date) return 'Non spécifié';
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return dateObj.toLocaleDateString('fr-FR');
    } catch {
      return 'Date invalide';
    }
  }

  /**
   * Load UF organisations (CR → POLE) for all UFs in all activites
   */
  private loadUfOrganisations(): void {
    if (!this.activites.length || !this.periods.p1) return;

    this.activites.forEach(activite => {
      const activiteId = activite['@id'];
      if (!activiteId) return;

      // Initialize loading states
      this.organisationLoading.set(activiteId, {});

      // Load organisation for UF Principal
      if (activite.ufPrincipal?.ufcode) {
        this.setOrganisationLoading(activiteId, 'principal', true);
        this.ufOrganisationService.getOrganisationByUfCode(activite.ufPrincipal.ufcode, this.periods.p1!)
          .subscribe({
            next: (org) => {
              if (org) {
                this.setOrganisationData(activiteId, 'principal', org);
              }
              this.setOrganisationLoading(activiteId, 'principal', false);
            },
            error: (error) => {
              console.error('Error loading UF Principal organisation:', error);
              this.setOrganisationLoading(activiteId, 'principal', false);
            }
          });
      }

      // Load organisation for UF Demande (only if ufDemande is not null)
      if (activite.ufDemande?.ufcode) {
        this.setOrganisationLoading(activiteId, 'demande', true);
        this.ufOrganisationService.getOrganisationByUfCode(activite.ufDemande.ufcode, this.periods.p1!)
          .subscribe({
            next: (org) => {
              if (org) {
                this.setOrganisationData(activiteId, 'demande', org);
              }
              this.setOrganisationLoading(activiteId, 'demande', false);
            },
            error: (error) => {
              console.error('Error loading UF Demande organisation:', error);
              this.setOrganisationLoading(activiteId, 'demande', false);
            }
          });
      }

      // Load organisation for UF Intervention (only if ufIntervention is not null)
      if (activite.ufIntervention?.ufcode) {
        this.setOrganisationLoading(activiteId, 'intervention', true);
        this.ufOrganisationService.getOrganisationByUfCode(activite.ufIntervention.ufcode, this.periods.p1!)
          .subscribe({
            next: (org) => {
              if (org) {
                this.setOrganisationData(activiteId, 'intervention', org);
              }
              this.setOrganisationLoading(activiteId, 'intervention', false);
            },
            error: (error) => {
              console.error('Error loading UF Intervention organisation:', error);
              this.setOrganisationLoading(activiteId, 'intervention', false);
            }
          });
      }
    });
  }

  /**
   * Set organisation loading state
   */
  private setOrganisationLoading(activiteId: string, type: 'principal' | 'demande' | 'intervention', loading: boolean): void {
    const currentLoading = this.organisationLoading.get(activiteId) || {};
    currentLoading[type] = loading;
    this.organisationLoading.set(activiteId, currentLoading);
  }

  /**
   * Set organisation data
   */
  private setOrganisationData(activiteId: string, type: 'principal' | 'demande' | 'intervention', org: UfOrganisation): void {
    const currentData = this.organisationData.get(activiteId) || {};
    currentData[type] = org;
    this.organisationData.set(activiteId, currentData);
  }

  /**
   * Get organisation data for a specific activite and UF type
   */
  getOrganisationForActivite(activite: ActiviteModel, type: 'principal' | 'demande' | 'intervention'): UfOrganisation | undefined {
    const activiteId = activite['@id'];
    if (!activiteId) return undefined;

    const orgData = this.organisationData.get(activiteId);
    return orgData?.[type];
  }

  /**
   * Get organisation loading state for a specific activite and UF type
   */
  isOrganisationLoading(activite: ActiviteModel, type: 'principal' | 'demande' | 'intervention'): boolean {
    const activiteId = activite['@id'];
    if (!activiteId) return false;

    const loadingData = this.organisationLoading.get(activiteId);
    return loadingData?.[type] || false;
  }

  /**
   * Format period for display
   */
  formatPeriod(period: DatePeriod | null): string {
    if (!period?.dateDebut || !period?.dateFin) return 'Non défini';
    return `${this.formatDateObject(period.dateDebut)} - ${this.formatDateObject(period.dateFin)}`;
  }

  /**
   * Format date for month/year display
   */
  formatDateMonthYear(date: Date | null): string {
    if (!date) return 'Non défini';
    return date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });
  }

  /**
   * Format period for display (month/year only)
   */
  formatPeriodMonthYear(period: DatePeriod | null): string {
    if (!period?.dateDebut || !period?.dateFin) return 'Non défini';

    const startMonth = period.dateDebut.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });
    const endMonth = period.dateFin.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });

    // Si même mois/année, afficher une seule fois
    if (startMonth === endMonth) {
      return startMonth;
    }

    return `${startMonth} - ${endMonth}`;
  }

  /**
   * Load chart data from API (optimized)
   */
  private loadChartData(): void {
    this.loadingChartData = true;

    // Extraire les codes d'actes uniques des activités chargées
    const acteCodes = [...new Set(this.activites.map(activite => activite.code))];

    if (acteCodes.length === 0) {
      this.loadingChartData = false;
      this.chartData = [];
      return;
    }

    // Optimisation: Démarrer le timer pour mesurer la performance
    const startTime = performance.now();

    // Appel API avec les codes d'actes et les périodes
    this.acteMultiStatsService.getMultiActeStats(acteCodes, this.periods.p1, this.periods.p2, this.periods.p3)
      .subscribe({
        next: (data) => {
          const endTime = performance.now();
          console.log(`Chart data loaded in ${Math.round(endTime - startTime)}ms`);

          this.chartData = data;
          this.initializeActeSelection();
          this.generateBarChartData();
          this.loadingChartData = false;
        },
        error: (error) => {
          console.error('Error loading chart data:', error);
          this.chartData = [];
          this.loadingChartData = false;
        }
      });
  }

  /**
   * Initialize acte selection (select first 5 by default)
   */
  private initializeActeSelection(): void {
    this.selectedActes.clear();

    // Select first 5 actes by default, or all if less than 5
    const actesToSelect = this.chartData.slice(0, Math.min(5, this.chartData.length));
    actesToSelect.forEach(acte => {
      this.selectedActes.add(acte.acteCode);
    });

    // Show selector if more than 5 actes
    this.showActeSelector = this.chartData.length > 5;
  }

  /**
   * Generate bar chart data from loaded data (filtered by selection)
   */
  private generateBarChartData(): void {
    if (!this.chartData.length) return;

    // Filter data based on selected actes
    const filteredData = this.chartData.filter(item => this.selectedActes.has(item.acteCode));

    if (!filteredData.length) return;

    const labels = filteredData.map(item => item.acteCode);
    const p1Data = filteredData.map(item => item.p1Count);
    const p2Data = filteredData.map(item => item.p2Count);
    const p3Data = filteredData.map(item => item.p3Count);

    this.barChartData = {
      labels: labels,
      datasets: [
        {
          label: `P1 (${this.getPeriodLabel(this.periods.p1)})`,
          data: p1Data,
          backgroundColor: 'rgba(54, 162, 235, 0.8)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: `P2 (${this.getPeriodLabel(this.periods.p2)})`,
          data: p2Data,
          backgroundColor: 'rgba(75, 192, 192, 0.8)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        },
        {
          label: `P3 (${this.getPeriodLabel(this.periods.p3)})`,
          data: p3Data,
          backgroundColor: 'rgba(153, 102, 255, 0.8)',
          borderColor: 'rgba(153, 102, 255, 1)',
          borderWidth: 1
        }
      ]
    };

    this.setupChartOptions();
  }

  /**
   * Setup chart options for bar chart
   */
  private setupChartOptions(): void {
    this.chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: true,
          text: 'Répartition des réalisations par acte et période',
          font: {
            size: 16,
            weight: 'bold'
          },
          padding: {
            top: 10,
            bottom: 20
          }
        },
        legend: {
          display: true,
          position: 'top',
          labels: {
            padding: 20,
            font: {
              size: 12
            }
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleFont: {
            size: 14
          },
          bodyFont: {
            size: 13
          },
          callbacks: {
            title: (context: any) => {
              const dataIndex = context[0].dataIndex;
              const acte = this.chartData[dataIndex];
              return `${acte.acteCode} - ${acte.acteDescription}`;
            },
            label: (context: any) => {
              return `${context.dataset.label}: ${context.parsed.y} réalisation(s)`;
            },
            afterBody: (context: any) => {
              const dataIndex = context[0].dataIndex;
              const acte = this.chartData[dataIndex];
              return `Total activités: ${acte.totalActivites}`;
            }
          }
        }
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Codes d\'actes',
            font: {
              size: 14,
              weight: 'bold'
            }
          },
          ticks: {
            font: {
              size: 11
            }
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Nombre de réalisations',
            font: {
              size: 14,
              weight: 'bold'
            }
          },
          beginAtZero: true,
          ticks: {
            stepSize: 5,
            font: {
              size: 11
            }
          }
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      }
    };
  }

  /**
   * Get period label for display
   */
  getPeriodLabel(period: DatePeriod | null): string {
    if (!period?.dateDebut) return 'Non défini';
    return period.dateDebut.getFullYear().toString();
  }

  /**
   * Toggle acte selection
   */
  toggleActeSelection(acteCode: string): void {
    if (this.selectedActes.has(acteCode)) {
      this.selectedActes.delete(acteCode);
    } else {
      this.selectedActes.add(acteCode);
    }
    this.generateBarChartData();
  }

  /**
   * Check if acte is selected
   */
  isActeSelected(acteCode: string): boolean {
    return this.selectedActes.has(acteCode);
  }

  /**
   * Select all actes
   */
  selectAllActes(): void {
    this.selectedActes.clear();
    this.chartData.forEach(acte => {
      this.selectedActes.add(acte.acteCode);
    });
    this.generateBarChartData();
  }

  /**
   * Deselect all actes
   */
  deselectAllActes(): void {
    this.selectedActes.clear();
    this.generateBarChartData();
  }

  /**
   * Get count of selected actes
   */
  getSelectedActesCount(): number {
    return this.selectedActes.size;
  }

  /**
   * Get total count of actes
   */
  getTotalActesCount(): number {
    return this.chartData.length;
  }
}
