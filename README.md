# Supra-Front

Supra Front est la partie frontend de l'application Supra, une solution modernisée de gestion des statistiques hospitalières au CHRU. Ce projet utilise Angular (18.0.3), Tailwind CSS pour la mise en page, et PrimeNG pour d'autre composants d'interface utilisateur.

## Technologies

* Angular : 18.0.3
* Tailwind CSS : pour une mise en page moderne et réactive.
* TypeScript

# Installation
Prérequis

* Node.js : >= 16.x
* npm : >= 8.x
* Angular CLI : >= 18.x
* Git

# Étapes d'installation

* Clonez le dépôt :

```shell
git clone https://gilly.chu-nancy.fr/u074647/supra-front.git
cd supra-front
```

* Installez les dépendances du projet :

```shell
npm install
```

* Configurez les variables d'environnement 

```shell
cd src/environement
```

* Exemple d'environnement de développement (src/environments/environment.ts) :

```shell
 export const environment = {
  production: false,
  apiBaseUrl: 'http://localhost:8080', // URL de l'API backend local 
};
```

* Exemple d'environnement de production (src/environments/environment.prod.ts) :

```shell
 
export const environment = {
  production: true,
  apiBaseUrl: 'https://api.supra.fr', // URL de l'API backend prod 
};
```

* Lancez le projet localement :

```shell
ng serve
```

## Documentation
* Lancer Storybook (documentation des composants UI) :
```shell
 npm run storybook
```
- Accédez à Storybook dans votre navigateur à l'adresse :http://localhost:6006
* Lancer Compodoc (documentation technique Angular) :
```shell
 npm run compodoc
```
- Accédez à Compodoc dans votre navigateur à l'adresse : http://localhost:8080

## Commandes Utiles
* Lancer l'application en mode développement :

```shell
 ng serve
```
* Construire l'application pour la production :

```shell
ng build --configuration=production --output-hashing=all
```

* Tests Unitaires

```shell
ng test
```

* Tests End-to-End (E2E)
Les tests End-to-End utilisent Cypress, un outil puissant pour simuler les interactions utilisateurs.

```shell
cypress/
  ├── e2e/
  │   ├── login.cy.ts
  │   ├── dashboard.cy.ts
  │   └── filter_search.cy.ts
  ├── fixtures/
  ├── support/
```

* Exécuter les test e2e :

```shell
npx cypress run  
npx cypress open   
```

# Structure des Dossiers
L'architecture du projet suit une approche modulaire avec Lazy Loading pour optimiser les performances. Chaque fonctionnalité dispose de son propre module pour assurer une organisation claire et évolutive.

* ``core/`` : Contient les éléments partagés à travers l'application, tels que les services, les modèles et les pipes, ainsi que le module principal core.module.ts.
* `` graphical/`` et ``organization/`` : Correspondent à des modules fonctionnels avec leurs propres composants et configurations de routing (par exemple, graphical-routing.module.ts et organization-routing.module.ts).
* ``shared/`` : Regroupe les composants partagés entre plusieurs fonctionnalités.
* ``pages/`` : Contient les pages principales de l'application.

Ce découpage permet une maintenance facilitée et une évolutivité pour le développement de nouvelles fonctionnalités.

# Bonnes Pratiques
* Respectez les conventions Angular et Tailwind.
* Assurez-vous que le code passe tous les tests.
* Ajoutez des tests pour chaque nouvelle fonctionnalité.