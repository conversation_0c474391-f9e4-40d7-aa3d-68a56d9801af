export interface Practicien {
  id: string;
  nom: string;
  prenom: string;
  specialite: string;
  anneeNmoinsUn: string;
  anneeN: string;
  repartitionUF: RepartitionUF[]; // Nouvelle clé ajoutée
}

export interface RepartitionReelle {
  ccam: string;
  ngap: string;
  labo: string;
}

export interface RepartitionUF {
  uf: string;
  repartition: string;
  repartitionReelle: RepartitionReelle;
  effectifPraticienDansUF: number;
  effectifUF: number;
  partPraticienDansUF: string;
  partReelle: RepartitionReelle;
  entree: string;
  sortie: string;
}

export interface EvolutionMensuelle {
  mois: string;
  anneeNmoinsUn: number;
  anneeN: number;
}

export interface RepartitionHebdomadaire {
  semaine: string;
  anneeNmoinsUn: number;
  anneeN: number;
}

export interface TableauComparatif {
  code: string;
  description: string;
  totalAnneeNmoinsUn: number;
  totalAnneeN: number;
}

export interface ActeData {
  evolutionMensuelle: EvolutionMensuelle[];
  repartitionHebdomadaire: RepartitionHebdomadaire[];
  tableauComparatif: TableauComparatif[];
}

export interface SinglePraticienData {
  praticien: Practicien;
  ngap: ActeData;
  labo: ActeData;
  ccam: ActeData;
}
