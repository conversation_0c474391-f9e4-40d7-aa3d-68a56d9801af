/* Styles for the pole-list component */

/* Custom styles for checkboxes to improve visibility */
:host ::ng-deep {
  .p-checkbox {
    /* Make checkbox more visible with border and subtle shadow */
    .p-checkbox-box {
      border: 2px solid #cbd5e1 !important; /* Light gray border */
      background-color: #f8fafc !important; /* Very light background */
      border-radius: 4px !important; /* Slightly rounded corners */
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important; /* Subtle shadow */
      transition: all 0.2s ease-in-out !important; /* Smooth transition for hover/focus */

      /* When checkbox is checked */
      &.p-highlight {
        border-color: #0ea5e9 !important; /* Cyan border when checked */
        background-color: #0ea5e9 !important; /* Cyan background when checked */
      }

      /* Checkbox icon (checkmark) */
      .p-checkbox-icon {
        color: white !important; /* White checkmark */
        font-weight: bold !important; /* Make checkmark more visible */
      }
    }

    /* Hover state */
    &:not(.p-checkbox-disabled):hover .p-checkbox-box {
      border-color: #0ea5e9 !important; /* Cyan border on hover */
      box-shadow: 0 2px 4px rgba(14, 165, 233, 0.2) !important; /* Enhanced shadow on hover */
    }

    /* Focus state */
    &:not(.p-checkbox-disabled).p-focus .p-checkbox-box {
      border-color: #0ea5e9 !important; /* Cyan border on focus */
      box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2) !important; /* Focus ring */
    }
  }

  /* Center checkbox in table cell */
  td .p-checkbox {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
}
