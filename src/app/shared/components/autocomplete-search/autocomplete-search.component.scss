// Variables
:root {
  --search-primary: #3b82f6;
  --search-primary-light: #dbeafe;
  --search-success: #10b981;
  --search-warning: #f59e0b;
  --search-danger: #ef4444;
  --search-gray-50: #f9fafb;
  --search-gray-100: #f3f4f6;
  --search-gray-200: #e5e7eb;
  --search-gray-300: #d1d5db;
  --search-gray-400: #9ca3af;
  --search-gray-500: #6b7280;
  --search-gray-600: #4b5563;
  --search-gray-700: #374151;
  --search-gray-800: #1f2937;
  --search-gray-900: #111827;
}

.autocomplete-search-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// Input de recherche
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid var(--search-gray-200);
  border-radius: 12px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    border-color: var(--search-gray-300);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  &.focused {
    border-color: var(--search-primary);
    box-shadow: 0 0 0 3px var(--search-primary-light), 0 4px 6px rgba(0, 0, 0, 0.05);
  }

  &.loading {
    border-color: var(--search-primary);
  }
}

.search-icon {
  position: absolute;
  left: 16px;
  color: var(--search-gray-400);
  font-size: 18px;
  z-index: 2;
  transition: color 0.2s ease;

  .search-input-wrapper.focused & {
    color: var(--search-primary);
  }
}

.search-input {
  width: 100%;
  padding: 16px 50px 16px 50px;
  border: none;
  outline: none;
  font-size: 16px;
  font-weight: 400;
  color: var(--search-gray-900);
  background: transparent;
  border-radius: 10px;

  &::placeholder {
    color: var(--search-gray-400);
    font-weight: 400;
  }
}

.loading-spinner {
  position: absolute;
  right: 50px;
  color: var(--search-primary);
  font-size: 16px;
  animation: spin 1s linear infinite;
}

.clear-button {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  color: var(--search-gray-400);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: var(--search-gray-600);
    background: var(--search-gray-100);
  }
}

// Dropdown des résultats
.search-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--search-gray-200);
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 10002;
  max-height: 500px;
  overflow: hidden;
  animation: slideDown 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

// Header avec statistiques
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--search-gray-50);
  border-bottom: 1px solid var(--search-gray-200);
  font-size: 12px;
}

.search-stats {
  display: flex;
  gap: 8px;
  align-items: center;
  color: var(--search-gray-600);

  .results-count {
    font-weight: 500;
  }

  .search-time {
    color: var(--search-gray-400);
  }
}

.powered-by {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--search-gray-400);
  font-size: 11px;

  .pi-search {
    color: var(--search-primary);
  }
}

// Résultats
.search-results {
  max-height: 400px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--search-gray-100);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--search-gray-300);
    border-radius: 3px;
  }
}

// Catégories
.result-category {
  &:not(:last-child) {
    border-bottom: 1px solid var(--search-gray-100);
  }
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px 8px;
  background: var(--search-gray-50);
  font-size: 12px;
  font-weight: 600;
  color: var(--search-gray-700);
  text-transform: uppercase;
  letter-spacing: 0.5px;

  .category-count {
    color: var(--search-gray-400);
    font-weight: 400;
  }
}

.category-results {
  padding: 0;
}

// Items de résultat
.search-result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.15s ease;
  border-left: 3px solid transparent;

  &:hover,
  &.highlighted {
    background: var(--search-primary-light);
    border-left-color: var(--search-primary);
  }

  &.highlighted {
    background: linear-gradient(90deg, var(--search-primary-light) 0%, rgba(59, 130, 246, 0.05) 100%);
  }
}

.result-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--search-gray-100);
  border-radius: 8px;
  font-size: 14px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--search-gray-900);
  margin-bottom: 2px;

  ::ng-deep mark.highlight {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); // Dégradé orange moderne
    color: #1f2937; // Texte sombre (gris très foncé)
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 700;
    box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
    border: 1px solid #f59e0b;
    text-shadow: none; // Assurer qu'il n'y a pas d'ombre de texte
  }
}

.result-details {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--search-gray-500);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;

  .pi {
    font-size: 10px;
  }
}

.result-badge {
  flex-shrink: 0;
}

.badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Message aucun résultat
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
}

.no-results-icon {
  width: 48px;
  height: 48px;
  background: var(--search-gray-100);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;

  .pi {
    font-size: 20px;
    color: var(--search-gray-400);
  }
}

.no-results-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--search-gray-900);
  margin-bottom: 4px;
}

.no-results-subtitle {
  font-size: 14px;
  color: var(--search-gray-500);
}

// Footer avec raccourcis
.search-footer {
  padding: 12px 16px;
  background: var(--search-gray-50);
  border-top: 1px solid var(--search-gray-200);
}

.keyboard-shortcuts {
  display: flex;
  gap: 16px;
  font-size: 11px;
  color: var(--search-gray-500);
}

.shortcut {
  display: flex;
  align-items: center;
  gap: 4px;
}

kbd {
  display: inline-block;
  padding: 2px 4px;
  background: var(--search-gray-200);
  border: 1px solid var(--search-gray-300);
  border-radius: 3px;
  font-size: 10px;
  font-family: monospace;
  color: var(--search-gray-700);
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Responsive
@media (max-width: 768px) {
  .search-input {
    font-size: 16px; // Évite le zoom sur iOS
  }

  .search-dropdown {
    max-height: 70vh;
  }

  .keyboard-shortcuts {
    display: none; // Masquer sur mobile
  }
}
