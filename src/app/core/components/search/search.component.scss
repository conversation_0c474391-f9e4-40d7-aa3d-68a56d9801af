// Styles ultra professionnels pour le composant de recherche

// Suppression globale des bordures pour les inputs de recherche
input[type="search"] {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;

  &:focus {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
  }

  &:active {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
  }
}

// Largeur optimisée pour utiliser 75-80% de l'espace header
:host {
  width: 100%;
  max-width: none;
}

// Container principal avec largeur maximale
.search-container {
  width: 100%;
  max-width: none;
}

// Input container avec largeur optimisée
.search-input-container {
  width: 100%;
  min-width: 0; // Permet au flex de réduire si nécessaire

  input {
    width: 100%;
    min-width: 300px; // Largeur minimale pour la lisibilité
    border: none !important; // Force la suppression de toutes les bordures
    outline: none !important; // Supprime l'outline par défaut
    box-shadow: none !important; // Supprime les box-shadow par défaut

    &:focus {
      border: none !important;
      outline: none !important;
      box-shadow: none !important;
    }

    &:active {
      border: none !important;
      outline: none !important;
      box-shadow: none !important;
    }
  }
}

// Animation personnalisée pour le spinner
@keyframes spin-smooth {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin-smooth {
  animation: spin-smooth 1s linear infinite;
}

// Styles pour le paginator moderne
:host ::ng-deep .modern-paginator {
  .p-paginator {
    @apply bg-transparent border-0 p-3;

    .p-paginator-first,
    .p-paginator-prev,
    .p-paginator-next,
    .p-paginator-last {
      @apply w-8 h-8 rounded-lg bg-white hover:bg-cyan-50 text-gray-600 hover:text-cyan-600 transition-all duration-200;
      margin: 0 2px;
      border: none;

      &.p-disabled {
        @apply opacity-40 cursor-not-allowed hover:bg-white hover:text-gray-600;
      }
    }

    .p-paginator-pages {
      .p-paginator-page {
        @apply w-8 h-8 rounded-lg bg-white hover:bg-cyan-50 text-gray-600 hover:text-cyan-600 text-sm font-medium transition-all duration-200;
        margin: 0 2px;
        border: none;

        &.p-highlight {
          @apply bg-cyan-500 text-white hover:bg-cyan-600;
        }
      }
    }

    .p-paginator-current {
      @apply text-sm text-gray-600 font-medium;
    }
  }
}

// Styles pour les badges de type
.type-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;

  &.praticien {
    @apply bg-blue-100 text-blue-800;
  }

  &.pole {
    @apply bg-green-100 text-green-800;
  }

  &.service {
    @apply bg-purple-100 text-purple-800;
  }

  &.ufs {
    @apply bg-yellow-100 text-yellow-800;
  }
}

// Amélioration des transitions et animations
.search-dropdown {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

// Styles pour les états de focus et hover
.search-input-container {
  &:focus-within {
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
  }
}

// Responsive design
@media (max-width: 640px) {
  .search-dropdown {
    @apply mx-2 max-w-none;
    left: 8px;
    right: 8px;
    width: auto;
  }
}
