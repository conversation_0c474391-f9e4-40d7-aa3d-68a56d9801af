import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import {SinglePraticienData} from "../../models/overview/singlePraticien-overview";

@Injectable({
  providedIn: 'root',
})
export class SinglePraticienService {
  private praticienDataSubject = new BehaviorSubject<SinglePraticienData | null>(null);
  praticienData$: Observable<SinglePraticienData | null> = this.praticienDataSubject.asObservable();

  constructor(private http: HttpClient) {
    // this.loadPraticienData();
  }

  private loadPraticienDataOld(): void {
    this.http.get<SinglePraticienData>('/assets/data/overview/single-praticien.json').subscribe(
      (data) => {
        this.praticienDataSubject.next(data);
      },
      (error) => {
        console.error('Erreur lors du chargement des données du praticien:', error);
      }
    );
  }
  // On va passer l'id du praticien pour le getPraticien by uuid

  loadPraticienData(praticienId: string): void {
    // const url = `/assets/data/overview/${praticienId}-single-praticien.json`;
    const url = `/assets/data/overview/single-praticien.json`;
    this.http.get<SinglePraticienData>(url).subscribe(
      (data) => {
        this.praticienDataSubject.next(data); // Met à jour le BehaviorSubject avec les données chargées
      },
      (error) => {
        console.error(`Erreur lors du chargement des données du praticien (ID: ${praticienId}):`, error);
      }
    );
  }
}
