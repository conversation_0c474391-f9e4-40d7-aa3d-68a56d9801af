<div  class="p-4  min-h-screen">
  <section class="pdfContent" id="pdfContent">
    <!-- Fil d'ariane -->
    <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>

    <div *ngIf="isLoading" class="text-center">
      <div class="grid grid-cols-12 gap-4 p-4   rounded-lg">
        <div class="col-span-8 bg-white p-4 shadow rounded-lg">
          <h5 class="text-lg font-bold text-gray-700 mb-3">
            Évolution mensuelle des actes réalisés
          </h5>
          <p-progressSpinner
            styleClass="w-4rem h-4rem"
            strokeWidth="8"
            fill="var(--surface-ground)"
            animationDuration=".5s" />
        </div>

        <div class="col-span-4 bg-white p-4 shadow rounded-lg">
          <h5 class="text-lg font-bold text-gray-700 mb-3">
            Répartition des actes par UF
          </h5>
          <p-progressSpinner ariaLabel="loading" />
        </div>

      </div>

    </div>


    <div [ngClass]="{ 'no-shadow': isPdfGenerating }"  *ngIf="!isLoading">
      <div class="grid grid-cols-12 gap-4 p-4 rounded-lg">
        <!-- Colonne gauche : Graphique -->
        <div class="col-span-8 bg-white p-4 shadow rounded-lg">
          <h5 class="text-lg font-bold text-gray-700 mb-3">
            Évolution mensuelle des actes réalisés
          </h5>

          <p-chart
            type="line"
            [data]="chartData"
            [options]="chartOptions"
            class="w-full h-full"
          ></p-chart>
        </div>

        <!-- Colonne droite : Liste des UF -->
        <div class="col-span-4 bg-white p-4 shadow rounded-lg">
          <h5 class="text-lg font-bold text-gray-700 mb-3">
            Répartition des actes par UF
          </h5>

          <!-- Liste des UF paginée -->
          <ul class="divide-y divide-gray-200">
            <li *ngFor="let uf of paginatedUfActesSummary" class="py-2">
              <div class="flex justify-between">
                <div>
                  <p class="text-sm font-bold text-gray-600 flex items-center">
                    {{ uf.nomUF }}
                    <i
                      class="pi pi-eye text-indigo-600 cursor-pointer ml-2"
                      title="Voir les détails de {{ uf.nomUF }}"
                      (click)="viewUfDetails(uf)"
                    ></i>
                  </p>
                  <p class="text-xs text-gray-500">
                    CCAM : {{ uf.ccam }} | NGAP : {{ uf.ngap }} | LABO : {{ uf.labo }}
                  </p>
                </div>
                <p class="text-sm font-bold text-indigo-600">{{ uf.total }} actes</p>
              </div>
            </li>
          </ul>

          <!-- Paginator -->
          <div class="flex justify-center mt-4">
            <p-paginator
              [rows]="5"
              [totalRecords]="ufActesSummary.length"
              (onPageChange)="onPageChange($event)"
            ></p-paginator>
          </div>
        </div>
      </div>
    </div>

  </section>


<!--  <button (click)="downloadPDF('pdfContent','service')">Télécharger en PDF</button>-->

  <!-- POC LISTE DES Services-->
<!--  <h1 class="text-2xl font-semibold text-gray-800 mb-4">Liste des Services Hospitaliers</h1>-->

<!--  <div *ngIf="servicesHospitaliers.length === 0" class="text-gray-500 text-center p-4">-->
<!--    Aucun service hospitalier disponible.-->
<!--  </div>-->

<!--  <div *ngFor="let service of servicesHospitaliers; " class="bg-white shadow rounded-lg p-4 mb-4">-->
<!--    <h2 class="text-xl font-bold text-cyan-700">{{ service.name }}</h2>-->
<!--    <p class="text-gray-600 mt-1"> Nb Uf: {{ service.ufs.length }}</p>-->
<!--    <div class="text-gray-500 text-sm mt-2">-->
<!--      <span class="font-semibold">Code:</span> {{ service.code }}-->
<!--    </div>-->
<!--  </div>-->
  <!-- Composant Information Panel pour afficher les actes et praticiens associés au service -->
  <app-information-panel   [elementId]="currentServiceId" elementType="service"></app-information-panel>
</div>





<!--TEST PDF GENERATORE-->

<!--<main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">-->
<!--  <div class="text-center">-->
<!--    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">-->
<!--      <span class="block xl:inline">Supra Test </span>-->
<!--      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>-->
<!--    </h1>-->
<!--    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">-->
<!--      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.-->
<!--      This is a test content to check if the PDF generator service works as expected (A4 size).-->
<!--    </p>-->
<!--    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">-->
<!--      <div class="rounded-md shadow">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>-->
<!--      </div>-->
<!--      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--</main>-->
<!--<br>-->

<!--<main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">-->
<!--  <div class="text-center">-->
<!--    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">-->
<!--      <span class="block xl:inline">Supra Test </span>-->
<!--      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>-->
<!--    </h1>-->
<!--    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">-->
<!--      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.-->
<!--      This is a test content to check if the PDF generator service works as expected (A4 size).-->
<!--    </p>-->
<!--    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">-->
<!--      <div class="rounded-md shadow">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>-->
<!--      </div>-->
<!--      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--</main>-->

<!--<main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">-->
<!--  <div class="text-center">-->
<!--    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">-->
<!--      <span class="block xl:inline">Supra Test </span>-->
<!--      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>-->
<!--    </h1>-->
<!--    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">-->
<!--      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.-->
<!--      This is a test content to check if the PDF generator service works as expected (A4 size).-->
<!--    </p>-->
<!--    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">-->
<!--      <div class="rounded-md shadow">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>-->
<!--      </div>-->
<!--      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--</main>-->
<!--<main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">-->
<!--  <div class="text-center">-->
<!--    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">-->
<!--      <span class="block xl:inline">Supra Test </span>-->
<!--      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>-->
<!--    </h1>-->
<!--    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">-->
<!--      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.-->
<!--      This is a test content to check if the PDF generator service works as expected (A4 size).-->
<!--    </p>-->
<!--    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">-->
<!--      <div class="rounded-md shadow">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>-->
<!--      </div>-->
<!--      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--</main>-->

<!--<main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">-->
<!--  <div class="text-center">-->
<!--    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">-->
<!--      <span class="block xl:inline">Supra Test </span>-->
<!--      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>-->
<!--    </h1>-->
<!--    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">-->
<!--      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.-->
<!--      This is a test content to check if the PDF generator service works as expected (A4 size).-->
<!--    </p>-->
<!--    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">-->
<!--      <div class="rounded-md shadow">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>-->
<!--      </div>-->
<!--      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--</main>-->

<!--<main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">-->
<!--  <div class="text-center">-->
<!--    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">-->
<!--      <span class="block xl:inline">Supra Test </span>-->
<!--      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>-->
<!--    </h1>-->
<!--    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">-->
<!--      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.-->
<!--      This is a test content to check if the PDF generator service works as expected (A4 size).-->
<!--    </p>-->
<!--    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">-->
<!--      <div class="rounded-md shadow">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>-->
<!--      </div>-->
<!--      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">-->
<!--        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--</main>-->

<!--<button (click)="downloadPDFMany('pdfContent','service_eh-multi-section')">Télécharger en PDF</button>-->
