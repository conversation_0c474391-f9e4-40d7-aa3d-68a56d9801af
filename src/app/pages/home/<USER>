import { Component, OnInit } from '@angular/core';
import { Observable, of } from 'rxjs';
import { OlympicService } from 'src/app/core/services/olympic.service';
import { trigger, state, style, transition, animate } from '@angular/animations';


@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  animations: [
    trigger('fadeInOut', [
      state('void', style({ opacity: 0 })),
      transition('void <=> *', animate(300))
    ])
  ]
})
export class HomeComponent implements OnInit {
  // public olympics$: Observable<any> = of(null);

  // constructor(private olympicService: OlympicService) {}

  showMoreCCAM = false;
  showMoreNGAP = false;
  showMoreNABM = false;
  showMoreETP = false;



  ngOnInit(): void {
    // this.olympics$ = this.olympicService.getOlympics();
  }

  /**
   * Scroll smooth vers la section features
   */
  scrollToFeatures(): void {
    const featuresElement = document.getElementById('features');
    if (featuresElement) {
      featuresElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    } else {
      console.warn('Section #features non trouvée');
    }
  }
}
