
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {ActeCCAM} from "../../models/acte/acte-ccam.model";
import {ActeNGAP} from "../../models/acte/acte-ngap.model";
import {RealisationActe} from "../../models/acte/realisation-acte.model";
import {ActeLABO} from "../../models/acte/acte-labo.model";

@Injectable({
  providedIn: 'root'
})
export class ActeService {
  private actesCCAMSubject = new BehaviorSubject<ActeCCAM[]>([]);
  private actesNGAPSubject = new BehaviorSubject<ActeNGAP[]>([]);
  private actesLABOSubject = new BehaviorSubject<ActeLABO[]>([]);
  private realisationsSubject = new BehaviorSubject<RealisationActe[]>([]);

  actesCCAM$ = this.actesCCAMSubject.asObservable();
  actesNGAP$ = this.actesNGAPSubject.asObservable();
  actesLABO$ = this.actesLABOSubject.asObservable();
  realisations$ = this.realisationsSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadActesData();
    this.loadRealisationsData();
  }

  private loadActesData(): void {
    this.http.get<ActeCCAM[]>('/assets/data/actesCCAM.json').subscribe((data) => {
      this.actesCCAMSubject.next(data);
    });

    this.http.get<ActeNGAP[]>('/assets/data/actesNGAP.json').subscribe((data) => {
      this.actesNGAPSubject.next(data);
    });

    this.http.get<ActeLABO[]>('/assets/data/actesLABO.json').subscribe((data) => {
      this.actesLABOSubject.next(data);
    });
  }

  private loadRealisationsData(): void {
    this.http.get<RealisationActe[]>('/assets/data/realisationActes.json').subscribe((data) => {
      this.realisationsSubject.next(data);
    });
  }

  // Filtrer les réalisations par service ID
  getRealisationsByServiceId(serviceId: string): Observable<RealisationActe[]> {
    return this.realisations$.pipe(
      map((realisations) => realisations.filter((r) => r.serviceId === serviceId))
    );
  }
}
