import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { HomeComponent } from './home.component';

const meta: Meta<HomeComponent> = {
  title: 'Pages/Home',
  component: HomeComponent,
  decorators: [
    moduleMetadata({
      declarations: [HomeComponent],
    }),
  ],
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
La **Page d'accueil** de Supra V2 inclut :
- Un en-tête avec le titre et une description de l'application.
- Des sections pour les fonctionnalités, mandataires, glossaire, et un footer.
        `,
      },
    },
  },
};

export default meta;
type Story = StoryObj<HomeComponent>;

export const Default: Story = {
  name: '<PERSON><PERSON>',
  args: {},
  parameters: {
    docs: {
      description: {
        story: `
Affiche la page d'accueil avec toutes ses sections et les images correctement affichées via la fonction play.
        `,
      },
    },
  },
  play: async ({ canvasElement }) => {
    const headerImage = canvasElement.querySelector('img[src="./assets/icon/statistiques.png"]') as HTMLImageElement | null;
    if (headerImage) {
      headerImage.src = 'https://via.placeholder.com/800'; // Remplace par une URL publique
    }

    const collecteImage = canvasElement.querySelector('img[src="./assets/icon/collecte-de-donnees.png"]') as HTMLImageElement | null;
    if (collecteImage) {
      collecteImage.src = 'https://via.placeholder.com/150'; // Remplace par une URL publique
    }

    const transfertImage = canvasElement.querySelector('img[src="./assets/icon/transfert-de-donnees.png"]') as HTMLImageElement | null;
    if (transfertImage) {
      transfertImage.src = 'https://via.placeholder.com/150'; // Remplace par une URL publique
    }
    // Fiabilisation des données
    const fiabilisationImage = canvasElement.querySelector('img[src="./assets/icon/proteger.png"]') as HTMLImageElement | null;
    if (fiabilisationImage) {
      fiabilisationImage.src = 'https://via.placeholder.com/150';
    }

    // Confidentialité et RGPD
    const rgpdImage = canvasElement.querySelector('img[src="./assets/icon/certification.png"]') as HTMLImageElement | null;
    if (rgpdImage) {
      rgpdImage.src = 'https://via.placeholder.com/150';
    }

    // Données non intégrées
    const donneesNonIntegreesImage = canvasElement.querySelector('img[src="./assets/icon/serveur.png"]') as HTMLImageElement | null;
    if (donneesNonIntegreesImage) {
      donneesNonIntegreesImage.src = 'https://via.placeholder.com/150';
    }
  },
};
