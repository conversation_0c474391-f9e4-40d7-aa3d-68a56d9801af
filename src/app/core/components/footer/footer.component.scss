/* Footer Collapsible Styles */

/* Animation pour l'apparition en fondu */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Amélioration des transitions pour le bouton toggle */
.transition-transform {
  transition: transform 0.3s ease-in-out;
}

/* Styles pour le contenu collapsible */
.max-h-0 {
  max-height: 0;
}

.max-h-96 {
  max-height: 24rem; /* 384px */
}

/* Styles pour le titre cliquable */
.cursor-pointer {
  cursor: pointer;
}

/* Effet hover pour le titre */
h3.cursor-pointer:hover {
  transform: translateY(-1px);
  text-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}

/* Animation pour le changement de taille du titre */
h3 {
  transition: all 0.3s ease-in-out;
}

/* Amélioration des tooltips natifs */
[title] {
  position: relative;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .max-h-96 {
    max-height: 32rem; /* Plus d'espace sur mobile */
  }

  /* Titre plus petit sur mobile quand compacté */
  h3.text-base {
    font-size: 0.875rem; /* 14px */
  }

  /* Espacement réduit sur mobile */
  .py-4 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
}
