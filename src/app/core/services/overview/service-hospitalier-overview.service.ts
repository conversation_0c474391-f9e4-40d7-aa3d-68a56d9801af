import { Injectable } from '@angular/core';
import {BehaviorSubject, filter, Observable} from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';
import {ServiceData, UFActeSummary} from "../../models/overview/service-overview.model";

@Injectable({
  providedIn: 'root',
})
export class ServiceHospitalierOverviewService {
  private servicesSubject = new BehaviorSubject<ServiceData[]>([]);
  services$: Observable<ServiceData[]> = this.servicesSubject.asObservable();

  constructor(private http: HttpClient) {
    this.servicesSubject.next([]); // Initialiser avec un tableau vide
    this.loadServices();
  }

  private loadServices(): void {
    this.http
      .get<ServiceData[]>('/assets/data/overview/service-overview.json')
      .subscribe((data) => {
        this.servicesSubject.next(data);
      });
  }

  getServiceById(serviceId: string): Observable<ServiceData | undefined> {
    return this.services$.pipe(
      map((services) => {
        if (Array.isArray(services)) {
          return services.find((service) => service.serviceId === serviceId);
        }
        console.error('Services is not an array:', services);
        return undefined;
      })
    );
  }





}
