import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { NotFoundComponent } from './pages/not-found/not-found.component';
import {SigapsComponent} from "./pages/sigaps/sigaps.component";

import {GardeAstreinteComponent} from "./pages/garde-astreinte/garde-astreinte.component";
import {ParticlesComponent} from "./shared/particles/particles.component";
import {AuthGuard} from "./auth/guards/auth.guards";
import {EmailAuthGuard} from "./auth/guards/email-auth.guard";
import {LiberaleComponent} from "./pages/liberale/liberale.component";
import {AgentDetailsComponent} from "./core/components/agent-details/agent-details.component";
import {FeatureFlagGuard} from "./core/guards/feature-flag.guard";

const routes: Routes = [
  // ✅ Routes activées pour ce sprint
  {
    path: 'auth',
    loadChildren: () => import ('./auth/auth.module').then(m=>m.AuthModule)
  },
  {
    path: 'activites',
    loadChildren: () => import ('./activites/activites.module').then(m=>m.ActivitesModule),
    canActivate: [AuthGuard, FeatureFlagGuard]
  },
  {
    path: 'referent',
    loadChildren: () => import ('./admin/admin.module').then(m=>m.AdminModule),
    canActivate: [AuthGuard, FeatureFlagGuard]
  },
  {
    path: '',
    component: HomeComponent,
  },

  // 🚧 Routes désactivées pour ce sprint (protégées par AuthGuard + FeatureFlagGuard)
  {
    path: 'praticien',
    loadChildren: () => import ('./praticien/praticien.module').then(m=>m.PraticienModule),
    canActivate: [AuthGuard, FeatureFlagGuard]
  },
  {
    path: 'graphic',
    loadChildren: () => import ('./graphical/graphical.module').then(m=>m.GraphicalModule),
    canActivate: [AuthGuard, FeatureFlagGuard]
  },
  {
    path: 'structure',
    loadChildren: () => import ('./structure/structure.module').then(m=>m.StructureModule),
    canActivate: [AuthGuard, FeatureFlagGuard]
  },
  {
    path: 'agent-details',
    component: AgentDetailsComponent,
    canActivate: [AuthGuard, EmailAuthGuard, FeatureFlagGuard],
  },
  {
    path: 'particule',
    component: ParticlesComponent,
    canActivate: [AuthGuard, FeatureFlagGuard]
  },
  {
    path: 'sigaps',
    component: SigapsComponent,
    canActivate: [AuthGuard, FeatureFlagGuard]
  },
  {
    path: 'garde-et-astreinte',
    component: GardeAstreinteComponent,
    canActivate: [AuthGuard, FeatureFlagGuard]
  },
  {
    path: 'liberale',
    component: LiberaleComponent,
    canActivate: [AuthGuard, FeatureFlagGuard]
  },

  // Routes système (toujours accessibles)
  {
    path: '**', // wildcard
    component: NotFoundComponent,
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes,{ useHash: true })],
  exports: [RouterModule],
})
export class AppRoutingModule {}
