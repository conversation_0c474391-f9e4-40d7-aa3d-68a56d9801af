export interface SigapsData {
  lastUpdated: string; // Date de la dernière mise à jour
  data: SigapsAuthor[]; // Liste des auteurs et leurs données SIGAPS
}

export interface SigapsAuthor {
  authorName: string; // Nom de l'auteur
  service: string; // Service affilié
  uf: string; // Unité Fonctionnelle affiliée
  totalPublications: number; // Nombre total de publications
  totalSigapsScore: number; // Score SIGAPS total
  categories: SigapsCategories; // Répartition des catégories
  yearlyEvolution: YearlyEvolution[]; // Évolution annuelle du score SIGAPS
}

export interface SigapsCategories {
  APlus: number; // Nombre de publications dans la catégorie A+
  A: number; // Nombre de publications dans la catégorie A
  B: number; // Nombre de publications dans la catégorie B
  C: number; // Nombre de publications dans la catégorie C
  D: number; // Nombre de publications dans la catégorie D
  [key: string]: number; // Autorise l'accès dynamique
}

export interface YearlyEvolution {
  year: number; // Année
  score: number; // Score SIGAPS pour l'année
}
