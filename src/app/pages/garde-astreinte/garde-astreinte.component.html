<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>


<section>
  <h2 class="text-xl font-bold mb-4">Garde et Astreinte</h2>

  <!-- <PERSON><PERSON><PERSON> mise à jour -->
  <div class="text-gray-600 mb-4">
    Dernière actualisation : {{ gardeAstreinteData?.lastUpdated | date: 'dd/MM/yyyy' }}
  </div>

  <!-- Tableau des données -->
  <div>
    <div class="relative mt-2 rounded-md shadow-sm">
      <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span class="text-gray-500 sm:text-sm mr-3">
                <i class="pi pi-search"></i>
              </span>
      </div>
      <input
        type="text" name="price"
        (input)="gardeAstreinteTable.filterGlobal(onInput($event), 'contains')"
        class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 "
        placeholder="  Filtrer par praticien,service,uf ..."
      />
    </div>
  </div>
  <p-table
    #gardeAstreinteTable
    [value]="gardeAstreinteData?.data ?? []"
    [paginator]="true"
    [rows]="5"
    [globalFilterFields]="['praticienName', 'service', 'uf']"
    [rowsPerPageOptions]="[5, 10, 20]"
    [showCurrentPageReport]="true"
    currentPageReportTemplate="Affichage {first} à {last} sur {totalRecords}"
    class="mb-6"
  >
    <ng-template pTemplate="header">
      <tr>
        <th pSortableColumn="praticienName">Nom du praticien <p-sortIcon field="praticienName"></p-sortIcon></th>
        <th pSortableColumn="service">Service <p-sortIcon field="service"></p-sortIcon></th>
        <th pSortableColumn="uf">UF <p-sortIcon field="uf"></p-sortIcon></th>
        <th>Total Gardes</th>
        <th>Total Astreintes</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-practitioner>
      <tr>
        <td>{{ practitioner.praticienName }}</td>
        <td>{{ practitioner.service }}</td>
        <td>{{ practitioner.uf }}</td>
        <td>{{ getTotalOnCalls(practitioner) }}</td>
        <td>{{ getTotalStandbys(practitioner) }}</td>
      </tr>
    </ng-template>
  </p-table>

  <!-- Graphique en barres -->
  <h3 class="text-lg font-semibold mb-3">Répartition des Gardes et Astreintes</h3>
  <p-chart
    type="bar"
    [data]="barChartData"
    [options]="barChartOptions"
    class="w-full h-96"
  ></p-chart>
</section>

<!--<section class="mt-10">-->
<!--  <h2 class="text-lg font-semibold mb-4">Comparaison des Gardes et Astreintes des 3 dernières années</h2>-->
<!--  <div class="mb-6">-->
<!--    <label for="practitionerSelect" class="block text-sm font-medium text-gray-700 mb-2">-->
<!--      Sélectionnez un praticien :-->
<!--    </label>-->
<!--    <div class="relative w-full">-->
<!--      <p-autoComplete-->
<!--        [(ngModel)]="selectedPractitioner"-->
<!--        [dropdown]="true"-->
<!--        [suggestions]="filteredPractitioners"-->
<!--        optionLabel="praticienName"-->
<!--        [forceSelection]="true"-->
<!--        (completeMethod)="filterPractitioners($event)"-->
<!--        (onSelect)="initializeComparisonChart()"-->
<!--        [placeholder]="'Filtrer par praticien...'"-->
<!--        id="practitionerSelect"-->
<!--        class="w-full p-3 text-gray-700 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"-->
<!--      ></p-autoComplete>-->
<!--    </div>-->
<!--  </div>-->

<!--  &lt;!&ndash; Graphique comparatif &ndash;&gt;-->
<!--  <p-chart-->
<!--    type="line"-->
<!--    [data]="comparisonChartData"-->
<!--    [options]="comparisonChartOptions"-->
<!--    *ngIf="comparisonChartData"-->
<!--  ></p-chart>-->
<!--</section>-->

