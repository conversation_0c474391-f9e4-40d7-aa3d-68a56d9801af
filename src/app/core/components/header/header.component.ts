import {Component} from '@angular/core';
import {SidebarService} from "../../services/sidebar.service";
import {AuthService} from "../../services/auth/auth.service";
import { SearchEntity } from '../../models/search-entity.model';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';

/**
 * some composoc class documentation
 */
@Component({
  selector: 'app-header',
  standalone: false,
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent {
  isDropdownOpen = false;

  constructor(
    private sidebarService: SidebarService,
    private authService: AuthService,
    private sanitizer: DomSanitizer
    ) {}

  /**
   *  cette methode dois se montrer dsna compodoc
   */
  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  logout(): void {
    this.authService.logout();
  }

  isAuthenticated(){
    return this.authService.isAuthenticated();
  }

  getUserName(): string {
    return this.authService.getUserName();
  }

  getUserEmail(): string | null {
    return this.authService.getUserEmail();
  }

  isEmailAuthUser(): boolean {
    return this.authService.isEmailAuthUser();
  }

  /**
   * Extrait les initiales à partir de l'email de l'utilisateur
   * Par exemple: <EMAIL> -> TT
   */
  getInitialsFromEmail(): string {
    const email = this.getUserEmail();
    if (!email) return 'U'; // Default initial if no email

    // Récupérer la partie avant le @ de l'email
    const localPart = email.split('@')[0];

    // Diviser par le point pour obtenir prénom.nom
    const parts = localPart.split('.');

    if (parts.length >= 2) {
      // Format prénom.nom - prendre la première lettre de chaque partie
      return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
    } else if (parts.length === 1) {
      // S'il n'y a pas de point, prendre les deux premières lettres ou une seule si trop court
      return parts[0].length > 1
        ? (parts[0].charAt(0) + parts[0].charAt(1)).toUpperCase()
        : parts[0].charAt(0).toUpperCase();
    }

    return 'U'; // Fallback
  }

  /**
   * Génère un avatar avec les initiales de l'utilisateur
   */
  generateAvatar(): SafeHtml {
    const initials = this.getInitialsFromEmail();
    const colors = [
      '#4299E1', // blue-500
      '#38B2AC', // teal-500
      '#ED8936', // orange-500
      '#9F7AEA', // purple-500
      '#F56565', // red-500
      '#48BB78'  // green-500
    ];

    // Utiliser une valeur déterministe pour choisir la couleur (basée sur les initiales)
    const colorIndex = (initials.charCodeAt(0) + (initials.length > 1 ? initials.charCodeAt(1) : 0)) % colors.length;
    const bgColor = colors[colorIndex];

    const avatarHtml = `
      <div style="
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: ${bgColor};
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
      ">
        ${initials}
      </div>
    `;

    return this.sanitizer.bypassSecurityTrustHtml(avatarHtml);
  }

  /**
   * Gère la sélection d'un résultat de recherche
   */
  onSearchResultSelected(result: SearchEntity): void {
    console.log('Résultat sélectionné:', result);
    // TODO: Implémenter la navigation ou l'action selon le type d'entité
    // Par exemple: router.navigate(['/praticien', result.id]) pour un praticien
  }

  /**
   * Gère les changements de recherche
   */
  onSearchChanged(query: string): void {
    console.log('Recherche changée:', query);
    // TODO: Optionnel - tracker les recherches pour analytics
  }

  /**
   * Check if the current user has admin roles
   * @returns true if the user has any of the admin roles, false otherwise
   */
  hasAdminRole(): boolean {
    return this.authService.hasAdminRole();
  }

  /**
   * Get the admin dashboard URL from environment
   * @returns the admin dashboard URL
   */
  getAdminDashboardUrl(): string {
    return this.authService.getAdminDashboardUrl();
  }
}
