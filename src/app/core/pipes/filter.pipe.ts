import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filter',
  standalone: true
})
export class FilterPipe implements PipeTransform {
  transform(items: any[], property: string, value: any): any[] {
    if (!items || !property || !value) {
      return items;
    }

    return items.filter(item => {
      if (item[property] !== undefined) {
        if (typeof item[property] === 'string') {
          return item[property] === value;
        } else {
          return item[property] == value;
        }
      }
      return false;
    });
  }
}
