export interface Dataset {
  label: string;
  data: number[];
  borderColor: string;
  backgroundColor: string;
  fill: boolean;
}

export interface ChartDataModel {
  labels: string[];
  datasets: Dataset[];
}

export interface UFActeSummary {
  nomUF: string;
  ccam: number;
  ngap: number;
  labo: number;
  total: number;
}

export interface ServiceData {
  serviceId: string;
  chartData: ChartDataModel;
  ufActesSummary: UFActeSummary[];
}
