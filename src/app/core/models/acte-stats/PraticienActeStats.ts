/**
 * Interface for Praticien statistics for a specific acte across periods
 */
export interface PraticienActeStats {
  praticienId: string;
  praticienNom: string;
  praticienPrenom: string;
  praticienTitre: string;
  praticienFullName: string;
  p1Count: number;
  p1Frequency: number;
  p2Count: number;
  p2Frequency: number;
  p3Count: number;
  p3Frequency: number;
  totalCount: number;
  globalFrequency: number;
}

/**
 * Interface for the API Platform response containing Praticien statistics
 */
export interface PraticienActeStatsResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  totalItems: number;
  member: PraticienActeStats[];
  view?: {
    '@id': string;
    '@type': string;
    first?: string;
    last?: string;
    next?: string;
    previous?: string;
  };
}
