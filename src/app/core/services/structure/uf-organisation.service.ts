import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { UfOrganisation, UfOrganisationResponse } from '../../models/structure/UfOrganisation';
import { DatePeriod } from '../global-filter/global-filter.service';

/**
 * Service pour récupérer l'organisation d'une UF (CR → POLE)
 *
 * Route API: GET /api/structure/get-organisation-by-ufcode
 *
 * Exemple d'utilisation:
 * GET /api/structure/get-organisation-by-ufcode?code=Y75Z&p1Start=2024-01-01&p1End=2024-12-31
 */
@Injectable({
  providedIn: 'root'
})
export class UfOrganisationService {

  constructor(private http: HttpClient) {}

  /**
   * Récupère l'organisation d'une UF par son code
   *
   * @param ufCode Code de l'UF (ex: "Y75Z")
   * @param period Période de recherche (utilise p1 par défaut)
   * @returns Observable<UfOrganisation | null>
   */
  getOrganisationByUfCode(ufCode: string, period: DatePeriod | null = null): Observable<UfOrganisation | null> {
    if (!ufCode) {
      return of(null);
    }

    // Construction des paramètres de période
    const params = new URLSearchParams();
    params.set('code', ufCode);

    if (period?.dateDebut && period?.dateFin) {
      params.set('p1Start', this.formatDate(period.dateDebut));
      params.set('p1End', this.formatDate(period.dateFin));
    } else {
      // Période par défaut si aucune période fournie
      const currentYear = new Date().getFullYear();
      params.set('p1Start', `${currentYear}-01-01`);
      params.set('p1End', `${currentYear}-12-31`);
    }

    const url = `${environment.api_url}/api/structure/get-organisation-by-ufcode?${params.toString()}`;

    return this.http.get<UfOrganisationResponse>(url).pipe(
      map(response => ({
        ufcode: response.ufcode,
        ufLibelle: response.ufLibelle,
        poleCode: response.poleCode,
        poleLibelle: response.poleLibelle,
        crCode: response.crCode,
        crLibelle: response.crLibelle,
        dateDebut: response.dateDebut,
        dateFin: response.dateFin,
        searchStart: response.searchStart,
        searchEnd: response.searchEnd
      } as UfOrganisation)),
      catchError(error => {
        console.error('Error fetching UF organisation:', error);
        return of(null);
      })
    );
  }

  /**
   * Formate une date en string YYYY-MM-DD
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }
}
