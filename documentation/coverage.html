<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="./images/favicon.ico">
	   <link rel="stylesheet" href="./styles/style.css">
        <link rel="stylesheet" href="./styles/dark.css">
        <link rel="stylesheet" href="./styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="./" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content coverage">
                   <div class="content-data">



















<ol class="breadcrumb">
    <li class="breadcrumb-item">Documentation coverage</li>
</ol>

<div>
    <img src="./images/coverage-badge-documentation.svg">
</div>

<table class="table table-bordered coverage" id="coverage-table">
    <thead class="coverage-header">
        <tr>
            <th>File</th>
            <th>Type</th>
            <th>Identifier</th>
            <th style="text-align:right" class="statements" data-sort-default>Statements</th>
        </tr>
    </thead>
    <tbody>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/SingleActiviteComponent.html">src/app/activite/components/single-activite/single-activite.component.ts</a>
            </td>
            <td>component</td>
            <td>SingleActiviteComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/AppComponent.html">src/app/app.component.ts</a>
            </td>
            <td>component</td>
            <td>AppComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="medium">
            <td>
                <!-- miscellaneous -->
                <a href="./components/HeaderComponent.html">src/app/core/components/header/header.component.ts</a>
            </td>
            <td>component</td>
            <td>HeaderComponent</td>
            <td align="right" data-sort="33">
                <span class="coverage-percent">33 %</span>
                <span class="coverage-count">(2/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/SidebarComponent.html">src/app/core/components/sidebar/sidebar.component.ts</a>
            </td>
            <td>component</td>
            <td>SidebarComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/8)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/AffectationPraticienComponent.html">src/app/graphical/components/affectation-praticien/affectation-praticien.component.ts</a>
            </td>
            <td>component</td>
            <td>AffectationPraticienComponent</td>
            <td align="right" data-sort="2">
                <span class="coverage-percent">2 %</span>
                <span class="coverage-count">(1/36)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/AnalyticsDashboardComponent.html">src/app/graphical/components/analytics-dashboard/analytics-dashboard.component.ts</a>
            </td>
            <td>component</td>
            <td>AnalyticsDashboardComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/CcamGraphComponent.html">src/app/graphical/components/ccam-graph/ccam-graph.component.ts</a>
            </td>
            <td>component</td>
            <td>CcamGraphComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/11)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/DashboardComponent.html">src/app/graphical/components/dashboard/dashboard.component.ts</a>
            </td>
            <td>component</td>
            <td>DashboardComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/27)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/NgapGraphComponent.html">src/app/graphical/components/ngap-graph/ngap-graph.component.ts</a>
            </td>
            <td>component</td>
            <td>NgapGraphComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/17)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/ProfilePictureComponent.html">src/app/graphical/components/profile-picture/profile-picture.component.ts</a>
            </td>
            <td>component</td>
            <td>ProfilePictureComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/12)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/DepartementComponent.html">src/app/organization/components/departement/departement.component.ts</a>
            </td>
            <td>component</td>
            <td>DepartementComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/HierarchyTreeComponent.html">src/app/organization/components/hierarchy-tree/hierarchy-tree.component.ts</a>
            </td>
            <td>component</td>
            <td>HierarchyTreeComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/InformationPanelComponent.html">src/app/organization/components/information-panel/information-panel.component.ts</a>
            </td>
            <td>component</td>
            <td>InformationPanelComponent</td>
            <td align="right" data-sort="1">
                <span class="coverage-percent">1 %</span>
                <span class="coverage-count">(1/63)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/ActeSummary.html">src/app/organization/components/information-panel/information-panel.component.ts</a>
            </td>
            <td>interface</td>
            <td>ActeSummary</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./interfaces/PracticienActeSummary.html">src/app/organization/components/information-panel/information-panel.component.ts</a>
            </td>
            <td>interface</td>
            <td>PracticienActeSummary</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/PoleComponent.html">src/app/organization/components/pole/pole-list.component.ts</a>
            </td>
            <td>component</td>
            <td>PoleListComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/4)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/ServiceHospitalierComponent.html">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts</a>
            </td>
            <td>component</td>
            <td>ServiceHospitalierComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/UfComponent.html">src/app/organization/components/uf/uf.component.ts</a>
            </td>
            <td>component</td>
            <td>UfComponent</td>
            <td align="right" data-sort="11">
                <span class="coverage-percent">11 %</span>
                <span class="coverage-count">(1/9)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/ActeDetailsComponent.html">src/app/pages/acte-details/acte-details.component.ts</a>
            </td>
            <td>component</td>
            <td>ActeDetailsComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/27)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/BreadcrumbComponent.html">src/app/pages/breadcrumb/breadcrumb.component.ts</a>
            </td>
            <td>component</td>
            <td>BreadcrumbComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/EnseignementComponent.html">src/app/pages/enseignement/enseignement.component.ts</a>
            </td>
            <td>component</td>
            <td>EnseignementComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/19)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/GardeAstreinteComponent.html">src/app/pages/garde-astreinte/garde-astreinte.component.ts</a>
            </td>
            <td>component</td>
            <td>GardeAstreinteComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/20)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/HomeComponent.html">src/app/pages/home/<USER>/a>
            </td>
            <td>component</td>
            <td>HomeComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/2)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/NotFoundComponent.html">src/app/pages/not-found/not-found.component.ts</a>
            </td>
            <td>component</td>
            <td>NotFoundComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/OverviewComponent.html">src/app/pages/overview/overview.component.ts</a>
            </td>
            <td>component</td>
            <td>OverviewComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/3)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/ResumeProfilsComponent.html">src/app/pages/resume-profils/resume-profils.component.ts</a>
            </td>
            <td>component</td>
            <td>ResumeProfilsComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/1)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/SigapsComponent.html">src/app/pages/sigaps/sigaps.component.ts</a>
            </td>
            <td>component</td>
            <td>SigapsComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/20)</span>
            </td>
        </tr>
        <tr class="very-good">
            <td>
                <!-- miscellaneous -->
                <a href="./components/ButtonComponent.html">src/stories/button.component.ts</a>
            </td>
            <td>component</td>
            <td>ButtonComponent</td>
            <td align="right" data-sort="83">
                <span class="coverage-percent">83 %</span>
                <span class="coverage-count">(5/6)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/HeaderComponent.html">src/stories/header.component.ts</a>
            </td>
            <td>component</td>
            <td>HeaderComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
        <tr class="low">
            <td>
                <!-- miscellaneous -->
                <a href="./components/PageComponent.html">src/stories/page.component.ts</a>
            </td>
            <td>component</td>
            <td>PageComponent</td>
            <td align="right" data-sort="0">
                <span class="coverage-percent">0 %</span>
                <span class="coverage-count">(0/5)</span>
            </td>
        </tr>
    </tbody>
</table>

<script src="js/libs/tablesort.min.js"></script>
<script src="js/libs/tablesort.number.min.js"></script>
<script>
    new Tablesort(document.getElementById('coverage-table'));
</script>

                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 0;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'coverage';
            var COMPODOC_CURRENT_PAGE_URL = 'coverage.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="./js/libs/custom-elements.min.js"></script>
       <script src="./js/libs/lit-html.js"></script>

       <script src="./js/menu-wc.js" defer></script>
       <script nomodule src="./js/menu-wc_es5.js" defer></script>

       <script src="./js/libs/bootstrap-native.js"></script>

       <script src="./js/libs/es6-shim.min.js"></script>
       <script src="./js/libs/EventDispatcher.js"></script>
       <script src="./js/libs/promise.min.js"></script>

       <script src="./js/compodoc.js"></script>

       <script src="./js/tabs.js"></script>
       <script src="./js/menu.js"></script>
       <script src="./js/libs/clipboard.min.js"></script>
       <script src="./js/libs/prism.js"></script>
       <script src="./js/sourceCode.js"></script>
          <script src="./js/search/search.js"></script>
          <script src="./js/search/lunr.min.js"></script>
          <script src="./js/search/search-lunr.js"></script>
          <script src="./js/search/search_index.js"></script>
       <script src="./js/lazy-load-graphs.js"></script>


    </body>
</html>
