import { UFModel } from '../structure/UFModel';

export interface ActiviteModel {
  '@id': string;
  '@type': string;
  code: string;
  description: string;
  activite: string;
  activiteLib: string;
  dateRealisation: string;
  typeActe: string;
  nombreDeRealisation: number;
  annee: number;
  mois: number;
  agent: AgentModel;
  internum: string;
  semaineIso: number;
  typeVenue: number;
  libTypeVenue: string;
  ufPrincipal: UFModel;
  ufDemande: UFModel;
  ufIntervention: UFModel;
  icrA: number;
  coefficient: number;
  lettreCoef: string;
  validFrom: string;
  validTo: string;
  periodeType: string;
  source: string | null;
  isActif: boolean;
  dateCreation: string;
}

export interface AgentModel {
  '@id': string;
  '@type': string;
  email: string;
  nom: string;
  prenom: string;
  matricule: string;
  hrUser: string;
  titre: string;
  categorie: string;
  etablissement: string;
  dateDepart: string | null;
  dateVenue: string;
  dateMaj: string;
  createurFiche: string;
  isAdmin: boolean;
  isAnesthesiste: boolean;
  fullName: string;
  imgUrl: string;
  badge: string;
  roles: string[];
  hopital: HopitalModel;
  affectations: AffectationModel[];
  isActif: boolean;
  dateCreation: string;
}

export interface HopitalModel {
  '@id': string;
  '@type': string;
  nom: string;
  code: string;
  adresse: string;
  telephone: string;
  email: string;
  dateCreation: string;
  tenant: string;
  parametre: ParametreModel;
  isActif: boolean;
}

export interface ParametreModel {
  addresses: {
    boAgentFilesPath: string;
    reportsPath: string;
  };
  theme: {
    primaryColor: string;
    secondaryColor: string;
    logoUrl: string;
  };
  notifications: {
    adminEmail: string;
  };
  featureFlags: {
    enableNewUi: boolean;
    betaReporting: boolean;
  };
  apiEndpoints: {
    dataIntegratorApi: string;
    backendUrl: string;
  };
  allowedEmailDomains: string[];
}

export interface AffectationModel {
  '@id': string;
  '@type': string;
  dateDebut: string;
  dateFin: string;
  rgt: string;
  etpStatutaire: number;
  tauxAffectation: number;
  etp: number;
  affectationPrincipale: string;
  typeGrade: string;
  libelleGrade: string;
  absences: number;
  validFrom: string;
  validTo: string | null;
  periodeType: string;
  source: string;
  hrUser: string | null;
  matricule: string | null;
  email: string | null;
  codeUf: string | null;
  agent: string | null;
  ufs: string;
  isActif: boolean;
  dateCreation: string;
}
