/**
 * Interface pour les entités de recherche
 */
export interface SearchEntity {
  id: string;
  type: 'praticien' | 'pole' | 'service' | 'ufs';
  nom: string;
  prenom?: string;
  specialite?: string;
  matricule?: string;
  extra?: string;
  fullName?: string;
  keywords?: string[];
}

/**
 * Interface pour les résultats de recherche Fuse.js
 */
export interface FuseSearchResult {
  item: SearchEntity;
  score?: number;
  matches?: any[];
}

/**
 * Interface pour la réponse de recherche
 */
export interface SearchResponse {
  results: SearchEntity[];
  total_results: number;
}

/**
 * Type pour les types d'entités
 */
export type EntityType = 'praticien' | 'pole' | 'service' | 'ufs';

/**
 * Configuration pour les icônes par type
 */
export const ENTITY_TYPE_CONFIG = {
  praticien: {
    icon: 'user',
    color: '#3b82f6', // Bleu moderne
    label: 'Praticien'
  },
  pole: {
    icon: 'building',
    color: '#10b981', // Vert émeraude
    label: 'Pôle'
  },
  service: {
    icon: 'cog',
    color: '#8b5cf6', // Violet moderne
    label: 'Service'
  },
  ufs: {
    icon: 'folder',
    color: '#f59e0b', // Orange moderne
    label: 'Unité Fonctionnelle'
  }
} as const;
