// From d3-flextree
!function(){function n(n){return n&&(n.ownerDocument||n.document||n).documentElement}function t(n){return n&&(n.ownerDocument&&n.ownerDocument.defaultView||n.document&&n||n.defaultView)}function e(n,t){return n<t?-1:n>t?1:n>=t?0:NaN}function r(n){return null===n?NaN:+n}function i(n){return!isNaN(n)}function u(n){return{left:function(t,e,r,i){for(arguments.length<3&&(r=0),arguments.length<4&&(i=t.length);r<i;){var u=r+i>>>1;n(t[u],e)<0?r=u+1:i=u}return r},right:function(t,e,r,i){for(arguments.length<3&&(r=0),arguments.length<4&&(i=t.length);r<i;){var u=r+i>>>1;n(t[u],e)>0?i=u:r=u+1}return r}}}function o(n){return n.length}function a(n){for(var t=1;n*t%1;)t*=10;return t}function l(n,t){for(var e in t)Object.defineProperty(n.prototype,e,{value:t[e],enumerable:!1})}function c(){this._=Object.create(null)}function f(n){return(n+="")===ho||n[0]===po?po+n:n}function s(n){return(n+="")[0]===po?n.slice(1):n}function h(n){return f(n)in this._}function p(n){return(n=f(n))in this._&&delete this._[n]}function g(){var n=[];for(var t in this._)n.push(s(t));return n}function d(){var n=0;for(var t in this._)++n;return n}function v(){for(var n in this._)return!1;return!0}function y(){this._=Object.create(null)}function m(n){return n}function x(n,t,e){return function(){var r=e.apply(t,arguments);return r===t?n:r}}function M(n,t){if(t in n)return t;t=t.charAt(0).toUpperCase()+t.slice(1);for(var e=0,r=go.length;e<r;++e){var i=go[e]+t;if(i in n)return i}}function _(){}function b(){}function w(n){function t(){for(var t,r=e,i=-1,u=r.length;++i<u;)(t=r[i].on)&&t.apply(this,arguments);return n}var e=[],r=new c;return t.on=function(t,i){var u,o=r.get(t);return arguments.length<2?o&&o.on:(o&&(o.on=null,e=e.slice(0,u=e.indexOf(o)).concat(e.slice(u+1)),r.remove(t)),i&&e.push(r.set(t,{on:i})),n)},t}function S(){to.event.preventDefault()}function k(){for(var n,t=to.event;n=t.sourceEvent;)t=n;return t}function N(n){for(var t=new b,e=0,r=arguments.length;++e<r;)t[arguments[e]]=w(t);return t.of=function(e,r){return function(i){try{var u=i.sourceEvent=to.event;i.target=n,to.event=i,t[i.type].apply(e,r)}finally{to.event=u}}},t}function E(n){return yo(n,_o),n}function A(n){return"function"==typeof n?n:function(){return mo(n,this)}}function C(n){return"function"==typeof n?n:function(){return xo(n,this)}}function z(n,t){function e(){this.removeAttribute(n)}function r(){this.removeAttributeNS(n.space,n.local)}function i(){this.setAttribute(n,t)}function u(){this.setAttributeNS(n.space,n.local,t)}function o(){var e=t.apply(this,arguments);null==e?this.removeAttribute(n):this.setAttribute(n,e)}function a(){var e=t.apply(this,arguments);null==e?this.removeAttributeNS(n.space,n.local):this.setAttributeNS(n.space,n.local,e)}return n=to.ns.qualify(n),null==t?n.local?r:e:"function"==typeof t?n.local?a:o:n.local?u:i}function L(n){return n.trim().replace(/\s+/g," ")}function q(n){return new RegExp("(?:^|\\s+)"+to.requote(n)+"(?:\\s+|$)","g")}function T(n){return(n+"").trim().split(/^|\s+/)}function R(n,t){function e(){for(var e=-1;++e<i;)n[e](this,t)}function r(){for(var e=-1,r=t.apply(this,arguments);++e<i;)n[e](this,r)}var i=(n=T(n).map(D)).length;return"function"==typeof t?r:e}function D(n){var t=q(n);return function(e,r){if(i=e.classList)return r?i.add(n):i.remove(n);var i=e.getAttribute("class")||"";r?(t.lastIndex=0,t.test(i)||e.setAttribute("class",L(i+" "+n))):e.setAttribute("class",L(i.replace(t," ")))}}function P(n,t,e){function r(){this.style.removeProperty(n)}function i(){this.style.setProperty(n,t,e)}function u(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(n):this.style.setProperty(n,r,e)}return null==t?r:"function"==typeof t?u:i}function j(n,t){function e(){delete this[n]}function r(){this[n]=t}function i(){var e=t.apply(this,arguments);null==e?delete this[n]:this[n]=e}return null==t?e:"function"==typeof t?i:r}function U(n){function t(){var t=this.ownerDocument,e=this.namespaceURI;return e===bo&&t.documentElement.namespaceURI===bo?t.createElement(n):t.createElementNS(e,n)}function e(){return this.ownerDocument.createElementNS(n.space,n.local)}return"function"==typeof n?n:(n=to.ns.qualify(n)).local?e:t}function F(){var n=this.parentNode;n&&n.removeChild(this)}function H(n){return{__data__:n}}function O(n){return function(){return Mo(this,n)}}function Y(n){return arguments.length||(n=e),function(t,e){return t&&e?n(t.__data__,e.__data__):!t-!e}}function I(n,t){for(var e=0,r=n.length;e<r;e++)for(var i,u=n[e],o=0,a=u.length;o<a;o++)(i=u[o])&&t(i,o,e);return n}function Z(n){return yo(n,So),n}function V(n){var t,e;return function(r,i,u){var o,a=n[u].update,l=a.length;for(u!=e&&(e=u,t=0),i>=t&&(t=i+1);!(o=a[t])&&++t<l;);return o}}function X(n,t,e){function r(){var t=this[o];t&&(this.removeEventListener(n,t,t.$),delete this[o])}function i(){var i=l(t,ro(arguments));r.call(this),this.addEventListener(n,this[o]=i,i.$=e),i._=t}function u(){var t,e=new RegExp("^__on([^.]+)"+to.requote(n)+"$");for(var r in this)if(t=r.match(e)){var i=this[r];this.removeEventListener(t[1],i,i.$),delete this[r]}}var o="__on"+n,a=n.indexOf("."),l=$;a>0&&(n=n.slice(0,a));var c=ko.get(n);return c&&(n=c,l=B),a?t?i:r:t?_:u}function $(n,t){return function(e){var r=to.event;to.event=e,t[0]=this.__data__;try{n.apply(this,t)}finally{to.event=r}}}function B(n,t){var e=$(n,t);return function(n){var t=this,r=n.relatedTarget;r&&(r===t||8&r.compareDocumentPosition(t))||e.call(t,n)}}function W(e){var r=".dragsuppress-"+ ++Eo,i="click"+r,u=to.select(t(e)).on("touchmove"+r,S).on("dragstart"+r,S).on("selectstart"+r,S);if(null==No&&(No=!("onselectstart"in e)&&M(e.style,"userSelect")),No){var o=n(e).style,a=o[No];o[No]="none"}return function(n){if(u.on(r,null),No&&(o[No]=a),n){var t=function(){u.on(i,null)};u.on(i,function(){S(),t()},!0),setTimeout(t,0)}}}function J(n,e){e.changedTouches&&(e=e.changedTouches[0]);var r=n.ownerSVGElement||n;if(r.createSVGPoint){var i=r.createSVGPoint();if(Ao<0){var u=t(n);if(u.scrollX||u.scrollY){var o=(r=to.select("body").append("svg").style({position:"absolute",top:0,left:0,margin:0,padding:0,border:"none"},"important"))[0][0].getScreenCTM();Ao=!(o.f||o.e),r.remove()}}return Ao?(i.x=e.pageX,i.y=e.pageY):(i.x=e.clientX,i.y=e.clientY),i=i.matrixTransform(n.getScreenCTM().inverse()),[i.x,i.y]}var a=n.getBoundingClientRect();return[e.clientX-a.left-n.clientLeft,e.clientY-a.top-n.clientTop]}function G(){return to.event.changedTouches[0].identifier}function K(n){return n>0?1:n<0?-1:0}function Q(n,t,e){return(t[0]-n[0])*(e[1]-n[1])-(t[1]-n[1])*(e[0]-n[0])}function nn(n){return n>1?0:n<-1?Lo:Math.acos(n)}function tn(n){return n>1?Ro:n<-1?-Ro:Math.asin(n)}function en(n){return((n=Math.exp(n))-1/n)/2}function rn(n){return((n=Math.exp(n))+1/n)/2}function un(n){return((n=Math.exp(2*n))-1)/(n+1)}function on(n){return(n=Math.sin(n/2))*n}function an(){}function ln(n,t,e){return this instanceof ln?(this.h=+n,this.s=+t,void(this.l=+e)):arguments.length<2?n instanceof ln?new ln(n.h,n.s,n.l):bn(""+n,wn,ln):new ln(n,t,e)}function cn(n,t,e){function r(n){return n>360?n-=360:n<0&&(n+=360),n<60?u+(o-u)*n/60:n<180?o:n<240?u+(o-u)*(240-n)/60:u}function i(n){return Math.round(255*r(n))}var u,o;return n=isNaN(n)?0:(n%=360)<0?n+360:n,t=isNaN(t)?0:t<0?0:t>1?1:t,e=e<0?0:e>1?1:e,o=e<=.5?e*(1+t):e+t-e*t,u=2*e-o,new mn(i(n+120),i(n),i(n-120))}function fn(n,t,e){return this instanceof fn?(this.h=+n,this.c=+t,void(this.l=+e)):arguments.length<2?n instanceof fn?new fn(n.h,n.c,n.l):n instanceof hn?gn(n.l,n.a,n.b):gn((n=Sn((n=to.rgb(n)).r,n.g,n.b)).l,n.a,n.b):new fn(n,t,e)}function sn(n,t,e){return isNaN(n)&&(n=0),isNaN(t)&&(t=0),new hn(e,Math.cos(n*=Do)*t,Math.sin(n)*t)}function hn(n,t,e){return this instanceof hn?(this.l=+n,this.a=+t,void(this.b=+e)):arguments.length<2?n instanceof hn?new hn(n.l,n.a,n.b):n instanceof fn?sn(n.h,n.c,n.l):Sn((n=mn(n)).r,n.g,n.b):new hn(n,t,e)}function pn(n,t,e){var r=(n+16)/116,i=r+t/500,u=r-e/200;return i=dn(i)*Zo,r=dn(r)*Vo,u=dn(u)*Xo,new mn(yn(3.2404542*i-1.5371385*r-.4985314*u),yn(-.969266*i+1.8760108*r+.041556*u),yn(.0556434*i-.2040259*r+1.0572252*u))}function gn(n,t,e){return n>0?new fn(Math.atan2(e,t)*Po,Math.sqrt(t*t+e*e),n):new fn(NaN,NaN,n)}function dn(n){return n>.206893034?n*n*n:(n-4/29)/7.787037}function vn(n){return n>.008856?Math.pow(n,1/3):7.787037*n+4/29}function yn(n){return Math.round(255*(n<=.00304?12.92*n:1.055*Math.pow(n,1/2.4)-.055))}function mn(n,t,e){return this instanceof mn?(this.r=~~n,this.g=~~t,void(this.b=~~e)):arguments.length<2?n instanceof mn?new mn(n.r,n.g,n.b):bn(""+n,mn,cn):new mn(n,t,e)}function xn(n){return new mn(n>>16,n>>8&255,255&n)}function Mn(n){return xn(n)+""}function _n(n){return n<16?"0"+Math.max(0,n).toString(16):Math.min(255,n).toString(16)}function bn(n,t,e){var r,i,u,o=0,a=0,l=0;if(r=/([a-z]+)\((.*)\)/.exec(n=n.toLowerCase()))switch(i=r[2].split(","),r[1]){case"hsl":return e(parseFloat(i[0]),parseFloat(i[1])/100,parseFloat(i[2])/100);case"rgb":return t(Nn(i[0]),Nn(i[1]),Nn(i[2]))}return(u=Wo.get(n))?t(u.r,u.g,u.b):(null==n||"#"!==n.charAt(0)||isNaN(u=parseInt(n.slice(1),16))||(4===n.length?(o=(3840&u)>>4,o|=o>>4,a=240&u,a|=a>>4,l=15&u,l|=l<<4):7===n.length&&(o=(16711680&u)>>16,a=(65280&u)>>8,l=255&u)),t(o,a,l))}function wn(n,t,e){var r,i,u=Math.min(n/=255,t/=255,e/=255),o=Math.max(n,t,e),a=o-u,l=(o+u)/2;return a?(i=l<.5?a/(o+u):a/(2-o-u),r=n==o?(t-e)/a+(t<e?6:0):t==o?(e-n)/a+2:(n-t)/a+4,r*=60):(r=NaN,i=l>0&&l<1?0:r),new ln(r,i,l)}function Sn(n,t,e){var r=vn((.4124564*(n=kn(n))+.3575761*(t=kn(t))+.1804375*(e=kn(e)))/Zo),i=vn((.2126729*n+.7151522*t+.072175*e)/Vo);return hn(116*i-16,500*(r-i),200*(i-vn((.0193339*n+.119192*t+.9503041*e)/Xo)))}function kn(n){return(n/=255)<=.04045?n/12.92:Math.pow((n+.055)/1.055,2.4)}function Nn(n){var t=parseFloat(n);return"%"===n.charAt(n.length-1)?Math.round(2.55*t):t}function En(n){return"function"==typeof n?n:function(){return n}}function An(n){return function(t,e,r){return 2===arguments.length&&"function"==typeof e&&(r=e,e=null),Cn(t,e,n,r)}}function Cn(n,t,e,r){function i(){var n,t=l.status;if(!t&&Ln(l)||t>=200&&t<300||304===t){try{n=e.call(u,l)}catch(n){return void o.error.call(u,n)}o.load.call(u,n)}else o.error.call(u,l)}var u={},o=to.dispatch("beforesend","progress","load","error"),a={},l=new XMLHttpRequest,c=null;return!this.XDomainRequest||"withCredentials"in l||!/^(http(s)?:)?\/\//.test(n)||(l=new XDomainRequest),"onload"in l?l.onload=l.onerror=i:l.onreadystatechange=function(){l.readyState>3&&i()},l.onprogress=function(n){var t=to.event;to.event=n;try{o.progress.call(u,l)}finally{to.event=t}},u.header=function(n,t){return n=(n+"").toLowerCase(),arguments.length<2?a[n]:(null==t?delete a[n]:a[n]=t+"",u)},u.mimeType=function(n){return arguments.length?(t=null==n?null:n+"",u):t},u.responseType=function(n){return arguments.length?(c=n,u):c},u.response=function(n){return e=n,u},["get","post"].forEach(function(n){u[n]=function(){return u.send.apply(u,[n].concat(ro(arguments)))}}),u.send=function(e,r,i){if(2===arguments.length&&"function"==typeof r&&(i=r,r=null),l.open(e,n,!0),null==t||"accept"in a||(a.accept=t+",*/*"),l.setRequestHeader)for(var f in a)l.setRequestHeader(f,a[f]);return null!=t&&l.overrideMimeType&&l.overrideMimeType(t),null!=c&&(l.responseType=c),null!=i&&u.on("error",i).on("load",function(n){i(null,n)}),o.beforesend.call(u,l),l.send(null==r?null:r),u},u.abort=function(){return l.abort(),u},to.rebind(u,o,"on"),null==r?u:u.get(zn(r))}function zn(n){return 1===n.length?function(t,e){n(null==t?e:null)}:n}function Ln(n){var t=n.responseType;return t&&"text"!==t?n.response:n.responseText}function qn(n,t,e){var r=arguments.length;r<2&&(t=0),r<3&&(e=Date.now());var i={c:n,t:e+t,n:null};return Go?Go.n=i:Jo=i,Go=i,Ko||(Qo=clearTimeout(Qo),Ko=1,na(Tn)),i}function Tn(){var n=Rn(),t=Dn()-n;t>24?(isFinite(t)&&(clearTimeout(Qo),Qo=setTimeout(Tn,t)),Ko=0):(Ko=1,na(Tn))}function Rn(){for(var n=Date.now(),t=Jo;t;)n>=t.t&&t.c(n-t.t)&&(t.c=null),t=t.n;return n}function Dn(){for(var n,t=Jo,e=1/0;t;)t.c?(t.t<e&&(e=t.t),t=(n=t).n):t=n?n.n=t.n:Jo=t.n;return Go=n,e}function Pn(n,t){return t-(n?Math.ceil(Math.log(n)/Math.LN10):1)}function jn(n,t){var e=Math.pow(10,3*so(8-t));return{scale:t>8?function(n){return n/e}:function(n){return n*e},symbol:n}}function Un(n){var t=n.decimal,e=n.thousands,r=n.grouping,i=n.currency,u=r&&e?function(n,t){for(var i=n.length,u=[],o=0,a=r[0],l=0;i>0&&a>0&&(l+a+1>t&&(a=Math.max(1,t-l)),u.push(n.substring(i-=a,i+a)),!((l+=a+1)>t));)a=r[o=(o+1)%r.length];return u.reverse().join(e)}:m;return function(n){var e=ea.exec(n),r=e[1]||" ",o=e[2]||">",a=e[3]||"-",l=e[4]||"",c=e[5],f=+e[6],s=e[7],h=e[8],p=e[9],g=1,d="",v="",y=!1,m=!0;switch(h&&(h=+h.substring(1)),(c||"0"===r&&"="===o)&&(c=r="0",o="="),p){case"n":s=!0,p="g";break;case"%":g=100,v="%",p="f";break;case"p":g=100,v="%",p="r";break;case"b":case"o":case"x":case"X":"#"===l&&(d="0"+p.toLowerCase());case"c":m=!1;case"d":y=!0,h=0;break;case"s":g=-1,p="r"}"$"===l&&(d=i[0],v=i[1]),"r"!=p||h||(p="g"),null!=h&&("g"==p?h=Math.max(1,Math.min(21,h)):"e"!=p&&"f"!=p||(h=Math.max(0,Math.min(20,h)))),p=ra.get(p)||Fn;var x=c&&s;return function(n){var e=v;if(y&&n%1)return"";var i=n<0||0===n&&1/n<0?(n=-n,"-"):"-"===a?"":a;if(g<0){var l=to.formatPrefix(n,h);n=l.scale(n),e=l.symbol+v}else n*=g;var M,_,b=(n=p(n,h)).lastIndexOf(".");if(b<0){var w=m?n.lastIndexOf("e"):-1;w<0?(M=n,_=""):(M=n.substring(0,w),_=n.substring(w))}else M=n.substring(0,b),_=t+n.substring(b+1);!c&&s&&(M=u(M,1/0));var S=d.length+M.length+_.length+(x?0:i.length),k=S<f?new Array(S=f-S+1).join(r):"";return x&&(M=u(k+M,k.length?f-_.length:1/0)),i+=d,n=M+_,("<"===o?i+n+k:">"===o?k+i+n:"^"===o?k.substring(0,S>>=1)+i+n+k.substring(S):i+(x?n:k+n))+e}}}function Fn(n){return n+""}function Hn(){this._=new Date(arguments.length>1?Date.UTC.apply(this,arguments):arguments[0])}function On(n,t,e){function r(t){var e=n(t),r=u(e,1);return t-e<r-t?e:r}function i(e){return t(e=n(new ua(e-1)),1),e}function u(n,e){return t(n=new ua(+n),e),n}function o(n,r,u){var o=i(n),a=[];if(u>1)for(;o<r;)e(o)%u||a.push(new Date(+o)),t(o,1);else for(;o<r;)a.push(new Date(+o)),t(o,1);return a}function a(n,t,e){try{ua=Hn;var r=new Hn;return r._=n,o(r,t,e)}finally{ua=Date}}n.floor=n,n.round=r,n.ceil=i,n.offset=u,n.range=o;var l=n.utc=Yn(n);return l.floor=l,l.round=Yn(r),l.ceil=Yn(i),l.offset=Yn(u),l.range=a,n}function Yn(n){return function(t,e){try{ua=Hn;var r=new Hn;return r._=t,n(r,e)._}finally{ua=Date}}}function In(n){function t(n){function t(t){for(var e,i,u,o=[],a=-1,l=0;++a<r;)37===n.charCodeAt(a)&&(o.push(n.slice(l,a)),null!=(i=aa[e=n.charAt(++a)])&&(e=n.charAt(++a)),(u=A[e])&&(e=u(t,null==i?"e"===e?" ":"0":i)),o.push(e),l=a+1);return o.push(n.slice(l,a)),o.join("")}var r=n.length;return t.parse=function(t){var r={y:1900,m:0,d:1,H:0,M:0,S:0,L:0,Z:null};if(e(r,n,t,0)!=t.length)return null;"p"in r&&(r.H=r.H%12+12*r.p);var i=null!=r.Z&&ua!==Hn,u=new(i?Hn:ua);return"j"in r?u.setFullYear(r.y,0,r.j):"W"in r||"U"in r?("w"in r||(r.w="W"in r?1:0),u.setFullYear(r.y,0,1),u.setFullYear(r.y,0,"W"in r?(r.w+6)%7+7*r.W-(u.getDay()+5)%7:r.w+7*r.U-(u.getDay()+6)%7)):u.setFullYear(r.y,r.m,r.d),u.setHours(r.H+(r.Z/100|0),r.M+r.Z%100,r.S,r.L),i?u._:u},t.toString=function(){return n},t}function e(n,t,e,r){for(var i,u,o,a=0,l=t.length,c=e.length;a<l;){if(r>=c)return-1;if(37===(i=t.charCodeAt(a++))){if(o=t.charAt(a++),!(u=C[o in aa?t.charAt(a++):o])||(r=u(n,e,r))<0)return-1}else if(i!=e.charCodeAt(r++))return-1}return r}function r(n,t,e){b.lastIndex=0;var r=b.exec(t.slice(e));return r?(n.w=w.get(r[0].toLowerCase()),e+r[0].length):-1}function i(n,t,e){M.lastIndex=0;var r=M.exec(t.slice(e));return r?(n.w=_.get(r[0].toLowerCase()),e+r[0].length):-1}function u(n,t,e){N.lastIndex=0;var r=N.exec(t.slice(e));return r?(n.m=E.get(r[0].toLowerCase()),e+r[0].length):-1}function o(n,t,e){S.lastIndex=0;var r=S.exec(t.slice(e));return r?(n.m=k.get(r[0].toLowerCase()),e+r[0].length):-1}function a(n,t,r){return e(n,A.c.toString(),t,r)}function l(n,t,r){return e(n,A.x.toString(),t,r)}function c(n,t,r){return e(n,A.X.toString(),t,r)}function f(n,t,e){var r=x.get(t.slice(e,e+=2).toLowerCase());return null==r?-1:(n.p=r,e)}var s=n.dateTime,h=n.date,p=n.time,g=n.periods,d=n.days,v=n.shortDays,y=n.months,m=n.shortMonths;t.utc=function(n){function e(n){try{var t=new(ua=Hn);return t._=n,r(t)}finally{ua=Date}}var r=t(n);return e.parse=function(n){try{ua=Hn;var t=r.parse(n);return t&&t._}finally{ua=Date}},e.toString=r.toString,e},t.multi=t.utc.multi=ct;var x=to.map(),M=Vn(d),_=Xn(d),b=Vn(v),w=Xn(v),S=Vn(y),k=Xn(y),N=Vn(m),E=Xn(m);g.forEach(function(n,t){x.set(n.toLowerCase(),t)});var A={a:function(n){return v[n.getDay()]},A:function(n){return d[n.getDay()]},b:function(n){return m[n.getMonth()]},B:function(n){return y[n.getMonth()]},c:t(s),d:function(n,t){return Zn(n.getDate(),t,2)},e:function(n,t){return Zn(n.getDate(),t,2)},H:function(n,t){return Zn(n.getHours(),t,2)},I:function(n,t){return Zn(n.getHours()%12||12,t,2)},j:function(n,t){return Zn(1+ia.dayOfYear(n),t,3)},L:function(n,t){return Zn(n.getMilliseconds(),t,3)},m:function(n,t){return Zn(n.getMonth()+1,t,2)},M:function(n,t){return Zn(n.getMinutes(),t,2)},p:function(n){return g[+(n.getHours()>=12)]},S:function(n,t){return Zn(n.getSeconds(),t,2)},U:function(n,t){return Zn(ia.sundayOfYear(n),t,2)},w:function(n){return n.getDay()},W:function(n,t){return Zn(ia.mondayOfYear(n),t,2)},x:t(h),X:t(p),y:function(n,t){return Zn(n.getFullYear()%100,t,2)},Y:function(n,t){return Zn(n.getFullYear()%1e4,t,4)},Z:at,"%":function(){return"%"}},C={a:r,A:i,b:u,B:o,c:a,d:tt,e:tt,H:rt,I:rt,j:et,L:ot,m:nt,M:it,p:f,S:ut,U:Bn,w:$n,W:Wn,x:l,X:c,y:Gn,Y:Jn,Z:Kn,"%":lt};return t}function Zn(n,t,e){var r=n<0?"-":"",i=(r?-n:n)+"",u=i.length;return r+(u<e?new Array(e-u+1).join(t)+i:i)}function Vn(n){return new RegExp("^(?:"+n.map(to.requote).join("|")+")","i")}function Xn(n){for(var t=new c,e=-1,r=n.length;++e<r;)t.set(n[e].toLowerCase(),e);return t}function $n(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+1));return r?(n.w=+r[0],e+r[0].length):-1}function Bn(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e));return r?(n.U=+r[0],e+r[0].length):-1}function Wn(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e));return r?(n.W=+r[0],e+r[0].length):-1}function Jn(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+4));return r?(n.y=+r[0],e+r[0].length):-1}function Gn(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+2));return r?(n.y=Qn(+r[0]),e+r[0].length):-1}function Kn(n,t,e){return/^[+-]\d{4}$/.test(t=t.slice(e,e+5))?(n.Z=-t,e+5):-1}function Qn(n){return n+(n>68?1900:2e3)}function nt(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+2));return r?(n.m=r[0]-1,e+r[0].length):-1}function tt(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+2));return r?(n.d=+r[0],e+r[0].length):-1}function et(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+3));return r?(n.j=+r[0],e+r[0].length):-1}function rt(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+2));return r?(n.H=+r[0],e+r[0].length):-1}function it(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+2));return r?(n.M=+r[0],e+r[0].length):-1}function ut(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+2));return r?(n.S=+r[0],e+r[0].length):-1}function ot(n,t,e){la.lastIndex=0;var r=la.exec(t.slice(e,e+3));return r?(n.L=+r[0],e+r[0].length):-1}function at(n){var t=n.getTimezoneOffset(),e=t>0?"-":"+",r=so(t)/60|0,i=so(t)%60;return e+Zn(r,"0",2)+Zn(i,"0",2)}function lt(n,t,e){ca.lastIndex=0;var r=ca.exec(t.slice(e,e+1));return r?e+r[0].length:-1}function ct(n){for(var t=n.length,e=-1;++e<t;)n[e][0]=this(n[e][0]);return function(t){for(var e=0,r=n[e];!r[1](t);)r=n[++e];return r[0](t)}}function ft(){}function st(n,t,e){var r=e.s=n+t,i=r-n,u=r-i;e.t=n-u+(t-i)}function ht(n,t){n&&pa.hasOwnProperty(n.type)&&pa[n.type](n,t)}function pt(n,t,e){var r,i=-1,u=n.length-e;for(t.lineStart();++i<u;)r=n[i],t.point(r[0],r[1],r[2]);t.lineEnd()}function gt(n,t){var e=-1,r=n.length;for(t.polygonStart();++e<r;)pt(n[e],t,1);t.polygonEnd()}function dt(){function n(n,t){t=t*Do/2+Lo/4;var e=(n*=Do)-r,o=e>=0?1:-1,a=o*e,l=Math.cos(t),c=Math.sin(t),f=u*c,s=i*l+f*Math.cos(a),h=f*o*Math.sin(a);da.add(Math.atan2(h,s)),r=n,i=l,u=c}var t,e,r,i,u;va.point=function(o,a){va.point=n,r=(t=o)*Do,i=Math.cos(a=(e=a)*Do/2+Lo/4),u=Math.sin(a)},va.lineEnd=function(){n(t,e)}}function vt(n){var t=n[0],e=n[1],r=Math.cos(e);return[r*Math.cos(t),r*Math.sin(t),Math.sin(e)]}function yt(n,t){return n[0]*t[0]+n[1]*t[1]+n[2]*t[2]}function mt(n,t){return[n[1]*t[2]-n[2]*t[1],n[2]*t[0]-n[0]*t[2],n[0]*t[1]-n[1]*t[0]]}function xt(n,t){n[0]+=t[0],n[1]+=t[1],n[2]+=t[2]}function Mt(n,t){return[n[0]*t,n[1]*t,n[2]*t]}function _t(n){var t=Math.sqrt(n[0]*n[0]+n[1]*n[1]+n[2]*n[2]);n[0]/=t,n[1]/=t,n[2]/=t}function bt(n){return[Math.atan2(n[1],n[0]),tn(n[2])]}function wt(n,t){return so(n[0]-t[0])<Co&&so(n[1]-t[1])<Co}function St(n,t){n*=Do;var e=Math.cos(t*=Do);kt(e*Math.cos(n),e*Math.sin(n),Math.sin(t))}function kt(n,t,e){xa+=(n-xa)/++ya,Ma+=(t-Ma)/ya,_a+=(e-_a)/ya}function Nt(){function n(n,i){n*=Do;var u=Math.cos(i*=Do),o=u*Math.cos(n),a=u*Math.sin(n),l=Math.sin(i),c=Math.atan2(Math.sqrt((c=e*l-r*a)*c+(c=r*o-t*l)*c+(c=t*a-e*o)*c),t*o+e*a+r*l);ma+=c,ba+=c*(t+(t=o)),wa+=c*(e+(e=a)),Sa+=c*(r+(r=l)),kt(t,e,r)}var t,e,r;Aa.point=function(i,u){i*=Do;var o=Math.cos(u*=Do);t=o*Math.cos(i),e=o*Math.sin(i),r=Math.sin(u),Aa.point=n,kt(t,e,r)}}function Et(){Aa.point=St}function At(){function n(n,t){n*=Do;var e=Math.cos(t*=Do),o=e*Math.cos(n),a=e*Math.sin(n),l=Math.sin(t),c=i*l-u*a,f=u*o-r*l,s=r*a-i*o,h=Math.sqrt(c*c+f*f+s*s),p=r*o+i*a+u*l,g=h&&-nn(p)/h,d=Math.atan2(h,p);ka+=g*c,Na+=g*f,Ea+=g*s,ma+=d,ba+=d*(r+(r=o)),wa+=d*(i+(i=a)),Sa+=d*(u+(u=l)),kt(r,i,u)}var t,e,r,i,u;Aa.point=function(o,a){t=o,e=a,Aa.point=n,o*=Do;var l=Math.cos(a*=Do);r=l*Math.cos(o),i=l*Math.sin(o),u=Math.sin(a),kt(r,i,u)},Aa.lineEnd=function(){n(t,e),Aa.lineEnd=Et,Aa.point=St}}function Ct(n,t){function e(e,r){return e=n(e,r),t(e[0],e[1])}return n.invert&&t.invert&&(e.invert=function(e,r){return(e=t.invert(e,r))&&n.invert(e[0],e[1])}),e}function zt(){return!0}function Lt(n,t,e,r,i){var u=[],o=[];if(n.forEach(function(n){if(!((t=n.length-1)<=0)){var t,e=n[0],r=n[t];if(wt(e,r)){i.lineStart();for(var a=0;a<t;++a)i.point((e=n[a])[0],e[1]);return void i.lineEnd()}var l=new Tt(e,n,null,!0),c=new Tt(e,null,l,!1);l.o=c,u.push(l),o.push(c),c=new Tt(r,null,l=new Tt(r,n,null,!1),!0),l.o=c,u.push(l),o.push(c)}}),o.sort(t),qt(u),qt(o),u.length){for(var a=0,l=e,c=o.length;a<c;++a)o[a].e=l=!l;for(var f,s,h=u[0];;){for(var p=h,g=!0;p.v;)if((p=p.n)===h)return;f=p.z,i.lineStart();do{if(p.v=p.o.v=!0,p.e){if(g)for(var a=0,c=f.length;a<c;++a)i.point((s=f[a])[0],s[1]);else r(p.x,p.n.x,1,i);p=p.n}else{if(g)for(a=(f=p.p.z).length-1;a>=0;--a)i.point((s=f[a])[0],s[1]);else r(p.x,p.p.x,-1,i);p=p.p}f=(p=p.o).z,g=!g}while(!p.v);i.lineEnd()}}}function qt(n){if(t=n.length){for(var t,e,r=0,i=n[0];++r<t;)i.n=e=n[r],e.p=i,i=e;i.n=e=n[0],e.p=i}}function Tt(n,t,e,r){this.x=n,this.z=t,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function Rt(n,t,e,r){return function(i,u){function o(t,e){var r=i(t,e);n(t=r[0],e=r[1])&&u.point(t,e)}function a(n,t){var e=i(n,t);v.point(e[0],e[1])}function l(){m.point=a,v.lineStart()}function c(){m.point=o,v.lineEnd()}function f(n,t){d.push([n,t]);var e=i(n,t);M.point(e[0],e[1])}function s(){M.lineStart(),d=[]}function h(){f(d[0][0],d[0][1]),M.lineEnd();var n,t=M.clean(),e=x.buffer(),r=e.length;if(d.pop(),g.push(d),d=null,r)if(1&t){var i,o=-1;if((r=(n=e[0]).length-1)>0){for(_||(u.polygonStart(),_=!0),u.lineStart();++o<r;)u.point((i=n[o])[0],i[1]);u.lineEnd()}}else r>1&&2&t&&e.push(e.pop().concat(e.shift())),p.push(e.filter(Dt))}var p,g,d,v=t(u),y=i.invert(r[0],r[1]),m={point:o,lineStart:l,lineEnd:c,polygonStart:function(){m.point=f,m.lineStart=s,m.lineEnd=h,p=[],g=[]},polygonEnd:function(){m.point=o,m.lineStart=l,m.lineEnd=c,p=to.merge(p);var n=Ot(y,g);p.length?(_||(u.polygonStart(),_=!0),Lt(p,jt,n,e,u)):n&&(_||(u.polygonStart(),_=!0),u.lineStart(),e(null,null,1,u),u.lineEnd()),_&&(u.polygonEnd(),_=!1),p=g=null},sphere:function(){u.polygonStart(),u.lineStart(),e(null,null,1,u),u.lineEnd(),u.polygonEnd()}},x=Pt(),M=t(x),_=!1;return m}}function Dt(n){return n.length>1}function Pt(){var n,t=[];return{lineStart:function(){t.push(n=[])},point:function(t,e){n.push([t,e])},lineEnd:_,buffer:function(){var e=t;return t=[],n=null,e},rejoin:function(){t.length>1&&t.push(t.pop().concat(t.shift()))}}}function jt(n,t){return((n=n.x)[0]<0?n[1]-Ro-Co:Ro-n[1])-((t=t.x)[0]<0?t[1]-Ro-Co:Ro-t[1])}function Ut(n){var t,e=NaN,r=NaN,i=NaN;return{lineStart:function(){n.lineStart(),t=1},point:function(u,o){var a=u>0?Lo:-Lo,l=so(u-e);so(l-Lo)<Co?(n.point(e,r=(r+o)/2>0?Ro:-Ro),n.point(i,r),n.lineEnd(),n.lineStart(),n.point(a,r),n.point(u,r),t=0):i!==a&&l>=Lo&&(so(e-i)<Co&&(e-=i*Co),so(u-a)<Co&&(u-=a*Co),r=Ft(e,r,u,o),n.point(i,r),n.lineEnd(),n.lineStart(),n.point(a,r),t=0),n.point(e=u,r=o),i=a},lineEnd:function(){n.lineEnd(),e=r=NaN},clean:function(){return 2-t}}}function Ft(n,t,e,r){var i,u,o=Math.sin(n-e);return so(o)>Co?Math.atan((Math.sin(t)*(u=Math.cos(r))*Math.sin(e)-Math.sin(r)*(i=Math.cos(t))*Math.sin(n))/(i*u*o)):(t+r)/2}function Ht(n,t,e,r){var i;if(null==n)i=e*Ro,r.point(-Lo,i),r.point(0,i),r.point(Lo,i),r.point(Lo,0),r.point(Lo,-i),r.point(0,-i),r.point(-Lo,-i),r.point(-Lo,0),r.point(-Lo,i);else if(so(n[0]-t[0])>Co){var u=n[0]<t[0]?Lo:-Lo;i=e*u/2,r.point(-u,i),r.point(0,i),r.point(u,i)}else r.point(t[0],t[1])}function Ot(n,t){var e=n[0],r=n[1],i=[Math.sin(e),-Math.cos(e),0],u=0,o=0;da.reset();for(var a=0,l=t.length;a<l;++a){var c=t[a],f=c.length;if(f)for(var s=c[0],h=s[0],p=s[1]/2+Lo/4,g=Math.sin(p),d=Math.cos(p),v=1;;){v===f&&(v=0);var y=(n=c[v])[0],m=n[1]/2+Lo/4,x=Math.sin(m),M=Math.cos(m),_=y-h,b=_>=0?1:-1,w=b*_,S=w>Lo,k=g*x;if(da.add(Math.atan2(k*b*Math.sin(w),d*M+k*Math.cos(w))),u+=S?_+b*qo:_,S^h>=e^y>=e){var N=mt(vt(s),vt(n));_t(N);var E=mt(i,N);_t(E);var A=(S^_>=0?-1:1)*tn(E[2]);(r>A||r===A&&(N[0]||N[1]))&&(o+=S^_>=0?1:-1)}if(!v++)break;h=y,g=x,d=M,s=n}}return(u<-Co||u<Co&&da<-Co)^1&o}function Yt(n){function t(n,t){return Math.cos(n)*Math.cos(t)>u}function e(n){var e,u,l,c,f;return{lineStart:function(){c=l=!1,f=1},point:function(s,h){var p,g=[s,h],d=t(s,h),v=o?d?0:i(s,h):d?i(s+(s<0?Lo:-Lo),h):0;if(!e&&(c=l=d)&&n.lineStart(),d!==l&&(p=r(e,g),(wt(e,p)||wt(g,p))&&(g[0]+=Co,g[1]+=Co,d=t(g[0],g[1]))),d!==l)f=0,d?(n.lineStart(),p=r(g,e),n.point(p[0],p[1])):(p=r(e,g),n.point(p[0],p[1]),n.lineEnd()),e=p;else if(a&&e&&o^d){var y;v&u||!(y=r(g,e,!0))||(f=0,o?(n.lineStart(),n.point(y[0][0],y[0][1]),n.point(y[1][0],y[1][1]),n.lineEnd()):(n.point(y[1][0],y[1][1]),n.lineEnd(),n.lineStart(),n.point(y[0][0],y[0][1])))}!d||e&&wt(e,g)||n.point(g[0],g[1]),e=g,l=d,u=v},lineEnd:function(){l&&n.lineEnd(),e=null},clean:function(){return f|(c&&l)<<1}}}function r(n,t,e){var r=[1,0,0],i=mt(vt(n),vt(t)),o=yt(i,i),a=i[0],l=o-a*a;if(!l)return!e&&n;var c=u*o/l,f=-u*a/l,s=mt(r,i),h=Mt(r,c);xt(h,Mt(i,f));var p=s,g=yt(h,p),d=yt(p,p),v=g*g-d*(yt(h,h)-1);if(!(v<0)){var y=Math.sqrt(v),m=Mt(p,(-g-y)/d);if(xt(m,h),m=bt(m),!e)return m;var x,M=n[0],_=t[0],b=n[1],w=t[1];_<M&&(x=M,M=_,_=x);var S=_-M,k=so(S-Lo)<Co,N=k||S<Co;if(!k&&w<b&&(x=b,b=w,w=x),N?k?b+w>0^m[1]<(so(m[0]-M)<Co?b:w):b<=m[1]&&m[1]<=w:S>Lo^(M<=m[0]&&m[0]<=_)){var E=Mt(p,(-g+y)/d);return xt(E,h),[m,bt(E)]}}}function i(t,e){var r=o?n:Lo-n,i=0;return t<-r?i|=1:t>r&&(i|=2),e<-r?i|=4:e>r&&(i|=8),i}var u=Math.cos(n),o=u>0,a=so(u)>Co;return Rt(t,e,de(n,6*Do),o?[0,-n]:[-Lo,n-Lo])}function It(n,t,e,r){return function(i){var u,o=i.a,a=i.b,l=o.x,c=o.y,f=0,s=1,h=a.x-l,p=a.y-c;if(u=n-l,h||!(u>0)){if(u/=h,h<0){if(u<f)return;u<s&&(s=u)}else if(h>0){if(u>s)return;u>f&&(f=u)}if(u=e-l,h||!(u<0)){if(u/=h,h<0){if(u>s)return;u>f&&(f=u)}else if(h>0){if(u<f)return;u<s&&(s=u)}if(u=t-c,p||!(u>0)){if(u/=p,p<0){if(u<f)return;u<s&&(s=u)}else if(p>0){if(u>s)return;u>f&&(f=u)}if(u=r-c,p||!(u<0)){if(u/=p,p<0){if(u>s)return;u>f&&(f=u)}else if(p>0){if(u<f)return;u<s&&(s=u)}return f>0&&(i.a={x:l+f*h,y:c+f*p}),s<1&&(i.b={x:l+s*h,y:c+s*p}),i}}}}}}function Zt(n,t,e,r){function i(r,i){return so(r[0]-n)<Co?i>0?0:3:so(r[0]-e)<Co?i>0?2:1:so(r[1]-t)<Co?i>0?1:0:i>0?3:2}function u(n,t){return o(n.x,t.x)}function o(n,t){var e=i(n,1),r=i(t,1);return e!==r?e-r:0===e?t[1]-n[1]:1===e?n[0]-t[0]:2===e?n[1]-t[1]:t[0]-n[0]}return function(a){function l(n){for(var t=0,e=v.length,r=n[1],i=0;i<e;++i)for(var u,o=1,a=v[i],l=a.length,c=a[0];o<l;++o)u=a[o],c[1]<=r?u[1]>r&&Q(c,u,n)>0&&++t:u[1]<=r&&Q(c,u,n)<0&&--t,c=u;return 0!==t}function c(u,a,l,c){var f=0,s=0;if(null==u||(f=i(u,l))!==(s=i(a,l))||o(u,a)<0^l>0)do{c.point(0===f||3===f?n:e,f>1?r:t)}while((f=(f+l+4)%4)!==s);else c.point(a[0],a[1])}function f(i,u){return n<=i&&i<=e&&t<=u&&u<=r}function s(n,t){f(n,t)&&a.point(n,t)}function h(){C.point=g,v&&v.push(y=[]),S=!0,w=!1,_=b=NaN}function p(){d&&(g(m,x),M&&w&&E.rejoin(),d.push(E.buffer())),C.point=s,w&&a.lineEnd()}function g(n,t){var e=f(n=Math.max(-za,Math.min(za,n)),t=Math.max(-za,Math.min(za,t)));if(v&&y.push([n,t]),S)m=n,x=t,M=e,S=!1,e&&(a.lineStart(),a.point(n,t));else if(e&&w)a.point(n,t);else{var r={a:{x:_,y:b},b:{x:n,y:t}};A(r)?(w||(a.lineStart(),a.point(r.a.x,r.a.y)),a.point(r.b.x,r.b.y),e||a.lineEnd(),k=!1):e&&(a.lineStart(),a.point(n,t),k=!1)}_=n,b=t,w=e}var d,v,y,m,x,M,_,b,w,S,k,N=a,E=Pt(),A=It(n,t,e,r),C={point:s,lineStart:h,lineEnd:p,polygonStart:function(){a=E,d=[],v=[],k=!0},polygonEnd:function(){a=N,d=to.merge(d);var t=l([n,r]),e=k&&t,i=d.length;(e||i)&&(a.polygonStart(),e&&(a.lineStart(),c(null,null,1,a),a.lineEnd()),i&&Lt(d,u,t,c,a),a.polygonEnd()),d=v=y=null}};return C}}function Vt(n){var t=0,e=Lo/3,r=ae(n),i=r(t,e);return i.parallels=function(n){return arguments.length?r(t=n[0]*Lo/180,e=n[1]*Lo/180):[t/Lo*180,e/Lo*180]},i}function Xt(n,t){function e(n,t){var e=Math.sqrt(u-2*i*Math.sin(t))/i;return[e*Math.sin(n*=i),o-e*Math.cos(n)]}var r=Math.sin(n),i=(r+Math.sin(t))/2,u=1+r*(2*i-r),o=Math.sqrt(u)/i;return e.invert=function(n,t){var e=o-t;return[Math.atan2(n,e)/i,tn((u-(n*n+e*e)*i*i)/(2*i))]},e}function $t(){function n(n,t){qa+=i*n-r*t,r=n,i=t}var t,e,r,i;ja.point=function(u,o){ja.point=n,t=r=u,e=i=o},ja.lineEnd=function(){n(t,e)}}function Bt(n,t){n<Ta&&(Ta=n),n>Da&&(Da=n),t<Ra&&(Ra=t),t>Pa&&(Pa=t)}function Wt(){function n(n,t){o.push("M",n,",",t,u)}function t(n,t){o.push("M",n,",",t),a.point=e}function e(n,t){o.push("L",n,",",t)}function r(){a.point=n}function i(){o.push("Z")}var u=Jt(4.5),o=[],a={point:n,lineStart:function(){a.point=t},lineEnd:r,polygonStart:function(){a.lineEnd=i},polygonEnd:function(){a.lineEnd=r,a.point=n},pointRadius:function(n){return u=Jt(n),a},result:function(){if(o.length){var n=o.join("");return o=[],n}}};return a}function Jt(n){return"m0,"+n+"a"+n+","+n+" 0 1,1 0,"+-2*n+"a"+n+","+n+" 0 1,1 0,"+2*n+"z"}function Gt(n,t){xa+=n,Ma+=t,++_a}function Kt(){function n(n,r){var i=n-t,u=r-e,o=Math.sqrt(i*i+u*u);ba+=o*(t+n)/2,wa+=o*(e+r)/2,Sa+=o,Gt(t=n,e=r)}var t,e;Fa.point=function(r,i){Fa.point=n,Gt(t=r,e=i)}}function Qt(){Fa.point=Gt}function ne(){function n(n,t){var e=n-r,u=t-i,o=Math.sqrt(e*e+u*u);ba+=o*(r+n)/2,wa+=o*(i+t)/2,Sa+=o,ka+=(o=i*n-r*t)*(r+n),Na+=o*(i+t),Ea+=3*o,Gt(r=n,i=t)}var t,e,r,i;Fa.point=function(u,o){Fa.point=n,Gt(t=r=u,e=i=o)},Fa.lineEnd=function(){n(t,e)}}function te(n){function t(t,e){n.moveTo(t+o,e),n.arc(t,e,o,0,qo)}function e(t,e){n.moveTo(t,e),a.point=r}function r(t,e){n.lineTo(t,e)}function i(){a.point=t}function u(){n.closePath()}var o=4.5,a={point:t,lineStart:function(){a.point=e},lineEnd:i,polygonStart:function(){a.lineEnd=u},polygonEnd:function(){a.lineEnd=i,a.point=t},pointRadius:function(n){return o=n,a},result:_};return a}function ee(n){function t(n){return(a?r:e)(n)}function e(t){return ue(t,function(e,r){e=n(e,r),t.point(e[0],e[1])})}function r(t){function e(e,r){e=n(e,r),t.point(e[0],e[1])}function r(){x=NaN,S.point=u,t.lineStart()}function u(e,r){var u=vt([e,r]),o=n(e,r);i(x,M,m,_,b,w,x=o[0],M=o[1],m=e,_=u[0],b=u[1],w=u[2],a,t),t.point(x,M)}function o(){S.point=e,t.lineEnd()}function l(){r(),S.point=c,S.lineEnd=f}function c(n,t){u(s=n,h=t),p=x,g=M,d=_,v=b,y=w,S.point=u}function f(){i(x,M,m,_,b,w,p,g,s,d,v,y,a,t),S.lineEnd=o,o()}var s,h,p,g,d,v,y,m,x,M,_,b,w,S={point:e,lineStart:r,lineEnd:o,polygonStart:function(){t.polygonStart(),S.lineStart=l},polygonEnd:function(){t.polygonEnd(),S.lineStart=r}};return S}function i(t,e,r,a,l,c,f,s,h,p,g,d,v,y){var m=f-t,x=s-e,M=m*m+x*x;if(M>4*u&&v--){var _=a+p,b=l+g,w=c+d,S=Math.sqrt(_*_+b*b+w*w),k=Math.asin(w/=S),N=so(so(w)-1)<Co||so(r-h)<Co?(r+h)/2:Math.atan2(b,_),E=n(N,k),A=E[0],C=E[1],z=A-t,L=C-e,q=x*z-m*L;(q*q/M>u||so((m*z+x*L)/M-.5)>.3||a*p+l*g+c*d<o)&&(i(t,e,r,a,l,c,A,C,N,_/=S,b/=S,w,v,y),y.point(A,C),i(A,C,N,_,b,w,f,s,h,p,g,d,v,y))}}var u=.5,o=Math.cos(30*Do),a=16;return t.precision=function(n){return arguments.length?(a=(u=n*n)>0&&16,t):Math.sqrt(u)},t}function re(n){var t=ee(function(t,e){return n([t*Po,e*Po])});return function(n){return le(t(n))}}function ie(n){this.stream=n}function ue(n,t){return{point:t,sphere:function(){n.sphere()},lineStart:function(){n.lineStart()},lineEnd:function(){n.lineEnd()},polygonStart:function(){n.polygonStart()},polygonEnd:function(){n.polygonEnd()}}}function oe(n){return ae(function(){return n})()}function ae(n){function t(n){return n=a(n[0]*Do,n[1]*Do),[n[0]*h+l,c-n[1]*h]}function e(n){return(n=a.invert((n[0]-l)/h,(c-n[1])/h))&&[n[0]*Po,n[1]*Po]}function r(){a=Ct(o=se(y,x,M),u);var n=u(d,v);return l=p-n[0]*h,c=g+n[1]*h,i()}function i(){return f&&(f.valid=!1,f=null),t}var u,o,a,l,c,f,s=ee(function(n,t){return n=u(n,t),[n[0]*h+l,c-n[1]*h]}),h=150,p=480,g=250,d=0,v=0,y=0,x=0,M=0,_=Ca,b=m,w=null,S=null;return t.stream=function(n){return f&&(f.valid=!1),f=le(_(o,s(b(n)))),f.valid=!0,f},t.clipAngle=function(n){return arguments.length?(_=null==n?(w=n,Ca):Yt((w=+n)*Do),i()):w},t.clipExtent=function(n){return arguments.length?(S=n,b=n?Zt(n[0][0],n[0][1],n[1][0],n[1][1]):m,i()):S},t.scale=function(n){return arguments.length?(h=+n,r()):h},t.translate=function(n){return arguments.length?(p=+n[0],g=+n[1],r()):[p,g]},t.center=function(n){return arguments.length?(d=n[0]%360*Do,v=n[1]%360*Do,r()):[d*Po,v*Po]},t.rotate=function(n){return arguments.length?(y=n[0]%360*Do,x=n[1]%360*Do,M=n.length>2?n[2]%360*Do:0,r()):[y*Po,x*Po,M*Po]},to.rebind(t,s,"precision"),function(){return u=n.apply(this,arguments),t.invert=u.invert&&e,r()}}function le(n){return ue(n,function(t,e){n.point(t*Do,e*Do)})}function ce(n,t){return[n,t]}function fe(n,t){return[n>Lo?n-qo:n<-Lo?n+qo:n,t]}function se(n,t,e){return n?t||e?Ct(pe(n),ge(t,e)):pe(n):t||e?ge(t,e):fe}function he(n){return function(t,e){return t+=n,[t>Lo?t-qo:t<-Lo?t+qo:t,e]}}function pe(n){var t=he(n);return t.invert=he(-n),t}function ge(n,t){function e(n,t){var e=Math.cos(t),a=Math.cos(n)*e,l=Math.sin(n)*e,c=Math.sin(t),f=c*r+a*i;return[Math.atan2(l*u-f*o,a*r-c*i),tn(f*u+l*o)]}var r=Math.cos(n),i=Math.sin(n),u=Math.cos(t),o=Math.sin(t);return e.invert=function(n,t){var e=Math.cos(t),a=Math.cos(n)*e,l=Math.sin(n)*e,c=Math.sin(t),f=c*u-l*o;return[Math.atan2(l*u+c*o,a*r+f*i),tn(f*r-a*i)]},e}function de(n,t){var e=Math.cos(n),r=Math.sin(n);return function(i,u,o,a){var l=o*t;null!=i?(i=ve(e,i),u=ve(e,u),(o>0?i<u:i>u)&&(i+=o*qo)):(i=n+o*qo,u=n-.5*l);for(var c,f=i;o>0?f>u:f<u;f-=l)a.point((c=bt([e,-r*Math.cos(f),-r*Math.sin(f)]))[0],c[1])}}function ve(n,t){var e=vt(t);e[0]-=n,_t(e);var r=nn(-e[1]);return((-e[2]<0?-r:r)+2*Math.PI-Co)%(2*Math.PI)}function ye(n,t,e){var r=to.range(n,t-Co,e).concat(t);return function(n){return r.map(function(t){return[n,t]})}}function me(n,t,e){var r=to.range(n,t-Co,e).concat(t);return function(n){return r.map(function(t){return[t,n]})}}function xe(n){return n.source}function Me(n){return n.target}function _e(n,t,e,r){var i=Math.cos(t),u=Math.sin(t),o=Math.cos(r),a=Math.sin(r),l=i*Math.cos(n),c=i*Math.sin(n),f=o*Math.cos(e),s=o*Math.sin(e),h=2*Math.asin(Math.sqrt(on(r-t)+i*o*on(e-n))),p=1/Math.sin(h),g=h?function(n){var t=Math.sin(n*=h)*p,e=Math.sin(h-n)*p,r=e*l+t*f,i=e*c+t*s,o=e*u+t*a;return[Math.atan2(i,r)*Po,Math.atan2(o,Math.sqrt(r*r+i*i))*Po]}:function(){return[n*Po,t*Po]};return g.distance=h,g}function be(){function n(n,i){var u=Math.sin(i*=Do),o=Math.cos(i),a=so((n*=Do)-t),l=Math.cos(a);Ha+=Math.atan2(Math.sqrt((a=o*Math.sin(a))*a+(a=r*u-e*o*l)*a),e*u+r*o*l),t=n,e=u,r=o}var t,e,r;Oa.point=function(i,u){t=i*Do,e=Math.sin(u*=Do),r=Math.cos(u),Oa.point=n},Oa.lineEnd=function(){Oa.point=Oa.lineEnd=_}}function we(n,t){function e(t,e){var r=Math.cos(t),i=Math.cos(e),u=n(r*i);return[u*i*Math.sin(t),u*Math.sin(e)]}return e.invert=function(n,e){var r=Math.sqrt(n*n+e*e),i=t(r),u=Math.sin(i),o=Math.cos(i);return[Math.atan2(n*u,r*o),Math.asin(r&&e*u/r)]},e}function Se(n,t){function e(n,t){o>0?t<-Ro+Co&&(t=-Ro+Co):t>Ro-Co&&(t=Ro-Co);var e=o/Math.pow(i(t),u);return[e*Math.sin(u*n),o-e*Math.cos(u*n)]}var r=Math.cos(n),i=function(n){return Math.tan(Lo/4+n/2)},u=n===t?Math.sin(n):Math.log(r/Math.cos(t))/Math.log(i(t)/i(n)),o=r*Math.pow(i(n),u)/u;return u?(e.invert=function(n,t){var e=o-t,r=K(u)*Math.sqrt(n*n+e*e);return[Math.atan2(n,e)/u,2*Math.atan(Math.pow(o/r,1/u))-Ro]},e):Ne}function ke(n,t){function e(n,t){var e=u-t;return[e*Math.sin(i*n),u-e*Math.cos(i*n)]}var r=Math.cos(n),i=n===t?Math.sin(n):(r-Math.cos(t))/(t-n),u=r/i+n;return so(i)<Co?ce:(e.invert=function(n,t){var e=u-t;return[Math.atan2(n,e)/i,u-K(i)*Math.sqrt(n*n+e*e)]},e)}function Ne(n,t){return[n,Math.log(Math.tan(Lo/4+t/2))]}function Ee(n){var t,e=oe(n),r=e.scale,i=e.translate,u=e.clipExtent;return e.scale=function(){var n=r.apply(e,arguments);return n===e?t?e.clipExtent(null):e:n},e.translate=function(){var n=i.apply(e,arguments);return n===e?t?e.clipExtent(null):e:n},e.clipExtent=function(n){var o=u.apply(e,arguments);if(o===e){if(t=null==n){var a=Lo*r(),l=i();u([[l[0]-a,l[1]-a],[l[0]+a,l[1]+a]])}}else t&&(o=null);return o},e.clipExtent(null)}function Ae(n,t){return[Math.log(Math.tan(Lo/4+t/2)),-n]}function Ce(n){return n[0]}function ze(n){return n[1]}function Le(n){for(var t=n.length,e=[0,1],r=2,i=2;i<t;i++){for(;r>1&&Q(n[e[r-2]],n[e[r-1]],n[i])<=0;)--r;e[r++]=i}return e.slice(0,r)}function qe(n,t){return n[0]-t[0]||n[1]-t[1]}function Te(n,t,e){return(e[0]-t[0])*(n[1]-t[1])<(e[1]-t[1])*(n[0]-t[0])}function Re(n,t,e,r){var i=n[0],u=e[0],o=t[0]-i,a=r[0]-u,l=n[1],c=e[1],f=t[1]-l,s=r[1]-c,h=(a*(l-c)-s*(i-u))/(s*o-a*f);return[i+h*o,l+h*f]}function De(n){var t=n[0],e=n[n.length-1];return!(t[0]-e[0]||t[1]-e[1])}function Pe(){rr(this),this.edge=this.site=this.circle=null}function je(n){var t=Qa.pop()||new Pe;return t.site=n,t}function Ue(n){Be(n),Ja.remove(n),Qa.push(n),rr(n)}function Fe(n){var t=n.circle,e=t.x,r=t.cy,i={x:e,y:r},u=n.P,o=n.N,a=[n];Ue(n);for(var l=u;l.circle&&so(e-l.circle.x)<Co&&so(r-l.circle.cy)<Co;)u=l.P,a.unshift(l),Ue(l),l=u;a.unshift(l),Be(l);for(var c=o;c.circle&&so(e-c.circle.x)<Co&&so(r-c.circle.cy)<Co;)o=c.N,a.push(c),Ue(c),c=o;a.push(c),Be(c);var f,s=a.length;for(f=1;f<s;++f)c=a[f],l=a[f-1],nr(c.edge,l.site,c.site,i);l=a[0],(c=a[s-1]).edge=Ke(l.site,c.site,null,i),$e(l),$e(c)}function He(n){for(var t,e,r,i,u=n.x,o=n.y,a=Ja._;a;)if((r=Oe(a,o)-u)>Co)a=a.L;else{if(!((i=u-Ye(a,o))>Co)){r>-Co?(t=a.P,e=a):i>-Co?(t=a,e=a.N):t=e=a;break}if(!a.R){t=a;break}a=a.R}var l=je(n);if(Ja.insert(t,l),t||e){if(t===e)return Be(t),e=je(t.site),Ja.insert(l,e),l.edge=e.edge=Ke(t.site,l.site),$e(t),void $e(e);if(e){Be(t),Be(e);var c=t.site,f=c.x,s=c.y,h=n.x-f,p=n.y-s,g=e.site,d=g.x-f,v=g.y-s,y=2*(h*v-p*d),m=h*h+p*p,x=d*d+v*v,M={x:(v*m-p*x)/y+f,y:(h*x-d*m)/y+s};nr(e.edge,c,g,M),l.edge=Ke(c,n,null,M),e.edge=Ke(n,g,null,M),$e(t),$e(e)}else l.edge=Ke(t.site,l.site)}}function Oe(n,t){var e=n.site,r=e.x,i=e.y,u=i-t;if(!u)return r;var o=n.P;if(!o)return-1/0;var a=(e=o.site).x,l=e.y,c=l-t;if(!c)return a;var f=a-r,s=1/u-1/c,h=f/c;return s?(-h+Math.sqrt(h*h-2*s*(f*f/(-2*c)-l+c/2+i-u/2)))/s+r:(r+a)/2}function Ye(n,t){var e=n.N;if(e)return Oe(e,t);var r=n.site;return r.y===t?r.x:1/0}function Ie(n){this.site=n,this.edges=[]}function Ze(n){for(var t,e,r,i,u,o,a,l,c,f,s=n[0][0],h=n[1][0],p=n[0][1],g=n[1][1],d=Wa,v=d.length;v--;)if((u=d[v])&&u.prepare())for(l=(a=u.edges).length,o=0;o<l;)r=(f=a[o].end()).x,i=f.y,t=(c=a[++o%l].start()).x,e=c.y,(so(r-t)>Co||so(i-e)>Co)&&(a.splice(o,0,new tr(Qe(u.site,f,so(r-s)<Co&&g-i>Co?{x:s,y:so(t-s)<Co?e:g}:so(i-g)<Co&&h-r>Co?{x:so(e-g)<Co?t:h,y:g}:so(r-h)<Co&&i-p>Co?{x:h,y:so(t-h)<Co?e:p}:so(i-p)<Co&&r-s>Co?{x:so(e-p)<Co?t:s,y:p}:null),u.site,null)),++l)}function Ve(n,t){return t.angle-n.angle}function Xe(){rr(this),this.x=this.y=this.arc=this.site=this.cy=null}function $e(n){var t=n.P,e=n.N;if(t&&e){var r=t.site,i=n.site,u=e.site;if(r!==u){var o=i.x,a=i.y,l=r.x-o,c=r.y-a,f=u.x-o,s=2*(l*(v=u.y-a)-c*f);if(!(s>=-zo)){var h=l*l+c*c,p=f*f+v*v,g=(v*h-c*p)/s,d=(l*p-f*h)/s,v=d+a,y=nl.pop()||new Xe;y.arc=n,y.site=i,y.x=g+o,y.y=v+Math.sqrt(g*g+d*d),y.cy=v,n.circle=y;for(var m=null,x=Ka._;x;)if(y.y<x.y||y.y===x.y&&y.x<=x.x){if(!x.L){m=x.P;break}x=x.L}else{if(!x.R){m=x;break}x=x.R}Ka.insert(m,y),m||(Ga=y)}}}}function Be(n){var t=n.circle;t&&(t.P||(Ga=t.N),Ka.remove(t),nl.push(t),rr(t),n.circle=null)}function We(n){for(var t,e=Ba,r=It(n[0][0],n[0][1],n[1][0],n[1][1]),i=e.length;i--;)(!Je(t=e[i],n)||!r(t)||so(t.a.x-t.b.x)<Co&&so(t.a.y-t.b.y)<Co)&&(t.a=t.b=null,e.splice(i,1))}function Je(n,t){var e=n.b;if(e)return!0;var r,i,u=n.a,o=t[0][0],a=t[1][0],l=t[0][1],c=t[1][1],f=n.l,s=n.r,h=f.x,p=f.y,g=s.x,d=s.y,v=(h+g)/2,y=(p+d)/2;if(d===p){if(v<o||v>=a)return;if(h>g){if(u){if(u.y>=c)return}else u={x:v,y:l};e={x:v,y:c}}else{if(u){if(u.y<l)return}else u={x:v,y:c};e={x:v,y:l}}}else if(r=(h-g)/(d-p),i=y-r*v,r<-1||r>1)if(h>g){if(u){if(u.y>=c)return}else u={x:(l-i)/r,y:l};e={x:(c-i)/r,y:c}}else{if(u){if(u.y<l)return}else u={x:(c-i)/r,y:c};e={x:(l-i)/r,y:l}}else if(p<d){if(u){if(u.x>=a)return}else u={x:o,y:r*o+i};e={x:a,y:r*a+i}}else{if(u){if(u.x<o)return}else u={x:a,y:r*a+i};e={x:o,y:r*o+i}}return n.a=u,n.b=e,!0}function Ge(n,t){this.l=n,this.r=t,this.a=this.b=null}function Ke(n,t,e,r){var i=new Ge(n,t);return Ba.push(i),e&&nr(i,n,t,e),r&&nr(i,t,n,r),Wa[n.i].edges.push(new tr(i,n,t)),Wa[t.i].edges.push(new tr(i,t,n)),i}function Qe(n,t,e){var r=new Ge(n,null);return r.a=t,r.b=e,Ba.push(r),r}function nr(n,t,e,r){n.a||n.b?n.l===e?n.b=r:n.a=r:(n.a=r,n.l=t,n.r=e)}function tr(n,t,e){var r=n.a,i=n.b;this.edge=n,this.site=t,this.angle=e?Math.atan2(e.y-t.y,e.x-t.x):n.l===t?Math.atan2(i.x-r.x,r.y-i.y):Math.atan2(r.x-i.x,i.y-r.y)}function er(){this._=null}function rr(n){n.U=n.C=n.L=n.R=n.P=n.N=null}function ir(n,t){var e=t,r=t.R,i=e.U;i?i.L===e?i.L=r:i.R=r:n._=r,r.U=i,e.U=r,e.R=r.L,e.R&&(e.R.U=e),r.L=e}function ur(n,t){var e=t,r=t.L,i=e.U;i?i.L===e?i.L=r:i.R=r:n._=r,r.U=i,e.U=r,e.L=r.R,e.L&&(e.L.U=e),r.R=e}function or(n){for(;n.L;)n=n.L;return n}function ar(n,t){var e,r,i,u=n.sort(lr).pop();for(Ba=[],Wa=new Array(n.length),Ja=new er,Ka=new er;;)if(i=Ga,u&&(!i||u.y<i.y||u.y===i.y&&u.x<i.x))u.x===e&&u.y===r||(Wa[u.i]=new Ie(u),He(u),e=u.x,r=u.y),u=n.pop();else{if(!i)break;Fe(i.arc)}t&&(We(t),Ze(t));var o={cells:Wa,edges:Ba};return Ja=Ka=Ba=Wa=null,o}function lr(n,t){return t.y-n.y||t.x-n.x}function cr(n,t,e){return(n.x-e.x)*(t.y-n.y)-(n.x-t.x)*(e.y-n.y)}function fr(n){return n.x}function sr(n){return n.y}function hr(n,t,e,r,i,u){if(!n(t,e,r,i,u)){var o=.5*(e+i),a=.5*(r+u),l=t.nodes;l[0]&&hr(n,l[0],e,r,o,a),l[1]&&hr(n,l[1],o,r,i,a),l[2]&&hr(n,l[2],e,a,o,u),l[3]&&hr(n,l[3],o,a,i,u)}}function pr(n,t,e,r,i,u,o){var a,l=1/0;return function n(c,f,s,h,p){if(!(f>u||s>o||h<r||p<i)){if(g=c.point){var g,d=t-c.x,v=e-c.y,y=d*d+v*v;if(y<l){var m=Math.sqrt(l=y);r=t-m,i=e-m,u=t+m,o=e+m,a=g}}for(var x=c.nodes,M=.5*(f+h),_=.5*(s+p),b=(e>=_)<<1|t>=M,w=b+4;b<w;++b)if(c=x[3&b])switch(3&b){case 0:n(c,f,s,M,_);break;case 1:n(c,M,s,h,_);break;case 2:n(c,f,_,M,p);break;case 3:n(c,M,_,h,p)}}}(n,r,i,u,o),a}function gr(n,t){n=to.rgb(n),t=to.rgb(t);var e=n.r,r=n.g,i=n.b,u=t.r-e,o=t.g-r,a=t.b-i;return function(n){return"#"+_n(Math.round(e+u*n))+_n(Math.round(r+o*n))+_n(Math.round(i+a*n))}}function dr(n,t){var e,r={},i={};for(e in n)e in t?r[e]=mr(n[e],t[e]):i[e]=n[e];for(e in t)e in n||(i[e]=t[e]);return function(n){for(e in r)i[e]=r[e](n);return i}}function vr(n,t){return n=+n,t=+t,function(e){return n*(1-e)+t*e}}function yr(n,t){var e,r,i,u=el.lastIndex=rl.lastIndex=0,o=-1,a=[],l=[];for(n+="",t+="";(e=el.exec(n))&&(r=rl.exec(t));)(i=r.index)>u&&(i=t.slice(u,i),a[o]?a[o]+=i:a[++o]=i),(e=e[0])===(r=r[0])?a[o]?a[o]+=r:a[++o]=r:(a[++o]=null,l.push({i:o,x:vr(e,r)})),u=rl.lastIndex;return u<t.length&&(i=t.slice(u),a[o]?a[o]+=i:a[++o]=i),a.length<2?l[0]?(t=l[0].x,function(n){return t(n)+""}):function(){return t}:(t=l.length,function(n){for(var e,r=0;r<t;++r)a[(e=l[r]).i]=e.x(n);return a.join("")})}function mr(n,t){for(var e,r=to.interpolators.length;--r>=0&&!(e=to.interpolators[r](n,t)););return e}function xr(n,t){var e,r=[],i=[],u=n.length,o=t.length,a=Math.min(n.length,t.length);for(e=0;e<a;++e)r.push(mr(n[e],t[e]));for(;e<u;++e)i[e]=n[e];for(;e<o;++e)i[e]=t[e];return function(n){for(e=0;e<a;++e)i[e]=r[e](n);return i}}function Mr(n){return function(t){return t<=0?0:t>=1?1:n(t)}}function _r(n){return function(t){return 1-n(1-t)}}function br(n){return function(t){return.5*(t<.5?n(2*t):2-n(2-2*t))}}function wr(n){return n*n}function Sr(n){return n*n*n}function kr(n){if(n<=0)return 0;if(n>=1)return 1;var t=n*n,e=t*n;return 4*(n<.5?e:3*(n-t)+e-.75)}function Nr(n){return function(t){return Math.pow(t,n)}}function Er(n){return 1-Math.cos(n*Ro)}function Ar(n){return Math.pow(2,10*(n-1))}function Cr(n){return 1-Math.sqrt(1-n*n)}function zr(n,t){var e;return arguments.length<2&&(t=.45),arguments.length?e=t/qo*Math.asin(1/n):(n=1,e=t/4),function(r){return 1+n*Math.pow(2,-10*r)*Math.sin((r-e)*qo/t)}}function Lr(n){return n||(n=1.70158),function(t){return t*t*((n+1)*t-n)}}function qr(n){return n<1/2.75?7.5625*n*n:n<2/2.75?7.5625*(n-=1.5/2.75)*n+.75:n<2.5/2.75?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375}function Tr(n,t){n=to.hcl(n),t=to.hcl(t);var e=n.h,r=n.c,i=n.l,u=t.h-e,o=t.c-r,a=t.l-i;return isNaN(o)&&(o=0,r=isNaN(r)?t.c:r),isNaN(u)?(u=0,e=isNaN(e)?t.h:e):u>180?u-=360:u<-180&&(u+=360),function(n){return sn(e+u*n,r+o*n,i+a*n)+""}}function Rr(n,t){n=to.hsl(n),t=to.hsl(t);var e=n.h,r=n.s,i=n.l,u=t.h-e,o=t.s-r,a=t.l-i;return isNaN(o)&&(o=0,r=isNaN(r)?t.s:r),isNaN(u)?(u=0,e=isNaN(e)?t.h:e):u>180?u-=360:u<-180&&(u+=360),function(n){return cn(e+u*n,r+o*n,i+a*n)+""}}function Dr(n,t){n=to.lab(n),t=to.lab(t);var e=n.l,r=n.a,i=n.b,u=t.l-e,o=t.a-r,a=t.b-i;return function(n){return pn(e+u*n,r+o*n,i+a*n)+""}}function Pr(n,t){return t-=n,function(e){return Math.round(n+t*e)}}function jr(n){var t=[n.a,n.b],e=[n.c,n.d],r=Fr(t),i=Ur(t,e),u=Fr(Hr(e,t,-i))||0;t[0]*e[1]<e[0]*t[1]&&(t[0]*=-1,t[1]*=-1,r*=-1,i*=-1),this.rotate=(r?Math.atan2(t[1],t[0]):Math.atan2(-e[0],e[1]))*Po,this.translate=[n.e,n.f],this.scale=[r,u],this.skew=u?Math.atan2(i,u)*Po:0}function Ur(n,t){return n[0]*t[0]+n[1]*t[1]}function Fr(n){var t=Math.sqrt(Ur(n,n));return t&&(n[0]/=t,n[1]/=t),t}function Hr(n,t,e){return n[0]+=e*t[0],n[1]+=e*t[1],n}function Or(n){return n.length?n.pop()+",":""}function Yr(n,t,e,r){if(n[0]!==t[0]||n[1]!==t[1]){var i=e.push("translate(",null,",",null,")");r.push({i:i-4,x:vr(n[0],t[0])},{i:i-2,x:vr(n[1],t[1])})}else(t[0]||t[1])&&e.push("translate("+t+")")}function Ir(n,t,e,r){n!==t?(n-t>180?t+=360:t-n>180&&(n+=360),r.push({i:e.push(Or(e)+"rotate(",null,")")-2,x:vr(n,t)})):t&&e.push(Or(e)+"rotate("+t+")")}function Zr(n,t,e,r){n!==t?r.push({i:e.push(Or(e)+"skewX(",null,")")-2,x:vr(n,t)}):t&&e.push(Or(e)+"skewX("+t+")")}function Vr(n,t,e,r){if(n[0]!==t[0]||n[1]!==t[1]){var i=e.push(Or(e)+"scale(",null,",",null,")");r.push({i:i-4,x:vr(n[0],t[0])},{i:i-2,x:vr(n[1],t[1])})}else 1===t[0]&&1===t[1]||e.push(Or(e)+"scale("+t+")")}function Xr(n,t){var e=[],r=[];return n=to.transform(n),t=to.transform(t),Yr(n.translate,t.translate,e,r),Ir(n.rotate,t.rotate,e,r),Zr(n.skew,t.skew,e,r),Vr(n.scale,t.scale,e,r),n=t=null,function(n){for(var t,i=-1,u=r.length;++i<u;)e[(t=r[i]).i]=t.x(n);return e.join("")}}function $r(n,t){return t=(t-=n=+n)||1/t,function(e){return(e-n)/t}}function Br(n,t){return t=(t-=n=+n)||1/t,function(e){return Math.max(0,Math.min(1,(e-n)/t))}}function Wr(n){for(var t=n.source,e=n.target,r=Gr(t,e),i=[t];t!==r;)t=t.parent,i.push(t);for(var u=i.length;e!==r;)i.splice(u,0,e),e=e.parent;return i}function Jr(n){for(var t=[],e=n.parent;null!=e;)t.push(n),n=e,e=e.parent;return t.push(n),t}function Gr(n,t){if(n===t)return n;for(var e=Jr(n),r=Jr(t),i=e.pop(),u=r.pop(),o=null;i===u;)o=i,i=e.pop(),u=r.pop();return o}function Kr(n){n.fixed|=2}function Qr(n){n.fixed&=-7}function ni(n){n.fixed|=4,n.px=n.x,n.py=n.y}function ti(n){n.fixed&=-5}function ei(n,t,e){var r=0,i=0;if(n.charge=0,!n.leaf)for(var u,o=n.nodes,a=o.length,l=-1;++l<a;)null!=(u=o[l])&&(ei(u,t,e),n.charge+=u.charge,r+=u.charge*u.cx,i+=u.charge*u.cy);if(n.point){n.leaf||(n.point.x+=Math.random()-.5,n.point.y+=Math.random()-.5);var c=t*e[n.point.index];n.charge+=n.pointCharge=c,r+=c*n.point.x,i+=c*n.point.y}n.cx=r/n.charge,n.cy=i/n.charge}function ri(n,t){return to.rebind(n,t,"sort","children","value"),n.nodes=n,n.links=ci,n}function ii(n,t){for(var e=[n];null!=(n=e.pop());)if(t(n),(i=n.children)&&(r=i.length))for(var r,i;--r>=0;)e.push(i[r])}function ui(n,t){for(var e=[n],r=[];null!=(n=e.pop());)if(r.push(n),(u=n.children)&&(i=u.length))for(var i,u,o=-1;++o<i;)e.push(u[o]);for(;null!=(n=r.pop());)t(n)}function oi(n){return n.children}function ai(n){return n.value}function li(n,t){return t.value-n.value}function ci(n){return to.merge(n.map(function(n){return(n.children||[]).map(function(t){return{source:n,target:t}})}))}function fi(n){return n.x}function si(n){return n.y}function hi(n,t,e){n.y0=t,n.y=e}function pi(n){return to.range(n.length)}function gi(n){for(var t=-1,e=n[0].length,r=[];++t<e;)r[t]=0;return r}function di(n){for(var t,e=1,r=0,i=n[0][1],u=n.length;e<u;++e)(t=n[e][1])>i&&(r=e,i=t);return r}function vi(n){return n.reduce(yi,0)}function yi(n,t){return n+t[1]}function mi(n,t){return xi(n,Math.ceil(Math.log(t.length)/Math.LN2+1))}function xi(n,t){for(var e=-1,r=+n[0],i=(n[1]-r)/t,u=[];++e<=t;)u[e]=i*e+r;return u}function Mi(n){return[to.min(n),to.max(n)]}function _i(n,t){return n.value-t.value}function bi(n,t){var e=n._pack_next;n._pack_next=t,t._pack_prev=n,t._pack_next=e,e._pack_prev=t}function wi(n,t){n._pack_next=t,t._pack_prev=n}function Si(n,t){var e=t.x-n.x,r=t.y-n.y,i=n.r+t.r;return.999*i*i>e*e+r*r}function ki(n){function t(n){f=Math.min(n.x-n.r,f),s=Math.max(n.x+n.r,s),h=Math.min(n.y-n.r,h),p=Math.max(n.y+n.r,p)}if((e=n.children)&&(c=e.length)){var e,r,i,u,o,a,l,c,f=1/0,s=-1/0,h=1/0,p=-1/0;if(e.forEach(Ni),r=e[0],r.x=-r.r,r.y=0,t(r),c>1&&(i=e[1],i.x=i.r,i.y=0,t(i),c>2))for(Ci(r,i,u=e[2]),t(u),bi(r,u),r._pack_prev=u,bi(u,i),i=r._pack_next,o=3;o<c;o++){Ci(r,i,u=e[o]);var g=0,d=1,v=1;for(a=i._pack_next;a!==i;a=a._pack_next,d++)if(Si(a,u)){g=1;break}if(1==g)for(l=r._pack_prev;l!==a._pack_prev&&!Si(l,u);l=l._pack_prev,v++);g?(d<v||d==v&&i.r<r.r?wi(r,i=a):wi(r=l,i),o--):(bi(r,u),i=u,t(u))}var y=(f+s)/2,m=(h+p)/2,x=0;for(o=0;o<c;o++)(u=e[o]).x-=y,u.y-=m,x=Math.max(x,u.r+Math.sqrt(u.x*u.x+u.y*u.y));n.r=x,e.forEach(Ei)}}function Ni(n){n._pack_next=n._pack_prev=n}function Ei(n){delete n._pack_next,delete n._pack_prev}function Ai(n,t,e,r){var i=n.children;if(n.x=t+=r*n.x,n.y=e+=r*n.y,n.r*=r,i)for(var u=-1,o=i.length;++u<o;)Ai(i[u],t,e,r)}function Ci(n,t,e){var r=n.r+e.r,i=t.x-n.x,u=t.y-n.y;if(r&&(i||u)){var o=t.r+e.r,a=i*i+u*u,l=.5+((r*=r)-(o*=o))/(2*a),c=Math.sqrt(Math.max(0,2*o*(r+a)-(r-=a)*r-o*o))/(2*a);e.x=n.x+l*i+c*u,e.y=n.y+l*u-c*i}else e.x=n.x+r,e.y=n.y}function zi(n,t){return n.parent==t.parent?1:2}function Li(n){return 1+to.max(n,function(n){return n.y})}function qi(n){return n.reduce(function(n,t){return n+t.x},0)/n.length}function Ti(n){var t=n.children;return t&&t.length?Ti(t[0]):n}function Ri(n){var t,e=n.children;return e&&(t=e.length)?Ri(e[t-1]):n}function Di(n){return{x:n.x,y:n.y,dx:n.dx,dy:n.dy}}function Pi(n,t){var e=n.x+t[3],r=n.y+t[0],i=n.dx-t[1]-t[3],u=n.dy-t[0]-t[2];return i<0&&(e+=i/2,i=0),u<0&&(r+=u/2,u=0),{x:e,y:r,dx:i,dy:u}}function ji(n){var t=n[0],e=n[n.length-1];return t<e?[t,e]:[e,t]}function Ui(n){return n.rangeExtent?n.rangeExtent():ji(n.range())}function Fi(n,t,e,r){var i=e(n[0],n[1]),u=r(t[0],t[1]);return function(n){return u(i(n))}}function Hi(n,t){var e,r=0,i=n.length-1,u=n[r],o=n[i];return o<u&&(e=r,r=i,i=e,e=u,u=o,o=e),n[r]=t.floor(u),n[i]=t.ceil(o),n}function Oi(n){return n?{floor:function(t){return Math.floor(t/n)*n},ceil:function(t){return Math.ceil(t/n)*n}}:gl}function Yi(n,t,e,r){var i=[],u=[],o=0,a=Math.min(n.length,t.length)-1;for(n[a]<n[0]&&(n=n.slice().reverse(),t=t.slice().reverse());++o<=a;)i.push(e(n[o-1],n[o])),u.push(r(t[o-1],t[o]));return function(t){var e=to.bisect(n,t,1,a)-1;return u[e](i[e](t))}}function Ii(n,t,e,r){function i(){var i=Math.min(n.length,t.length)>2?Yi:Fi,l=r?Br:$r;return o=i(n,t,l,e),a=i(t,n,l,mr),u}function u(n){return o(n)}var o,a;return u.invert=function(n){return a(n)},u.domain=function(t){return arguments.length?(n=t.map(Number),i()):n},u.range=function(n){return arguments.length?(t=n,i()):t},u.rangeRound=function(n){return u.range(n).interpolate(Pr)},u.clamp=function(n){return arguments.length?(r=n,i()):r},u.interpolate=function(n){return arguments.length?(e=n,i()):e},u.ticks=function(t){return $i(n,t)},u.tickFormat=function(t,e){return Bi(n,t,e)},u.nice=function(t){return Vi(n,t),i()},u.copy=function(){return Ii(n,t,e,r)},i()}function Zi(n,t){return to.rebind(n,t,"range","rangeRound","interpolate","clamp")}function Vi(n,t){return Hi(n,Oi(Xi(n,t)[2])),Hi(n,Oi(Xi(n,t)[2])),n}function Xi(n,t){null==t&&(t=10);var e=ji(n),r=e[1]-e[0],i=Math.pow(10,Math.floor(Math.log(r/t)/Math.LN10)),u=t/r*i;return u<=.15?i*=10:u<=.35?i*=5:u<=.75&&(i*=2),e[0]=Math.ceil(e[0]/i)*i,e[1]=Math.floor(e[1]/i)*i+.5*i,e[2]=i,e}function $i(n,t){return to.range.apply(to,Xi(n,t))}function Bi(n,t,e){var r=Xi(n,t);if(e){var i=ea.exec(e);if(i.shift(),"s"===i[8]){var u=to.formatPrefix(Math.max(so(r[0]),so(r[1])));return i[7]||(i[7]="."+Wi(u.scale(r[2]))),i[8]="f",e=to.format(i.join("")),function(n){return e(u.scale(n))+u.symbol}}i[7]||(i[7]="."+Ji(i[8],r)),e=i.join("")}else e=",."+Wi(r[2])+"f";return to.format(e)}function Wi(n){return-Math.floor(Math.log(n)/Math.LN10+.01)}function Ji(n,t){var e=Wi(t[2]);return n in dl?Math.abs(e-Wi(Math.max(so(t[0]),so(t[1]))))+ +("e"!==n):e-2*("%"===n)}function Gi(n,t,e,r){function i(n){return(e?Math.log(n<0?0:n):-Math.log(n>0?0:-n))/Math.log(t)}function u(n){return e?Math.pow(t,n):-Math.pow(t,-n)}function o(t){return n(i(t))}return o.invert=function(t){return u(n.invert(t))},o.domain=function(t){return arguments.length?(e=t[0]>=0,n.domain((r=t.map(Number)).map(i)),o):r},o.base=function(e){return arguments.length?(t=+e,n.domain(r.map(i)),o):t},o.nice=function(){var t=Hi(r.map(i),e?Math:yl);return n.domain(t),r=t.map(u),o},o.ticks=function(){var n=ji(r),o=[],a=n[0],l=n[1],c=Math.floor(i(a)),f=Math.ceil(i(l)),s=t%1?2:t;if(isFinite(f-c)){if(e){for(;c<f;c++)for(h=1;h<s;h++)o.push(u(c)*h);o.push(u(c))}else for(o.push(u(c));c++<f;)for(var h=s-1;h>0;h--)o.push(u(c)*h);for(c=0;o[c]<a;c++);for(f=o.length;o[f-1]>l;f--);o=o.slice(c,f)}return o},o.tickFormat=function(n,e){if(!arguments.length)return vl;arguments.length<2?e=vl:"function"!=typeof e&&(e=to.format(e));var r=Math.max(1,t*n/o.ticks().length);return function(n){var o=n/u(Math.round(i(n)));return o*t<t-.5&&(o*=t),o<=r?e(n):""}},o.copy=function(){return Gi(n.copy(),t,e,r)},Zi(o,n)}function Ki(n,t,e){function r(t){return n(i(t))}var i=Qi(t),u=Qi(1/t);return r.invert=function(t){return u(n.invert(t))},r.domain=function(t){return arguments.length?(n.domain((e=t.map(Number)).map(i)),r):e},r.ticks=function(n){return $i(e,n)},r.tickFormat=function(n,t){return Bi(e,n,t)},r.nice=function(n){return r.domain(Vi(e,n))},r.exponent=function(o){return arguments.length?(i=Qi(t=o),u=Qi(1/t),n.domain(e.map(i)),r):t},r.copy=function(){return Ki(n.copy(),t,e)},Zi(r,n)}function Qi(n){return function(t){return t<0?-Math.pow(-t,n):Math.pow(t,n)}}function nu(n,t){function e(e){return u[((i.get(e)||("range"===t.t?i.set(e,n.push(e)):NaN))-1)%u.length]}function r(t,e){return to.range(n.length).map(function(n){return t+e*n})}var i,u,o;return e.domain=function(r){if(!arguments.length)return n;n=[],i=new c;for(var u,o=-1,a=r.length;++o<a;)i.has(u=r[o])||i.set(u,n.push(u));return e[t.t].apply(e,t.a)},e.range=function(n){return arguments.length?(u=n,o=0,t={t:"range",a:arguments},e):u},e.rangePoints=function(i,a){arguments.length<2&&(a=0);var l=i[0],c=i[1],f=n.length<2?(l=(l+c)/2,0):(c-l)/(n.length-1+a);return u=r(l+f*a/2,f),o=0,t={t:"rangePoints",a:arguments},e},e.rangeRoundPoints=function(i,a){arguments.length<2&&(a=0);var l=i[0],c=i[1],f=n.length<2?(l=c=Math.round((l+c)/2),0):(c-l)/(n.length-1+a)|0;return u=r(l+Math.round(f*a/2+(c-l-(n.length-1+a)*f)/2),f),o=0,t={t:"rangeRoundPoints",a:arguments},e},e.rangeBands=function(i,a,l){arguments.length<2&&(a=0),arguments.length<3&&(l=a);var c=i[1]<i[0],f=i[c-0],s=(i[1-c]-f)/(n.length-a+2*l);return u=r(f+s*l,s),c&&u.reverse(),o=s*(1-a),t={t:"rangeBands",a:arguments},e},e.rangeRoundBands=function(i,a,l){arguments.length<2&&(a=0),arguments.length<3&&(l=a);var c=i[1]<i[0],f=i[c-0],s=i[1-c],h=Math.floor((s-f)/(n.length-a+2*l));return u=r(f+Math.round((s-f-(n.length-a)*h)/2),h),c&&u.reverse(),o=Math.round(h*(1-a)),t={t:"rangeRoundBands",a:arguments},e},e.rangeBand=function(){return o},e.rangeExtent=function(){return ji(t.a[0])},e.copy=function(){return nu(n,t)},e.domain(n)}function tu(n,t){function u(){var e=0,r=t.length;for(a=[];++e<r;)a[e-1]=to.quantile(n,e/r);return o}function o(n){if(!isNaN(n=+n))return t[to.bisect(a,n)]}var a;return o.domain=function(t){return arguments.length?(n=t.map(r).filter(i).sort(e),u()):n},o.range=function(n){return arguments.length?(t=n,u()):t},o.quantiles=function(){return a},o.invertExtent=function(e){return e=t.indexOf(e),e<0?[NaN,NaN]:[e>0?a[e-1]:n[0],e<a.length?a[e]:n[n.length-1]]},o.copy=function(){return tu(n,t)},u()}function eu(n,t,e){function r(t){return e[Math.max(0,Math.min(o,Math.floor(u*(t-n))))]}function i(){return u=e.length/(t-n),o=e.length-1,r}var u,o;return r.domain=function(e){return arguments.length?(n=+e[0],t=+e[e.length-1],i()):[n,t]},r.range=function(n){return arguments.length?(e=n,i()):e},r.invertExtent=function(t){return t=e.indexOf(t),t=t<0?NaN:t/u+n,[t,t+1/u]},r.copy=function(){return eu(n,t,e)},i()}function ru(n,t){function e(e){if(e<=e)return t[to.bisect(n,e)]}return e.domain=function(t){return arguments.length?(n=t,e):n},e.range=function(n){return arguments.length?(t=n,e):t},e.invertExtent=function(e){return e=t.indexOf(e),[n[e-1],n[e]]},e.copy=function(){return ru(n,t)},e}function iu(n){function t(n){return+n}return t.invert=t,t.domain=t.range=function(e){return arguments.length?(n=e.map(t),t):n},t.ticks=function(t){return $i(n,t)},t.tickFormat=function(t,e){return Bi(n,t,e)},t.copy=function(){return iu(n)},t}function uu(){return 0}function ou(n){return n.innerRadius}function au(n){return n.outerRadius}function lu(n){return n.startAngle}function cu(n){return n.endAngle}function fu(n){return n&&n.padAngle}function su(n,t,e,r){return(n-e)*t-(t-r)*n>0?0:1}function hu(n,t,e,r,i){var u=n[0]-t[0],o=n[1]-t[1],a=(i?r:-r)/Math.sqrt(u*u+o*o),l=a*o,c=-a*u,f=n[0]+l,s=n[1]+c,h=t[0]+l,p=t[1]+c,g=(f+h)/2,d=(s+p)/2,v=h-f,y=p-s,m=v*v+y*y,x=e-r,M=f*p-h*s,_=(y<0?-1:1)*Math.sqrt(Math.max(0,x*x*m-M*M)),b=(M*y-v*_)/m,w=(-M*v-y*_)/m,S=(M*y+v*_)/m,k=(-M*v+y*_)/m,N=b-g,E=w-d,A=S-g,C=k-d;return N*N+E*E>A*A+C*C&&(b=S,w=k),[[b-l,w-c],[b*e/x,w*e/x]]}function pu(n){function t(t){function o(){c.push("M",u(n(f),a))}for(var l,c=[],f=[],s=-1,h=t.length,p=En(e),g=En(r);++s<h;)i.call(this,l=t[s],s)?f.push([+p.call(this,l,s),+g.call(this,l,s)]):f.length&&(o(),f=[]);return f.length&&o(),c.length?c.join(""):null}var e=Ce,r=ze,i=zt,u=gu,o=u.key,a=.7;return t.x=function(n){return arguments.length?(e=n,t):e},t.y=function(n){return arguments.length?(r=n,t):r},t.defined=function(n){return arguments.length?(i=n,t):i},t.interpolate=function(n){return arguments.length?(o="function"==typeof n?u=n:(u=wl.get(n)||gu).key,t):o},t.tension=function(n){return arguments.length?(a=n,t):a},t}function gu(n){return n.length>1?n.join("L"):n+"Z"}function du(n){return n.join("L")+"Z"}function vu(n){for(var t=0,e=n.length,r=n[0],i=[r[0],",",r[1]];++t<e;)i.push("H",(r[0]+(r=n[t])[0])/2,"V",r[1]);return e>1&&i.push("H",r[0]),i.join("")}function yu(n){for(var t=0,e=n.length,r=n[0],i=[r[0],",",r[1]];++t<e;)i.push("V",(r=n[t])[1],"H",r[0]);return i.join("")}function mu(n){for(var t=0,e=n.length,r=n[0],i=[r[0],",",r[1]];++t<e;)i.push("H",(r=n[t])[0],"V",r[1]);return i.join("")}function xu(n,t){return n.length<4?gu(n):n[1]+bu(n.slice(1,-1),wu(n,t))}function Mu(n,t){return n.length<3?du(n):n[0]+bu((n.push(n[0]),n),wu([n[n.length-2]].concat(n,[n[1]]),t))}function _u(n,t){return n.length<3?gu(n):n[0]+bu(n,wu(n,t))}function bu(n,t){if(t.length<1||n.length!=t.length&&n.length!=t.length+2)return gu(n);var e=n.length!=t.length,r="",i=n[0],u=n[1],o=t[0],a=o,l=1;if(e&&(r+="Q"+(u[0]-2*o[0]/3)+","+(u[1]-2*o[1]/3)+","+u[0]+","+u[1],i=n[1],l=2),t.length>1){a=t[1],u=n[l],l++,r+="C"+(i[0]+o[0])+","+(i[1]+o[1])+","+(u[0]-a[0])+","+(u[1]-a[1])+","+u[0]+","+u[1];for(var c=2;c<t.length;c++,l++)u=n[l],a=t[c],r+="S"+(u[0]-a[0])+","+(u[1]-a[1])+","+u[0]+","+u[1]}if(e){var f=n[l];r+="Q"+(u[0]+2*a[0]/3)+","+(u[1]+2*a[1]/3)+","+f[0]+","+f[1]}return r}function wu(n,t){for(var e,r=[],i=(1-t)/2,u=n[0],o=n[1],a=1,l=n.length;++a<l;)e=u,u=o,o=n[a],r.push([i*(o[0]-e[0]),i*(o[1]-e[1])]);return r}function Su(n){if(n.length<3)return gu(n);var t=1,e=n.length,r=n[0],i=r[0],u=r[1],o=[i,i,i,(r=n[1])[0]],a=[u,u,u,r[1]],l=[i,",",u,"L",Au(Nl,o),",",Au(Nl,a)];for(n.push(n[e-1]);++t<=e;)r=n[t],o.shift(),o.push(r[0]),a.shift(),a.push(r[1]),Cu(l,o,a);return n.pop(),l.push("L",r),l.join("")}function ku(n){if(n.length<4)return gu(n);for(var t,e=[],r=-1,i=n.length,u=[0],o=[0];++r<3;)t=n[r],u.push(t[0]),o.push(t[1]);for(e.push(Au(Nl,u)+","+Au(Nl,o)),--r;++r<i;)t=n[r],u.shift(),u.push(t[0]),o.shift(),o.push(t[1]),Cu(e,u,o);return e.join("")}function Nu(n){for(var t,e,r=-1,i=n.length,u=i+4,o=[],a=[];++r<4;)e=n[r%i],o.push(e[0]),a.push(e[1]);for(t=[Au(Nl,o),",",Au(Nl,a)],--r;++r<u;)e=n[r%i],o.shift(),o.push(e[0]),a.shift(),a.push(e[1]),Cu(t,o,a);return t.join("")}function Eu(n,t){var e=n.length-1;if(e)for(var r,i,u=n[0][0],o=n[0][1],a=n[e][0]-u,l=n[e][1]-o,c=-1;++c<=e;)i=c/e,(r=n[c])[0]=t*r[0]+(1-t)*(u+i*a),r[1]=t*r[1]+(1-t)*(o+i*l);return Su(n)}function Au(n,t){return n[0]*t[0]+n[1]*t[1]+n[2]*t[2]+n[3]*t[3]}function Cu(n,t,e){n.push("C",Au(Sl,t),",",Au(Sl,e),",",Au(kl,t),",",Au(kl,e),",",Au(Nl,t),",",Au(Nl,e))}function zu(n,t){return(t[1]-n[1])/(t[0]-n[0])}function Lu(n){for(var t=0,e=n.length-1,r=[],i=n[0],u=n[1],o=r[0]=zu(i,u);++t<e;)r[t]=(o+(o=zu(i=u,u=n[t+1])))/2;return r[t]=o,r}function qu(n){for(var t,e,r,i,u=[],o=Lu(n),a=-1,l=n.length-1;++a<l;)t=zu(n[a],n[a+1]),so(t)<Co?o[a]=o[a+1]=0:(i=(e=o[a]/t)*e+(r=o[a+1]/t)*r)>9&&(i=3*t/Math.sqrt(i),o[a]=i*e,o[a+1]=i*r);for(a=-1;++a<=l;)i=(n[Math.min(l,a+1)][0]-n[Math.max(0,a-1)][0])/(6*(1+o[a]*o[a])),u.push([i||0,o[a]*i||0]);return u}function Tu(n){return n.length<3?gu(n):n[0]+bu(n,qu(n))}function Ru(n){for(var t,e,r,i=-1,u=n.length;++i<u;)e=(t=n[i])[0],r=t[1]-Ro,t[0]=e*Math.cos(r),t[1]=e*Math.sin(r);return n}function Du(n){function t(t){function l(){d.push("M",a(n(y),s),f,c(n(v.reverse()),s),"Z")}for(var h,p,g,d=[],v=[],y=[],m=-1,x=t.length,M=En(e),_=En(i),b=e===r?function(){return p}:En(r),w=i===u?function(){return g}:En(u);++m<x;)o.call(this,h=t[m],m)?(v.push([p=+M.call(this,h,m),g=+_.call(this,h,m)]),y.push([+b.call(this,h,m),+w.call(this,h,m)])):v.length&&(l(),v=[],y=[]);return v.length&&l(),d.length?d.join(""):null}var e=Ce,r=Ce,i=0,u=ze,o=zt,a=gu,l=a.key,c=a,f="L",s=.7;return t.x=function(n){return arguments.length?(e=r=n,t):r},t.x0=function(n){return arguments.length?(e=n,t):e},t.x1=function(n){return arguments.length?(r=n,t):r},t.y=function(n){return arguments.length?(i=u=n,t):u},t.y0=function(n){return arguments.length?(i=n,t):i},t.y1=function(n){return arguments.length?(u=n,t):u},t.defined=function(n){return arguments.length?(o=n,t):o},t.interpolate=function(n){return arguments.length?(l="function"==typeof n?a=n:(a=wl.get(n)||gu).key,c=a.reverse||a,f=a.closed?"M":"L",t):l},t.tension=function(n){return arguments.length?(s=n,t):s},t}function Pu(n){return n.radius}function ju(n){return[n.x,n.y]}function Uu(n){return function(){var t=n.apply(this,arguments),e=t[0],r=t[1]-Ro;return[e*Math.cos(r),e*Math.sin(r)]}}function Fu(){return 64}function Hu(){return"circle"}function Ou(n){var t=Math.sqrt(n/Lo);return"M0,"+t+"A"+t+","+t+" 0 1,1 0,"+-t+"A"+t+","+t+" 0 1,1 0,"+t+"Z"}function Yu(n){return function(){var t,e,r;(t=this[n])&&(r=t[e=t.active])&&(r.timer.c=null,r.timer.t=NaN,--t.count?delete t[e]:delete this[n],t.active+=.5,r.event&&r.event.interrupt.call(this,this.__data__,r.index))}}function Iu(n,t,e){return yo(n,Tl),n.namespace=t,n.id=e,n}function Zu(n,t,e,r){var i=n.id,u=n.namespace;return I(n,"function"==typeof e?function(n,o,a){n[u][i].tween.set(t,r(e.call(n,n.__data__,o,a)))}:(e=r(e),function(n){n[u][i].tween.set(t,e)}))}function Vu(n){return null==n&&(n=""),function(){this.textContent=n}}function Xu(n){return null==n?"__transition__":"__transition_"+n+"__"}function $u(n,t,e,r,i){function u(n){var t=d.delay;if(f.t=t+l,t<=n)return o(n-t);f.c=o}function o(e){var i=g.active,u=g[i];u&&(u.timer.c=null,u.timer.t=NaN,--g.count,delete g[i],u.event&&u.event.interrupt.call(n,n.__data__,u.index));for(var o in g)if(+o<r){var c=g[o];c.timer.c=null,c.timer.t=NaN,--g.count,delete g[o]}f.c=a,qn(function(){return f.c&&a(e||1)&&(f.c=null,f.t=NaN),1},0,l),g.active=r,d.event&&d.event.start.call(n,n.__data__,t),p=[],d.tween.forEach(function(e,r){(r=r.call(n,n.__data__,t))&&p.push(r)}),h=d.ease,s=d.duration}function a(i){for(var u=i/s,o=h(u),a=p.length;a>0;)p[--a].call(n,o);if(u>=1)return d.event&&d.event.end.call(n,n.__data__,t),--g.count?delete g[r]:delete n[e],1}var l,f,s,h,p,g=n[e]||(n[e]={active:0,count:0}),d=g[r];d||(l=i.time,f=qn(u,0,l),d=g[r]={tween:new c,time:l,timer:f,delay:i.delay,duration:i.duration,ease:i.ease,index:t},i=null,++g.count)}function Bu(n,t,e){n.attr("transform",function(n){var r=t(n);return"translate("+(isFinite(r)?r:e(n))+",0)"})}function Wu(n,t,e){n.attr("transform",function(n){var r=t(n);return"translate(0,"+(isFinite(r)?r:e(n))+")"})}function Ju(n){return n.toISOString()}function Gu(n,t,e){function r(t){return n(t)}function i(n,e){var r=(n[1]-n[0])/e,i=to.bisect(Yl,r);return i==Yl.length?[t.year,Xi(n.map(function(n){return n/31536e6}),e)[2]]:i?t[r/Yl[i-1]<Yl[i]/r?i-1:i]:[Vl,Xi(n,e)[2]]}return r.invert=function(t){return Ku(n.invert(t))},r.domain=function(t){return arguments.length?(n.domain(t),r):n.domain().map(Ku)},r.nice=function(n,t){function e(e){return!isNaN(e)&&!n.range(e,Ku(+e+1),t).length}var u=r.domain(),o=ji(u),a=null==n?i(o,10):"number"==typeof n&&i(o,n);return a&&(n=a[0],t=a[1]),r.domain(Hi(u,t>1?{floor:function(t){for(;e(t=n.floor(t));)t=Ku(t-1);return t},ceil:function(t){for(;e(t=n.ceil(t));)t=Ku(+t+1);return t}}:n))},r.ticks=function(n,t){var e=ji(r.domain()),u=null==n?i(e,10):"number"==typeof n?i(e,n):!n.range&&[{range:n},t];return u&&(n=u[0],t=u[1]),n.range(e[0],Ku(+e[1]+1),t<1?1:t)},r.tickFormat=function(){return e},r.copy=function(){return Gu(n.copy(),t,e)},Zi(r,n)}function Ku(n){return new Date(n)}function Qu(n){return JSON.parse(n.responseText)}function no(n){var t=io.createRange();return t.selectNode(io.body),t.createContextualFragment(n.responseText)}var to={version:"3.5.17"},eo=[].slice,ro=function(n){return eo.call(n)},io=this.document;if(io)try{ro(io.documentElement.childNodes)[0].nodeType}catch(n){ro=function(n){for(var t=n.length,e=new Array(t);t--;)e[t]=n[t];return e}}if(Date.now||(Date.now=function(){return+new Date}),io)try{io.createElement("DIV").style.setProperty("opacity",0,"")}catch(n){var uo=this.Element.prototype,oo=uo.setAttribute,ao=uo.setAttributeNS,lo=this.CSSStyleDeclaration.prototype,co=lo.setProperty;uo.setAttribute=function(n,t){oo.call(this,n,t+"")},uo.setAttributeNS=function(n,t,e){ao.call(this,n,t,e+"")},lo.setProperty=function(n,t,e){co.call(this,n,t+"",e)}}to.ascending=e,to.descending=function(n,t){return t<n?-1:t>n?1:t>=n?0:NaN},to.min=function(n,t){var e,r,i=-1,u=n.length;if(1===arguments.length){for(;++i<u;)if(null!=(r=n[i])&&r>=r){e=r;break}for(;++i<u;)null!=(r=n[i])&&e>r&&(e=r)}else{for(;++i<u;)if(null!=(r=t.call(n,n[i],i))&&r>=r){e=r;break}for(;++i<u;)null!=(r=t.call(n,n[i],i))&&e>r&&(e=r)}return e},to.max=function(n,t){var e,r,i=-1,u=n.length;if(1===arguments.length){for(;++i<u;)if(null!=(r=n[i])&&r>=r){e=r;break}for(;++i<u;)null!=(r=n[i])&&r>e&&(e=r)}else{for(;++i<u;)if(null!=(r=t.call(n,n[i],i))&&r>=r){e=r;break}for(;++i<u;)null!=(r=t.call(n,n[i],i))&&r>e&&(e=r)}return e},to.extent=function(n,t){var e,r,i,u=-1,o=n.length;if(1===arguments.length){for(;++u<o;)if(null!=(r=n[u])&&r>=r){e=i=r;break}for(;++u<o;)null!=(r=n[u])&&(e>r&&(e=r),i<r&&(i=r))}else{for(;++u<o;)if(null!=(r=t.call(n,n[u],u))&&r>=r){e=i=r;break}for(;++u<o;)null!=(r=t.call(n,n[u],u))&&(e>r&&(e=r),i<r&&(i=r))}return[e,i]},to.sum=function(n,t){var e,r=0,u=n.length,o=-1;if(1===arguments.length)for(;++o<u;)i(e=+n[o])&&(r+=e);else for(;++o<u;)i(e=+t.call(n,n[o],o))&&(r+=e);return r},to.mean=function(n,t){var e,u=0,o=n.length,a=-1,l=o;if(1===arguments.length)for(;++a<o;)i(e=r(n[a]))?u+=e:--l;else for(;++a<o;)i(e=r(t.call(n,n[a],a)))?u+=e:--l;if(l)return u/l},to.quantile=function(n,t){var e=(n.length-1)*t+1,r=Math.floor(e),i=+n[r-1],u=e-r;return u?i+u*(n[r]-i):i},to.median=function(n,t){var u,o=[],a=n.length,l=-1;if(1===arguments.length)for(;++l<a;)i(u=r(n[l]))&&o.push(u);else for(;++l<a;)i(u=r(t.call(n,n[l],l)))&&o.push(u);if(o.length)return to.quantile(o.sort(e),.5)},to.variance=function(n,t){var e,u,o=n.length,a=0,l=0,c=-1,f=0;if(1===arguments.length)for(;++c<o;)i(e=r(n[c]))&&(l+=(u=e-a)*(e-(a+=u/++f)));else for(;++c<o;)i(e=r(t.call(n,n[c],c)))&&(l+=(u=e-a)*(e-(a+=u/++f)));if(f>1)return l/(f-1)},to.deviation=function(){var n=to.variance.apply(this,arguments);return n?Math.sqrt(n):n};var fo=u(e);to.bisectLeft=fo.left,to.bisect=to.bisectRight=fo.right,to.bisector=function(n){return u(1===n.length?function(t,r){return e(n(t),r)}:n)},to.shuffle=function(n,t,e){(u=arguments.length)<3&&(e=n.length,u<2&&(t=0));for(var r,i,u=e-t;u;)i=Math.random()*u--|0,r=n[u+t],n[u+t]=n[i+t],n[i+t]=r;return n},to.permute=function(n,t){for(var e=t.length,r=new Array(e);e--;)r[e]=n[t[e]];return r},to.pairs=function(n){for(var t=0,e=n.length-1,r=n[0],i=new Array(e<0?0:e);t<e;)i[t]=[r,r=n[++t]];return i},to.transpose=function(n){if(!(i=n.length))return[];for(var t=-1,e=to.min(n,o),r=new Array(e);++t<e;)for(var i,u=-1,a=r[t]=new Array(i);++u<i;)a[u]=n[u][t];return r},to.zip=function(){return to.transpose(arguments)},to.keys=function(n){var t=[];for(var e in n)t.push(e);return t},to.values=function(n){var t=[];for(var e in n)t.push(n[e]);return t},to.entries=function(n){var t=[];for(var e in n)t.push({key:e,value:n[e]});return t},to.merge=function(n){for(var t,e,r,i=n.length,u=-1,o=0;++u<i;)o+=n[u].length;for(e=new Array(o);--i>=0;)for(t=(r=n[i]).length;--t>=0;)e[--o]=r[t];return e};var so=Math.abs;to.range=function(n,t,e){if(arguments.length<3&&(e=1,arguments.length<2&&(t=n,n=0)),(t-n)/e==1/0)throw new Error("infinite range");var r,i=[],u=a(so(e)),o=-1;if(n*=u,t*=u,(e*=u)<0)for(;(r=n+e*++o)>t;)i.push(r/u);else for(;(r=n+e*++o)<t;)i.push(r/u);return i},to.map=function(n,t){var e=new c;if(n instanceof c)n.forEach(function(n,t){e.set(n,t)});else if(Array.isArray(n)){var r,i=-1,u=n.length;if(1===arguments.length)for(;++i<u;)e.set(i,n[i]);else for(;++i<u;)e.set(t.call(n,r=n[i],i),r)}else for(var o in n)e.set(o,n[o]);return e};var ho="__proto__",po="\0";l(c,{has:h,get:function(n){return this._[f(n)]},set:function(n,t){return this._[f(n)]=t},remove:p,keys:g,values:function(){var n=[];for(var t in this._)n.push(this._[t]);return n},entries:function(){var n=[];for(var t in this._)n.push({key:s(t),value:this._[t]});return n},size:d,empty:v,forEach:function(n){for(var t in this._)n.call(this,s(t),this._[t])}}),to.nest=function(){function n(t,o,a){if(a>=u.length)return r?r.call(i,o):e?o.sort(e):o;for(var l,f,s,h,p=-1,g=o.length,d=u[a++],v=new c;++p<g;)(h=v.get(l=d(f=o[p])))?h.push(f):v.set(l,[f]);return t?(f=t(),s=function(e,r){f.set(e,n(t,r,a))}):(f={},s=function(e,r){f[e]=n(t,r,a)}),v.forEach(s),f}function t(n,e){if(e>=u.length)return n;var r=[],i=o[e++];return n.forEach(function(n,i){r.push({key:n,values:t(i,e)})}),i?r.sort(function(n,t){return i(n.key,t.key)}):r}var e,r,i={},u=[],o=[];return i.map=function(t,e){return n(e,t,0)},i.entries=function(e){return t(n(to.map,e,0),0)},i.key=function(n){return u.push(n),i},i.sortKeys=function(n){return o[u.length-1]=n,i},i.sortValues=function(n){return e=n,i},i.rollup=function(n){return r=n,i},i},to.set=function(n){var t=new y;if(n)for(var e=0,r=n.length;e<r;++e)t.add(n[e]);return t},l(y,{has:h,add:function(n){return this._[f(n+="")]=!0,n},remove:p,values:g,size:d,empty:v,forEach:function(n){for(var t in this._)n.call(this,s(t))}}),to.behavior={},to.rebind=function(n,t){for(var e,r=1,i=arguments.length;++r<i;)n[e=arguments[r]]=x(n,t,t[e]);return n};var go=["webkit","ms","moz","Moz","o","O"];to.dispatch=function(){for(var n=new b,t=-1,e=arguments.length;++t<e;)n[arguments[t]]=w(n);return n},b.prototype.on=function(n,t){var e=n.indexOf("."),r="";if(e>=0&&(r=n.slice(e+1),n=n.slice(0,e)),n)return arguments.length<2?this[n].on(r):this[n].on(r,t);if(2===arguments.length){if(null==t)for(n in this)this.hasOwnProperty(n)&&this[n].on(r,null);return this}},to.event=null,to.requote=function(n){return n.replace(vo,"\\$&")};var vo=/[\\\^\$\*\+\?\|\[\]\(\)\.\{\}]/g,yo={}.__proto__?function(n,t){n.__proto__=t}:function(n,t){for(var e in t)n[e]=t[e]},mo=function(n,t){return t.querySelector(n)},xo=function(n,t){return t.querySelectorAll(n)},Mo=function(n,t){var e=n.matches||n[M(n,"matchesSelector")];return(Mo=function(n,t){return e.call(n,t)})(n,t)};"function"==typeof Sizzle&&(mo=function(n,t){return Sizzle(n,t)[0]||null},xo=Sizzle,Mo=Sizzle.matchesSelector),to.selection=function(){return to.select(io.documentElement)};var _o=to.selection.prototype=[];_o.select=function(n){var t,e,r,i,u=[];n=A(n);for(var o=-1,a=this.length;++o<a;){u.push(t=[]),t.parentNode=(r=this[o]).parentNode;for(var l=-1,c=r.length;++l<c;)(i=r[l])?(t.push(e=n.call(i,i.__data__,l,o)),e&&"__data__"in i&&(e.__data__=i.__data__)):t.push(null)}return E(u)},_o.selectAll=function(n){var t,e,r=[];n=C(n);for(var i=-1,u=this.length;++i<u;)for(var o=this[i],a=-1,l=o.length;++a<l;)(e=o[a])&&(r.push(t=ro(n.call(e,e.__data__,a,i))),t.parentNode=e);return E(r)};var bo="http://www.w3.org/1999/xhtml",wo={svg:"http://www.w3.org/2000/svg",xhtml:bo,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};to.ns={prefix:wo,qualify:function(n){var t=n.indexOf(":"),e=n;return t>=0&&"xmlns"!==(e=n.slice(0,t))&&(n=n.slice(t+1)),wo.hasOwnProperty(e)?{space:wo[e],local:n}:n}},_o.attr=function(n,t){if(arguments.length<2){if("string"==typeof n){var e=this.node();return n=to.ns.qualify(n),n.local?e.getAttributeNS(n.space,n.local):e.getAttribute(n)}for(t in n)this.each(z(t,n[t]));return this}return this.each(z(n,t))},_o.classed=function(n,t){if(arguments.length<2){if("string"==typeof n){var e=this.node(),r=(n=T(n)).length,i=-1;if(t=e.classList){for(;++i<r;)if(!t.contains(n[i]))return!1}else for(t=e.getAttribute("class");++i<r;)if(!q(n[i]).test(t))return!1;return!0}for(t in n)this.each(R(t,n[t]));return this}return this.each(R(n,t))},_o.style=function(n,e,r){var i=arguments.length;if(i<3){if("string"!=typeof n){i<2&&(e="");for(r in n)this.each(P(r,n[r],e));return this}if(i<2){var u=this.node();return t(u).getComputedStyle(u,null).getPropertyValue(n)}r=""}return this.each(P(n,e,r))},_o.property=function(n,t){if(arguments.length<2){if("string"==typeof n)return this.node()[n];for(t in n)this.each(j(t,n[t]));return this}return this.each(j(n,t))},_o.text=function(n){return arguments.length?this.each("function"==typeof n?function(){var t=n.apply(this,arguments);this.textContent=null==t?"":t}:null==n?function(){this.textContent=""}:function(){this.textContent=n}):this.node().textContent},_o.html=function(n){return arguments.length?this.each("function"==typeof n?function(){var t=n.apply(this,arguments);this.innerHTML=null==t?"":t}:null==n?function(){this.innerHTML=""}:function(){this.innerHTML=n}):this.node().innerHTML},_o.append=function(n){return n=U(n),this.select(function(){return this.appendChild(n.apply(this,arguments))})},_o.insert=function(n,t){return n=U(n),t=A(t),this.select(function(){return this.insertBefore(n.apply(this,arguments),t.apply(this,arguments)||null)})},_o.remove=function(){return this.each(F)},_o.data=function(n,t){function e(n,e){var r,i,u,o=n.length,s=e.length,h=Math.min(o,s),p=new Array(s),g=new Array(s),d=new Array(o);if(t){var v,y=new c,m=new Array(o);for(r=-1;++r<o;)(i=n[r])&&(y.has(v=t.call(i,i.__data__,r))?d[r]=i:y.set(v,i),m[r]=v);for(r=-1;++r<s;)(i=y.get(v=t.call(e,u=e[r],r)))?!0!==i&&(p[r]=i,i.__data__=u):g[r]=H(u),y.set(v,!0);for(r=-1;++r<o;)r in m&&!0!==y.get(m[r])&&(d[r]=n[r])}else{for(r=-1;++r<h;)i=n[r],u=e[r],i?(i.__data__=u,p[r]=i):g[r]=H(u);for(;r<s;++r)g[r]=H(e[r]);for(;r<o;++r)d[r]=n[r]}g.update=p,g.parentNode=p.parentNode=d.parentNode=n.parentNode,a.push(g),l.push(p),f.push(d)}var r,i,u=-1,o=this.length;if(!arguments.length){for(n=new Array(o=(r=this[0]).length);++u<o;)(i=r[u])&&(n[u]=i.__data__);return n}var a=Z([]),l=E([]),f=E([]);if("function"==typeof n)for(;++u<o;)e(r=this[u],n.call(r,r.parentNode.__data__,u));else for(;++u<o;)e(r=this[u],n);return l.enter=function(){return a},l.exit=function(){return f},l},_o.datum=function(n){return arguments.length?this.property("__data__",n):this.property("__data__")},_o.filter=function(n){var t,e,r,i=[];"function"!=typeof n&&(n=O(n));for(var u=0,o=this.length;u<o;u++){i.push(t=[]),t.parentNode=(e=this[u]).parentNode;for(var a=0,l=e.length;a<l;a++)(r=e[a])&&n.call(r,r.__data__,a,u)&&t.push(r)}return E(i)},_o.order=function(){for(var n=-1,t=this.length;++n<t;)for(var e,r=this[n],i=r.length-1,u=r[i];--i>=0;)(e=r[i])&&(u&&u!==e.nextSibling&&u.parentNode.insertBefore(e,u),u=e);return this},_o.sort=function(n){n=Y.apply(this,arguments);for(var t=-1,e=this.length;++t<e;)this[t].sort(n);return this.order()},_o.each=function(n){return I(this,function(t,e,r){n.call(t,t.__data__,e,r)})},_o.call=function(n){var t=ro(arguments);return n.apply(t[0]=this,t),this},_o.empty=function(){return!this.node()},_o.node=function(){for(var n=0,t=this.length;n<t;n++)for(var e=this[n],r=0,i=e.length;r<i;r++){var u=e[r];if(u)return u}return null},_o.size=function(){var n=0;return I(this,function(){++n}),n};var So=[];to.selection.enter=Z,to.selection.enter.prototype=So,So.append=_o.append,So.empty=_o.empty,So.node=_o.node,So.call=_o.call,So.size=_o.size,So.select=function(n){for(var t,e,r,i,u,o=[],a=-1,l=this.length;++a<l;){r=(i=this[a]).update,o.push(t=[]),t.parentNode=i.parentNode;for(var c=-1,f=i.length;++c<f;)(u=i[c])?(t.push(r[c]=e=n.call(i.parentNode,u.__data__,c,a)),e.__data__=u.__data__):t.push(null)}return E(o)},So.insert=function(n,t){return arguments.length<2&&(t=V(this)),_o.insert.call(this,n,t)},to.select=function(t){var e;return"string"==typeof t?(e=[mo(t,io)]).parentNode=io.documentElement:(e=[t]).parentNode=n(t),E([e])},to.selectAll=function(n){var t;return"string"==typeof n?(t=ro(xo(n,io))).parentNode=io.documentElement:(t=ro(n)).parentNode=null,E([t])},_o.on=function(n,t,e){var r=arguments.length;if(r<3){if("string"!=typeof n){r<2&&(t=!1);for(e in n)this.each(X(e,n[e],t));return this}if(r<2)return(r=this.node()["__on"+n])&&r._;e=!1}return this.each(X(n,t,e))};var ko=to.map({mouseenter:"mouseover",mouseleave:"mouseout"});io&&ko.forEach(function(n){"on"+n in io&&ko.remove(n)});var No,Eo=0;to.mouse=function(n){return J(n,k())};var Ao=this.navigator&&/WebKit/.test(this.navigator.userAgent)?-1:0;to.touch=function(n,t,e){if(arguments.length<3&&(e=t,t=k().changedTouches),t)for(var r,i=0,u=t.length;i<u;++i)if((r=t[i]).identifier===e)return J(n,r)},to.behavior.drag=function(){function n(){this.on("mousedown.drag",u).on("touchstart.drag",o)}function e(n,t,e,u,o){return function(){function a(){var n,e,r=t(h,d);r&&(n=r[0]-x[0],e=r[1]-x[1],g|=n|e,x=r,p({type:"drag",x:r[0]+c[0],y:r[1]+c[1],dx:n,dy:e}))}function l(){t(h,d)&&(y.on(u+v,null).on(o+v,null),m(g),p({type:"dragend"}))}var c,f=this,s=to.event.target.correspondingElement||to.event.target,h=f.parentNode,p=r.of(f,arguments),g=0,d=n(),v=".drag"+(null==d?"":"-"+d),y=to.select(e(s)).on(u+v,a).on(o+v,l),m=W(s),x=t(h,d);i?(c=i.apply(f,arguments),c=[c.x-x[0],c.y-x[1]]):c=[0,0],p({type:"dragstart"})}}var r=N(n,"drag","dragstart","dragend"),i=null,u=e(_,to.mouse,t,"mousemove","mouseup"),o=e(G,to.touch,m,"touchmove","touchend");return n.origin=function(t){return arguments.length?(i=t,n):i},to.rebind(n,r,"on")},to.touches=function(n,t){return arguments.length<2&&(t=k().touches),t?ro(t).map(function(t){var e=J(n,t);return e.identifier=t.identifier,e}):[]};var Co=1e-6,zo=Co*Co,Lo=Math.PI,qo=2*Lo,To=qo-Co,Ro=Lo/2,Do=Lo/180,Po=180/Lo,jo=Math.SQRT2;to.interpolateZoom=function(n,t){var e,r,i=n[0],u=n[1],o=n[2],a=t[0],l=t[1],c=t[2],f=a-i,s=l-u,h=f*f+s*s;if(h<zo)r=Math.log(c/o)/jo,e=function(n){return[i+n*f,u+n*s,o*Math.exp(jo*n*r)]};else{var p=Math.sqrt(h),g=(c*c-o*o+4*h)/(2*o*2*p),d=(c*c-o*o-4*h)/(2*c*2*p),v=Math.log(Math.sqrt(g*g+1)-g),y=Math.log(Math.sqrt(d*d+1)-d);r=(y-v)/jo,e=function(n){var t=n*r,e=rn(v),a=o/(2*p)*(e*un(jo*t+v)-en(v));return[i+a*f,u+a*s,o*e/rn(jo*t+v)]}}return e.duration=1e3*r,e},to.behavior.zoom=function(){function n(n){n.on(L,s).on(Fo+".zoom",p).on("dblclick.zoom",g).on(R,h)}function e(n){return[(n[0]-k.x)/k.k,(n[1]-k.y)/k.k]}function r(n){return[n[0]*k.k+k.x,n[1]*k.k+k.y]}function i(n){k.k=Math.max(A[0],Math.min(A[1],n))}function u(n,t){t=r(t),k.x+=n[0]-t[0],k.y+=n[1]-t[1]}function o(t,e,r,o){t.__chart__={x:k.x,y:k.y,k:k.k},i(Math.pow(2,o)),u(v=e,r),t=to.select(t),C>0&&(t=t.transition().duration(C)),t.call(n.event)}function a(){_&&_.domain(M.range().map(function(n){return(n-k.x)/k.k}).map(M.invert)),w&&w.domain(b.range().map(function(n){return(n-k.y)/k.k}).map(b.invert))}function l(n){z++||n({type:"zoomstart"})}function c(n){a(),n({type:"zoom",scale:k.k,translate:[k.x,k.y]})}function f(n){--z||(n({type:"zoomend"}),v=null)}function s(){function n(){a=1,u(to.mouse(i),h),c(o)}function r(){s.on(q,null).on(T,null),p(a),f(o)}var i=this,o=D.of(i,arguments),a=0,s=to.select(t(i)).on(q,n).on(T,r),h=e(to.mouse(i)),p=W(i);ql.call(i),l(o)}function h(){function n(){var n=to.touches(g);return p=k.k,n.forEach(function(n){n.identifier in v&&(v[n.identifier]=e(n))}),n}function t(){var t=to.event.target;to.select(t).on(M,r).on(_,a),b.push(t);for(var e=to.event.changedTouches,i=0,u=e.length;i<u;++i)v[e[i].identifier]=null;var l=n(),c=Date.now();if(1===l.length){if(c-x<500){f=l[0];o(g,f,v[f.identifier],Math.floor(Math.log(k.k)/Math.LN2)+1),S()}x=c}else if(l.length>1){var f=l[0],s=l[1],h=f[0]-s[0],p=f[1]-s[1];y=h*h+p*p}}function r(){var n,t,e,r,o=to.touches(g);ql.call(g);for(var a=0,l=o.length;a<l;++a,r=null)if(e=o[a],r=v[e.identifier]){if(t)break;n=e,t=r}if(r){var f=(f=e[0]-n[0])*f+(f=e[1]-n[1])*f,s=y&&Math.sqrt(f/y);n=[(n[0]+e[0])/2,(n[1]+e[1])/2],t=[(t[0]+r[0])/2,(t[1]+r[1])/2],i(s*p)}x=null,u(n,t),c(d)}function a(){if(to.event.touches.length){for(var t=to.event.changedTouches,e=0,r=t.length;e<r;++e)delete v[t[e].identifier];for(var i in v)return void n()}to.selectAll(b).on(m,null),w.on(L,s).on(R,h),N(),f(d)}var p,g=this,d=D.of(g,arguments),v={},y=0,m=".zoom-"+to.event.changedTouches[0].identifier,M="touchmove"+m,_="touchend"+m,b=[],w=to.select(g),N=W(g);t(),l(d),w.on(L,null).on(R,t)}function p(){var n=D.of(this,arguments);m?clearTimeout(m):(ql.call(this),d=e(v=y||to.mouse(this)),l(n)),m=setTimeout(function(){m=null,f(n)},50),S(),i(Math.pow(2,.002*Uo())*k.k),u(v,d),c(n)}function g(){var n=to.mouse(this),t=Math.log(k.k)/Math.LN2;o(this,n,e(n),to.event.shiftKey?Math.ceil(t)-1:Math.floor(t)+1)}var d,v,y,m,x,M,_,b,w,k={x:0,y:0,k:1},E=[960,500],A=Ho,C=250,z=0,L="mousedown.zoom",q="mousemove.zoom",T="mouseup.zoom",R="touchstart.zoom",D=N(n,"zoomstart","zoom","zoomend");return Fo||(Fo="onwheel"in io?(Uo=function(){return-to.event.deltaY*(to.event.deltaMode?120:1)},"wheel"):"onmousewheel"in io?(Uo=function(){return to.event.wheelDelta},"mousewheel"):(Uo=function(){return-to.event.detail},"MozMousePixelScroll")),n.event=function(n){n.each(function(){var n=D.of(this,arguments),t=k;zl?to.select(this).transition().each("start.zoom",function(){k=this.__chart__||{x:0,y:0,k:1},l(n)}).tween("zoom:zoom",function(){var e=E[0],r=E[1],i=v?v[0]:e/2,u=v?v[1]:r/2,o=to.interpolateZoom([(i-k.x)/k.k,(u-k.y)/k.k,e/k.k],[(i-t.x)/t.k,(u-t.y)/t.k,e/t.k]);return function(t){var r=o(t),a=e/r[2];this.__chart__=k={x:i-r[0]*a,y:u-r[1]*a,k:a},c(n)}}).each("interrupt.zoom",function(){f(n)}).each("end.zoom",function(){f(n)}):(this.__chart__=k,l(n),c(n),f(n))})},n.translate=function(t){return arguments.length?(k={x:+t[0],y:+t[1],k:k.k},a(),n):[k.x,k.y]},n.scale=function(t){return arguments.length?(k={x:k.x,y:k.y,k:null},i(+t),a(),n):k.k},n.scaleExtent=function(t){return arguments.length?(A=null==t?Ho:[+t[0],+t[1]],n):A},n.center=function(t){return arguments.length?(y=t&&[+t[0],+t[1]],n):y},n.size=function(t){return arguments.length?(E=t&&[+t[0],+t[1]],n):E},n.duration=function(t){return arguments.length?(C=+t,n):C},n.x=function(t){return arguments.length?(_=t,M=t.copy(),k={x:0,y:0,k:1},n):_},n.y=function(t){return arguments.length?(w=t,b=t.copy(),k={x:0,y:0,k:1},n):w},to.rebind(n,D,"on")};var Uo,Fo,Ho=[0,1/0];to.color=an,an.prototype.toString=function(){return this.rgb()+""},to.hsl=ln;var Oo=ln.prototype=new an;Oo.brighter=function(n){return n=Math.pow(.7,arguments.length?n:1),new ln(this.h,this.s,this.l/n)},Oo.darker=function(n){return n=Math.pow(.7,arguments.length?n:1),new ln(this.h,this.s,n*this.l)},Oo.rgb=function(){return cn(this.h,this.s,this.l)},to.hcl=fn;var Yo=fn.prototype=new an;Yo.brighter=function(n){return new fn(this.h,this.c,Math.min(100,this.l+Io*(arguments.length?n:1)))},Yo.darker=function(n){return new fn(this.h,this.c,Math.max(0,this.l-Io*(arguments.length?n:1)))},Yo.rgb=function(){return sn(this.h,this.c,this.l).rgb()},to.lab=hn;var Io=18,Zo=.95047,Vo=1,Xo=1.08883,$o=hn.prototype=new an;$o.brighter=function(n){return new hn(Math.min(100,this.l+Io*(arguments.length?n:1)),this.a,this.b)},$o.darker=function(n){return new hn(Math.max(0,this.l-Io*(arguments.length?n:1)),this.a,this.b)},$o.rgb=function(){return pn(this.l,this.a,this.b)},to.rgb=mn;var Bo=mn.prototype=new an;Bo.brighter=function(n){n=Math.pow(.7,arguments.length?n:1);var t=this.r,e=this.g,r=this.b,i=30;return t||e||r?(t&&t<i&&(t=i),e&&e<i&&(e=i),r&&r<i&&(r=i),new mn(Math.min(255,t/n),Math.min(255,e/n),Math.min(255,r/n))):new mn(i,i,i)},Bo.darker=function(n){return n=Math.pow(.7,arguments.length?n:1),new mn(n*this.r,n*this.g,n*this.b)},Bo.hsl=function(){return wn(this.r,this.g,this.b)},Bo.toString=function(){return"#"+_n(this.r)+_n(this.g)+_n(this.b)};var Wo=to.map({aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074});Wo.forEach(function(n,t){Wo.set(n,xn(t))}),to.functor=En,to.xhr=An(m),to.dsv=function(n,t){function e(n,e,u){arguments.length<3&&(u=e,e=null);var o=Cn(n,t,null==e?r:i(e),u);return o.row=function(n){return arguments.length?o.response(null==(e=n)?r:i(n)):e},o}function r(n){return e.parse(n.responseText)}function i(n){return function(t){return e.parse(t.responseText,n)}}function u(t){return t.map(o).join(n)}function o(n){return a.test(n)?'"'+n.replace(/\"/g,'""')+'"':n}var a=new RegExp('["'+n+"\n]"),l=n.charCodeAt(0);return e.parse=function(n,t){var r;return e.parseRows(n,function(n,e){if(r)return r(n,e-1);var i=new Function("d","return {"+n.map(function(n,t){return JSON.stringify(n)+": d["+t+"]"}).join(",")+"}");r=t?function(n,e){return t(i(n),e)}:i})},e.parseRows=function(n,t){function e(){if(f>=c)return o;if(i)return i=!1,u;var t=f;if(34===n.charCodeAt(t)){for(var e=t;e++<c;)if(34===n.charCodeAt(e)){if(34!==n.charCodeAt(e+1))break;++e}return f=e+2,13===(r=n.charCodeAt(e+1))?(i=!0,10===n.charCodeAt(e+2)&&++f):10===r&&(i=!0),n.slice(t+1,e).replace(/""/g,'"')}for(;f<c;){var r=n.charCodeAt(f++),a=1;if(10===r)i=!0;else if(13===r)i=!0,10===n.charCodeAt(f)&&(++f,++a);else if(r!==l)continue;return n.slice(t,f-a)}return n.slice(t)}for(var r,i,u={},o={},a=[],c=n.length,f=0,s=0;(r=e())!==o;){for(var h=[];r!==u&&r!==o;)h.push(r),r=e();t&&null==(h=t(h,s++))||a.push(h)}return a},e.format=function(t){if(Array.isArray(t[0]))return e.formatRows(t);var r=new y,i=[];return t.forEach(function(n){for(var t in n)r.has(t)||i.push(r.add(t))}),[i.map(o).join(n)].concat(t.map(function(t){return i.map(function(n){return o(t[n])}).join(n)})).join("\n")},e.formatRows=function(n){return n.map(u).join("\n")},e},to.csv=to.dsv(",","text/csv"),to.tsv=to.dsv("\t","text/tab-separated-values");var Jo,Go,Ko,Qo,na=this[M(this,"requestAnimationFrame")]||function(n){setTimeout(n,17)};to.timer=function(){qn.apply(this,arguments)},to.timer.flush=function(){Rn(),Dn()},to.round=function(n,t){return t?Math.round(n*(t=Math.pow(10,t)))/t:Math.round(n)};var ta=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"].map(jn);to.formatPrefix=function(n,t){var e=0;return(n=+n)&&(n<0&&(n*=-1),t&&(n=to.round(n,Pn(n,t))),e=1+Math.floor(1e-12+Math.log(n)/Math.LN10),e=Math.max(-24,Math.min(24,3*Math.floor((e-1)/3)))),ta[8+e/3]};var ea=/(?:([^{])?([<>=^]))?([+\- ])?([$#])?(0)?(\d+)?(,)?(\.-?\d+)?([a-z%])?/i,ra=to.map({b:function(n){return n.toString(2)},c:function(n){return String.fromCharCode(n)},o:function(n){return n.toString(8)},x:function(n){return n.toString(16)},X:function(n){return n.toString(16).toUpperCase()},g:function(n,t){return n.toPrecision(t)},e:function(n,t){return n.toExponential(t)},f:function(n,t){return n.toFixed(t)},r:function(n,t){return(n=to.round(n,Pn(n,t))).toFixed(Math.max(0,Math.min(20,Pn(n*(1+1e-15),t))))}}),ia=to.time={},ua=Date;Hn.prototype={getDate:function(){return this._.getUTCDate()},getDay:function(){return this._.getUTCDay()},getFullYear:function(){return this._.getUTCFullYear()},getHours:function(){return this._.getUTCHours()},getMilliseconds:function(){return this._.getUTCMilliseconds()},getMinutes:function(){return this._.getUTCMinutes()},getMonth:function(){return this._.getUTCMonth()},getSeconds:function(){return this._.getUTCSeconds()},getTime:function(){return this._.getTime()},getTimezoneOffset:function(){return 0},valueOf:function(){return this._.valueOf()},setDate:function(){oa.setUTCDate.apply(this._,arguments)},setDay:function(){oa.setUTCDay.apply(this._,arguments)},setFullYear:function(){oa.setUTCFullYear.apply(this._,arguments)},setHours:function(){oa.setUTCHours.apply(this._,arguments)},setMilliseconds:function(){oa.setUTCMilliseconds.apply(this._,arguments)},setMinutes:function(){oa.setUTCMinutes.apply(this._,arguments)},setMonth:function(){oa.setUTCMonth.apply(this._,arguments)},setSeconds:function(){oa.setUTCSeconds.apply(this._,arguments)},setTime:function(){oa.setTime.apply(this._,arguments)}};var oa=Date.prototype;ia.year=On(function(n){return(n=ia.day(n)).setMonth(0,1),n},function(n,t){n.setFullYear(n.getFullYear()+t)},function(n){return n.getFullYear()}),ia.years=ia.year.range,ia.years.utc=ia.year.utc.range,ia.day=On(function(n){var t=new ua(2e3,0);return t.setFullYear(n.getFullYear(),n.getMonth(),n.getDate()),t},function(n,t){n.setDate(n.getDate()+t)},function(n){return n.getDate()-1}),ia.days=ia.day.range,ia.days.utc=ia.day.utc.range,ia.dayOfYear=function(n){var t=ia.year(n);return Math.floor((n-t-6e4*(n.getTimezoneOffset()-t.getTimezoneOffset()))/864e5)},["sunday","monday","tuesday","wednesday","thursday","friday","saturday"].forEach(function(n,t){t=7-t;var e=ia[n]=On(function(n){return(n=ia.day(n)).setDate(n.getDate()-(n.getDay()+t)%7),n},function(n,t){n.setDate(n.getDate()+7*Math.floor(t))},function(n){var e=ia.year(n).getDay();return Math.floor((ia.dayOfYear(n)+(e+t)%7)/7)-(e!==t)});ia[n+"s"]=e.range,ia[n+"s"].utc=e.utc.range,ia[n+"OfYear"]=function(n){var e=ia.year(n).getDay();return Math.floor((ia.dayOfYear(n)+(e+t)%7)/7)}}),ia.week=ia.sunday,ia.weeks=ia.sunday.range,ia.weeks.utc=ia.sunday.utc.range,ia.weekOfYear=ia.sundayOfYear;var aa={"-":"",_:" ",0:"0"},la=/^\s*\d+/,ca=/^%/;to.locale=function(n){return{numberFormat:Un(n),timeFormat:In(n)}};var fa=to.locale({decimal:".",thousands:",",grouping:[3],currency:["$",""],dateTime:"%a %b %e %X %Y",date:"%m/%d/%Y",time:"%H:%M:%S",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});to.format=fa.numberFormat,to.geo={},ft.prototype={s:0,t:0,add:function(n){st(n,this.t,sa),st(sa.s,this.s,this),this.s?this.t+=sa.t:this.s=sa.t},reset:function(){this.s=this.t=0},valueOf:function(){return this.s}};var sa=new ft;to.geo.stream=function(n,t){n&&ha.hasOwnProperty(n.type)?ha[n.type](n,t):ht(n,t)};var ha={Feature:function(n,t){ht(n.geometry,t)},FeatureCollection:function(n,t){for(var e=n.features,r=-1,i=e.length;++r<i;)ht(e[r].geometry,t)}},pa={Sphere:function(n,t){t.sphere()},Point:function(n,t){n=n.coordinates,t.point(n[0],n[1],n[2])},MultiPoint:function(n,t){for(var e=n.coordinates,r=-1,i=e.length;++r<i;)n=e[r],t.point(n[0],n[1],n[2])},LineString:function(n,t){pt(n.coordinates,t,0)},MultiLineString:function(n,t){for(var e=n.coordinates,r=-1,i=e.length;++r<i;)pt(e[r],t,0)},Polygon:function(n,t){gt(n.coordinates,t)},MultiPolygon:function(n,t){for(var e=n.coordinates,r=-1,i=e.length;++r<i;)gt(e[r],t)},GeometryCollection:function(n,t){for(var e=n.geometries,r=-1,i=e.length;++r<i;)ht(e[r],t)}};to.geo.area=function(n){return ga=0,to.geo.stream(n,va),ga};var ga,da=new ft,va={sphere:function(){ga+=4*Lo},point:_,lineStart:_,lineEnd:_,polygonStart:function(){da.reset(),va.lineStart=dt},polygonEnd:function(){var n=2*da;ga+=n<0?4*Lo+n:n,va.lineStart=va.lineEnd=va.point=_}};to.geo.bounds=function(){function n(n,t){x.push(M=[f=n,h=n]),t<s&&(s=t),t>p&&(p=t)}function t(t,e){var r=vt([t*Do,e*Do]);if(y){var i=mt(y,r),u=mt([i[1],-i[0],0],i);_t(u),u=bt(u);var o=t-g,l=o>0?1:-1,c=u[0]*Po*l,d=so(o)>180;if(d^(l*g<c&&c<l*t))(v=u[1]*Po)>p&&(p=v);else if(c=(c+360)%360-180,d^(l*g<c&&c<l*t)){var v=-u[1]*Po;v<s&&(s=v)}else e<s&&(s=e),e>p&&(p=e);d?t<g?a(f,t)>a(f,h)&&(h=t):a(t,h)>a(f,h)&&(f=t):h>=f?(t<f&&(f=t),t>h&&(h=t)):t>g?a(f,t)>a(f,h)&&(h=t):a(t,h)>a(f,h)&&(f=t)}else n(t,e);y=r,g=t}function e(){_.point=t}function r(){M[0]=f,M[1]=h,_.point=n,y=null}function i(n,e){if(y){var r=n-g;m+=so(r)>180?r+(r>0?360:-360):r}else d=n,v=e;va.point(n,e),t(n,e)}function u(){va.lineStart()}function o(){i(d,v),va.lineEnd(),so(m)>Co&&(f=-(h=180)),M[0]=f,M[1]=h,y=null}function a(n,t){return(t-=n)<0?t+360:t}function l(n,t){return n[0]-t[0]}function c(n,t){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}var f,s,h,p,g,d,v,y,m,x,M,_={point:n,lineStart:e,lineEnd:r,polygonStart:function(){_.point=i,_.lineStart=u,_.lineEnd=o,m=0,va.polygonStart()},polygonEnd:function(){va.polygonEnd(),_.point=n,_.lineStart=e,_.lineEnd=r,da<0?(f=-(h=180),s=-(p=90)):m>Co?p=90:m<-Co&&(s=-90),M[0]=f,M[1]=h}};return function(n){p=h=-(f=s=1/0),x=[],to.geo.stream(n,_);var t=x.length;if(t){x.sort(l);for(var e=1,r=[g=x[0]];e<t;++e)c((u=x[e])[0],g)||c(u[1],g)?(a(g[0],u[1])>a(g[0],g[1])&&(g[1]=u[1]),a(u[0],g[1])>a(g[0],g[1])&&(g[0]=u[0])):r.push(g=u);for(var i,u,o=-1/0,e=0,g=r[t=r.length-1];e<=t;g=u,++e)u=r[e],(i=a(g[1],u[0]))>o&&(o=i,f=u[0],h=g[1])}return x=M=null,f===1/0||s===1/0?[[NaN,NaN],[NaN,NaN]]:[[f,s],[h,p]]}}(),to.geo.centroid=function(n){ya=ma=xa=Ma=_a=ba=wa=Sa=ka=Na=Ea=0,to.geo.stream(n,Aa);var t=ka,e=Na,r=Ea,i=t*t+e*e+r*r;return i<zo&&(t=ba,e=wa,r=Sa,ma<Co&&(t=xa,e=Ma,r=_a),(i=t*t+e*e+r*r)<zo)?[NaN,NaN]:[Math.atan2(e,t)*Po,tn(r/Math.sqrt(i))*Po]};var ya,ma,xa,Ma,_a,ba,wa,Sa,ka,Na,Ea,Aa={sphere:_,point:St,lineStart:Nt,lineEnd:Et,polygonStart:function(){Aa.lineStart=At},polygonEnd:function(){Aa.lineStart=Nt}},Ca=Rt(zt,Ut,Ht,[-Lo,-Lo/2]),za=1e9;to.geo.clipExtent=function(){var n,t,e,r,i,u,o={stream:function(n){return i&&(i.valid=!1),i=u(n),i.valid=!0,i},extent:function(a){return arguments.length?(u=Zt(n=+a[0][0],t=+a[0][1],e=+a[1][0],r=+a[1][1]),i&&(i.valid=!1,i=null),o):[[n,t],[e,r]]}};return o.extent([[0,0],[960,500]])},(to.geo.conicEqualArea=function(){return Vt(Xt)}).raw=Xt,to.geo.albers=function(){return to.geo.conicEqualArea().rotate([96,0]).center([-.6,38.7]).parallels([29.5,45.5]).scale(1070)},to.geo.albersUsa=function(){function n(n){var u=n[0],o=n[1];return t=null,e(u,o),t||(r(u,o),t)||i(u,o),t}var t,e,r,i,u=to.geo.albers(),o=to.geo.conicEqualArea().rotate([154,0]).center([-2,58.5]).parallels([55,65]),a=to.geo.conicEqualArea().rotate([157,0]).center([-3,19.9]).parallels([8,18]),l={point:function(n,e){t=[n,e]}};return n.invert=function(n){var t=u.scale(),e=u.translate(),r=(n[0]-e[0])/t,i=(n[1]-e[1])/t;return(i>=.12&&i<.234&&r>=-.425&&r<-.214?o:i>=.166&&i<.234&&r>=-.214&&r<-.115?a:u).invert(n)},n.stream=function(n){var t=u.stream(n),e=o.stream(n),r=a.stream(n);return{point:function(n,i){t.point(n,i),e.point(n,i),r.point(n,i)},sphere:function(){t.sphere(),e.sphere(),r.sphere()},lineStart:function(){t.lineStart(),e.lineStart(),r.lineStart()},lineEnd:function(){t.lineEnd(),e.lineEnd(),r.lineEnd()},polygonStart:function(){t.polygonStart(),e.polygonStart(),r.polygonStart()},polygonEnd:function(){t.polygonEnd(),e.polygonEnd(),r.polygonEnd()}}},n.precision=function(t){return arguments.length?(u.precision(t),o.precision(t),a.precision(t),n):u.precision()},n.scale=function(t){return arguments.length?(u.scale(t),o.scale(.35*t),a.scale(t),n.translate(u.translate())):u.scale()},n.translate=function(t){if(!arguments.length)return u.translate();var c=u.scale(),f=+t[0],s=+t[1];return e=u.translate(t).clipExtent([[f-.455*c,s-.238*c],[f+.455*c,s+.238*c]]).stream(l).point,r=o.translate([f-.307*c,s+.201*c]).clipExtent([[f-.425*c+Co,s+.12*c+Co],[f-.214*c-Co,s+.234*c-Co]]).stream(l).point,i=a.translate([f-.205*c,s+.212*c]).clipExtent([[f-.214*c+Co,s+.166*c+Co],[f-.115*c-Co,s+.234*c-Co]]).stream(l).point,n},n.scale(1070)};var La,qa,Ta,Ra,Da,Pa,ja={point:_,lineStart:_,lineEnd:_,polygonStart:function(){qa=0,ja.lineStart=$t},polygonEnd:function(){ja.lineStart=ja.lineEnd=ja.point=_,La+=so(qa/2)}},Ua={point:Bt,lineStart:_,lineEnd:_,polygonStart:_,polygonEnd:_},Fa={point:Gt,lineStart:Kt,lineEnd:Qt,polygonStart:function(){Fa.lineStart=ne},polygonEnd:function(){Fa.point=Gt,Fa.lineStart=Kt,Fa.lineEnd=Qt}};to.geo.path=function(){function n(n){return n&&("function"==typeof a&&u.pointRadius(+a.apply(this,arguments)),o&&o.valid||(o=i(u)),to.geo.stream(n,o)),u.result()}function t(){return o=null,n}var e,r,i,u,o,a=4.5;return n.area=function(n){return La=0,to.geo.stream(n,i(ja)),La},n.centroid=function(n){return xa=Ma=_a=ba=wa=Sa=ka=Na=Ea=0,to.geo.stream(n,i(Fa)),Ea?[ka/Ea,Na/Ea]:Sa?[ba/Sa,wa/Sa]:_a?[xa/_a,Ma/_a]:[NaN,NaN]},n.bounds=function(n){return Da=Pa=-(Ta=Ra=1/0),to.geo.stream(n,i(Ua)),[[Ta,Ra],[Da,Pa]]},n.projection=function(n){return arguments.length?(i=(e=n)?n.stream||re(n):m,t()):e},n.context=function(n){return arguments.length?(u=null==(r=n)?new Wt:new te(n),"function"!=typeof a&&u.pointRadius(a),t()):r},n.pointRadius=function(t){return arguments.length?(a="function"==typeof t?t:(u.pointRadius(+t),+t),n):a},n.projection(to.geo.albersUsa()).context(null)},to.geo.transform=function(n){return{stream:function(t){var e=new ie(t);for(var r in n)e[r]=n[r];return e}}},ie.prototype={point:function(n,t){this.stream.point(n,t)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}},to.geo.projection=oe,to.geo.projectionMutator=ae,(to.geo.equirectangular=function(){return oe(ce)}).raw=ce.invert=ce,to.geo.rotation=function(n){function t(t){return t=n(t[0]*Do,t[1]*Do),t[0]*=Po,t[1]*=Po,t}return n=se(n[0]%360*Do,n[1]*Do,n.length>2?n[2]*Do:0),t.invert=function(t){return t=n.invert(t[0]*Do,t[1]*Do),t[0]*=Po,t[1]*=Po,t},t},fe.invert=ce,to.geo.circle=function(){function n(){var n="function"==typeof r?r.apply(this,arguments):r,t=se(-n[0]*Do,-n[1]*Do,0).invert,i=[];return e(null,null,1,{point:function(n,e){i.push(n=t(n,e)),n[0]*=Po,n[1]*=Po}}),{type:"Polygon",coordinates:[i]}}var t,e,r=[0,0],i=6;return n.origin=function(t){return arguments.length?(r=t,n):r},n.angle=function(r){return arguments.length?(e=de((t=+r)*Do,i*Do),n):t},n.precision=function(r){return arguments.length?(e=de(t*Do,(i=+r)*Do),n):i},n.angle(90)},to.geo.distance=function(n,t){var e,r=(t[0]-n[0])*Do,i=n[1]*Do,u=t[1]*Do,o=Math.sin(r),a=Math.cos(r),l=Math.sin(i),c=Math.cos(i),f=Math.sin(u),s=Math.cos(u);return Math.atan2(Math.sqrt((e=s*o)*e+(e=c*f-l*s*a)*e),l*f+c*s*a)},to.geo.graticule=function(){function n(){return{type:"MultiLineString",coordinates:t()}}function t(){return to.range(Math.ceil(u/v)*v,i,v).map(h).concat(to.range(Math.ceil(c/y)*y,l,y).map(p)).concat(to.range(Math.ceil(r/g)*g,e,g).filter(function(n){return so(n%v)>Co}).map(f)).concat(to.range(Math.ceil(a/d)*d,o,d).filter(function(n){return so(n%y)>Co}).map(s))}var e,r,i,u,o,a,l,c,f,s,h,p,g=10,d=g,v=90,y=360,m=2.5;return n.lines=function(){return t().map(function(n){return{type:"LineString",coordinates:n}})},n.outline=function(){return{type:"Polygon",coordinates:[h(u).concat(p(l).slice(1),h(i).reverse().slice(1),p(c).reverse().slice(1))]}},n.extent=function(t){return arguments.length?n.majorExtent(t).minorExtent(t):n.minorExtent()},n.majorExtent=function(t){return arguments.length?(u=+t[0][0],i=+t[1][0],c=+t[0][1],l=+t[1][1],u>i&&(t=u,u=i,i=t),c>l&&(t=c,c=l,l=t),n.precision(m)):[[u,c],[i,l]]},n.minorExtent=function(t){return arguments.length?(r=+t[0][0],e=+t[1][0],a=+t[0][1],o=+t[1][1],r>e&&(t=r,r=e,e=t),a>o&&(t=a,a=o,o=t),n.precision(m)):[[r,a],[e,o]]},n.step=function(t){return arguments.length?n.majorStep(t).minorStep(t):n.minorStep()},n.majorStep=function(t){return arguments.length?(v=+t[0],y=+t[1],n):[v,y]},n.minorStep=function(t){return arguments.length?(g=+t[0],d=+t[1],n):[g,d]},n.precision=function(t){return arguments.length?(m=+t,f=ye(a,o,90),s=me(r,e,m),h=ye(c,l,90),p=me(u,i,m),n):m},n.majorExtent([[-180,-90+Co],[180,90-Co]]).minorExtent([[-180,-80-Co],[180,80+Co]])},to.geo.greatArc=function(){function n(){return{type:"LineString",coordinates:[t||r.apply(this,arguments),e||i.apply(this,arguments)]}}var t,e,r=xe,i=Me;return n.distance=function(){return to.geo.distance(t||r.apply(this,arguments),e||i.apply(this,arguments))},n.source=function(e){return arguments.length?(r=e,t="function"==typeof e?null:e,n):r},n.target=function(t){return arguments.length?(i=t,e="function"==typeof t?null:t,n):i},n.precision=function(){return arguments.length?n:0},n},to.geo.interpolate=function(n,t){return _e(n[0]*Do,n[1]*Do,t[0]*Do,t[1]*Do)},to.geo.length=function(n){return Ha=0,to.geo.stream(n,Oa),Ha};var Ha,Oa={sphere:_,point:_,lineStart:be,lineEnd:_,polygonStart:_,polygonEnd:_},Ya=we(function(n){return Math.sqrt(2/(1+n))},function(n){return 2*Math.asin(n/2)});(to.geo.azimuthalEqualArea=function(){return oe(Ya)}).raw=Ya;var Ia=we(function(n){var t=Math.acos(n);return t&&t/Math.sin(t)},m);(to.geo.azimuthalEquidistant=function(){return oe(Ia)}).raw=Ia,(to.geo.conicConformal=function(){return Vt(Se)}).raw=Se,(to.geo.conicEquidistant=function(){return Vt(ke)}).raw=ke;var Za=we(function(n){return 1/n},Math.atan);(to.geo.gnomonic=function(){return oe(Za)}).raw=Za,Ne.invert=function(n,t){return[n,2*Math.atan(Math.exp(t))-Ro]},(to.geo.mercator=function(){return Ee(Ne)}).raw=Ne;var Va=we(function(){return 1},Math.asin);(to.geo.orthographic=function(){return oe(Va)}).raw=Va;var Xa=we(function(n){return 1/(1+n)},function(n){return 2*Math.atan(n)});(to.geo.stereographic=function(){return oe(Xa)}).raw=Xa,Ae.invert=function(n,t){return[-t,2*Math.atan(Math.exp(n))-Ro]},(to.geo.transverseMercator=function(){var n=Ee(Ae),t=n.center,e=n.rotate;return n.center=function(n){return n?t([-n[1],n[0]]):(n=t(),[n[1],-n[0]])},n.rotate=function(n){return n?e([n[0],n[1],n.length>2?n[2]+90:90]):(n=e(),[n[0],n[1],n[2]-90])},e([0,0,90])}).raw=Ae,to.geom={},to.geom.hull=function(n){function t(n){if(n.length<3)return[];var t,i=En(e),u=En(r),o=n.length,a=[],l=[];for(t=0;t<o;t++)a.push([+i.call(this,n[t],t),+u.call(this,n[t],t),t]);for(a.sort(qe),t=0;t<o;t++)l.push([a[t][0],-a[t][1]]);var c=Le(a),f=Le(l),s=f[0]===c[0],h=f[f.length-1]===c[c.length-1],p=[];for(t=c.length-1;t>=0;--t)p.push(n[a[c[t]][2]]);for(t=+s;t<f.length-h;++t)p.push(n[a[f[t]][2]]);return p}var e=Ce,r=ze;return arguments.length?t(n):(t.x=function(n){return arguments.length?(e=n,t):e},t.y=function(n){return arguments.length?(r=n,t):r},t)},to.geom.polygon=function(n){return yo(n,$a),n};var $a=to.geom.polygon.prototype=[];$a.area=function(){for(var n,t=-1,e=this.length,r=this[e-1],i=0;++t<e;)n=r,r=this[t],i+=n[1]*r[0]-n[0]*r[1];return.5*i},$a.centroid=function(n){var t,e,r=-1,i=this.length,u=0,o=0,a=this[i-1];for(arguments.length||(n=-1/(6*this.area()));++r<i;)t=a,a=this[r],e=t[0]*a[1]-a[0]*t[1],u+=(t[0]+a[0])*e,o+=(t[1]+a[1])*e;return[u*n,o*n]},$a.clip=function(n){for(var t,e,r,i,u,o,a=De(n),l=-1,c=this.length-De(this),f=this[c-1];++l<c;){for(t=n.slice(),n.length=0,i=this[l],u=t[(r=t.length-a)-1],e=-1;++e<r;)Te(o=t[e],f,i)?(Te(u,f,i)||n.push(Re(u,o,f,i)),n.push(o)):Te(u,f,i)&&n.push(Re(u,o,f,i)),u=o;a&&n.push(n[0]),f=i}return n};var Ba,Wa,Ja,Ga,Ka,Qa=[],nl=[];Ie.prototype.prepare=function(){for(var n,t=this.edges,e=t.length;e--;)(n=t[e].edge).b&&n.a||t.splice(e,1);return t.sort(Ve),t.length},tr.prototype={start:function(){return this.edge.l===this.site?this.edge.a:this.edge.b},end:function(){return this.edge.l===this.site?this.edge.b:this.edge.a}},er.prototype={insert:function(n,t){var e,r,i;if(n){if(t.P=n,t.N=n.N,n.N&&(n.N.P=t),n.N=t,n.R){for(n=n.R;n.L;)n=n.L;n.L=t}else n.R=t;e=n}else this._?(n=or(this._),t.P=null,t.N=n,n.P=n.L=t,e=n):(t.P=t.N=null,this._=t,e=null);for(t.L=t.R=null,t.U=e,t.C=!0,n=t;e&&e.C;)e===(r=e.U).L?(i=r.R)&&i.C?(e.C=i.C=!1,r.C=!0,n=r):(n===e.R&&(ir(this,e),e=(n=e).U),e.C=!1,r.C=!0,ur(this,r)):(i=r.L)&&i.C?(e.C=i.C=!1,r.C=!0,n=r):(n===e.L&&(ur(this,e),e=(n=e).U),e.C=!1,r.C=!0,ir(this,r)),e=n.U;this._.C=!1},remove:function(n){n.N&&(n.N.P=n.P),n.P&&(n.P.N=n.N),n.N=n.P=null;var t,e,r,i=n.U,u=n.L,o=n.R;if(e=u?o?or(o):u:o,i?i.L===n?i.L=e:i.R=e:this._=e,u&&o?(r=e.C,e.C=n.C,e.L=u,u.U=e,e!==o?(i=e.U,e.U=n.U,n=e.R,i.L=n,e.R=o,o.U=e):(e.U=i,i=e,n=e.R)):(r=n.C,n=e),n&&(n.U=i),!r)if(n&&n.C)n.C=!1;else{do{if(n===this._)break;if(n===i.L){if((t=i.R).C&&(t.C=!1,i.C=!0,ir(this,i),t=i.R),t.L&&t.L.C||t.R&&t.R.C){t.R&&t.R.C||(t.L.C=!1,t.C=!0,ur(this,t),t=i.R),t.C=i.C,i.C=t.R.C=!1,ir(this,i),n=this._;break}}else if((t=i.L).C&&(t.C=!1,i.C=!0,ur(this,i),t=i.L),t.L&&t.L.C||t.R&&t.R.C){t.L&&t.L.C||(t.R.C=!1,t.C=!0,ir(this,t),t=i.L),t.C=i.C,i.C=t.L.C=!1,ur(this,i),n=this._;break}t.C=!0,n=i,i=i.U}while(!n.C);n&&(n.C=!1)}}},to.geom.voronoi=function(n){function t(n){var t=new Array(n.length),r=a[0][0],i=a[0][1],u=a[1][0],o=a[1][1];return ar(e(n),a).cells.forEach(function(e,a){var l=e.edges,c=e.site;(t[a]=l.length?l.map(function(n){var t=n.start();return[t.x,t.y]}):c.x>=r&&c.x<=u&&c.y>=i&&c.y<=o?[[r,o],[u,o],[u,i],[r,i]]:[]).point=n[a]}),t}function e(n){return n.map(function(n,t){return{x:Math.round(u(n,t)/Co)*Co,y:Math.round(o(n,t)/Co)*Co,i:t}})}var r=Ce,i=ze,u=r,o=i,a=tl;return n?t(n):(t.links=function(n){return ar(e(n)).edges.filter(function(n){return n.l&&n.r}).map(function(t){return{source:n[t.l.i],target:n[t.r.i]}})},t.triangles=function(n){var t=[];return ar(e(n)).cells.forEach(function(e,r){for(var i,u=e.site,o=e.edges.sort(Ve),a=-1,l=o.length,c=o[l-1].edge,f=c.l===u?c.r:c.l;++a<l;)c,i=f,f=(c=o[a].edge).l===u?c.r:c.l,r<i.i&&r<f.i&&cr(u,i,f)<0&&t.push([n[r],n[i.i],n[f.i]])}),t},t.x=function(n){return arguments.length?(u=En(r=n),t):r},t.y=function(n){return arguments.length?(o=En(i=n),t):i},t.clipExtent=function(n){return arguments.length?(a=null==n?tl:n,t):a===tl?null:a},t.size=function(n){return arguments.length?t.clipExtent(n&&[[0,0],n]):a===tl?null:a&&a[1]},t)};var tl=[[-1e6,-1e6],[1e6,1e6]];to.geom.delaunay=function(n){return to.geom.voronoi().triangles(n)},to.geom.quadtree=function(n,t,e,r,i){function u(n){function u(n,t,e,r,i,u,o,a){if(!isNaN(e)&&!isNaN(r))if(n.leaf){var l=n.x,f=n.y;if(null!=l)if(so(l-e)+so(f-r)<.01)c(n,t,e,r,i,u,o,a);else{var s=n.point;n.x=n.y=n.point=null,c(n,s,l,f,i,u,o,a),c(n,t,e,r,i,u,o,a)}else n.x=e,n.y=r,n.point=t}else c(n,t,e,r,i,u,o,a)}function c(n,t,e,r,i,o,a,l){var c=.5*(i+a),f=.5*(o+l),s=e>=c,h=r>=f,p=h<<1|s;n.leaf=!1,n=n.nodes[p]||(n.nodes[p]={leaf:!0,nodes:[],point:null,x:null,y:null}),s?i=c:a=c,h?o=f:l=f,u(n,t,e,r,i,o,a,l)}var f,s,h,p,g,d,v,y,m,x=En(a),M=En(l);if(null!=t)d=t,v=e,y=r,m=i;else if(y=m=-(d=v=1/0),s=[],h=[],g=n.length,o)for(p=0;p<g;++p)(f=n[p]).x<d&&(d=f.x),f.y<v&&(v=f.y),f.x>y&&(y=f.x),f.y>m&&(m=f.y),s.push(f.x),h.push(f.y);else for(p=0;p<g;++p){var _=+x(f=n[p],p),b=+M(f,p);_<d&&(d=_),b<v&&(v=b),_>y&&(y=_),b>m&&(m=b),s.push(_),h.push(b)}var w=y-d,S=m-v;w>S?m=v+w:y=d+S;var k={leaf:!0,nodes:[],point:null,x:null,y:null};if(k.add=function(n){u(k,n,+x(n,++p),+M(n,p),d,v,y,m)},k.visit=function(n){hr(n,k,d,v,y,m)},k.find=function(n){return pr(k,n[0],n[1],d,v,y,m)},p=-1,null==t){for(;++p<g;)u(k,n[p],s[p],h[p],d,v,y,m);--p}else n.forEach(k.add);return s=h=n=f=null,k}var o,a=Ce,l=ze;return(o=arguments.length)?(a=fr,l=sr,3===o&&(i=e,r=t,e=t=0),u(n)):(u.x=function(n){return arguments.length?(a=n,u):a},u.y=function(n){return arguments.length?(l=n,u):l},u.extent=function(n){return arguments.length?(null==n?t=e=r=i=null:(t=+n[0][0],e=+n[0][1],r=+n[1][0],i=+n[1][1]),u):null==t?null:[[t,e],[r,i]]},u.size=function(n){return arguments.length?(null==n?t=e=r=i=null:(t=e=0,r=+n[0],i=+n[1]),u):null==t?null:[r-t,i-e]},u)},to.interpolateRgb=gr,to.interpolateObject=dr,to.interpolateNumber=vr,to.interpolateString=yr;var el=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,rl=new RegExp(el.source,"g");to.interpolate=mr,to.interpolators=[function(n,t){var e=typeof t;return("string"===e?Wo.has(t.toLowerCase())||/^(#|rgb\(|hsl\()/i.test(t)?gr:yr:t instanceof an?gr:Array.isArray(t)?xr:"object"===e&&isNaN(t)?dr:vr)(n,t)}],to.interpolateArray=xr;var il=function(){return m},ul=to.map({linear:il,poly:Nr,quad:function(){return wr},cubic:function(){return Sr},sin:function(){return Er},exp:function(){return Ar},circle:function(){return Cr},elastic:zr,back:Lr,bounce:function(){return qr}}),ol=to.map({in:m,out:_r,"in-out":br,"out-in":function(n){return br(_r(n))}});to.ease=function(n){var t=n.indexOf("-"),e=t>=0?n.slice(0,t):n,r=t>=0?n.slice(t+1):"in";return e=ul.get(e)||il,r=ol.get(r)||m,Mr(r(e.apply(null,eo.call(arguments,1))))},to.interpolateHcl=Tr,to.interpolateHsl=Rr,to.interpolateLab=Dr,to.interpolateRound=Pr,to.transform=function(n){var t=io.createElementNS(to.ns.prefix.svg,"g");return(to.transform=function(n){if(null!=n){t.setAttribute("transform",n);var e=t.transform.baseVal.consolidate()}return new jr(e?e.matrix:al)})(n)},jr.prototype.toString=function(){return"translate("+this.translate+")rotate("+this.rotate+")skewX("+this.skew+")scale("+this.scale+")"};var al={a:1,b:0,c:0,d:1,e:0,f:0};to.interpolateTransform=Xr,to.layout={},to.layout.bundle=function(){return function(n){for(var t=[],e=-1,r=n.length;++e<r;)t.push(Wr(n[e]));return t}},to.layout.chord=function(){function n(){var n,c,s,h,p,g={},d=[],v=to.range(u),y=[];for(e=[],r=[],n=0,h=-1;++h<u;){for(c=0,p=-1;++p<u;)c+=i[h][p];d.push(c),y.push(to.range(u)),n+=c}for(o&&v.sort(function(n,t){return o(d[n],d[t])}),a&&y.forEach(function(n,t){n.sort(function(n,e){return a(i[t][n],i[t][e])})}),n=(qo-f*u)/n,c=0,h=-1;++h<u;){for(s=c,p=-1;++p<u;){var m=v[h],x=y[m][p],M=i[m][x],_=c,b=c+=M*n;g[m+"-"+x]={index:m,subindex:x,startAngle:_,endAngle:b,value:M}}r[m]={index:m,startAngle:s,endAngle:c,value:d[m]},c+=f}for(h=-1;++h<u;)for(p=h-1;++p<u;){var w=g[h+"-"+p],S=g[p+"-"+h];(w.value||S.value)&&e.push(w.value<S.value?{source:S,target:w}:{source:w,target:S})}l&&t()}function t(){e.sort(function(n,t){return l((n.source.value+n.target.value)/2,(t.source.value+t.target.value)/2)})}var e,r,i,u,o,a,l,c={},f=0;return c.matrix=function(n){return arguments.length?(u=(i=n)&&i.length,e=r=null,c):i},c.padding=function(n){return arguments.length?(f=n,e=r=null,c):f},c.sortGroups=function(n){return arguments.length?(o=n,e=r=null,c):o},c.sortSubgroups=function(n){return arguments.length?(a=n,e=null,c):a},c.sortChords=function(n){return arguments.length?(l=n,e&&t(),c):l},c.chords=function(){return e||n(),e},c.groups=function(){return r||n(),r},c},to.layout.force=function(){function n(n){return function(t,e,r,i){if(t.point!==n){var u=t.cx-n.x,o=t.cy-n.y,a=i-e,l=u*u+o*o;if(a*a/y<l){if(l<d){c=t.charge/l;n.px-=u*c,n.py-=o*c}return!0}if(t.point&&l&&l<d){var c=t.pointCharge/l;n.px-=u*c,n.py-=o*c}}return!t.charge}}function t(n){n.px=to.event.x,n.py=to.event.y,l.resume()}var e,r,i,u,o,a,l={},c=to.dispatch("start","tick","end"),f=[1,1],s=.9,h=ll,p=cl,g=-30,d=fl,v=.1,y=.64,x=[],M=[];return l.tick=function(){if((i*=.99)<.005)return e=null,c.end({type:"end",alpha:i=0}),!0;var t,r,l,h,p,d,y,m,_,b=x.length,w=M.length;for(r=0;r<w;++r)h=(l=M[r]).source,(d=(m=(p=l.target).x-h.x)*m+(_=p.y-h.y)*_)&&(m*=d=i*o[r]*((d=Math.sqrt(d))-u[r])/d,_*=d,p.x-=m*(y=h.weight+p.weight?h.weight/(h.weight+p.weight):.5),p.y-=_*y,h.x+=m*(y=1-y),h.y+=_*y);if((y=i*v)&&(m=f[0]/2,_=f[1]/2,r=-1,y))for(;++r<b;)(l=x[r]).x+=(m-l.x)*y,l.y+=(_-l.y)*y;if(g)for(ei(t=to.geom.quadtree(x),i,a),r=-1;++r<b;)(l=x[r]).fixed||t.visit(n(l));for(r=-1;++r<b;)(l=x[r]).fixed?(l.x=l.px,l.y=l.py):(l.x-=(l.px-(l.px=l.x))*s,l.y-=(l.py-(l.py=l.y))*s);c.tick({type:"tick",alpha:i})},l.nodes=function(n){return arguments.length?(x=n,l):x},l.links=function(n){return arguments.length?(M=n,l):M},l.size=function(n){return arguments.length?(f=n,l):f},l.linkDistance=function(n){return arguments.length?(h="function"==typeof n?n:+n,l):h},l.distance=l.linkDistance,l.linkStrength=function(n){return arguments.length?(p="function"==typeof n?n:+n,l):p},l.friction=function(n){return arguments.length?(s=+n,l):s},l.charge=function(n){return arguments.length?(g="function"==typeof n?n:+n,l):g},l.chargeDistance=function(n){return arguments.length?(d=n*n,l):Math.sqrt(d)},l.gravity=function(n){return arguments.length?(v=+n,l):v},l.theta=function(n){return arguments.length?(y=n*n,l):Math.sqrt(y)},l.alpha=function(n){return arguments.length?(n=+n,i?n>0?i=n:(e.c=null,e.t=NaN,e=null,c.end({type:"end",alpha:i=0})):n>0&&(c.start({type:"start",alpha:i=n}),e=qn(l.tick)),l):i},l.start=function(){function n(n,r){if(!e){for(e=new Array(i),l=0;l<i;++l)e[l]=[];for(l=0;l<c;++l){var u=M[l];e[u.source.index].push(u.target),e[u.target.index].push(u.source)}}for(var o,a=e[t],l=-1,f=a.length;++l<f;)if(!isNaN(o=a[l][n]))return o;return Math.random()*r}var t,e,r,i=x.length,c=M.length,s=f[0],d=f[1];for(t=0;t<i;++t)(r=x[t]).index=t,r.weight=0;for(t=0;t<c;++t)"number"==typeof(r=M[t]).source&&(r.source=x[r.source]),"number"==typeof r.target&&(r.target=x[r.target]),++r.source.weight,++r.target.weight;for(t=0;t<i;++t)r=x[t],isNaN(r.x)&&(r.x=n("x",s)),isNaN(r.y)&&(r.y=n("y",d)),isNaN(r.px)&&(r.px=r.x),isNaN(r.py)&&(r.py=r.y);if(u=[],"function"==typeof h)for(t=0;t<c;++t)u[t]=+h.call(this,M[t],t);else for(t=0;t<c;++t)u[t]=h;if(o=[],"function"==typeof p)for(t=0;t<c;++t)o[t]=+p.call(this,M[t],t);else for(t=0;t<c;++t)o[t]=p;if(a=[],"function"==typeof g)for(t=0;t<i;++t)a[t]=+g.call(this,x[t],t);else for(t=0;t<i;++t)a[t]=g;return l.resume()},l.resume=function(){return l.alpha(.1)},l.stop=function(){return l.alpha(0)},l.drag=function(){if(r||(r=to.behavior.drag().origin(m).on("dragstart.force",Kr).on("drag.force",t).on("dragend.force",Qr)),!arguments.length)return r;this.on("mouseover.force",ni).on("mouseout.force",ti).call(r)},to.rebind(l,c,"on")};var ll=20,cl=1,fl=1/0;to.layout.hierarchy=function(){function n(i){var u,o=[i],a=[];for(i.depth=0;null!=(u=o.pop());)if(a.push(u),(c=e.call(n,u,u.depth))&&(l=c.length)){for(var l,c,f;--l>=0;)o.push(f=c[l]),f.parent=u,f.depth=u.depth+1;r&&(u.value=0),u.children=c}else r&&(u.value=+r.call(n,u,u.depth)||0),delete u.children;return ui(i,function(n){var e,i;t&&(e=n.children)&&e.sort(t),r&&(i=n.parent)&&(i.value+=n.value)}),a}var t=li,e=oi,r=ai;return n.sort=function(e){return arguments.length?(t=e,n):t},n.children=function(t){return arguments.length?(e=t,n):e},n.value=function(t){return arguments.length?(r=t,n):r},n.revalue=function(t){return r&&(ii(t,function(n){n.children&&(n.value=0)}),ui(t,function(t){var e;t.children||(t.value=+r.call(n,t,t.depth)||0),(e=t.parent)&&(e.value+=t.value)})),t},n},to.layout.partition=function(){function n(t,e,r,i){var u=t.children;if(t.x=e,t.y=t.depth*i,t.dx=r,t.dy=i,u&&(o=u.length)){var o,a,l,c=-1;for(r=t.value?r/t.value:0;++c<o;)n(a=u[c],e,l=a.value*r,i),e+=l}}function t(n){var e=n.children,r=0;if(e&&(i=e.length))for(var i,u=-1;++u<i;)r=Math.max(r,t(e[u]));return 1+r}function e(e,u){var o=r.call(this,e,u);return n(o[0],0,i[0],i[1]/t(o[0])),o}var r=to.layout.hierarchy(),i=[1,1];return e.size=function(n){return arguments.length?(i=n,e):i},ri(e,r)},to.layout.pie=function(){function n(o){var a,l=o.length,c=o.map(function(e,r){return+t.call(n,e,r)}),f=+("function"==typeof r?r.apply(this,arguments):r),s=("function"==typeof i?i.apply(this,arguments):i)-f,h=Math.min(Math.abs(s)/l,+("function"==typeof u?u.apply(this,arguments):u)),p=h*(s<0?-1:1),g=to.sum(c),d=g?(s-l*p)/g:0,v=to.range(l),y=[];return null!=e&&v.sort(e===sl?function(n,t){return c[t]-c[n]}:function(n,t){return e(o[n],o[t])}),v.forEach(function(n){y[n]={data:o[n],value:a=c[n],startAngle:f,endAngle:f+=a*d+p,padAngle:h}}),y}var t=Number,e=sl,r=0,i=qo,u=0;return n.value=function(e){return arguments.length?(t=e,n):t},n.sort=function(t){return arguments.length?(e=t,n):e},n.startAngle=function(t){return arguments.length?(r=t,n):r},n.endAngle=function(t){return arguments.length?(i=t,n):i},n.padAngle=function(t){return arguments.length?(u=t,n):u},n};var sl={};to.layout.stack=function(){function n(a,l){if(!(h=a.length))return a;var c=a.map(function(e,r){return t.call(n,e,r)}),f=c.map(function(t){return t.map(function(t,e){return[u.call(n,t,e),o.call(n,t,e)]})}),s=e.call(n,f,l);c=to.permute(c,s),f=to.permute(f,s);var h,p,g,d,v=r.call(n,f,l),y=c[0].length;for(g=0;g<y;++g)for(i.call(n,c[0][g],d=v[g],f[0][g][1]),p=1;p<h;++p)i.call(n,c[p][g],d+=f[p-1][g][1],f[p][g][1]);return a}var t=m,e=pi,r=gi,i=hi,u=fi,o=si;return n.values=function(e){return arguments.length?(t=e,n):t},n.order=function(t){return arguments.length?(e="function"==typeof t?t:hl.get(t)||pi,n):e},n.offset=function(t){return arguments.length?(r="function"==typeof t?t:pl.get(t)||gi,n):r},n.x=function(t){return arguments.length?(u=t,n):u},n.y=function(t){return arguments.length?(o=t,n):o},n.out=function(t){return arguments.length?(i=t,n):i},n};var hl=to.map({"inside-out":function(n){var t,e,r=n.length,i=n.map(di),u=n.map(vi),o=to.range(r).sort(function(n,t){return i[n]-i[t]}),a=0,l=0,c=[],f=[];for(t=0;t<r;++t)e=o[t],a<l?(a+=u[e],c.push(e)):(l+=u[e],f.push(e));return f.reverse().concat(c)},reverse:function(n){return to.range(n.length).reverse()},default:pi}),pl=to.map({silhouette:function(n){var t,e,r,i=n.length,u=n[0].length,o=[],a=0,l=[];for(e=0;e<u;++e){for(t=0,r=0;t<i;t++)r+=n[t][e][1];r>a&&(a=r),o.push(r)}for(e=0;e<u;++e)l[e]=(a-o[e])/2;return l},wiggle:function(n){var t,e,r,i,u,o,a,l,c,f=n.length,s=n[0],h=s.length,p=[];for(p[0]=l=c=0,e=1;e<h;++e){for(t=0,i=0;t<f;++t)i+=n[t][e][1];for(t=0,u=0,a=s[e][0]-s[e-1][0];t<f;++t){for(r=0,o=(n[t][e][1]-n[t][e-1][1])/(2*a);r<t;++r)o+=(n[r][e][1]-n[r][e-1][1])/a;u+=o*n[t][e][1]}p[e]=l-=i?u/i*a:0,l<c&&(c=l)}for(e=0;e<h;++e)p[e]-=c;return p},expand:function(n){var t,e,r,i=n.length,u=n[0].length,o=1/i,a=[];for(e=0;e<u;++e){for(t=0,r=0;t<i;t++)r+=n[t][e][1];if(r)for(t=0;t<i;t++)n[t][e][1]/=r;else for(t=0;t<i;t++)n[t][e][1]=o}for(e=0;e<u;++e)a[e]=0;return a},zero:gi});to.layout.histogram=function(){function n(n,u){for(var o,a,l=[],c=n.map(e,this),f=r.call(this,c,u),s=i.call(this,f,c,u),u=-1,h=c.length,p=s.length-1,g=t?1:1/h;++u<p;)(o=l[u]=[]).dx=s[u+1]-(o.x=s[u]),o.y=0;if(p>0)for(u=-1;++u<h;)(a=c[u])>=f[0]&&a<=f[1]&&((o=l[to.bisect(s,a,1,p)-1]).y+=g,o.push(n[u]));return l}var t=!0,e=Number,r=Mi,i=mi;return n.value=function(t){return arguments.length?(e=t,n):e},n.range=function(t){return arguments.length?(r=En(t),n):r},n.bins=function(t){return arguments.length?(i="number"==typeof t?function(n){return xi(n,t)}:En(t),n):i},n.frequency=function(e){return arguments.length?(t=!!e,n):t},n},to.layout.pack=function(){function n(n,u){var o=e.call(this,n,u),a=o[0],l=i[0],c=i[1],f=null==t?Math.sqrt:"function"==typeof t?t:function(){return t};if(a.x=a.y=0,ui(a,function(n){n.r=+f(n.value)}),ui(a,ki),r){var s=r*(t?1:Math.max(2*a.r/l,2*a.r/c))/2;ui(a,function(n){n.r+=s}),ui(a,ki),ui(a,function(n){n.r-=s})}return Ai(a,l/2,c/2,t?1:1/Math.max(2*a.r/l,2*a.r/c)),o}var t,e=to.layout.hierarchy().sort(_i),r=0,i=[1,1];return n.size=function(t){return arguments.length?(i=t,n):i},n.radius=function(e){return arguments.length?(t=null==e||"function"==typeof e?e:+e,n):t},n.padding=function(t){return arguments.length?(r=+t,n):r},ri(n,e)},to.layout.tree=function(){function n(n,r){var u=M.call(this,n,r),o=t(u[0]);return N=o,e(o,0),i(o),g(o,0),m(o),u}function t(n){var e={t:n,prelim:0,mod:0,shift:0,change:0,msel:0,mser:0};if(n.x=0,n.y=0,w)e.x_size=1,e.y_size=1;else if("object"==typeof S)e.x_size=S[0],e.y_size=S[1];else{var r=S(n);e.x_size=r[0],e.y_size=r[1]}k&&(n.x_size=e.x_size,n.y_size=e.y_size);for(var i=[],u=n.children?n.children.length:0,o=0;o<u;++o)i.push(t(n.children[o]));return e.children=i,e.num_children=u,e}function e(n,t){n.t.y=t,n.t.depth=0,r(n)}function r(n){var t,e=n.t.y+n.y_size,i=n.t.depth+1;for(t=0;t<n.children.length;++t){var u=n.children[t];u.t.y=e,u.t.parent=n.t,u.t.depth=i,r(n.children[t])}}function i(n){if(0!=n.num_children){i(n.children[0]);for(var t=y(f(n.children[0].el),0,null),e=1;e<n.num_children;++e){i(n.children[e]);var r=f(n.children[e].er);o(n,e,t),t=y(r,e,t)}p(n),u(n)}else u(n)}function u(n){0==n.num_children?(n.el=n,n.er=n,n.msel=n.mser=0):(n.el=n.children[0].el,n.msel=n.children[0].msel,n.er=n.children[n.num_children-1].er,n.mser=n.children[n.num_children-1].mser)}function o(n,t,e){for(var r=n.children[t-1],i=r.mod,u=n.children[t],o=u.mod;null!=r&&null!=u;){f(r)>e.lowY&&(e=e.nxt);var p=i+r.prelim-(o+u.prelim);null!=_?p+=_(r.t,u.t)*N.x_size:null!=b&&(p+=r.x_size/2+u.x_size/2+b(r.t,u.t)),p>0?(o+=p,a(n,t,e.index,p)):1===t&&0===o&&0===r.num_children&&u.num_children>1&&p<0&&(o+=p,a(n,t,e.index,p));var g=f(r),d=f(u);g<=d&&null!=(r=c(r))&&(i+=r.mod),g>=d&&null!=(u=l(u))&&(o+=u.mod)}null==r&&null!=u?s(n,t,u,o):null!=r&&null==u&&h(n,t,r,i)}function a(n,t,e,r){n.children[t].mod+=r,n.children[t].msel+=r,n.children[t].mser+=r,d(n,t,e,r)}function l(n){return 0==n.num_children?n.tl:n.children[0]}function c(n){return 0==n.num_children?n.tr:n.children[n.num_children-1]}function f(n){return n.t.y+n.y_size}function s(n,t,e,r){var i=n.children[0].el;i.tl=e;var u=r-e.mod-n.children[0].msel;i.mod+=u,i.prelim-=u,n.children[0].el=n.children[t].el,n.children[0].msel=n.children[t].msel}function h(n,t,e,r){var i=n.children[t].er;i.tr=e;var u=r-e.mod-n.children[t].mser;i.mod+=u,i.prelim-=u,n.children[t].er=n.children[t-1].er,n.children[t].mser=n.children[t-1].mser}function p(n){n.prelim=(n.children[0].prelim+n.children[0].mod-n.children[0].x_size/2+n.children[n.num_children-1].mod+n.children[n.num_children-1].prelim+n.children[n.num_children-1].x_size/2)/2}function g(n,t){t+=n.mod,n.t.x=n.prelim+t,v(n);for(var e=0;e<n.num_children;e++)g(n.children[e],t)}function d(n,t,e,r){if(e!=t-1){var i=t-e;n.children[e+1].shift+=r/i,n.children[t].shift-=r/i,n.children[t].change-=r-r/i}}function v(n){for(var t=0,e=0,r=0;r<n.num_children;r++)e+=(t+=n.children[r].shift)+n.children[r].change,n.children[r].mod+=e}function y(n,t,e){for(;null!=e&&n>=e.lowY;)e=e.nxt;return{lowY:n,index:t,nxt:e}}function m(n){if(null!=w){for(var t,e=n,r=n,i=n,u=[n];t=u.pop();)(f=t.t).x<e.t.x&&(e=t),f.x>r.t.x&&(r=t),f.depth>i.t.depth&&(i=t),t.children&&(u=u.concat(t.children));var o=null==_?.5:_(e.t,r.t)/2,a=o-e.t.x,l=w[0]/(r.t.x+o+a),c=w[1]/(i.t.depth>0?i.t.depth:1);for(u=[n];t=u.pop();){var f=t.t;f.x=(f.x+a)*l,f.y=f.depth*c,k&&(f.x_size*=l,f.y_size*=c),t.children&&(u=u.concat(t.children))}}else x(n,-n.t.x)}function x(n,t){n.t.x+=t;for(var e=0;e<n.num_children;++e)x(n.children[e],t)}var M=to.layout.hierarchy().sort(null).value(null),_=zi,b=null,w=[1,1],S=null,k=!1,N=null;return n.separation=function(t){return arguments.length?(_=t,b=null,n):_},n.spacing=function(t){return arguments.length?(b=t,_=null,n):b},n.size=function(t){return arguments.length?(w=t,S=null,n):w},n.nodeSize=function(t){return arguments.length?(S=t,w=null,n):S},n.setNodeSizes=function(t){return arguments.length?(k=t,n):k},n.rootXSize=function(){return N?N.x_size:null},ri(n,M)},to.layout.cluster=function(){function n(n,u){var o,a=t.call(this,n,u),l=a[0],c=0;ui(l,function(n){var t=n.children;t&&t.length?(n.x=qi(t),n.y=Li(t)):(n.x=o?c+=e(n,o):0,n.y=0,o=n)});var f=Ti(l),s=Ri(l),h=f.x-e(f,s)/2,p=s.x+e(s,f)/2;return ui(l,i?function(n){n.x=(n.x-l.x)*r[0],n.y=(l.y-n.y)*r[1]}:function(n){n.x=(n.x-h)/(p-h)*r[0],n.y=(1-(l.y?n.y/l.y:1))*r[1]}),a}var t=to.layout.hierarchy().sort(null).value(null),e=zi,r=[1,1],i=!1;return n.separation=function(t){return arguments.length?(e=t,n):e},n.size=function(t){return arguments.length?(i=null==(r=t),n):i?null:r},n.nodeSize=function(t){return arguments.length?(i=null!=(r=t),n):i?r:null},ri(n,t)},to.layout.treemap=function(){function n(n,t){for(var e,r,i=-1,u=n.length;++i<u;)r=(e=n[i]).value*(t<0?0:t),e.area=isNaN(r)||r<=0?0:r}function t(e){var u=e.children;if(u&&u.length){var o,a,l,c=s(e),f=[],h=u.slice(),g=1/0,d="slice"===p?c.dx:"dice"===p?c.dy:"slice-dice"===p?1&e.depth?c.dy:c.dx:Math.min(c.dx,c.dy);for(n(h,c.dx*c.dy/e.value),f.area=0;(l=h.length)>0;)f.push(o=h[l-1]),f.area+=o.area,"squarify"!==p||(a=r(f,d))<=g?(h.pop(),g=a):(f.area-=f.pop().area,i(f,d,c,!1),d=Math.min(c.dx,c.dy),f.length=f.area=0,g=1/0);f.length&&(i(f,d,c,!0),f.length=f.area=0),u.forEach(t)}}function e(t){var r=t.children;if(r&&r.length){var u,o=s(t),a=r.slice(),l=[];for(n(a,o.dx*o.dy/t.value),l.area=0;u=a.pop();)l.push(u),l.area+=u.area,null!=u.z&&(i(l,u.z?o.dx:o.dy,o,!a.length),l.length=l.area=0);r.forEach(e)}}function r(n,t){for(var e,r=n.area,i=0,u=1/0,o=-1,a=n.length;++o<a;)(e=n[o].area)&&(e<u&&(u=e),e>i&&(i=e));return r*=r,t*=t,r?Math.max(t*i*g/r,r/(t*u*g)):1/0}function i(n,t,e,r){var i,u=-1,o=n.length,a=e.x,c=e.y,f=t?l(n.area/t):0;if(t==e.dx){for((r||f>e.dy)&&(f=e.dy);++u<o;)(i=n[u]).x=a,i.y=c,i.dy=f,a+=i.dx=Math.min(e.x+e.dx-a,f?l(i.area/f):0);i.z=!0,i.dx+=e.x+e.dx-a,e.y+=f,e.dy-=f}else{for((r||f>e.dx)&&(f=e.dx);++u<o;)(i=n[u]).x=a,i.y=c,i.dx=f,c+=i.dy=Math.min(e.y+e.dy-c,f?l(i.area/f):0);i.z=!1,i.dy+=e.y+e.dy-c,e.x+=f,e.dx-=f}}function u(r){var i=o||a(r),u=i[0];return u.x=u.y=0,u.value?(u.dx=c[0],u.dy=c[1]):u.dx=u.dy=0,o&&a.revalue(u),n([u],u.dx*u.dy/u.value),(o?e:t)(u),h&&(o=i),i}var o,a=to.layout.hierarchy(),l=Math.round,c=[1,1],f=null,s=Di,h=!1,p="squarify",g=.5*(1+Math.sqrt(5));return u.size=function(n){return arguments.length?(c=n,u):c},u.padding=function(n){function t(t){var e=n.call(u,t,t.depth);return null==e?Di(t):Pi(t,"number"==typeof e?[e,e,e,e]:e)}function e(t){return Pi(t,n)}if(!arguments.length)return f;var r;return s=null==(f=n)?Di:"function"==(r=typeof n)?t:"number"===r?(n=[n,n,n,n],e):e,u},u.round=function(n){return arguments.length?(l=n?Math.round:Number,u):l!=Number},u.sticky=function(n){return arguments.length?(h=n,o=null,u):h},u.ratio=function(n){return arguments.length?(g=n,u):g},u.mode=function(n){return arguments.length?(p=n+"",u):p},ri(u,a)},to.random={normal:function(n,t){var e=arguments.length;return e<2&&(t=1),e<1&&(n=0),function(){var e,r,i;do{i=(e=2*Math.random()-1)*e+(r=2*Math.random()-1)*r}while(!i||i>1);return n+t*e*Math.sqrt(-2*Math.log(i)/i)}},logNormal:function(){var n=to.random.normal.apply(to,arguments);return function(){return Math.exp(n())}},bates:function(n){var t=to.random.irwinHall(n);return function(){return t()/n}},irwinHall:function(n){return function(){for(var t=0,e=0;e<n;e++)t+=Math.random();return t}}},to.scale={};var gl={floor:m,ceil:m};to.scale.linear=function(){return Ii([0,1],[0,1],mr,!1)};var dl={s:1,g:1,p:1,r:1,e:1};to.scale.log=function(){return Gi(to.scale.linear().domain([0,1]),10,!0,[1,10])};var vl=to.format(".0e"),yl={floor:function(n){return-Math.ceil(-n)},ceil:function(n){return-Math.floor(-n)}};to.scale.pow=function(){return Ki(to.scale.linear(),1,[0,1])},to.scale.sqrt=function(){return to.scale.pow().exponent(.5)},to.scale.ordinal=function(){return nu([],{t:"range",a:[[]]})},to.scale.category10=function(){return to.scale.ordinal().range(ml)},to.scale.category20=function(){return to.scale.ordinal().range(xl)},to.scale.category20b=function(){return to.scale.ordinal().range(Ml)},to.scale.category20c=function(){return to.scale.ordinal().range(_l)};var ml=[2062260,16744206,2924588,14034728,9725885,9197131,14907330,8355711,12369186,1556175].map(Mn),xl=[2062260,11454440,16744206,16759672,2924588,10018698,14034728,16750742,9725885,12955861,9197131,12885140,14907330,16234194,8355711,13092807,12369186,14408589,1556175,10410725].map(Mn),Ml=[3750777,5395619,7040719,10264286,6519097,9216594,11915115,13556636,9202993,12426809,15186514,15190932,8666169,11356490,14049643,15177372,8077683,10834324,13528509,14589654].map(Mn),_l=[3244733,7057110,10406625,13032431,15095053,16616764,16625259,16634018,3253076,7652470,10607003,13101504,7695281,10394312,12369372,14342891,6513507,9868950,12434877,14277081].map(Mn);to.scale.quantile=function(){return tu([],[])},to.scale.quantize=function(){return eu(0,1,[0,1])},to.scale.threshold=function(){return ru([.5],[0,1])},to.scale.identity=function(){return iu([0,1])},to.svg={},to.svg.arc=function(){function n(){var n=Math.max(0,+e.apply(this,arguments)),c=Math.max(0,+r.apply(this,arguments)),f=o.apply(this,arguments)-Ro,s=a.apply(this,arguments)-Ro,h=Math.abs(s-f),p=f>s?0:1;if(c<n&&(g=c,c=n,n=g),h>=To)return t(c,p)+(n?t(n,1-p):"")+"Z";var g,d,v,y,m,x,M,_,b,w,S,k,N=0,E=0,A=[];if((y=(+l.apply(this,arguments)||0)/2)&&(v=u===bl?Math.sqrt(n*n+c*c):+u.apply(this,arguments),p||(E*=-1),c&&(E=tn(v/c*Math.sin(y))),n&&(N=tn(v/n*Math.sin(y)))),c){m=c*Math.cos(f+E),x=c*Math.sin(f+E),M=c*Math.cos(s-E),_=c*Math.sin(s-E);var C=Math.abs(s-f-2*E)<=Lo?0:1;if(E&&su(m,x,M,_)===p^C){var z=(f+s)/2;m=c*Math.cos(z),x=c*Math.sin(z),M=_=null}}else m=x=0;if(n){b=n*Math.cos(s-N),w=n*Math.sin(s-N),S=n*Math.cos(f+N),k=n*Math.sin(f+N);var L=Math.abs(f-s+2*N)<=Lo?0:1;if(N&&su(b,w,S,k)===1-p^L){var q=(f+s)/2;b=n*Math.cos(q),w=n*Math.sin(q),S=k=null}}else b=w=0;if(h>Co&&(g=Math.min(Math.abs(c-n)/2,+i.apply(this,arguments)))>.001){d=n<c^p?0:1;var T=g,R=g;if(h<Lo){var D=null==S?[b,w]:null==M?[m,x]:Re([m,x],[S,k],[M,_],[b,w]),P=m-D[0],j=x-D[1],U=M-D[0],F=_-D[1],H=1/Math.sin(Math.acos((P*U+j*F)/(Math.sqrt(P*P+j*j)*Math.sqrt(U*U+F*F)))/2),O=Math.sqrt(D[0]*D[0]+D[1]*D[1]);R=Math.min(g,(n-O)/(H-1)),T=Math.min(g,(c-O)/(H+1))}if(null!=M){var Y=hu(null==S?[b,w]:[S,k],[m,x],c,T,p),I=hu([M,_],[b,w],c,T,p);g===T?A.push("M",Y[0],"A",T,",",T," 0 0,",d," ",Y[1],"A",c,",",c," 0 ",1-p^su(Y[1][0],Y[1][1],I[1][0],I[1][1]),",",p," ",I[1],"A",T,",",T," 0 0,",d," ",I[0]):A.push("M",Y[0],"A",T,",",T," 0 1,",d," ",I[0])}else A.push("M",m,",",x);if(null!=S){var Z=hu([m,x],[S,k],n,-R,p),V=hu([b,w],null==M?[m,x]:[M,_],n,-R,p);g===R?A.push("L",V[0],"A",R,",",R," 0 0,",d," ",V[1],"A",n,",",n," 0 ",p^su(V[1][0],V[1][1],Z[1][0],Z[1][1]),",",1-p," ",Z[1],"A",R,",",R," 0 0,",d," ",Z[0]):A.push("L",V[0],"A",R,",",R," 0 0,",d," ",Z[0])}else A.push("L",b,",",w)}else A.push("M",m,",",x),null!=M&&A.push("A",c,",",c," 0 ",C,",",p," ",M,",",_),A.push("L",b,",",w),null!=S&&A.push("A",n,",",n," 0 ",L,",",1-p," ",S,",",k);return A.push("Z"),A.join("")}function t(n,t){return"M0,"+n+"A"+n+","+n+" 0 1,"+t+" 0,"+-n+"A"+n+","+n+" 0 1,"+t+" 0,"+n}var e=ou,r=au,i=uu,u=bl,o=lu,a=cu,l=fu;return n.innerRadius=function(t){return arguments.length?(e=En(t),n):e},n.outerRadius=function(t){return arguments.length?(r=En(t),n):r},n.cornerRadius=function(t){return arguments.length?(i=En(t),n):i},n.padRadius=function(t){return arguments.length?(u=t==bl?bl:En(t),n):u},n.startAngle=function(t){return arguments.length?(o=En(t),n):o},n.endAngle=function(t){return arguments.length?(a=En(t),n):a},n.padAngle=function(t){return arguments.length?(l=En(t),n):l},n.centroid=function(){var n=(+e.apply(this,arguments)+ +r.apply(this,arguments))/2,t=(+o.apply(this,arguments)+ +a.apply(this,arguments))/2-Ro;return[Math.cos(t)*n,Math.sin(t)*n]},n};var bl="auto";to.svg.line=function(){return pu(m)};var wl=to.map({linear:gu,"linear-closed":du,step:vu,"step-before":yu,"step-after":mu,basis:Su,"basis-open":ku,"basis-closed":Nu,bundle:Eu,cardinal:_u,"cardinal-open":xu,"cardinal-closed":Mu,monotone:Tu});wl.forEach(function(n,t){t.key=n,t.closed=/-closed$/.test(n)});var Sl=[0,2/3,1/3,0],kl=[0,1/3,2/3,0],Nl=[0,1/6,2/3,1/6];to.svg.line.radial=function(){var n=pu(Ru);return n.radius=n.x,delete n.x,n.angle=n.y,delete n.y,n},yu.reverse=mu,mu.reverse=yu,to.svg.area=function(){return Du(m)},to.svg.area.radial=function(){var n=Du(Ru);return n.radius=n.x,delete n.x,n.innerRadius=n.x0,delete n.x0,n.outerRadius=n.x1,delete n.x1,n.angle=n.y,delete n.y,n.startAngle=n.y0,delete n.y0,n.endAngle=n.y1,delete n.y1,n},to.svg.chord=function(){function n(n,a){var l=t(this,u,n,a),c=t(this,o,n,a);return"M"+l.p0+r(l.r,l.p1,l.a1-l.a0)+(e(l,c)?i(l.r,l.p1,l.r,l.p0):i(l.r,l.p1,c.r,c.p0)+r(c.r,c.p1,c.a1-c.a0)+i(c.r,c.p1,l.r,l.p0))+"Z"}function t(n,t,e,r){var i=t.call(n,e,r),u=a.call(n,i,r),o=l.call(n,i,r)-Ro,f=c.call(n,i,r)-Ro;return{r:u,a0:o,a1:f,p0:[u*Math.cos(o),u*Math.sin(o)],p1:[u*Math.cos(f),u*Math.sin(f)]}}function e(n,t){return n.a0==t.a0&&n.a1==t.a1}function r(n,t,e){return"A"+n+","+n+" 0 "+ +(e>Lo)+",1 "+t}function i(n,t,e,r){return"Q 0,0 "+r}var u=xe,o=Me,a=Pu,l=lu,c=cu;return n.radius=function(t){return arguments.length?(a=En(t),n):a},n.source=function(t){return arguments.length?(u=En(t),n):u},n.target=function(t){return arguments.length?(o=En(t),n):o},n.startAngle=function(t){return arguments.length?(l=En(t),n):l},n.endAngle=function(t){return arguments.length?(c=En(t),n):c},n},to.svg.diagonal=function(){function n(n,i){var u=t.call(this,n,i),o=e.call(this,n,i),a=(u.y+o.y)/2,l=[u,{x:u.x,y:a},{x:o.x,y:a},o];return"M"+(l=l.map(r))[0]+"C"+l[1]+" "+l[2]+" "+l[3]}var t=xe,e=Me,r=ju;return n.source=function(e){return arguments.length?(t=En(e),n):t},n.target=function(t){return arguments.length?(e=En(t),n):e},n.projection=function(t){return arguments.length?(r=t,n):r},n},to.svg.diagonal.radial=function(){var n=to.svg.diagonal(),t=ju,e=n.projection;return n.projection=function(n){return arguments.length?e(Uu(t=n)):t},n},to.svg.symbol=function(){function n(n,r){return(El.get(t.call(this,n,r))||Ou)(e.call(this,n,r))}var t=Hu,e=Fu;return n.type=function(e){return arguments.length?(t=En(e),n):t},n.size=function(t){return arguments.length?(e=En(t),n):e},n};var El=to.map({circle:Ou,cross:function(n){var t=Math.sqrt(n/5)/2;return"M"+-3*t+","+-t+"H"+-t+"V"+-3*t+"H"+t+"V"+-t+"H"+3*t+"V"+t+"H"+t+"V"+3*t+"H"+-t+"V"+t+"H"+-3*t+"Z"},diamond:function(n){var t=Math.sqrt(n/(2*Cl)),e=t*Cl;return"M0,"+-t+"L"+e+",0 0,"+t+" "+-e+",0Z"},square:function(n){var t=Math.sqrt(n)/2;return"M"+-t+","+-t+"L"+t+","+-t+" "+t+","+t+" "+-t+","+t+"Z"},"triangle-down":function(n){var t=Math.sqrt(n/Al),e=t*Al/2;return"M0,"+e+"L"+t+","+-e+" "+-t+","+-e+"Z"},"triangle-up":function(n){var t=Math.sqrt(n/Al),e=t*Al/2;return"M0,"+-e+"L"+t+","+e+" "+-t+","+e+"Z"}});to.svg.symbolTypes=El.keys();var Al=Math.sqrt(3),Cl=Math.tan(30*Do);_o.transition=function(n){for(var t,e,r=zl||++Rl,i=Xu(n),u=[],o=Ll||{time:Date.now(),ease:kr,delay:0,duration:250},a=-1,l=this.length;++a<l;){u.push(t=[]);for(var c=this[a],f=-1,s=c.length;++f<s;)(e=c[f])&&$u(e,f,i,r,o),t.push(e)}return Iu(u,i,r)},_o.interrupt=function(n){return this.each(null==n?ql:Yu(Xu(n)))};var zl,Ll,ql=Yu(Xu()),Tl=[],Rl=0;Tl.call=_o.call,Tl.empty=_o.empty,Tl.node=_o.node,Tl.size=_o.size,to.transition=function(n,t){return n&&n.transition?zl?n.transition(t):n:to.selection().transition(n)},to.transition.prototype=Tl,Tl.select=function(n){var t,e,r,i=this.id,u=this.namespace,o=[];n=A(n);for(var a=-1,l=this.length;++a<l;){o.push(t=[]);for(var c=this[a],f=-1,s=c.length;++f<s;)(r=c[f])&&(e=n.call(r,r.__data__,f,a))?("__data__"in r&&(e.__data__=r.__data__),$u(e,f,u,i,r[u][i]),t.push(e)):t.push(null)}return Iu(o,u,i)},Tl.selectAll=function(n){var t,e,r,i,u,o=this.id,a=this.namespace,l=[];n=C(n);for(var c=-1,f=this.length;++c<f;)for(var s=this[c],h=-1,p=s.length;++h<p;)if(r=s[h]){u=r[a][o],e=n.call(r,r.__data__,h,c),l.push(t=[]);for(var g=-1,d=e.length;++g<d;)(i=e[g])&&$u(i,g,a,o,u),t.push(i)}return Iu(l,a,o)},Tl.filter=function(n){var t,e,r,i=[];"function"!=typeof n&&(n=O(n));for(var u=0,o=this.length;u<o;u++){i.push(t=[]);for(var a=0,l=(e=this[u]).length;a<l;a++)(r=e[a])&&n.call(r,r.__data__,a,u)&&t.push(r)}return Iu(i,this.namespace,this.id)},Tl.tween=function(n,t){var e=this.id,r=this.namespace;return arguments.length<2?this.node()[r][e].tween.get(n):I(this,null==t?function(t){t[r][e].tween.remove(n)}:function(i){i[r][e].tween.set(n,t)})},Tl.attr=function(n,t){function e(){this.removeAttribute(a)}function r(){this.removeAttributeNS(a.space,a.local)}function i(n){return null==n?e:(n+="",function(){var t,e=this.getAttribute(a);return e!==n&&(t=o(e,n),function(n){this.setAttribute(a,t(n))})})}function u(n){return null==n?r:(n+="",function(){var t,e=this.getAttributeNS(a.space,a.local);return e!==n&&(t=o(e,n),function(n){this.setAttributeNS(a.space,a.local,t(n))})})}if(arguments.length<2){for(t in n)this.attr(t,n[t]);return this}var o="transform"==n?Xr:mr,a=to.ns.qualify(n);return Zu(this,"attr."+n,t,a.local?u:i)},Tl.attrTween=function(n,t){function e(n,e){var r=t.call(this,n,e,this.getAttribute(i));return r&&function(n){this.setAttribute(i,r(n))}}function r(n,e){var r=t.call(this,n,e,this.getAttributeNS(i.space,i.local));return r&&function(n){this.setAttributeNS(i.space,i.local,r(n))}}var i=to.ns.qualify(n);return this.tween("attr."+n,i.local?r:e)},Tl.style=function(n,e,r){function i(){this.style.removeProperty(n)}function u(e){return null==e?i:(e+="",function(){var i,u=t(this).getComputedStyle(this,null).getPropertyValue(n);return u!==e&&(i=mr(u,e),function(t){this.style.setProperty(n,i(t),r)})})}var o=arguments.length;if(o<3){if("string"!=typeof n){o<2&&(e="");for(r in n)this.style(r,n[r],e);return this}r=""}return Zu(this,"style."+n,e,u)},Tl.styleTween=function(n,e,r){function i(i,u){var o=e.call(this,i,u,t(this).getComputedStyle(this,null).getPropertyValue(n));return o&&function(t){this.style.setProperty(n,o(t),r)}}return arguments.length<3&&(r=""),this.tween("style."+n,i)},Tl.text=function(n){return Zu(this,"text",n,Vu)},Tl.remove=function(){var n=this.namespace;return this.each("end.transition",function(){var t;this[n].count<2&&(t=this.parentNode)&&t.removeChild(this)})},Tl.ease=function(n){var t=this.id,e=this.namespace;return arguments.length<1?this.node()[e][t].ease:("function"!=typeof n&&(n=to.ease.apply(to,arguments)),I(this,function(r){r[e][t].ease=n}))},Tl.delay=function(n){var t=this.id,e=this.namespace;return arguments.length<1?this.node()[e][t].delay:I(this,"function"==typeof n?function(r,i,u){r[e][t].delay=+n.call(r,r.__data__,i,u)}:(n=+n,function(r){r[e][t].delay=n}))},Tl.duration=function(n){var t=this.id,e=this.namespace;return arguments.length<1?this.node()[e][t].duration:I(this,"function"==typeof n?function(r,i,u){r[e][t].duration=Math.max(1,n.call(r,r.__data__,i,u))}:(n=Math.max(1,n),function(r){r[e][t].duration=n}))},Tl.each=function(n,t){var e=this.id,r=this.namespace;if(arguments.length<2){var i=Ll,u=zl;try{zl=e,I(this,function(t,i,u){Ll=t[r][e],n.call(t,t.__data__,i,u)})}finally{Ll=i,zl=u}}else I(this,function(i){var u=i[r][e];(u.event||(u.event=to.dispatch("start","end","interrupt"))).on(n,t)});return this},Tl.transition=function(){for(var n,t,e,r,i=this.id,u=++Rl,o=this.namespace,a=[],l=0,c=this.length;l<c;l++){a.push(n=[]);for(var f=0,s=(t=this[l]).length;f<s;f++)(e=t[f])&&$u(e,f,o,u,{time:(r=e[o][i]).time,ease:r.ease,delay:r.delay+r.duration,duration:r.duration}),n.push(e)}return Iu(a,o,u)},to.svg.axis=function(){function n(n){n.each(function(){var n,c=to.select(this),f=this.__chart__||e,s=this.__chart__=e.copy(),h=null==l?s.ticks?s.ticks.apply(s,a):s.domain():l,p=null==t?s.tickFormat?s.tickFormat.apply(s,a):m:t,g=c.selectAll(".tick").data(h,s),d=g.enter().insert("g",".domain").attr("class","tick").style("opacity",Co),v=to.transition(g.exit()).style("opacity",Co).remove(),y=to.transition(g.order()).style("opacity",1),x=Math.max(i,0)+o,M=Ui(s),_=c.selectAll(".domain").data([0]),b=(_.enter().append("path").attr("class","domain"),to.transition(_));d.append("line"),d.append("text");var w,S,k,N,E=d.select("line"),A=y.select("line"),C=g.select("text").text(p),z=d.select("text"),L=y.select("text"),q="top"===r||"left"===r?-1:1;if("bottom"===r||"top"===r?(n=Bu,w="x",k="y",S="x2",N="y2",C.attr("dy",q<0?"0em":".71em").style("text-anchor","middle"),b.attr("d","M"+M[0]+","+q*u+"V0H"+M[1]+"V"+q*u)):(n=Wu,w="y",k="x",S="y2",N="x2",C.attr("dy",".32em").style("text-anchor",q<0?"end":"start"),b.attr("d","M"+q*u+","+M[0]+"H0V"+M[1]+"H"+q*u)),E.attr(N,q*i),z.attr(k,q*x),A.attr(S,0).attr(N,q*i),L.attr(w,0).attr(k,q*x),s.rangeBand){var T=s,R=T.rangeBand()/2;f=s=function(n){return T(n)+R}}else f.rangeBand?f=s:v.call(n,s,f);d.call(n,f,s),y.call(n,s,s)})}var t,e=to.scale.linear(),r=Dl,i=6,u=6,o=3,a=[10],l=null;return n.scale=function(t){return arguments.length?(e=t,n):e},n.orient=function(t){return arguments.length?(r=t in Pl?t+"":Dl,n):r},n.ticks=function(){return arguments.length?(a=ro(arguments),n):a},n.tickValues=function(t){return arguments.length?(l=t,n):l},n.tickFormat=function(e){return arguments.length?(t=e,n):t},n.tickSize=function(t){var e=arguments.length;return e?(i=+t,u=+arguments[e-1],n):i},n.innerTickSize=function(t){return arguments.length?(i=+t,n):i},n.outerTickSize=function(t){return arguments.length?(u=+t,n):u},n.tickPadding=function(t){return arguments.length?(o=+t,n):o},n.tickSubdivide=function(){return arguments.length&&n},n};var Dl="bottom",Pl={top:1,right:1,bottom:1,left:1};to.svg.brush=function(){function n(t){t.each(function(){var t=to.select(this).style("pointer-events","all").style("-webkit-tap-highlight-color","rgba(0,0,0,0)").on("mousedown.brush",u).on("touchstart.brush",u),o=t.selectAll(".background").data([0]);o.enter().append("rect").attr("class","background").style("visibility","hidden").style("cursor","crosshair"),t.selectAll(".extent").data([0]).enter().append("rect").attr("class","extent").style("cursor","move");var a=t.selectAll(".resize").data(d,m);a.exit().remove(),a.enter().append("g").attr("class",function(n){return"resize "+n}).style("cursor",function(n){return jl[n]}).append("rect").attr("x",function(n){return/[ew]$/.test(n)?-3:null}).attr("y",function(n){return/^[ns]/.test(n)?-3:null}).attr("width",6).attr("height",6).style("visibility","hidden"),a.style("display",n.empty()?"none":null);var l,s=to.transition(t),h=to.transition(o);c&&(l=Ui(c),h.attr("x",l[0]).attr("width",l[1]-l[0]),r(s)),f&&(l=Ui(f),h.attr("y",l[0]).attr("height",l[1]-l[0]),i(s)),e(s)})}function e(n){n.selectAll(".resize").attr("transform",function(n){return"translate("+s[+/e$/.test(n)]+","+h[+/^s/.test(n)]+")"})}function r(n){n.select(".extent").attr("x",s[0]),n.selectAll(".extent,.n>rect,.s>rect").attr("width",s[1]-s[0])}function i(n){n.select(".extent").attr("y",h[0]),n.selectAll(".extent,.e>rect,.w>rect").attr("height",h[1]-h[0])}function u(){function u(){32==to.event.keyCode&&(C||(x=null,L[0]-=s[1],L[1]-=h[1],C=2),S())}function d(){32==to.event.keyCode&&2==C&&(L[0]+=s[1],L[1]+=h[1],C=0,S())}function v(){var n=to.mouse(_),t=!1;M&&(n[0]+=M[0],n[1]+=M[1]),C||(to.event.altKey?(x||(x=[(s[0]+s[1])/2,(h[0]+h[1])/2]),L[0]=s[+(n[0]<x[0])],L[1]=h[+(n[1]<x[1])]):x=null),E&&y(n,c,0)&&(r(k),t=!0),A&&y(n,f,1)&&(i(k),t=!0),t&&(e(k),w({type:"brush",mode:C?"move":"resize"}))}function y(n,t,e){var r,i,u=Ui(t),l=u[0],c=u[1],f=L[e],d=e?h:s,v=d[1]-d[0];if(C&&(l-=f,c-=v+f),r=(e?g:p)?Math.max(l,Math.min(c,n[e])):n[e],C?i=(r+=f)+v:(x&&(f=Math.max(l,Math.min(c,2*x[e]-r))),f<r?(i=r,r=f):i=f),d[0]!=r||d[1]!=i)return e?a=null:o=null,d[0]=r,d[1]=i,!0}function m(){v(),k.style("pointer-events","all").selectAll(".resize").style("display",n.empty()?"none":null),to.select("body").style("cursor",null),q.on("mousemove.brush",null).on("mouseup.brush",null).on("touchmove.brush",null).on("touchend.brush",null).on("keydown.brush",null).on("keyup.brush",null),z(),w({type:"brushend"})}var x,M,_=this,b=to.select(to.event.target),w=l.of(_,arguments),k=to.select(_),N=b.datum(),E=!/^(n|s)$/.test(N)&&c,A=!/^(e|w)$/.test(N)&&f,C=b.classed("extent"),z=W(_),L=to.mouse(_),q=to.select(t(_)).on("keydown.brush",u).on("keyup.brush",d);if(to.event.changedTouches?q.on("touchmove.brush",v).on("touchend.brush",m):q.on("mousemove.brush",v).on("mouseup.brush",m),k.interrupt().selectAll("*").interrupt(),C)L[0]=s[0]-L[0],L[1]=h[0]-L[1];else if(N){var T=+/w$/.test(N),R=+/^n/.test(N);M=[s[1-T]-L[0],h[1-R]-L[1]],L[0]=s[T],L[1]=h[R]}else to.event.altKey&&(x=L.slice());k.style("pointer-events","none").selectAll(".resize").style("display",null),to.select("body").style("cursor",b.style("cursor")),w({type:"brushstart"}),v()}var o,a,l=N(n,"brushstart","brush","brushend"),c=null,f=null,s=[0,0],h=[0,0],p=!0,g=!0,d=Ul[0];return n.event=function(n){n.each(function(){var n=l.of(this,arguments),t={x:s,y:h,i:o,j:a},e=this.__chart__||t;this.__chart__=t,zl?to.select(this).transition().each("start.brush",function(){o=e.i,a=e.j,s=e.x,h=e.y,n({type:"brushstart"})}).tween("brush:brush",function(){var e=xr(s,t.x),r=xr(h,t.y);return o=a=null,function(i){s=t.x=e(i),h=t.y=r(i),n({type:"brush",mode:"resize"})}}).each("end.brush",function(){o=t.i,a=t.j,n({type:"brush",mode:"resize"}),n({type:"brushend"})}):(n({type:"brushstart"}),n({type:"brush",mode:"resize"}),n({type:"brushend"}))})},n.x=function(t){return arguments.length?(c=t,d=Ul[!c<<1|!f],n):c},n.y=function(t){return arguments.length?(f=t,d=Ul[!c<<1|!f],n):f},n.clamp=function(t){return arguments.length?(c&&f?(p=!!t[0],g=!!t[1]):c?p=!!t:f&&(g=!!t),n):c&&f?[p,g]:c?p:f?g:null},n.extent=function(t){var e,r,i,u,l;return arguments.length?(c&&(e=t[0],r=t[1],f&&(e=e[0],r=r[0]),o=[e,r],c.invert&&(e=c(e),r=c(r)),r<e&&(l=e,e=r,r=l),e==s[0]&&r==s[1]||(s=[e,r])),f&&(i=t[0],u=t[1],c&&(i=i[1],u=u[1]),a=[i,u],f.invert&&(i=f(i),u=f(u)),u<i&&(l=i,i=u,u=l),i==h[0]&&u==h[1]||(h=[i,u])),n):(c&&(o?(e=o[0],r=o[1]):(e=s[0],r=s[1],c.invert&&(e=c.invert(e),r=c.invert(r)),r<e&&(l=e,e=r,r=l))),f&&(a?(i=a[0],u=a[1]):(i=h[0],u=h[1],f.invert&&(i=f.invert(i),u=f.invert(u)),u<i&&(l=i,i=u,u=l))),c&&f?[[e,i],[r,u]]:c?[e,r]:f&&[i,u])},n.clear=function(){return n.empty()||(s=[0,0],h=[0,0],o=a=null),n},n.empty=function(){return!!c&&s[0]==s[1]||!!f&&h[0]==h[1]},to.rebind(n,l,"on")};var jl={n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},Ul=[["n","e","s","w","nw","ne","se","sw"],["e","w"],["n","s"],[]],Fl=ia.format=fa.timeFormat,Hl=Fl.utc,Ol=Hl("%Y-%m-%dT%H:%M:%S.%LZ");Fl.iso=Date.prototype.toISOString&&+new Date("2000-01-01T00:00:00.000Z")?Ju:Ol,Ju.parse=function(n){var t=new Date(n);return isNaN(t)?null:t},Ju.toString=Ol.toString,ia.second=On(function(n){return new ua(1e3*Math.floor(n/1e3))},function(n,t){n.setTime(n.getTime()+1e3*Math.floor(t))},function(n){return n.getSeconds()}),ia.seconds=ia.second.range,ia.seconds.utc=ia.second.utc.range,ia.minute=On(function(n){return new ua(6e4*Math.floor(n/6e4))},function(n,t){n.setTime(n.getTime()+6e4*Math.floor(t))},function(n){return n.getMinutes()}),ia.minutes=ia.minute.range,ia.minutes.utc=ia.minute.utc.range,ia.hour=On(function(n){var t=n.getTimezoneOffset()/60;return new ua(36e5*(Math.floor(n/36e5-t)+t))},function(n,t){n.setTime(n.getTime()+36e5*Math.floor(t))},function(n){return n.getHours()}),ia.hours=ia.hour.range,ia.hours.utc=ia.hour.utc.range,ia.month=On(function(n){return(n=ia.day(n)).setDate(1),n},function(n,t){n.setMonth(n.getMonth()+t)},function(n){return n.getMonth()}),ia.months=ia.month.range,ia.months.utc=ia.month.utc.range;var Yl=[1e3,5e3,15e3,3e4,6e4,3e5,9e5,18e5,36e5,108e5,216e5,432e5,864e5,1728e5,6048e5,2592e6,7776e6,31536e6],Il=[[ia.second,1],[ia.second,5],[ia.second,15],[ia.second,30],[ia.minute,1],[ia.minute,5],[ia.minute,15],[ia.minute,30],[ia.hour,1],[ia.hour,3],[ia.hour,6],[ia.hour,12],[ia.day,1],[ia.day,2],[ia.week,1],[ia.month,1],[ia.month,3],[ia.year,1]],Zl=Fl.multi([[".%L",function(n){return n.getMilliseconds()}],[":%S",function(n){return n.getSeconds()}],["%I:%M",function(n){return n.getMinutes()}],["%I %p",function(n){return n.getHours()}],["%a %d",function(n){return n.getDay()&&1!=n.getDate()}],["%b %d",function(n){return 1!=n.getDate()}],["%B",function(n){return n.getMonth()}],["%Y",zt]]),Vl={range:function(n,t,e){return to.range(Math.ceil(n/e)*e,+t,e).map(Ku)},floor:m,ceil:m};Il.year=ia.year,ia.scale=function(){return Gu(to.scale.linear(),Il,Zl)};var Xl=Il.map(function(n){return[n[0].utc,n[1]]}),$l=Hl.multi([[".%L",function(n){return n.getUTCMilliseconds()}],[":%S",function(n){return n.getUTCSeconds()}],["%I:%M",function(n){return n.getUTCMinutes()}],["%I %p",function(n){return n.getUTCHours()}],["%a %d",function(n){return n.getUTCDay()&&1!=n.getUTCDate()}],["%b %d",function(n){return 1!=n.getUTCDate()}],["%B",function(n){return n.getUTCMonth()}],["%Y",zt]]);Xl.year=ia.year.utc,ia.scale.utc=function(){return Gu(to.scale.linear(),Xl,$l)},to.text=An(function(n){return n.responseText}),to.json=function(n,t){return Cn(n,"application/json",Qu,t)},to.html=function(n,t){return console.log("d3.html"),Cn(n,"text/html",no,t)},to.xml=An(function(n){return n.responseXML}),"function"==typeof define&&define.amd?(this.d3=to,define(to)):"object"==typeof module&&module.exports?module.exports=to:this.d3=to}();
