import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { ProfilePictureComponent } from './profile-picture.component';
import { CommonModule } from '@angular/common';

export default {
  title: 'Components/ProfilePicture',
  component: ProfilePictureComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule, ProfilePictureComponent],
    }),
  ],
  argTypes: {
    nom: { control: 'text', description: 'Nom du praticien' },
    prenom: { control: 'text', description: 'Prénom du praticien' },
    status: {
      control: { type: 'select', options: ['online', 'away', 'offline'] },
      description: 'Statut du praticien (online, away, offline)',
    },
  },
} as Meta<ProfilePictureComponent>;

type Story = StoryObj<ProfilePictureComponent>;

export const Default: Story = {
  args: {
    nom: 'Dupont',
    prenom: 'Jean',
    status: 'offline',
  },
};

export const Online: Story = {
  args: {
    nom: 'Durand',
    prenom: '<PERSON>',
    status: 'online',
  },
};

export const Away: Story = {
  args: {
    nom: '<PERSON>',
    prenom: '<PERSON>',
    status: 'away',
  },
};
