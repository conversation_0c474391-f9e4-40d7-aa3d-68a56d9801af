import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { AuthService } from '../../core/services/auth/auth.service';

@Injectable({
  providedIn: 'root'
})
export class EmailAuthGuard implements CanActivate {

  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    // Check if user is authenticated and is not an email auth user
    if (this.authService.isAuthenticated() && !this.authService.isEmailAuthUser()) {
      return true;
    }

    // If user is an email auth user, redirect to home page
    if (this.authService.isEmailAuthUser()) {
      // Could add a notification service here to show a message
      this.router.navigate(['']);
    } else {
      // If not authenticated at all, redirect to login
      this.router.navigate(['/auth/login']);
    }

    return false;
  }
}
