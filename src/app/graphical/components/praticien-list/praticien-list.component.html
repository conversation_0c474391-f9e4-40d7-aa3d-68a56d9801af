<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
<!-- Banner-->
<section>
  <div class="p-4 bg-gray-50 rounded-lg shadow mb-4">
    <div class="flex justify-between items-center">
      <!-- Titre principal avec icône -->
      <div class="flex items-center space-x-3">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-8 h-8 text-cyan-700"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"
          />
        </svg>
        <h1 class="text-xl font-bold text-gray-700">Liste des Praticiens</h1>
      </div>

      <!-- Statistiques globales -->
      <div class="grid grid-cols-2 md:flex md:space-x-8">
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalPraticiens }}</p>
          <p class="text-sm text-gray-500">Praticiens</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalPraticiensPermanent }}</p>
          <p class="text-sm text-gray-500">Permanents</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalPraticiensTemporaire }}</p>
          <p class="text-sm text-gray-500">Temporaires</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalPraticiensJunior }}</p>
          <p class="text-sm text-gray-500">Juniors</p>
        </div>
      </div>

      <!-- Icône décorative -->
      <div class="hidden lg:block text-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-24 h-24 text-cyan-700 mx-auto"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"
          />
        </svg>
        <p class="mt-2 text-sm font-medium text-gray-500">CHRU Nancy</p>
      </div>
    </div>
  </div>
</section>



<section>
  <!-- Tableau des praticiens -->
  <div class="p-card">
    <p-table
      #praticienTable
      [value]="praticiens"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [responsiveLayout]="'scroll'"
      [globalFilterFields]="['nom', 'prenom', 'specialite', 'statut']"
      class="min-w-full bg-white border rounded-lg shadow"
    >
      <!-- Caption -->
      <ng-template pTemplate="caption">
        <div class="relative">
          <!-- Icône de recherche -->
          <span class="absolute inset-y-0 left-0 flex items-center pl-3">
          <i class="pi pi-search text-gray-400"></i>
        </span>
          <!-- Input de recherche -->
          <input
            type="text"
            (input)="praticienTable.filterGlobal(onInput($event), 'contains')"
            class="block w-full pl-10 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
             ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600
             sm:text-sm"
            placeholder="Filtrer par nom, spécialité ou statut"
          />
        </div>
      </ng-template>

      <!-- Header -->
      <ng-template pTemplate="header">
        <tr>
          <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            <i
              class="pi pi-hashtag cursor-pointer ml-2"
              title="Voir les détails de l'acte"
            ></i>
          </th>
          <th pSortableColumn="nom" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Nom
            <p-sortIcon field="nom"></p-sortIcon>
          </th>
          <th pSortableColumn="prenom" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Prénom
            <p-sortIcon field="prenom"></p-sortIcon>
          </th>
          <th pSortableColumn="specialite" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Spécialité
            <p-sortIcon field="specialite"></p-sortIcon>
          </th>
          <th pSortableColumn="statut" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            Statut
            <p-sortIcon field="statut"></p-sortIcon>
          </th>
          <th pSortableColumn="etp" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
            ETP
            <p-sortIcon field="etp"></p-sortIcon>
          </th>
        </tr>
      </ng-template>

      <!-- Body -->
      <ng-template pTemplate="body" let-praticien>
        <tr class="border-b hover:bg-gray-50">
          <td style="padding: 0.75rem; text-align: left;">
            <p class="text-sm font-bold text-gray-600 flex items-center">
              <i
                class="pi pi-eye text-lg text-cyan-700 cursor-pointer ml-2"
                title="Voir les détails du sevice {{praticien.nom }} {{praticien.prenom}}"
                (click)="viewPraticienDetails(praticien.id)"
              ></i>
            </p>
          </td>

          <td class="px-4 py-2"  (click)="viewPraticienDetails(praticien.id)">
            <a  class="text-lg text-cyan-700 hover:underline cursor-pointer font-semibold"
                (click)="viewPraticienDetails(praticien.id)">
              {{ praticien.nom }}
            </a>
          </td>
          <td class="px-4 py-2"  (click)="viewPraticienDetails(praticien.id)">
            <a  class="text-lg text-cyan-700 hover:underline cursor-pointer font-semibold"
                (click)="viewPraticienDetails(praticien.id)">
              {{ praticien.prenom }}
            </a>
          </td>
          <td class="px-4 py-2">{{ praticien.specialite }}</td>
          <td class="px-4 py-2">{{ praticien.statut }}</td>
          <td class="px-4 py-2">{{ praticien.etp }}</td>
        </tr>
      </ng-template>

      <!-- Message vide -->
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="text-center py-4">Aucun praticien trouvé</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</section>
