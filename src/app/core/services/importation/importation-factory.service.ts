import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseImportService, BaseImportResponse } from './base-import.service';
import { AffectationService } from './affectation.service';
import { SigapsService } from './sigaps.service';
import { GardeAstreinteService } from './garde-astreinte.service';
import { LiberaleService } from './liberale.service';
import { ActesService } from './actes.service';

export type ImportCategory = 'affectation' | 'sigaps' | 'garde-et-astreinte' | 'liberale' | 'actes';

// Interface unifiée pour les réponses d'import
export interface UnifiedImportResponse {
  success: boolean;
  status: 'success' | 'error';
  message: string;
  processed: number;
  created?: number;
  updated?: number;
  errors?: any[];
  results?: any[];
}

@Injectable({
  providedIn: 'root'
})
export class ImportationFactoryService {

  constructor(
    private affectationService: AffectationService,
    private sigapsService: SigapsService,
    private gardeAstreinteService: GardeAstreinteService,
    private liberaleService: LiberaleService,
    private actesService: ActesService
  ) {}

  /**
   * Obtenir le service d'import approprié selon la catégorie
   */
  private getImportService(category: ImportCategory): BaseImportService {
    switch (category) {
      case 'affectation':
        return this.affectationService;
      case 'sigaps':
        return this.sigapsService;
      case 'garde-et-astreinte':
        return this.gardeAstreinteService;
      case 'liberale':
        return this.liberaleService;
      case 'actes':
        return this.actesService;
      default:
        throw new Error(`Catégorie d'import non supportée: ${category}`);
    }
  }

  /**
   * Importer des données selon la catégorie
   */
  importData(category: ImportCategory, data: any[]): Observable<UnifiedImportResponse> {
    console.log(`📤 Factory import for category: ${category}`, {
      dataCount: data.length
    });

    const service = this.getImportService(category);

    return service.importData(data).pipe(
      map(response => this.normalizeResponse(response, category))
    );
  }

  /**
   * Normaliser les réponses des différents services
   */
  private normalizeResponse(response: BaseImportResponse, category: ImportCategory): UnifiedImportResponse {
    // Gestion des erreurs API Platform
    if (response['@type'] === 'Error') {
      return {
        success: false,
        status: 'error',
        message: response.detail || response.title || 'Erreur API Platform',
        processed: 0,
        errors: [response]
      };
    }

    // Gestion des réponses de succès selon le format
    if (response.success !== undefined) {
      // Format SIGAPS, Garde-Astreinte, Libérale
      return {
        success: response.success,
        status: response.success ? 'success' : 'error',
        message: this.getSuccessMessage(category, response.processed || 0),
        processed: response.processed || 0,
        results: response.results,
        errors: response.errors || []
      };
    } else if (response.status) {
      // Format Affectation
      return {
        success: response.status === 'success',
        status: response.status,
        message: response.message || this.getSuccessMessage(category, response.processed || 0),
        processed: response.processed || 0,
        created: response.created,
        updated: response.updated,
        errors: response.errors || []
      };
    }

    // Fallback
    return {
      success: false,
      status: 'error',
      message: 'Format de réponse non reconnu',
      processed: 0,
      errors: [response]
    };
  }

  /**
   * Générer un message de succès selon la catégorie
   */
  private getSuccessMessage(category: ImportCategory, processed: number): string {
    const categoryLabels = {
      'affectation': 'affectations',
      'sigaps': 'données SIGAPS',
      'garde-et-astreinte': 'gardes et astreintes',
      'liberale': 'activités libérales',
      'actes': 'actes'
    };

    const label = categoryLabels[category] || 'données';
    return `Import des ${label} terminé - ${processed} enregistrements traités`;
  }

  /**
   * Valider les données selon la catégorie
   */
  validateDataForCategory(category: ImportCategory, data: any[]): { isValid: boolean; errors: string[] } {
    try {
      const service = this.getImportService(category);
      return (service as any).validateData(data);
    } catch (error) {
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : 'Erreur de validation']
      };
    }
  }

  /**
   * Obtenir les informations de l'utilisateur connecté
   */
  getCurrentUserInfo(): { ejcode: string | null; username: string | null } {
    return this.affectationService.getCurrentUserInfo();
  }
}
