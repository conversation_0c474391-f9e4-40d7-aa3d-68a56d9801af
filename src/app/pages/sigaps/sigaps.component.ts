import { Component, OnInit } from '@angular/core';
import {SigapsService} from "../../core/services/sigaps.service";
import {SigapsData} from "../../core/models/sigaps.model";
import {DatePipe, NgForOf, NgIf} from "@angular/common";
import {ChartModule} from "primeng/chart";
import {StyleClassModule} from "primeng/styleclass";
import {BreadcrumbComponent} from "../breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../core/models/breadcrumbItem";
import {TableModule} from "primeng/table";
import {FormsModule} from "@angular/forms";

@Component({
  selector: 'app-sigaps',
  templateUrl: './sigaps.component.html',
  styleUrls: ['./sigaps.component.scss'],
  imports: [
    DatePipe,
    ChartModule,
    NgIf,
    NgForOf,
    StyleClassModule,
    BreadcrumbComponent,
    TableModule,
    FormsModule
  ],
  standalone: true
})
export class SigapsComponent implements OnInit {
  sigapsData: SigapsData | null = null;
  isLoading = true; // Indicateur pour afficher un état de chargement
  hasError = false; // Indicateur pour gérer les erreurs

  // Données et options des graphiques
  pieChartData: any;
  pieChartOptions: any;

  barChartData: any;
  barChartOptions: any;
  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'SIGAPS', url: '/sigaps' },
    { label: 'Liste des activités scientifiques par praticien' }
  ];
  //
  globalFilter: string = '';



  constructor(private sigapsService: SigapsService) {}

  ngOnInit(): void {
    this.loadSigapsData();
  }

  loadSigapsData(): void {
    this.sigapsService.getSigaps().subscribe({
      next: (data) => {
        if (data) {
          this.sigapsData = data;
          this.initializePieChart();
          this.initializeBarChart();
          this.initializeGroupedBarChart(); // Initialise les données du graphique
          this.hasError = false;
        } else {
          this.hasError = true;
        }
        this.isLoading = false;
      },
      error: () => {
        this.hasError = true;
        this.isLoading = false;
      },
    });

    // Charge les données si elles ne sont pas déjà chargées
    this.sigapsService.loadInitialData();
  }

  initializePieChart(): void {
    const firstAuthor = this.sigapsData?.data[0];
    if (!firstAuthor) return;

    // Utilisation de Object.keys et Object.values pour extraire les catégories et leurs valeurs
    const categories = Object.keys(firstAuthor.categories);
    const values = Object.values(firstAuthor.categories);

    this.pieChartData = {
      labels: categories,
      datasets: [
        {
          data: values,
          backgroundColor: ['#ff6384', '#36a2eb', '#ffcd56', '#4bc0c0', '#9966ff'],
          hoverBackgroundColor: ['#ff6384', '#36a2eb', '#ffcd56', '#4bc0c0', '#9966ff'],
        },
      ],
    };

    this.pieChartOptions = {
      plugins: {
        legend: {
          display: true,
          position: 'right',
        },
      },
    };
  }

  initializeBarChart(): void {
    const authors = this.sigapsData?.data;
    if (!authors) return;

    // Générer les labels (services) et scores depuis les données fictives
    const services = authors.map((author) => author.service);
    const scores = authors.map((author) => author.totalSigapsScore);

    this.barChartData = {
      labels: services,
      datasets: [
        {
          label: 'Score SIGAPS par Service',
          data: scores,
          backgroundColor: '#42a5f5',
          borderColor: '#1e88e5',
          borderWidth: 1,
        },
      ],
    };

    this.barChartOptions = {
      maintainAspectRatio: true,
      aspectRatio: 0.9,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: 'Services',
          },
        },
        y: {
          title: {
            display: true,
            text: 'Score SIGAPS',
          },
        },
      },
    };
  }

  //*****************************************************/
  groupedBarChartData: any;
  groupedBarChartOptions: any;

  initializeGroupedBarChart(): void {
    // Vérifie si les données existent
    if (!this.sigapsData || !this.sigapsData.data) return;

    const categories = ["A+", "A", "B", "C", "D"]; // Les catégories fixes de SIGAPS
    const colors = ['#42a5f5', '#66bb6a', '#ffa726']; // Couleurs fixes


    const datasets = this.sigapsData.data.map((author, index) => ({
      label: author.service, // Nom du service
      data: categories.map((category) => author.categories[category] || 0), // Récupère les données par catégorie
      backgroundColor: colors[index % colors.length], // Applique une couleur unique parmi les trois
    }));

    // Structure des données pour le graphique
    this.groupedBarChartData = {
      labels: categories,
      datasets: datasets,
    };

    // Options pour le graphique
    this.groupedBarChartOptions = {
      plugins: {
        legend: {
          display: true,
          position: "top",
        },
      },
      scales: {
        x: {
          stacked: true, // Active l'empilement sur l'axe x
          title: {
            display: true,
            text: "Catégories de publications",
          },
        },
        y: {
          stacked: true,  // Active l'empilement sur l'axe y
          title: {
            display: true,
            text: "Nombre de publications",
          },
        },
      },
    };
  }

// Fonction utilitaire pour générer une couleur aléatoire
  getRandomColor(): string {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }

  //
  onInput(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }
}
