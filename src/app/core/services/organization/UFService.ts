import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import {BehaviorSubject, map, Observable} from 'rxjs';
import {UF} from "../../models/organization/UF.model";

@Injectable({
  providedIn: 'root'
})
export class UFService {
  private ufsSubject = new BehaviorSubject<UF[]>([]);
  ufs$: Observable<UF[]> = this.ufsSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadUFs();
  }

  private loadUFs() {
    this.http.get<UF[]>('/assets/data/ufs.json').subscribe((data) => {
      this.ufsSubject.next(data);
    });
  }

  getUFById(id: string): Observable<UF | undefined> {
    return this.ufs$.pipe(
      map((ufs) => ufs.find((uf) => uf.id === id))
    );
  }
}
