<form class="w-full flex search-container" (submit)="$event.preventDefault()">
  <label for="search-field" class="sr-only">Search</label>
  <div class="relative w-full">
    <!-- Container avec design moderne et largeur maximale -->
    <div class="relative flex items-center w-full bg-gray-50 hover:bg-gray-100 focus-within:bg-white focus-within:ring-2 focus-within:ring-cyan-500 focus-within:ring-opacity-50 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md search-input-container">
      <!-- Icône de recherche -->
      <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none" aria-hidden="true">
        <svg class="h-5 w-5 text-gray-400 group-focus-within:text-cyan-500 transition-colors duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
        </svg>
      </div>

      <!-- Input de recherche avec largeur maximale -->
      <input
        id="search-field"
        name="search-field"
        class="block w-full h-12 pl-12 pr-12 py-3 bg-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-0 border-0 text-base font-medium"
        placeholder="Rechercher pôle, service, UF ou praticien..."
        type="search"
        (keydown)="handleSearchInput($event)"
        (keydown.enter)="handleEnter($event)"
        autocomplete="off"
      />

      <!-- Indicateur de chargement dans l'input -->
      <div *ngIf="isLoading" class="absolute inset-y-0 right-0 flex items-center pr-4">
        <div class="animate-spin rounded-full h-4 w-4 border-2 border-cyan-500 border-t-transparent"></div>
      </div>
    </div>

    <!-- Liste déroulante des résultats avec design moderne -->
    <div
      *ngIf="(searchResults && !isLoading) || isLoading"
      class="absolute top-full left-0 w-full mt-2 bg-white rounded-xl shadow-2xl z-50 max-h-96 overflow-hidden transition-all duration-300 ease-out transform"
      [@fadeAnimation]="'visible'"
      (click)="$event.stopPropagation()"
    >
      <!-- Debug info (supprimé) -->
      <!-- Spinner de chargement moderne -->
      <div
        *ngIf="isLoading"
        class="p-8 flex flex-col items-center justify-center space-y-3"
      >
        <div class="animate-spin rounded-full h-8 w-8 border-3 border-cyan-500 border-t-transparent"></div>
        <p class="text-sm text-gray-500 font-medium">Recherche en cours...</p>
      </div>
      <!-- Header de la dropdown avec bouton fermer -->
      <div *ngIf="searchResults && !isLoading" class="flex items-center justify-between p-4 bg-gray-50 rounded-t-xl">
        <div class="flex items-center space-x-2">
          <svg class="h-4 w-4 text-cyan-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <span class="text-sm font-medium text-gray-700">
            {{ searchResults.total_results }} résultat{{ searchResults.total_results > 1 ? 's' : '' }} trouvé{{ searchResults.total_results > 1 ? 's' : '' }}
          </span>
        </div>
        <button
          type="button"
          class="p-1 rounded-full hover:bg-gray-200 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-all duration-200"
          (click)="closeDropdown()"
        >
          <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Liste des résultats -->
      <div *ngIf="searchResults && !isLoading" class="max-h-80 overflow-y-auto">
        <ul class="divide-y divide-gray-50">
          <!-- Cas où il y a des résultats -->
          <ng-container *ngIf="searchResults.results.length > 0; else noResults">
            <li
              *ngFor="let result of searchResults.results.slice(first, first + rows); let i = index"
              class="group cursor-pointer flex items-center space-x-4 p-4 hover:bg-gradient-to-r hover:from-cyan-50 hover:to-blue-50 transition-all duration-200 transform hover:scale-[1.01]"
              [routerLink]="getRouteForResult(result)"
              (click)="closeDropdown()"
            >
              <!-- Icône avec badge moderne -->
              <div class="relative flex-shrink-0">
                <div class="w-12 h-12 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200"
                     [ngClass]="{
                       'bg-blue-100 text-blue-600': result.type === 'praticien',
                       'bg-green-100 text-green-600': result.type === 'pole',
                       'bg-purple-100 text-purple-600': result.type === 'service',
                       'bg-yellow-100 text-yellow-600': result.type === 'ufs',
                       'bg-gray-100 text-gray-600': !['praticien', 'pole', 'service', 'ufs'].includes(result.type)
                     }">
                  <ng-container [ngSwitch]="result.type">
                    <svg *ngSwitchCase="'praticien'" class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <svg *ngSwitchCase="'pole'" class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    <svg *ngSwitchCase="'service'" class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <svg *ngSwitchCase="'ufs'" class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                    </svg>
                    <svg *ngSwitchDefault class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </ng-container>
                </div>
              </div>

              <!-- Contenu avec hiérarchie visuelle -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2">
                  <h4 class="text-sm font-semibold text-gray-900 truncate group-hover:text-cyan-700 transition-colors duration-200">
                    {{ result.nom }}
                    <span *ngIf="result.prenom" class="font-normal text-gray-600">({{ result.prenom }})</span>
                  </h4>
                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                        [ngClass]="{
                          'bg-blue-100 text-blue-800': result.type === 'praticien',
                          'bg-green-100 text-green-800': result.type === 'pole',
                          'bg-purple-100 text-purple-800': result.type === 'service',
                          'bg-yellow-100 text-yellow-800': result.type === 'ufs',
                          'bg-gray-100 text-gray-800': !['praticien', 'pole', 'service', 'ufs'].includes(result.type)
                        }">
                    {{ result.type }}
                  </span>
                </div>
                <p *ngIf="result.specialite" class="text-sm text-gray-500 truncate mt-1">
                  {{ result.specialite }}
                </p>
              </div>

              <!-- Flèche de navigation -->
              <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </li>
          </ng-container>

          <!-- Cas où aucun résultat n'est trouvé -->
          <ng-template #noResults>
            <li class="p-8 text-center">
              <div class="flex flex-col items-center space-y-3">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                  <svg class="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <div>
                  <h3 class="text-sm font-medium text-gray-900">Aucun résultat trouvé</h3>
                  <p class="text-sm text-gray-500 mt-1">{{ noResultsMessage }}</p>
                </div>
              </div>
            </li>
          </ng-template>
        </ul>
      </div>

      <!-- Paginator moderne -->
      <div *ngIf="searchResults && searchResults.results.length > 0" class="bg-gray-50 rounded-b-xl">
        <p-paginator
          [rows]="rows"
          [totalRecords]="totalRecords"
          [first]="first"
          (onPageChange)="onPageChange($event)"
          styleClass="modern-paginator"
        ></p-paginator>
      </div>
    </div>

<!-- Overlay pour détecter les clics en dehors -->
<div *ngIf="searchResults && !isLoading" class="fixed inset-0 z-10" (click)="closeDropdown()"></div>
