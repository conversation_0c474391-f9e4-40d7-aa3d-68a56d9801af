/* SubHeader styles for activites-list */

/* Ensure autocomplete dropdowns appear above other elements */
:host ::ng-deep {
  .p-autocomplete-panel {
    z-index: 1050 !important;
  }

  .p-dropdown-panel {
    z-index: 1050 !important;
  }

  /* Style for autocomplete input focus */
  .p-autocomplete .p-inputtext:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
  }

  /* Style for dropdown focus */
  .p-dropdown:not(.p-disabled).p-focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6;
  }

  /* Ensure consistent height for all inputs */
  .p-autocomplete .p-inputtext,
  .p-dropdown .p-dropdown-label {
    min-height: 2.25rem;
    display: flex;
    align-items: center;
  }

  /* Style for autocomplete suggestions */
  .p-autocomplete-item {
    padding: 0.5rem 0.75rem;
    border-bottom: 1px solid #f3f4f6;

    &:hover {
      background-color: #f9fafb;
    }

    &.p-highlight {
      background-color: #eff6ff;
      color: #1d4ed8;
    }
  }

  /* Style for dropdown items */
  .p-dropdown-item {
    padding: 0.5rem 0.75rem;

    &:hover {
      background-color: #f9fafb;
    }

    &.p-highlight {
      background-color: #eff6ff;
      color: #1d4ed8;
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .flex-wrap {
    flex-direction: column;
    align-items: stretch;
  }

  .max-w-xs {
    max-width: none;
  }

  .gap-4 {
    gap: 0.75rem;
  }
}

/* Filter badges animations */
.inline-flex {
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Style for individual filter remove buttons */
  button {
    transition: all 0.15s ease-in-out;
    opacity: 0.7;

    &:hover {
      opacity: 1;
      transform: scale(1.1);
    }

    &:focus {
      opacity: 1;
      outline: none;
    }

    svg {
      transition: transform 0.15s ease-in-out;
    }

    &:hover svg {
      transform: rotate(90deg);
    }
  }
}

/* Clear button hover effect */
button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}
