<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  HeaderComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#styleData" class="nav-link"
                role="tab" id="styleData-tab" data-bs-toggle="tab" data-link="style">Styles</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/stories/header.component.ts</code>
</p>






<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>storybook-header</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>CommonModule</code>
                                <code><a href="../components/ButtonComponent.html" target="_self" >ButtonComponent</a></code>
                </td>
            </tr>

            <tr>
                <td class="col-md-3">styleUrls</td>
                <td class="col-md-9"><code>./header.css</code></td>
            </tr>


            <tr>
                <td class="col-md-3">template</td>
                <td class="col-md-9"><pre class="line-numbers"><code class="language-html">&lt;header&gt;  &lt;div class&#x3D;&quot;storybook-header&quot;&gt;
    &lt;div&gt;
      &lt;svg width&#x3D;&quot;32&quot; height&#x3D;&quot;32&quot; viewBox&#x3D;&quot;0 0 32 32&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;&gt;
        &lt;g fill&#x3D;&quot;none&quot; fillRule&#x3D;&quot;evenodd&quot;&gt;
          &lt;path
            d&#x3D;&quot;M10 0h12a10 10 0 0110 10v12a10 10 0 01-10 10H10A10 10 0 010 22V10A10 10 0 0110 0z&quot;
            fill&#x3D;&quot;#FFF&quot;
          /&gt;
          &lt;path
            d&#x3D;&quot;M5.3 10.6l10.4 6v11.1l-10.4-6v-11zm11.4-6.2l9.7 5.5-9.7 5.6V4.4z&quot;
            fill&#x3D;&quot;#555AB9&quot;
          /&gt;
          &lt;path d&#x3D;&quot;M27.2 10.6v11.2l-10.5 6V16.5l10.5-6zM15.7 4.4v11L6 10l9.7-5.5z&quot; fill&#x3D;&quot;#91BAF8&quot; /&gt;
        &lt;/g&gt;
      &lt;/svg&gt;
      &lt;h1&gt;Acme&lt;/h1&gt;
    &lt;/div&gt;
    &lt;div&gt;
      &lt;div *ngIf&#x3D;&quot;user&quot;&gt;
        &lt;span class&#x3D;&quot;welcome&quot;&gt;
          Welcome, &lt;b&gt;{{ user.name }}&lt;/b
          &gt;!
        &lt;/span&gt;
        &lt;storybook-button
          *ngIf&#x3D;&quot;user&quot;
          size&#x3D;&quot;small&quot;
          (onClick)&#x3D;&quot;onLogout.emit($event)&quot;
          label&#x3D;&quot;Log out&quot;
        &gt;&lt;/storybook-button&gt;
      &lt;/div&gt;
      &lt;div *ngIf&#x3D;&quot;!user&quot;&gt;
        &lt;storybook-button
          *ngIf&#x3D;&quot;!user&quot;
          size&#x3D;&quot;small&quot;
          class&#x3D;&quot;margin-left&quot;
          (onClick)&#x3D;&quot;onLogin.emit($event)&quot;
          label&#x3D;&quot;Log in&quot;
        &gt;&lt;/storybook-button&gt;
        &lt;storybook-button
          *ngIf&#x3D;&quot;!user&quot;
          size&#x3D;&quot;small&quot;
          [primary]&#x3D;&quot;true&quot;
          class&#x3D;&quot;margin-left&quot;
          (onClick)&#x3D;&quot;onCreateAccount.emit($event)&quot;
          label&#x3D;&quot;Sign up&quot;
        &gt;&lt;/storybook-button&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/header&gt;</code></pre></td>
            </tr>









        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>


                <tr>
                    <td class="col-md-4">
                        <h6><b>Inputs</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#user" >user</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Outputs</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#onCreateAccount" >onCreateAccount</a>
                            </li>
                            <li>
                                <a href="#onLogin" >onLogin</a>
                            </li>
                            <li>
                                <a href="#onLogout" >onLogout</a>
                            </li>
                        </ul>
                    </td>
                </tr>



        </tbody>
    </table>
</section>


    <section data-compodoc="block-inputs">
    <h3 id="inputs">Inputs</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="user"></a>
                        <b>user</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>User | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>null</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="66" class="link-to-prism">src/stories/header.component.ts:66</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>
    <section data-compodoc="block-outputs">
    <h3 id="outputs">Outputs</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="onCreateAccount"></a>
                        <b>onCreateAccount</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>EventEmitter</code>

                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="75" class="link-to-prism">src/stories/header.component.ts:75</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="onLogin"></a>
                        <b>onLogin</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>EventEmitter</code>

                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="69" class="link-to-prism">src/stories/header.component.ts:69</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
                        <a name="onLogout"></a>
                        <b>onLogout</b>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>EventEmitter</code>

                    </td>
                </tr>
                        <tr>
                            <td class="col-md-2" colspan="2">
                                    <div class="io-line">Defined in <a href="" data-line="72" class="link-to-prism">src/stories/header.component.ts:72</a></div>
                            </td>
                        </tr>
            </tbody>
        </table>
</section>




</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Component, Input, Output, EventEmitter } from &#x27;@angular/core&#x27;;
import { CommonModule } from &#x27;@angular/common&#x27;;

import { ButtonComponent } from &#x27;./button.component&#x27;;
import type { User } from &#x27;./user&#x27;;

@Component({
  selector: &#x27;storybook-header&#x27;,
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: &#x60;&lt;header&gt;
  &lt;div class&#x3D;&quot;storybook-header&quot;&gt;
    &lt;div&gt;
      &lt;svg width&#x3D;&quot;32&quot; height&#x3D;&quot;32&quot; viewBox&#x3D;&quot;0 0 32 32&quot; xmlns&#x3D;&quot;http://www.w3.org/2000/svg&quot;&gt;
        &lt;g fill&#x3D;&quot;none&quot; fillRule&#x3D;&quot;evenodd&quot;&gt;
          &lt;path
            d&#x3D;&quot;M10 0h12a10 10 0 0110 10v12a10 10 0 01-10 10H10A10 10 0 010 22V10A10 10 0 0110 0z&quot;
            fill&#x3D;&quot;#FFF&quot;
          /&gt;
          &lt;path
            d&#x3D;&quot;M5.3 10.6l10.4 6v11.1l-10.4-6v-11zm11.4-6.2l9.7 5.5-9.7 5.6V4.4z&quot;
            fill&#x3D;&quot;#555AB9&quot;
          /&gt;
          &lt;path d&#x3D;&quot;M27.2 10.6v11.2l-10.5 6V16.5l10.5-6zM15.7 4.4v11L6 10l9.7-5.5z&quot; fill&#x3D;&quot;#91BAF8&quot; /&gt;
        &lt;/g&gt;
      &lt;/svg&gt;
      &lt;h1&gt;Acme&lt;/h1&gt;
    &lt;/div&gt;
    &lt;div&gt;
      &lt;div *ngIf&#x3D;&quot;user&quot;&gt;
        &lt;span class&#x3D;&quot;welcome&quot;&gt;
          Welcome, &lt;b&gt;{{ user.name }}&lt;/b
          &gt;!
        &lt;/span&gt;
        &lt;storybook-button
          *ngIf&#x3D;&quot;user&quot;
          size&#x3D;&quot;small&quot;
          (onClick)&#x3D;&quot;onLogout.emit($event)&quot;
          label&#x3D;&quot;Log out&quot;
        &gt;&lt;/storybook-button&gt;
      &lt;/div&gt;
      &lt;div *ngIf&#x3D;&quot;!user&quot;&gt;
        &lt;storybook-button
          *ngIf&#x3D;&quot;!user&quot;
          size&#x3D;&quot;small&quot;
          class&#x3D;&quot;margin-left&quot;
          (onClick)&#x3D;&quot;onLogin.emit($event)&quot;
          label&#x3D;&quot;Log in&quot;
        &gt;&lt;/storybook-button&gt;
        &lt;storybook-button
          *ngIf&#x3D;&quot;!user&quot;
          size&#x3D;&quot;small&quot;
          [primary]&#x3D;&quot;true&quot;
          class&#x3D;&quot;margin-left&quot;
          (onClick)&#x3D;&quot;onCreateAccount.emit($event)&quot;
          label&#x3D;&quot;Sign up&quot;
        &gt;&lt;/storybook-button&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/header&gt;&#x60;,
  styleUrls: [&#x27;./header.css&#x27;],
})
export class HeaderComponent {
  @Input()
  user: User | null &#x3D; null;

  @Output()
  onLogin &#x3D; new EventEmitter&lt;Event&gt;();

  @Output()
  onLogout &#x3D; new EventEmitter&lt;Event&gt;();

  @Output()
  onCreateAccount &#x3D; new EventEmitter&lt;Event&gt;();
}
</code></pre>
    </div>


    <div class="tab-pane fade " id="styleData">
                <p class="comment">
                    <code>./header.css</code>
                </p>
                <pre class="line-numbers"><code class="language-scss">.storybook-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 15px 20px;
  font-family: &#x27;Nunito Sans&#x27;, &#x27;Helvetica Neue&#x27;, Helvetica, Arial, sans-serif;
}

.storybook-header svg {
  display: inline-block;
  vertical-align: top;
}

.storybook-header h1 {
  display: inline-block;
  vertical-align: top;
  margin: 6px 0 6px 10px;
  font-weight: 700;
  font-size: 20px;
  line-height: 1;
}

.storybook-header button + button {
  margin-left: 10px;
}

.storybook-header .welcome {
  margin-right: 10px;
  color: #333;
  font-size: 14px;
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><header>  <div class="storybook-header">    <div>      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">        <g fill="none" fillRule="evenodd">          <path            d="M10 0h12a10 10 0 0110 10v12a10 10 0 01-10 10H10A10 10 0 010 22V10A10 10 0 0110 0z"            fill="#FFF"          />          <path            d="M5.3 10.6l10.4 6v11.1l-10.4-6v-11zm11.4-6.2l9.7 5.5-9.7 5.6V4.4z"            fill="#555AB9"          />          <path d="M27.2 10.6v11.2l-10.5 6V16.5l10.5-6zM15.7 4.4v11L6 10l9.7-5.5z" fill="#91BAF8" />        </g>      </svg>      <h1>Acme</h1>    </div>    <div>      <div *ngIf="user">        <span class="welcome">          Welcome, <b>{{ user.name }}</b          >!        </span>        <storybook-button          *ngIf="user"          size="small"          (onClick)="onLogout.emit($event)"          label="Log out"        ></storybook-button>      </div>      <div *ngIf="!user">        <storybook-button          *ngIf="!user"          size="small"          class="margin-left"          (onClick)="onLogin.emit($event)"          label="Log in"        ></storybook-button>        <storybook-button          *ngIf="!user"          size="small"          [primary]="true"          class="margin-left"          (onClick)="onCreateAccount.emit($event)"          label="Sign up"        ></storybook-button>      </div>    </div>  </div></header></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'HeaderComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'HeaderComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
