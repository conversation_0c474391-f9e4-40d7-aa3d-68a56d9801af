<?php

namespace App\State\Partials;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use App\ApiResource\Activite\Partials\ActeTemporalStatsDto;
use App\Repository\ActesRepository;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Contracts\Cache\CacheInterface;


class ActeTemporalStatsProvider implements ProviderInterface
{
    public function __construct(
        private readonly ActesRepository $actesRepository,
        #[Autowire('%env(int:API_CACHE_TTL)%')] private int $apiCacheTtl = 3600,
        private readonly CacheInterface $cache
    ) {}

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): array
    {
        $acteCode = $uriVariables['acteCode'] ?? null;

        if (!$acteCode) {
            throw new \InvalidArgumentException('Le code acte est requis');
        }

        // Récupération des paramètres de requête
        $request = $context['request'] ?? null;
        $p1Start = $request?->query->get('p1Start');
        $p1End = $request?->query->get('p1End');
        $p2Start = $request?->query->get('p2Start');
        $p2End = $request?->query->get('p2End');
        $p3Start = $request?->query->get('p3Start');
        $p3End = $request?->query->get('p3End');

        // Validation et correction des dates
        $p1Start = $this->validateDate($p1Start ?? 'now');
        $p1End = $this->validateDate($p1End ?? 'now');
        $p2Start = $this->validateDate($p2Start ?? 'now');
        $p2End = $this->validateDate($p2End ?? 'now');
        $p3Start = $this->validateDate($p3Start ?? 'now');
        $p3End = $this->validateDate($p3End ?? 'now');

        $cacheKey = sprintf(
            'acte_temporal_stats_%s_%s_%s_%s_%s_%s_%s',
            $acteCode,
            $p1Start,
            $p1End,
            $p2Start,
            $p2End,
            $p3Start,
            $p3End
        );

        return $this->cache->get($cacheKey, function() use ($acteCode, $p1Start, $p1End, $p2Start, $p2End, $p3Start, $p3End) {
            // Récupération des statistiques pour chaque période
            $p1Stats = $this->actesRepository->findMonthlyStatsByPeriod($acteCode, $p1Start, $p1End);
            $p2Stats = $this->actesRepository->findMonthlyStatsByPeriod($acteCode, $p2Start, $p2End);
            $p3Stats = $this->actesRepository->findMonthlyStatsByPeriod($acteCode, $p3Start, $p3End);

            // Fusion de tous les mois uniques
            $allMonths = array_unique(array_merge(
                array_keys($p1Stats),
                array_keys($p2Stats),
                array_keys($p3Stats)
            ));
            sort($allMonths);

            $stats = [];
            foreach ($allMonths as $yearMonth) {
                list($year, $month) = explode('-', $yearMonth);

                $stats[] = new ActeTemporalStatsDto(
                    $yearMonth,                                    // mois
                    (int)$year,                                   // annee
                    (int)$month,                                  // moisNumero
                    (int)($p1Stats[$yearMonth] ?? 0),            // p1Count
                    (int)($p2Stats[$yearMonth] ?? 0),            // p2Count
                    (int)($p3Stats[$yearMonth] ?? 0)             // p3Count
                );
            }

            return $stats;
        }, $this->apiCacheTtl);
    }

    /**
     * Valide et corrige une date string, retourne une string au format Y-m-d.
     */
    private function validateDate(string $date): string
    {
        try {
            $dateTime = new \DateTime($date);
            $year = (int)$dateTime->format('Y');
            $month = (int)$dateTime->format('m');
            $day = (int)$dateTime->format('d');
            $lastDayOfMonth = (int)$dateTime->format('t');

            // Si le jour dépasse le dernier jour du mois, on corrige
            if ($day > $lastDayOfMonth) {
                $dateTime->setDate($year, $month, $lastDayOfMonth);
            }

            return $dateTime->format('Y-m-d');
        } catch (\Exception $e) {
            // Si la date est totalement invalide, on retourne la date du jour
            return (new \DateTime())->format('Y-m-d');
        }
    }
}
