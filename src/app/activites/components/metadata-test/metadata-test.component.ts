import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { MessageModule } from 'primeng/message';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ActesMetadataService } from '../../../core/services/activite/actes-metadata.service';
import { GlobalFilterService } from '../../../core/services/global-filter/global-filter.service';
import { ActesMetadataModel } from '../../../core/models/activite/ActesMetadataModel';
import { GlobalFilterState } from '../../../core/services/global-filter/global-filter.service';
import { environment } from '../../../../environments/environment';

/**
 * Composant de test pour l'endpoint /api/actes/metadata
 *
 * Ce composant permet de tester :
 * - La récupération des métadonnées avec et sans périodes
 * - L'intégration avec le GlobalFilterService
 * - L'affichage des statistiques par type d'acte
 * - Les performances de l'API
 */
@Component({
  selector: 'app-metadata-test',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    CardModule,
    MessageModule,
    ProgressSpinnerModule
  ],
  template: `
    <div class="metadata-test-container">
      <div class="p-card">
        <div class="p-card-header">
          <h2>🧪 Test Endpoint Métadonnées</h2>
          <p class="text-muted">Test de l'endpoint GET /api/actes/metadata avec périodes P1, P2, P3</p>
          <div class="cache-status">
            <span class="cache-badge" [class]="getCacheStatusClass()">
              {{ getCacheStatusText() }}
            </span>
          </div>
        </div>

        <div class="p-card-body">
          <!-- Boutons de test -->
          <div class="test-buttons mb-4">
            <button
              pButton
              type="button"
              label="Test Sans Périodes"
              icon="pi pi-play"
              class="p-button-outlined mr-2"
              [loading]="loading"
              (click)="testWithoutPeriods()">
            </button>

            <button
              pButton
              type="button"
              label="Test Avec Périodes Globales"
              icon="pi pi-calendar"
              class="p-button-outlined mr-2"
              [loading]="loading"
              (click)="testWithGlobalPeriods()">
            </button>

            <button
              pButton
              type="button"
              label="Vider Cache"
              icon="pi pi-refresh"
              class="p-button-outlined p-button-secondary"
              [disabled]="!isCacheEnabled()"
              (click)="clearCache()">
            </button>
          </div>

          <!-- Affichage des filtres globaux actuels -->
          <div class="global-filters-info mb-4" *ngIf="globalFilters">
            <h4>🔍 Filtres Globaux Actuels</h4>
            <div class="filter-grid">
              <div class="filter-item">
                <strong>Praticien:</strong>
                <span>{{ globalFilters.practitioner?.nom }} {{ globalFilters.practitioner?.prenom || 'Aucun' }}</span>
              </div>
              <div class="filter-item">
                <strong>Pôle:</strong>
                <span>{{ globalFilters.pole?.libelle || 'Aucun' }}</span>
              </div>
              <div class="filter-item">
                <strong>CR:</strong>
                <span>{{ globalFilters.service?.libelle || 'Aucun' }}</span>
              </div>
              <div class="filter-item">
                <strong>Type de venue:</strong>
                <span>{{ globalFilters.venueType || 'Aucun' }}</span>
              </div>
            </div>

            <div class="periods-info mt-3" *ngIf="globalFilters.p1 || globalFilters.p2 || globalFilters.p3">
              <h5>📅 Périodes Configurées</h5>
              <div class="period-grid">
                <div class="period-item" *ngIf="globalFilters.p1">
                  <strong>P1:</strong> {{ formatPeriod(globalFilters.p1) }}
                </div>
                <div class="period-item" *ngIf="globalFilters.p2">
                  <strong>P2:</strong> {{ formatPeriod(globalFilters.p2) }}
                </div>
                <div class="period-item" *ngIf="globalFilters.p3">
                  <strong>P3:</strong> {{ formatPeriod(globalFilters.p3) }}
                </div>
              </div>
            </div>
          </div>

          <!-- Résultats des métadonnées -->
          <div class="metadata-results" *ngIf="metadata">
            <h4>📊 Résultats des Métadonnées</h4>

            <!-- Statistiques générales -->
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-value">{{ metadata.totalItems | number }}</div>
                <div class="stat-label">Total Actes</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ metadata.totalAgents | number }}</div>
                <div class="stat-label">Agents</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ metadata.totalUfs | number }}</div>
                <div class="stat-label">UF</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ metadata.generationTimeMs }}ms</div>
                <div class="stat-label">Temps Génération</div>
              </div>
            </div>

            <!-- Répartition par type -->
            <div class="type-distribution mt-4">
              <h5>📈 Répartition par Type</h5>
              <div class="type-stats">
                <div class="type-item" *ngFor="let typestat of getTypeStats()">
                  <div class="type-bar">
                    <div class="type-label">{{ typestat.type }}</div>
                    <div class="type-progress">
                      <div class="progress-bar" [style.width.%]="typestat.percentage"></div>
                    </div>
                    <div class="type-values">
                      {{ typestat.count | number }} ({{ typestat.percentage }}%)
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Plage de dates -->
            <div class="date-range mt-4" *ngIf="metadata.dateRangeMin && metadata.dateRangeMax">
              <h5>📅 Plage de Dates</h5>
              <p>Du {{ metadata.dateRangeMin | date:'dd/MM/yyyy' }} au {{ metadata.dateRangeMax | date:'dd/MM/yyyy' }}</p>
            </div>

            <!-- Périodes appliquées -->
            <div class="applied-periods mt-4" *ngIf="hasAppliedPeriods()">
              <h5>🎯 Périodes Appliquées</h5>
              <div class="period-list">
                <div class="period-applied" *ngFor="let period of getAppliedPeriodsList()">
                  <strong>{{ period.key }}:</strong> {{ period.value }}
                </div>
              </div>
            </div>

            <!-- Dernière mise à jour -->
            <div class="last-update mt-4" *ngIf="metadata.lastUpdate">
              <h5>🕒 Dernière Mise à Jour</h5>
              <p>{{ metadata.lastUpdate | date:'dd/MM/yyyy HH:mm:ss' }}</p>
            </div>
          </div>

          <!-- Message d'erreur -->
          <div class="error-message" *ngIf="error">
            <p-message severity="error" [text]="error"></p-message>
          </div>

          <!-- Indicateur de chargement -->
          <div class="loading-indicator" *ngIf="loading">
            <p-progressSpinner></p-progressSpinner>
            <p>Chargement des métadonnées...</p>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .metadata-test-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .test-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .filter-grid, .period-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 10px;
      margin-top: 10px;
    }

    .filter-item, .period-item {
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 3px solid #007ad9;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }

    .stat-card {
      text-align: center;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .stat-value {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .type-stats {
      margin-top: 10px;
    }

    .type-item {
      margin-bottom: 15px;
    }

    .type-bar {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .type-label {
      min-width: 60px;
      font-weight: bold;
    }

    .type-progress {
      flex: 1;
      height: 20px;
      background: #e9ecef;
      border-radius: 10px;
      overflow: hidden;
    }

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #28a745, #20c997);
      transition: width 0.3s ease;
    }

    .type-values {
      min-width: 120px;
      text-align: right;
      font-weight: bold;
    }

    .period-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .period-applied {
      padding: 8px;
      background: #e3f2fd;
      border-radius: 4px;
      border-left: 3px solid #2196f3;
    }

    .loading-indicator {
      text-align: center;
      padding: 40px;
    }

    .error-message {
      margin-top: 20px;
    }

    .cache-status {
      margin-top: 10px;
    }

    .cache-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 0.85rem;
      font-weight: bold;
      text-transform: uppercase;
    }

    .cache-enabled {
      background: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #4caf50;
    }

    .cache-disabled {
      background: #fff3e0;
      color: #f57c00;
      border: 1px solid #ff9800;
    }
  `]
})
export class MetadataTestComponent implements OnInit, OnDestroy {
  metadata: ActesMetadataModel | null = null;
  globalFilters: GlobalFilterState | null = null;
  loading = false;
  error: string | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private metadataService: ActesMetadataService,
    private globalFilterService: GlobalFilterService
  ) {}

  ngOnInit(): void {
    // S'abonner aux changements de filtres globaux
    const filterSub = this.globalFilterService.getFilterState().subscribe(filters => {
      this.globalFilters = filters;
    });
    this.subscriptions.push(filterSub);

    // Charger les métadonnées initiales avec les périodes globales
    this.testWithGlobalPeriods();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Test sans périodes (métadonnées globales)
   */
  testWithoutPeriods(): void {
    this.loading = true;
    this.error = null;

    console.log('🧪 Test métadonnées sans périodes');

    this.metadataService.getGlobalMetadata().subscribe({
      next: (data) => {
        this.metadata = data;
        this.loading = false;
        console.log('✅ Métadonnées reçues (sans périodes):', data);
      },
      error: (err) => {
        this.error = 'Erreur lors du chargement des métadonnées: ' + err.message;
        this.loading = false;
        console.error('❌ Erreur métadonnées:', err);
      }
    });
  }

  /**
   * Test avec les périodes du GlobalFilterService
   */
  testWithGlobalPeriods(): void {
    this.loading = true;
    this.error = null;

    const filters = this.globalFilterService.getCurrentFilterState();

    console.log('🧪 Test métadonnées avec périodes globales:', {
      p1: filters.p1,
      p2: filters.p2,
      p3: filters.p3
    });

    this.metadataService.getMetadata(filters.p1, filters.p2, filters.p3).subscribe({
      next: (data) => {
        this.metadata = data;
        this.loading = false;
        console.log('✅ Métadonnées reçues (avec périodes):', data);
      },
      error: (err) => {
        this.error = 'Erreur lors du chargement des métadonnées: ' + err.message;
        this.loading = false;
        console.error('❌ Erreur métadonnées:', err);
      }
    });
  }

  /**
   * Vider le cache des métadonnées
   */
  clearCache(): void {
    this.metadataService.clearCache();
  }

  /**
   * Vérifie si le cache est activé
   */
  isCacheEnabled(): boolean {
    return environment.enableFrontendCache;
  }

  /**
   * Obtient le texte du statut du cache
   */
  getCacheStatusText(): string {
    return environment.enableFrontendCache ? 'Cache Activé' : 'Cache Désactivé';
  }

  /**
   * Obtient la classe CSS pour le statut du cache
   */
  getCacheStatusClass(): string {
    return environment.enableFrontendCache ? 'cache-enabled' : 'cache-disabled';
  }

  /**
   * Formate une période pour l'affichage
   */
  formatPeriod(period: any): string {
    if (!period?.dateDebut || !period?.dateFin) return 'Non définie';

    const debut = new Date(period.dateDebut).toLocaleDateString('fr-FR');
    const fin = new Date(period.dateFin).toLocaleDateString('fr-FR');
    return `${debut} → ${fin}`;
  }

  /**
   * Obtient les statistiques par type pour l'affichage
   */
  getTypeStats(): { type: string; count: number; percentage: number }[] {
    if (!this.metadata) return [];
    return this.metadataService.getTypeStats(this.metadata);
  }

  /**
   * Vérifie s'il y a des périodes appliquées
   */
  hasAppliedPeriods(): boolean {
    return this.metadata ? Object.keys(this.metadata.appliedPeriods).length > 0 : false;
  }

  /**
   * Obtient la liste des périodes appliquées
   */
  getAppliedPeriodsList(): { key: string; value: string }[] {
    if (!this.metadata) return [];

    return Object.entries(this.metadata.appliedPeriods).map(([key, value]) => ({
      key: key.toUpperCase(),
      value
    }));
  }
}
