import { Component, On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subscription } from 'rxjs';

// PrimeNG
import { Table } from 'primeng/table';

// Models
import { AgentModel } from '../../../core/models/praticien/AgentModel';

// Services
import { AgentService } from '../../../core/services/praticien/agent.service';
import { AuthService } from '../../../core/services/auth/auth.service';

// Breadcrumb
export interface BreadcrumbItem {
  label: string;
  url: string;
}

@Component({
  selector: 'app-praticien-list',
  templateUrl: './praticien-list.component.html',
  styleUrls: ['./praticien-list.component.scss']
})
export class PraticienListComponent implements OnInit, OnDestroy {
  // Breadcrumb configuration
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Accueil', url: '/' },
    { label: 'Praticiens', url: '/praticien/praticien-list' }
  ];

  // Data properties for agents
  agents: AgentModel[] = [];
  allAgents: AgentModel[] = [];
  filteredAgents: AgentModel[] = [];

  // Pagination
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalItems: number = 0;
  totalPages: number = 0;

  // Loading and error states
  loading: boolean = true;
  error: string = '';

  // Search/filter state
  currentFilters: any = {};

  // Active filters for display (persistent)
  activeFilters: Array<{key: string, value: string, label: string}> = [];



  // Subscriptions
  private subscriptions: Subscription[] = [];

  // System health indicator
  systemHealthStatus: 'excellent' | 'good' | 'slow' | 'offline' = 'offline';
  systemHealthMessage = 'Vitesse de chargement - Hors ligne';
  generationTimeMs = 0;

  constructor(
    private agentService: AgentService,
    private router: Router,
    private http: HttpClient,
    private authService: AuthService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadAgents();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load agents from the service
   */
  loadAgents(): void {
    this.loading = true;
    this.error = '';
    const startTime = performance.now();

    const subscription = this.agentService.getAgentsByCurrentEj(
      this.currentPage,
      this.itemsPerPage,
      this.currentFilters
    ).subscribe({
      next: (result) => {
        this.agents = result.items;
        this.allAgents = [...result.items];
        this.totalItems = result.totalItems;
        this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
        this.loading = false;

        // Calculate generation time and update system health
        this.generationTimeMs = performance.now() - startTime;
        this.updateSystemHealth();

        console.log(`✅ Loaded ${this.agents.length} agents in ${this.generationTimeMs.toFixed(0)}ms`);
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('Error loading agents:', error);
        this.error = 'Erreur lors du chargement des praticiens';
        this.loading = false;
        this.systemHealthStatus = 'offline';
        this.systemHealthMessage = 'Erreur de connexion';
        this.cdr.detectChanges();
      }
    });

    this.subscriptions.push(subscription);
  }

  /**
   * Handle pagination change
   */
  onPageChange(event: any): void {
    this.currentPage = event.page + 1; // PrimeNG uses 0-based indexing
    this.itemsPerPage = event.rows;
    this.loadAgents();
  }



  /**
   * Navigate to agent details page
   */
  viewAgentDetails(agentId: string): void {
    // Extract the ID from the IRI
    const id = agentId.split('/').pop();
    this.router.navigate(['/praticien/praticien', id]);
  }



  /**
   * Format date for display
   */
  formatDate(dateString: string | null): string {
    if (!dateString) return 'Pas connu de supra';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      return 'Date invalide';
    }
  }

  /**
   * Get full name display (legacy method, kept for compatibility)
   */
  getFullName(agent: AgentModel): string {
    if (agent.fullName) {
      return agent.fullName;
    }

    const parts = [];
    if (agent.prenom) parts.push(agent.prenom);
    if (agent.nom) parts.push(agent.nom);

    return parts.length > 0 ? parts.join(' ') : 'Nom inconnu';
  }

  /**
   * Get full name with title for display (titre + nom + prenom)
   */
  getFullNameWithTitle(agent: AgentModel): string {
    const parts = [];
    if (agent.titre) parts.push(agent.titre);
    if (agent.nom) parts.push(agent.nom);
    if (agent.prenom) parts.push(agent.prenom);

    return parts.length > 0 ? parts.join(' ') : 'Nom inconnu';
  }

  /**
   * Update system health based on generation time
   */
  private updateSystemHealth(): void {
    if (this.generationTimeMs < 500) {
      this.systemHealthStatus = 'excellent';
      this.systemHealthMessage = `Vitesse de chargement - Excellente (${this.generationTimeMs.toFixed(0)}ms)`;
    } else if (this.generationTimeMs < 1000) {
      this.systemHealthStatus = 'good';
      this.systemHealthMessage = `Vitesse de chargement - Bonne (${this.generationTimeMs.toFixed(0)}ms)`;
    } else if (this.generationTimeMs < 3000) {
      this.systemHealthStatus = 'slow';
      this.systemHealthMessage = `Vitesse de chargement - Lente (${this.generationTimeMs.toFixed(0)}ms)`;
    } else {
      this.systemHealthStatus = 'offline';
      this.systemHealthMessage = `Vitesse de chargement - Très lente (${this.generationTimeMs.toFixed(0)}ms)`;
    }
  }

  /**
   * Extract ID from API URL
   */
  extractIdFromApiUrl(url: string): string {
    return url.split('/').pop() || '';
  }

  /**
   * Handle filters change from subheader
   */
  onFiltersChanged(filters: any): void {
    console.log('🔍 Filters changed:', filters);
    this.currentFilters = filters;
    this.currentPage = 1; // Reset to first page when filters change

    // Update active filters display
    this.updateActiveFiltersDisplay(filters);

    this.loadAgents();
  }



  /**
   * Update active filters display
   */
  updateActiveFiltersDisplay(filters: any): void {
    this.activeFilters = Object.keys(filters)
      .filter(key => filters[key] !== '')
      .map(key => ({
        key,
        value: filters[key],
        label: this.getFilterLabel(key)
      }));
  }

  /**
   * Get human-readable label for filter key
   */
  getFilterLabel(key: string): string {
    const labels: { [key: string]: string } = {
      'nom': 'Nom',
      'prenom': 'Prénom',
      'hrUser': 'ID RH',
      'categorie': 'Catégorie',
      'email': 'Email',
      'agentUfs.rgt': 'RGT',
      'agentUfs.typeGrade': 'Type Grade',
      'agentUfs.libelleGrade': 'Libellé Grade',
      'agentUfs.etp': 'ETP',
      'agentUfs.ufs.ufcode': 'Code UF',
      'agentUfs.ufs.cr.crcode': 'Code CR'
    };
    return labels[key] || key;
  }

  /**
   * Remove individual filter
   */
  removeFilter(filterKey: string): void {
    // Remove from current filters
    delete this.currentFilters[filterKey];

    // Update display
    this.updateActiveFiltersDisplay(this.currentFilters);

    // Reload data with updated filters
    this.currentPage = 1;
    this.loadAgents();
  }

  /**
   * Clear all filters
   */
  clearAllFilters(): void {
    this.currentFilters = {};
    this.activeFilters = [];
    this.currentPage = 1;
    this.loadAgents();
  }
}
