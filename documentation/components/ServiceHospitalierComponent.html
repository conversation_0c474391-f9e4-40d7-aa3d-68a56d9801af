<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  ServiceHospitalierComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/organization/components/service-hospitalier/service-hospitalier.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-service-hospitalier</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>NgIf</code>
                            <code>NgForOf</code>
                                <code><a href="../components/InformationPanelComponent.html" target="_self" >InformationPanelComponent</a></code>
                            <code>CardModule</code>
                            <code>BadgeModule</code>
                            <code>ChartModule</code>
                                <code><a href="../components/BreadcrumbComponent.html" target="_self" >BreadcrumbComponent</a></code>
                            <code>StyleClassModule</code>
                            <code>TableModule</code>
                            <code>PaginatorModule</code>
                            <code>SpinnerModule</code>
                            <code>ProgressSpinnerModule</code>
                            <code>NgClass</code>
                </td>
            </tr>




            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./service-hospitalier.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./service-hospitalier.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#breadcrumbItems" >breadcrumbItems</a>
                            </li>
                            <li>
                                <a href="#chartData" >chartData</a>
                            </li>
                            <li>
                                <a href="#chartOptions" >chartOptions</a>
                            </li>
                            <li>
                                <a href="#currentServiceId" >currentServiceId</a>
                            </li>
                            <li>
                                <a href="#isLoading" >isLoading</a>
                            </li>
                            <li>
                                <a href="#isPdfGenerating" >isPdfGenerating</a>
                            </li>
                            <li>
                                <a href="#paginatedUfActesSummary" >paginatedUfActesSummary</a>
                            </li>
                            <li>
                                <a href="#rowsPerPage" >rowsPerPage</a>
                            </li>
                            <li>
                                <a href="#servicesHospitaliers" >servicesHospitaliers</a>
                            </li>
                            <li>
                                <a href="#ufActesSummary" >ufActesSummary</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#downloadPDF" >downloadPDF</a>
                            </li>
                            <li>
                                <a href="#downloadPDFMany" >downloadPDFMany</a>
                            </li>
                            <li>
                                <a href="#loadServiceData" >loadServiceData</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#onPageChange" >onPageChange</a>
                            </li>
                            <li>
                                <a href="#paginate" >paginate</a>
                            </li>
                            <li>
                                <a href="#viewUfDetails" >viewUfDetails</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(serviceHospitalierService: ServiceHospitalierService, route: ActivatedRoute, serviceHospitalierOverviewService: ServiceHospitalierOverviewService, pdfGenerator: PdfGeneratorService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="88" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:88</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>serviceHospitalierService</td>

                                                        <td>
                                                                    <code>ServiceHospitalierService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>route</td>

                                                        <td>
                                                                    <code>ActivatedRoute</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>serviceHospitalierOverviewService</td>

                                                        <td>
                                                                    <code>ServiceHospitalierOverviewService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                                <tr>
                                                        <td>pdfGenerator</td>

                                                        <td>
                                                                    <code>PdfGeneratorService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="downloadPDF"></a>
                    <span class="name">
                        <span ><b>downloadPDF</b></span>
                        <a href="#downloadPDF"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>downloadPDF(elementId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, filename: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="157"
                                    class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:157</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>elementId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>filename</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="downloadPDFMany"></a>
                    <span class="name">
                        <span ><b>downloadPDFMany</b></span>
                        <a href="#downloadPDFMany"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>downloadPDFMany(className: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>, filename: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="162"
                                    class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:162</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>className</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                        <tr>
                                                <td>filename</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadServiceData"></a>
                    <span class="name">
                        <span ><b>loadServiceData</b></span>
                        <a href="#loadServiceData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadServiceData(serviceId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank">string</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="119"
                                    class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:119</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>serviceId</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="100"
                                    class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:100</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onPageChange"></a>
                    <span class="name">
                        <span ><b>onPageChange</b></span>
                        <a href="#onPageChange"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onPageChange(event: <a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank">any</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="146"
                                    class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:146</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                            <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="paginate"></a>
                    <span class="name">
                        <span ><b>paginate</b></span>
                        <a href="#paginate"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>paginate(pageIndex: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank">number</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="139"
                                    class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:139</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>pageIndex</td>
                                            <td>
                                                            <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="viewUfDetails"></a>
                    <span class="name">
                        <span ><b>viewUfDetails</b></span>
                        <a href="#viewUfDetails"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>viewUfDetails(uf: <a href="../interfaces/ActeSummary.html" target="_self">UFActeSummary</a>)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="150"
                                    class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:150</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>uf</td>
                                            <td>
                                                            <code><a href="../interfaces/ActeSummary.html" target="_self" >UFActeSummary</a></code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="breadcrumbItems"></a>
                    <span class="name">
                        <span ><b>breadcrumbItems</b></span>
                        <a href="#breadcrumbItems"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>BreadcrumbItem[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
    { label: &#x27;Pôle Anesthésie-Réanimation&#x27;, url: &#x27;/chru-nancy/pole-anesthesie-reanimation&#x27; },
    // { label: &#x27;Département d’Anesthésie&#x27;, url: &#x27;/chru-nancy/pole-anesthesie-reanimation/departement-anesthesie&#x27; },
    { label: &#x27;Service d’Anesthésie Bloc Opératoire&#x27; }
  ]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="49" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:49</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chartData"></a>
                    <span class="name">
                        <span ><b>chartData</b></span>
                        <a href="#chartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>ChartDataModel</code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="83" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:83</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="chartOptions"></a>
                    <span class="name">
                        <span ><b>chartOptions</b></span>
                        <a href="#chartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/object" target="_blank" >object</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>{
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio:0.9,
    plugins: {
      legend: {
        display: true,
        position: &#x27;top&#x27;,
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: &#x27;Mois&#x27;,
        },
      },
      y: {
        title: {
          display: true,
          text: &#x27;Nombre d\&#x27;actes&#x27;,
        },
      },
    },
  }</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="56" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:56</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="currentServiceId"></a>
                    <span class="name">
                        <span ><b>currentServiceId</b></span>
                        <a href="#currentServiceId"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="44" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:44</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isLoading"></a>
                    <span class="name">
                        <span ><b>isLoading</b></span>
                        <a href="#isLoading"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>true</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="85" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:85</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isPdfGenerating"></a>
                    <span class="name">
                        <span ><b>isPdfGenerating</b></span>
                        <a href="#isPdfGenerating"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>false</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="155" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:155</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="paginatedUfActesSummary"></a>
                    <span class="name">
                        <span ><b>paginatedUfActesSummary</b></span>
                        <a href="#paginatedUfActesSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="87" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:87</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="rowsPerPage"></a>
                    <span class="name">
                        <span ><b>rowsPerPage</b></span>
                        <a href="#rowsPerPage"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>5</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="88" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:88</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="servicesHospitaliers"></a>
                    <span class="name">
                        <span ><b>servicesHospitaliers</b></span>
                        <a href="#servicesHospitaliers"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>ServiceHospitalier[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="46" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:46</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ufActesSummary"></a>
                    <span class="name">
                        <span ><b>ufActesSummary</b></span>
                        <a href="#ufActesSummary"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="../interfaces/ActeSummary.html" target="_self" >UFActeSummary[]</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="84" class="link-to-prism">src/app/organization/components/service-hospitalier/service-hospitalier.component.ts:84</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import {ServiceHospitalier} from &quot;../../../core/models/organization/ServiceHospitalier.model&quot;;
import {ServiceHospitalierService} from &quot;../../../core/services/organization/ServiceHospitalierService&quot;;
import {NgClass, NgForOf, NgIf} from &quot;@angular/common&quot;;
import {InformationPanelComponent} from &quot;../information-panel/information-panel.component&quot;;
import {ActivatedRoute} from &quot;@angular/router&quot;;
import {CardModule} from &quot;primeng/card&quot;;
import {BadgeModule} from &quot;primeng/badge&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {BreadcrumbComponent} from &quot;../../../pages/breadcrumb/breadcrumb.component&quot;;
import {BreadcrumbItem} from &quot;../../../core/models/breadcrumbItem&quot;;
import {GradeETP} from &quot;../../../core/models/GradeETP&quot;;
import {StyleClassModule} from &quot;primeng/styleclass&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {PaginatorModule} from &quot;primeng/paginator&quot;;
import {ServiceHospitalierOverviewService} from &quot;../../../core/services/overview/service-hospitalier-overview.service&quot;;
import {ChartDataModel, UFActeSummary} from &quot;../../../core/models/overview/service-overview.model&quot;;
import {SpinnerModule} from &quot;primeng/spinner&quot;;
import {ProgressSpinnerModule} from &quot;primeng/progressspinner&quot;;
import {PdfGeneratorService} from &quot;../../../core/services/pdf-generator.service&quot;;

@Component({
  selector: &#x27;app-service-hospitalier&#x27;,
  standalone: true,
  imports: [
    NgIf,
    NgForOf,
    InformationPanelComponent,
    CardModule,
    BadgeModule,
    ChartModule,
    BreadcrumbComponent,
    StyleClassModule,
    TableModule,
    PaginatorModule,
    SpinnerModule,
    ProgressSpinnerModule,
    NgClass
  ],
  templateUrl: &#x27;./service-hospitalier.component.html&#x27;,
  styleUrl: &#x27;./service-hospitalier.component.scss&#x27;
})
export class ServiceHospitalierComponent implements OnInit {
  currentServiceId: string &#x3D; &#x27;&#x27;;

  servicesHospitaliers: ServiceHospitalier[] &#x3D; [];

// fil d&#x27;ariane
  breadcrumbItems: BreadcrumbItem[] &#x3D; [
    { label: &#x27;Pôle Anesthésie-Réanimation&#x27;, url: &#x27;/chru-nancy/pole-anesthesie-reanimation&#x27; },
    // { label: &#x27;Département d’Anesthésie&#x27;, url: &#x27;/chru-nancy/pole-anesthesie-reanimation/departement-anesthesie&#x27; },
    { label: &#x27;Service d’Anesthésie Bloc Opératoire&#x27; }
  ];

  // Options pour personnaliser le graphique
  chartOptions &#x3D; {
    responsive: true,
    maintainAspectRatio: false,
    aspectRatio:0.9,
    plugins: {
      legend: {
        display: true,
        position: &#x27;top&#x27;,
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: &#x27;Mois&#x27;,
        },
      },
      y: {
        title: {
          display: true,
          text: &#x27;Nombre d\&#x27;actes&#x27;,
        },
      },
    },
  };

  //
  chartData!: ChartDataModel; // Défini avec le type ChartData
  ufActesSummary: UFActeSummary[] &#x3D; []; // Initialisé avec un tableau vide
  isLoading &#x3D; true;

  paginatedUfActesSummary: any &#x3D; []; // Liste paginée
  rowsPerPage &#x3D; 5; // Nombre d&#x27;éléments par page

  //********************************************


  constructor(
            private serviceHospitalierService: ServiceHospitalierService,
            private route: ActivatedRoute,
            private serviceHospitalierOverviewService :ServiceHospitalierOverviewService,
            private  pdfGenerator: PdfGeneratorService
            ) {}

  ngOnInit(): void {
    this.serviceHospitalierService.servicesHospitaliers$.subscribe((data) &#x3D;&gt; {
      this.servicesHospitaliers &#x3D; data;
    });

    //@todo plutard Récupérer l&#x27;ID du service depuis les paramètres de la route
    // this.serviceId &#x3D; this.route.snapshot.paramMap.get(&#x27;id&#x27;) || &#x27;&#x27;;
    this.currentServiceId &#x3D; &quot;550e8400-e29b-41d4-a716-446655440000&quot;;
    //
    this.serviceHospitalierOverviewService.services$.subscribe((data) &#x3D;&gt; {
      // this.servicesHospitaliers &#x3D; data;
      if (data.length &gt; 0){
        this.loadServiceData(this.currentServiceId);
      }
    });


  }

  loadServiceData(serviceId: string): void {
    console.log(&#x27;Loading service data for ID:&#x27;, serviceId);
    this.serviceHospitalierOverviewService.getServiceById(serviceId).subscribe((service) &#x3D;&gt; {
      console.log(&#x27;Received service data:&#x27;, service);
      if (service) {
        this.chartData &#x3D; service.chartData;
        this.ufActesSummary &#x3D; service.ufActesSummary;

        console.table(this.ufActesSummary)
        console.log(this.ufActesSummary)
        this.paginate(0);
      } else {
        this.isLoading &#x3D; true; // Arrêter le chargement
        console.error(&#x27;Service not found for ID:&#x27;, serviceId);
      }
      this.isLoading &#x3D; false; // Arrêter le chargement
    });
  }

  // Méthode pour paginer
  paginate(pageIndex: number): void {
    const start &#x3D; pageIndex * this.rowsPerPage;
    const end &#x3D; start + this.rowsPerPage;
    this.paginatedUfActesSummary &#x3D; this.ufActesSummary.slice(start, end);
  }

  // Méthode appelée par le paginator
  onPageChange(event: any): void {
    this.paginate(event.page);
  }

  viewUfDetails(uf: UFActeSummary): void {
    console.log(&#x60;Voir les détails pour l&#x27;UF : ${uf.nomUF}&#x60;, uf);
    alert(&#x60;Voir les détails pour l&#x27;UF : ${uf.nomUF}&#x60;);
  }

  isPdfGenerating &#x3D; false;

  downloadPDF(elementId:string,filename:string): void {
    this.pdfGenerator.exportAsPDF(elementId, &#x60;${filename}.pdf&#x60;);
    setTimeout(() &#x3D;&gt; (this.isPdfGenerating &#x3D; false), 1000); // Réinitialise après la génération

  }
  downloadPDFMany(className:string,filename:string): void {
    this.pdfGenerator.exportAsPDFMany(className, &#x60;${filename}.pdf&#x60;);
  }



}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;div  class&#x3D;&quot;p-4  min-h-screen&quot;&gt;
  &lt;section class&#x3D;&quot;pdfContent&quot; id&#x3D;&quot;pdfContent&quot;&gt;
    &lt;!-- Fil d&#x27;ariane --&gt;
    &lt;app-breadcrumb [items]&#x3D;&quot;breadcrumbItems&quot;&gt;&lt;/app-breadcrumb&gt;

    &lt;div *ngIf&#x3D;&quot;isLoading&quot; class&#x3D;&quot;text-center&quot;&gt;
      &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4 p-4   rounded-lg&quot;&gt;
        &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Évolution mensuelle des actes réalisés
          &lt;/h5&gt;
          &lt;p-progressSpinner
            styleClass&#x3D;&quot;w-4rem h-4rem&quot;
            strokeWidth&#x3D;&quot;8&quot;
            fill&#x3D;&quot;var(--surface-ground)&quot;
            animationDuration&#x3D;&quot;.5s&quot; /&gt;
        &lt;/div&gt;

        &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Répartition des actes par UF
          &lt;/h5&gt;
          &lt;p-progressSpinner ariaLabel&#x3D;&quot;loading&quot; /&gt;
        &lt;/div&gt;

      &lt;/div&gt;

    &lt;/div&gt;


    &lt;div [ngClass]&#x3D;&quot;{ &#x27;no-shadow&#x27;: isPdfGenerating }&quot;  *ngIf&#x3D;&quot;!isLoading&quot;&gt;
      &lt;div class&#x3D;&quot;grid grid-cols-12 gap-4 p-4 rounded-lg&quot;&gt;
        &lt;!-- Colonne gauche : Graphique --&gt;
        &lt;div class&#x3D;&quot;col-span-8 bg-white p-4 shadow rounded-lg&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Évolution mensuelle des actes réalisés
          &lt;/h5&gt;

          &lt;p-chart
            type&#x3D;&quot;line&quot;
            [data]&#x3D;&quot;chartData&quot;
            [options]&#x3D;&quot;chartOptions&quot;
            class&#x3D;&quot;w-full h-full&quot;
          &gt;&lt;/p-chart&gt;
        &lt;/div&gt;

        &lt;!-- Colonne droite : Liste des UF --&gt;
        &lt;div class&#x3D;&quot;col-span-4 bg-white p-4 shadow rounded-lg&quot;&gt;
          &lt;h5 class&#x3D;&quot;text-lg font-bold text-gray-700 mb-3&quot;&gt;
            Répartition des actes par UF
          &lt;/h5&gt;

          &lt;!-- Liste des UF paginée --&gt;
          &lt;ul class&#x3D;&quot;divide-y divide-gray-200&quot;&gt;
            &lt;li *ngFor&#x3D;&quot;let uf of paginatedUfActesSummary&quot; class&#x3D;&quot;py-2&quot;&gt;
              &lt;div class&#x3D;&quot;flex justify-between&quot;&gt;
                &lt;div&gt;
                  &lt;p class&#x3D;&quot;text-sm font-bold text-gray-600 flex items-center&quot;&gt;
                    {{ uf.nomUF }}
                    &lt;i
                      class&#x3D;&quot;pi pi-eye text-indigo-600 cursor-pointer ml-2&quot;
                      title&#x3D;&quot;Voir les détails de {{ uf.nomUF }}&quot;
                      (click)&#x3D;&quot;viewUfDetails(uf)&quot;
                    &gt;&lt;/i&gt;
                  &lt;/p&gt;
                  &lt;p class&#x3D;&quot;text-xs text-gray-500&quot;&gt;
                    CCAM : {{ uf.ccam }} | NGAP : {{ uf.ngap }} | LABO : {{ uf.labo }}
                  &lt;/p&gt;
                &lt;/div&gt;
                &lt;p class&#x3D;&quot;text-sm font-bold text-indigo-600&quot;&gt;{{ uf.total }} actes&lt;/p&gt;
              &lt;/div&gt;
            &lt;/li&gt;
          &lt;/ul&gt;

          &lt;!-- Paginator --&gt;
          &lt;div class&#x3D;&quot;flex justify-center mt-4&quot;&gt;
            &lt;p-paginator
              [rows]&#x3D;&quot;5&quot;
              [totalRecords]&#x3D;&quot;ufActesSummary.length&quot;
              (onPageChange)&#x3D;&quot;onPageChange($event)&quot;
            &gt;&lt;/p-paginator&gt;
          &lt;/div&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;

  &lt;/section&gt;


  &lt;button (click)&#x3D;&quot;downloadPDF(&#x27;pdfContent&#x27;,&#x27;service&#x27;)&quot;&gt;Télécharger en PDF&lt;/button&gt;

  &lt;!-- POC LISTE DES Services--&gt;
&lt;!--  &lt;h1 class&#x3D;&quot;text-2xl font-semibold text-gray-800 mb-4&quot;&gt;Liste des Services Hospitaliers&lt;/h1&gt;--&gt;

&lt;!--  &lt;div *ngIf&#x3D;&quot;servicesHospitaliers.length &#x3D;&#x3D;&#x3D; 0&quot; class&#x3D;&quot;text-gray-500 text-center p-4&quot;&gt;--&gt;
&lt;!--    Aucun service hospitalier disponible.--&gt;
&lt;!--  &lt;/div&gt;--&gt;

&lt;!--  &lt;div *ngFor&#x3D;&quot;let service of servicesHospitaliers; &quot; class&#x3D;&quot;bg-white shadow rounded-lg p-4 mb-4&quot;&gt;--&gt;
&lt;!--    &lt;h2 class&#x3D;&quot;text-xl font-bold text-cyan-700&quot;&gt;{{ service.name }}&lt;/h2&gt;--&gt;
&lt;!--    &lt;p class&#x3D;&quot;text-gray-600 mt-1&quot;&gt; Nb Uf: {{ service.ufs.length }}&lt;/p&gt;--&gt;
&lt;!--    &lt;div class&#x3D;&quot;text-gray-500 text-sm mt-2&quot;&gt;--&gt;
&lt;!--      &lt;span class&#x3D;&quot;font-semibold&quot;&gt;Code:&lt;/span&gt; {{ service.code }}--&gt;
&lt;!--    &lt;/div&gt;--&gt;
&lt;!--  &lt;/div&gt;--&gt;
  &lt;!-- Composant Information Panel pour afficher les actes et praticiens associés au service --&gt;
  &lt;app-information-panel   [elementId]&#x3D;&quot;currentServiceId&quot; elementType&#x3D;&quot;service&quot;&gt;&lt;/app-information-panel&gt;
&lt;/div&gt;






&lt;main class&#x3D;&quot;pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24&quot;&gt;
  &lt;div class&#x3D;&quot;text-center&quot;&gt;
    &lt;h1 class&#x3D;&quot;text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl&quot;&gt;
      &lt;span class&#x3D;&quot;block xl:inline&quot;&gt;Supra Test &lt;/span&gt;
      &lt;span class&#x3D;&quot;block text-indigo-600 xl:inline&quot;&gt; PDF Generation&lt;/span&gt;
    &lt;/h1&gt;
    &lt;p class&#x3D;&quot;mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl&quot;&gt;
      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.
      This is a test content to check if the PDF generator service works as expected (A4 size).
    &lt;/p&gt;
    &lt;div class&#x3D;&quot;mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8&quot;&gt;
      &lt;div class&#x3D;&quot;rounded-md shadow&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10&quot;&gt; Get started &lt;/a&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;mt-3 rounded-md shadow sm:mt-0 sm:ml-3&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10&quot;&gt; Live demo &lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/main&gt;
&lt;br&gt;

&lt;main class&#x3D;&quot;pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24&quot;&gt;
  &lt;div class&#x3D;&quot;text-center&quot;&gt;
    &lt;h1 class&#x3D;&quot;text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl&quot;&gt;
      &lt;span class&#x3D;&quot;block xl:inline&quot;&gt;Supra Test &lt;/span&gt;
      &lt;span class&#x3D;&quot;block text-indigo-600 xl:inline&quot;&gt; PDF Generation&lt;/span&gt;
    &lt;/h1&gt;
    &lt;p class&#x3D;&quot;mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl&quot;&gt;
      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.
      This is a test content to check if the PDF generator service works as expected (A4 size).
    &lt;/p&gt;
    &lt;div class&#x3D;&quot;mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8&quot;&gt;
      &lt;div class&#x3D;&quot;rounded-md shadow&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10&quot;&gt; Get started &lt;/a&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;mt-3 rounded-md shadow sm:mt-0 sm:ml-3&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10&quot;&gt; Live demo &lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/main&gt;

&lt;main class&#x3D;&quot;pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24&quot;&gt;
  &lt;div class&#x3D;&quot;text-center&quot;&gt;
    &lt;h1 class&#x3D;&quot;text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl&quot;&gt;
      &lt;span class&#x3D;&quot;block xl:inline&quot;&gt;Supra Test &lt;/span&gt;
      &lt;span class&#x3D;&quot;block text-indigo-600 xl:inline&quot;&gt; PDF Generation&lt;/span&gt;
    &lt;/h1&gt;
    &lt;p class&#x3D;&quot;mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl&quot;&gt;
      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.
      This is a test content to check if the PDF generator service works as expected (A4 size).
    &lt;/p&gt;
    &lt;div class&#x3D;&quot;mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8&quot;&gt;
      &lt;div class&#x3D;&quot;rounded-md shadow&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10&quot;&gt; Get started &lt;/a&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;mt-3 rounded-md shadow sm:mt-0 sm:ml-3&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10&quot;&gt; Live demo &lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/main&gt;
&lt;main class&#x3D;&quot;pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24&quot;&gt;
  &lt;div class&#x3D;&quot;text-center&quot;&gt;
    &lt;h1 class&#x3D;&quot;text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl&quot;&gt;
      &lt;span class&#x3D;&quot;block xl:inline&quot;&gt;Supra Test &lt;/span&gt;
      &lt;span class&#x3D;&quot;block text-indigo-600 xl:inline&quot;&gt; PDF Generation&lt;/span&gt;
    &lt;/h1&gt;
    &lt;p class&#x3D;&quot;mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl&quot;&gt;
      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.
      This is a test content to check if the PDF generator service works as expected (A4 size).
    &lt;/p&gt;
    &lt;div class&#x3D;&quot;mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8&quot;&gt;
      &lt;div class&#x3D;&quot;rounded-md shadow&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10&quot;&gt; Get started &lt;/a&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;mt-3 rounded-md shadow sm:mt-0 sm:ml-3&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10&quot;&gt; Live demo &lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/main&gt;

&lt;main class&#x3D;&quot;pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24&quot;&gt;
  &lt;div class&#x3D;&quot;text-center&quot;&gt;
    &lt;h1 class&#x3D;&quot;text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl&quot;&gt;
      &lt;span class&#x3D;&quot;block xl:inline&quot;&gt;Supra Test &lt;/span&gt;
      &lt;span class&#x3D;&quot;block text-indigo-600 xl:inline&quot;&gt; PDF Generation&lt;/span&gt;
    &lt;/h1&gt;
    &lt;p class&#x3D;&quot;mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl&quot;&gt;
      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.
      This is a test content to check if the PDF generator service works as expected (A4 size).
    &lt;/p&gt;
    &lt;div class&#x3D;&quot;mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8&quot;&gt;
      &lt;div class&#x3D;&quot;rounded-md shadow&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10&quot;&gt; Get started &lt;/a&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;mt-3 rounded-md shadow sm:mt-0 sm:ml-3&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10&quot;&gt; Live demo &lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/main&gt;

&lt;main class&#x3D;&quot;pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24&quot;&gt;
  &lt;div class&#x3D;&quot;text-center&quot;&gt;
    &lt;h1 class&#x3D;&quot;text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl&quot;&gt;
      &lt;span class&#x3D;&quot;block xl:inline&quot;&gt;Supra Test &lt;/span&gt;
      &lt;span class&#x3D;&quot;block text-indigo-600 xl:inline&quot;&gt; PDF Generation&lt;/span&gt;
    &lt;/h1&gt;
    &lt;p class&#x3D;&quot;mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl&quot;&gt;
      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.
      This is a test content to check if the PDF generator service works as expected (A4 size).
    &lt;/p&gt;
    &lt;div class&#x3D;&quot;mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8&quot;&gt;
      &lt;div class&#x3D;&quot;rounded-md shadow&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10&quot;&gt; Get started &lt;/a&gt;
      &lt;/div&gt;
      &lt;div class&#x3D;&quot;mt-3 rounded-md shadow sm:mt-0 sm:ml-3&quot;&gt;
        &lt;a href&#x3D;&quot;#&quot; class&#x3D;&quot;w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10&quot;&gt; Live demo &lt;/a&gt;
      &lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/main&gt;

&lt;button (click)&#x3D;&quot;downloadPDFMany(&#x27;pdfContent&#x27;,&#x27;service_eh-multi-section&#x27;)&quot;&gt;Télécharger en PDF&lt;/button&gt;
</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><div  class="p-4  min-h-screen">  <section class="pdfContent" id="pdfContent">    <!-- Fil d\'ariane -->    <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>    <div *ngIf="isLoading" class="text-center">      <div class="grid grid-cols-12 gap-4 p-4   rounded-lg">        <div class="col-span-8 bg-white p-4 shadow rounded-lg">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Évolution mensuelle des actes réalisés          </h5>          <p-progressSpinner            styleClass="w-4rem h-4rem"            strokeWidth="8"            fill="var(--surface-ground)"            animationDuration=".5s" />        </div>        <div class="col-span-4 bg-white p-4 shadow rounded-lg">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Répartition des actes par UF          </h5>          <p-progressSpinner ariaLabel="loading" />        </div>      </div>    </div>    <div [ngClass]="{ \'no-shadow\': isPdfGenerating }"  *ngIf="!isLoading">      <div class="grid grid-cols-12 gap-4 p-4 rounded-lg">        <!-- Colonne gauche : Graphique -->        <div class="col-span-8 bg-white p-4 shadow rounded-lg">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Évolution mensuelle des actes réalisés          </h5>          <p-chart            type="line"            [data]="chartData"            [options]="chartOptions"            class="w-full h-full"          ></p-chart>        </div>        <!-- Colonne droite : Liste des UF -->        <div class="col-span-4 bg-white p-4 shadow rounded-lg">          <h5 class="text-lg font-bold text-gray-700 mb-3">            Répartition des actes par UF          </h5>          <!-- Liste des UF paginée -->          <ul class="divide-y divide-gray-200">            <li *ngFor="let uf of paginatedUfActesSummary" class="py-2">              <div class="flex justify-between">                <div>                  <p class="text-sm font-bold text-gray-600 flex items-center">                    {{ uf.nomUF }}                    <i                      class="pi pi-eye text-indigo-600 cursor-pointer ml-2"                      title="Voir les détails de {{ uf.nomUF }}"                      (click)="viewUfDetails(uf)"                    ></i>                  </p>                  <p class="text-xs text-gray-500">                    CCAM : {{ uf.ccam }} | NGAP : {{ uf.ngap }} | LABO : {{ uf.labo }}                  </p>                </div>                <p class="text-sm font-bold text-indigo-600">{{ uf.total }} actes</p>              </div>            </li>          </ul>          <!-- Paginator -->          <div class="flex justify-center mt-4">            <p-paginator              [rows]="5"              [totalRecords]="ufActesSummary.length"              (onPageChange)="onPageChange($event)"            ></p-paginator>          </div>        </div>      </div>    </div>  </section>  <button (click)="downloadPDF(\'pdfContent\',\'service\')">Télécharger en PDF</button>  <!-- POC LISTE DES Services--><!--  <h1 class="text-2xl font-semibold text-gray-800 mb-4">Liste des Services Hospitaliers</h1>--><!--  <div *ngIf="servicesHospitaliers.length === 0" class="text-gray-500 text-center p-4">--><!--    Aucun service hospitalier disponible.--><!--  </div>--><!--  <div *ngFor="let service of servicesHospitaliers; " class="bg-white shadow rounded-lg p-4 mb-4">--><!--    <h2 class="text-xl font-bold text-cyan-700">{{ service.name }}</h2>--><!--    <p class="text-gray-600 mt-1"> Nb Uf: {{ service.ufs.length }}</p>--><!--    <div class="text-gray-500 text-sm mt-2">--><!--      <span class="font-semibold">Code:</span> {{ service.code }}--><!--    </div>--><!--  </div>-->  <!-- Composant Information Panel pour afficher les actes et praticiens associés au service -->  <app-information-panel   [elementId]="currentServiceId" elementType="service"></app-information-panel></div><main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">  <div class="text-center">    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">      <span class="block xl:inline">Supra Test </span>      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>    </h1>    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.      This is a test content to check if the PDF generator service works as expected (A4 size).    </p>    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">      <div class="rounded-md shadow">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>      </div>      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>      </div>    </div>  </div></main><br><main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">  <div class="text-center">    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">      <span class="block xl:inline">Supra Test </span>      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>    </h1>    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.      This is a test content to check if the PDF generator service works as expected (A4 size).    </p>    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">      <div class="rounded-md shadow">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>      </div>      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>      </div>    </div>  </div></main><main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">  <div class="text-center">    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">      <span class="block xl:inline">Supra Test </span>      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>    </h1>    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.      This is a test content to check if the PDF generator service works as expected (A4 size).    </p>    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">      <div class="rounded-md shadow">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>      </div>      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>      </div>    </div>  </div></main><main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">  <div class="text-center">    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">      <span class="block xl:inline">Supra Test </span>      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>    </h1>    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.      This is a test content to check if the PDF generator service works as expected (A4 size).    </p>    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">      <div class="rounded-md shadow">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>      </div>      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>      </div>    </div>  </div></main><main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">  <div class="text-center">    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">      <span class="block xl:inline">Supra Test </span>      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>    </h1>    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.      This is a test content to check if the PDF generator service works as expected (A4 size).    </p>    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">      <div class="rounded-md shadow">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>      </div>      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>      </div>    </div>  </div></main><main class="pdfContent mt-16 mx-auto max-w-7xl px-4 sm:mt-24">  <div class="text-center">    <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">      <span class="block xl:inline">Supra Test </span>      <span class="block text-indigo-600 xl:inline"> PDF Generation</span>    </h1>    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">      Anim aute id magna aliqua ad ad non deserunt sunt. Qui irure qui lorem cupidatat commodo. Elit sunt amet fugiat veniam occaecat fugiat aliqua.      This is a test content to check if the PDF generator service works as expected (A4 size).    </p>    <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">      <div class="rounded-md shadow">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"> Get started </a>      </div>      <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">        <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"> Live demo </a>      </div>    </div>  </div></main><button (click)="downloadPDFMany(\'pdfContent\',\'service_eh-multi-section\')">Télécharger en PDF</button></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'ServiceHospitalierComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'ServiceHospitalierComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
