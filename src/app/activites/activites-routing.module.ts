import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ActivitesListComponent } from "./components/activites-list/activites-list.component";
import { SingleActiviteComponent } from "./components/single-activite/single-activite.component";
import { ActivitesMultiDetailsComponent } from "./components/activites-multi-details/activites-multi-details.component";
import { MetadataTestComponent } from "./components/metadata-test/metadata-test.component";
import { FeatureFlagGuard } from "../core/guards/feature-flag.guard";

const routes: Routes = [
  // ✅ Route activée pour ce sprint
  {
    path: "activites-list",
    component: ActivitesListComponent
  },

  // 🚧 Routes désactivées pour ce sprint
  {
    path: "activite/:id",
    component: SingleActiviteComponent,
    canActivate: [FeatureFlagGuard]
  },
  {
    path: "activites-multi-details",
    component: ActivitesMultiDetailsComponent,
    canActivate: [FeatureFlagGuard]
  },
  {
    path: "metadata-test",
    component: MetadataTestComponent,
    canActivate: [FeatureFlagGuard]
  },

  // Redirection par défaut vers la liste (seule route autorisée)
  { path: "", redirectTo: "activites-list", pathMatch: "full" }
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ActivitesRoutingModule {}
