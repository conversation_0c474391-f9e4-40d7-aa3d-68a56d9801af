/* Custom styles for login tabs and dialog */
::ng-deep .login-tabs {
  .p-tabview-nav {
    border: none;
    background: transparent;
    border-radius: 8px;
    display: flex;
    margin-bottom: 1.5rem;

    li {
      flex: 1;
      margin-right: 0.5rem;

      &:last-child {
        margin-right: 0;
      }

      .p-tabview-nav-link {
        background: rgba(255, 255, 255, 0.5);
        border: 1px solid rgba(203, 213, 225, 0.5);
        border-radius: 8px;
        color: #475569;
        transition: all 0.3s ease;
        padding: 0.75rem 1rem;
        font-weight: 500;

        &:not(.p-disabled):focus {
          box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
        }

        &:hover {
          background: rgba(255, 255, 255, 0.8);
          border-color: rgba(203, 213, 225, 0.8);
        }
      }

      &.p-highlight .p-tabview-nav-link {
        background: white;
        border-color: #0ea5e9;
        color: #0ea5e9;
        font-weight: 600;
        box-shadow: 0 4px 6px -1px rgba(14, 165, 233, 0.1), 0 2px 4px -1px rgba(14, 165, 233, 0.06);
      }
    }
  }

  .p-tabview-panels {
    background: transparent;
    border: none;
    padding: 0;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .p-tabview-nav li .p-tabview-nav-link {
      padding: 0.5rem;

      i {
        margin-right: 0.25rem;
      }
    }
  }
}

/* Custom styles for verification dialog */
::ng-deep .p-dialog {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  .p-dialog-header {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    color: white;
    padding: 1rem 1.5rem;
    border-bottom: none;

    .p-dialog-title {
      font-weight: 600;
      font-size: 1.1rem;
    }
  }

  .p-dialog-content {
    padding: 1.5rem;
    background: white;
  }

  .p-fluid .p-button {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }
}
