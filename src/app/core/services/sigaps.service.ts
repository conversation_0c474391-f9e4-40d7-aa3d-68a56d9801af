import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { catchError, tap } from 'rxjs/operators';
import { SigapsData } from '../models/sigaps.model';

@Injectable({
  providedIn: 'root',
})
export class SigapsService {
  private sigapsUrl = './assets/data/sigaps.json';
  private sigaps$ = new BehaviorSubject<SigapsData | null>(null);

  constructor(private http: HttpClient) {}


  loadInitialData(): void {
    this.http.get<SigapsData>(this.sigapsUrl).pipe(
      tap((data) => {
        this.sigaps$.next(data); // Met à jour les données dans le BehaviorSubject
      }),
      catchError((error) => {
        console.error('Erreur lors du chargement des données SIGAPS:', error);
        this.sigaps$.next(null); // En cas d'erreur, passe les données à null
        throw error; // Propager l'erreur pour un traitement additionnel si nécessaire
      })
    ).subscribe();
  }

  /**
   * Retourne un Observable pour s'abonner aux données SIGAPS.
   */
  getSigaps(): Observable<SigapsData | null> {
    return this.sigaps$.asObservable();
  }
}
