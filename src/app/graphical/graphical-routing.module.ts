import {NgModule} from "@angular/core";
import {RouterModule, Routes} from "@angular/router";
import {CcamGraphComponent} from "./components/ccam-graph/ccam-graph.component";
import {NgapGraphComponent} from "./components/ngap-graph/ngap-graph.component";
import {AffectationPraticienComponent} from "./components/affectation-praticien/affectation-praticien.component";
import {PraticienListComponent} from "./components/praticien-list/praticien-list.component";

const routes: Routes = [
  { path: "ccam", component: CcamGraphComponent },
  { path: "ngap", component: NgapGraphComponent },
  { path: "praticien/:uid", component: AffectationPraticienComponent },
  { path: "praticien-list", component: PraticienListComponent },
];

@NgModule({
  imports :[RouterModule.forChild(routes)],
  exports :[RouterModule]
})
export class GraphicalRoutingModule  {}
