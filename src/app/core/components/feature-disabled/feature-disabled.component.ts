import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FeatureFlagService } from '../../services/feature-flag.service';

@Component({
  selector: 'app-feature-disabled',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="feature-disabled-container">
      <div class="feature-disabled-card">
        <div class="feature-disabled-icon">
          <i class="pi pi-clock text-4xl text-gray-400"></i>
        </div>
        
        <div class="feature-disabled-content">
          <h3 class="text-xl font-semibold text-gray-700 mb-2">
            Fonctionnalité en cours de développement
          </h3>
          
          <p class="text-gray-600 mb-4">
            {{ getMessage() }}
          </p>
          
          <div class="feature-disabled-info">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div class="flex items-start">
                <i class="pi pi-info-circle text-blue-500 mt-1 mr-3"></i>
                <div>
                  <h4 class="text-sm font-medium text-blue-800 mb-1">
                    Développement par sprints
                  </h4>
                  <p class="text-sm text-blue-700">
                    Cette fonctionnalité sera disponible dans un prochain sprint. 
                    Nous développons l'application de manière progressive pour vous offrir 
                    la meilleure expérience possible.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="feature-disabled-actions mt-6">
            <button 
              (click)="goBack()"
              class="px-4 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors">
              <i class="pi pi-arrow-left mr-2"></i>
              Retour au tableau de bord
            </button>
          </div>
        </div>
      </div>
      
      <!-- Debug info en mode développement -->
      <div *ngIf="showDebugInfo" class="feature-debug-info mt-6">
        <details class="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <summary class="cursor-pointer text-sm font-medium text-gray-700">
            Informations de développement
          </summary>
          <div class="mt-3 text-xs text-gray-600">
            <p><strong>Feature demandée:</strong> {{ featureName || 'Non spécifiée' }}</p>
            <p><strong>Features activées:</strong> {{ getEnabledFeatures().join(', ') }}</p>
            <p><strong>Routes activées:</strong></p>
            <ul class="list-disc list-inside ml-4">
              <li *ngFor="let route of getEnabledRoutes()">{{ route }}</li>
            </ul>
          </div>
        </details>
      </div>
    </div>
  `,
  styles: [`
    .feature-disabled-container {
      min-height: 60vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
    }
    
    .feature-disabled-card {
      max-width: 600px;
      width: 100%;
      background: white;
      border-radius: 1rem;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      padding: 3rem;
      text-align: center;
    }
    
    .feature-disabled-icon {
      margin-bottom: 2rem;
    }
    
    .feature-disabled-content {
      text-align: left;
    }
    
    .feature-disabled-info {
      margin: 1.5rem 0;
    }
    
    .feature-disabled-actions {
      text-align: center;
    }
    
    .feature-debug-info {
      max-width: 600px;
      width: 100%;
    }
  `]
})
export class FeatureDisabledComponent {
  @Input() featureName?: string;
  @Input() customMessage?: string;
  @Input() showDebugInfo: boolean = false; // Activer en mode dev

  constructor(private featureFlagService: FeatureFlagService) {
    // Activer les infos de debug en mode développement
    this.showDebugInfo = !this.isProduction();
  }

  getMessage(): string {
    if (this.customMessage) {
      return this.customMessage;
    }
    
    if (this.featureName) {
      return this.featureFlagService.getMaintenanceMessage(this.featureName);
    }
    
    return 'Cette fonctionnalité sera bientôt disponible.';
  }

  getEnabledFeatures(): string[] {
    return this.featureFlagService.getEnabledFeatures();
  }

  getEnabledRoutes(): string[] {
    return this.featureFlagService.getEnabledRoutes();
  }

  goBack(): void {
    window.history.back();
  }

  private isProduction(): boolean {
    // Vous pouvez adapter cette logique selon votre configuration
    return window.location.hostname !== 'localhost';
  }
}
