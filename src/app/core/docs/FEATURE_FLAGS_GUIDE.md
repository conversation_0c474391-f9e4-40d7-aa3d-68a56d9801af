# Guide d'utilisation des Feature Flags

## Vue d'ensemble

Le système de feature flags permet de gérer le déploiement par étapes de l'application Supra, en activant/désactivant facilement des fonctionnalités selon les sprints.

## Configuration actuelle (Sprint actuel)

### ✅ Features activées pour ce sprint
- **activites-list** : Composant Activités List uniquement (`/activites`, `/activites-list`)
- **import** : Module Import - admin seulement (`/referent/import`)
- **auth** : Authentification (`/auth/*`)
- **dashboard** : Tableau de bord (`/`)

### 🚧 Features désactivées (prochains sprints)
- **activites-details** : Composants détails Activités (`/activites-multi-details`, `/activites/details`)
- **praticiens** : Module Praticiens
- **structure** : Module Structure (Pôles, CR, Services, UF)
- **sigaps** : Module SIGAPS
- **garde-astreinte** : Module Garde et Astreinte
- **liberale** : Module Libérale
- **actes** : Module Actes

### 📋 Scope précis du sprint
- ✅ **Activités** : Uniquement la liste des activités (pas les détails)
- ✅ **Import** : Fonctionnalité complète (réservée aux admins)
- ✅ **Login/Auth** : Système d'authentification complet

## Utilisation

### 1. Protection des routes

Ajoutez le guard dans vos routes :

```typescript
// app-routing.module.ts
import { FeatureFlagGuard } from './core/guards/feature-flag.guard';

const routes: Routes = [
  {
    path: 'activites',
    loadChildren: () => import('./features/activites/activites.module').then(m => m.ActivitesModule),
    canActivate: [FeatureFlagGuard]
  },
  {
    path: 'praticiens',
    loadChildren: () => import('./features/praticiens/praticiens.module').then(m => m.PraticiensModule),
    canActivate: [FeatureFlagGuard] // Sera bloqué si désactivé
  }
];
```

### 2. Désactivation visuelle des éléments de menu

Utilisez la directive `appFeatureFlag` :

```html
<!-- Sidebar -->
<a routerLink="/praticiens" 
   appFeatureFlag="praticiens" 
   disableMode="blur"
   class="menu-item">
  Praticiens
</a>

<!-- Modes disponibles -->
<div appFeatureFlag="sigaps" disableMode="hide">Caché si désactivé</div>
<div appFeatureFlag="liberale" disableMode="disable">Grisé si désactivé</div>
<div appFeatureFlag="structure" disableMode="blur">Flouté si désactivé</div>
```

### 3. Vérification conditionnelle dans les composants

```typescript
// Dans un composant
import { FeatureFlagService } from './core/services/feature-flag.service';

export class MonComposant {
  constructor(private featureFlagService: FeatureFlagService) {}

  ngOnInit() {
    if (this.featureFlagService.isFeatureEnabled('praticiens')) {
      // Logique pour feature activée
    }
  }

  get canAccessImport(): boolean {
    return this.featureFlagService.isFeatureEnabled('import');
  }
}
```

### 4. Affichage conditionnel dans les templates

```html
<!-- Affichage conditionnel -->
<div *ngIf="featureFlagService.isFeatureEnabled('sigaps')">
  Contenu SIGAPS
</div>

<!-- Message pour feature désactivée -->
<app-feature-disabled 
  *ngIf="!featureFlagService.isFeatureEnabled('praticiens')"
  featureName="praticiens"
  customMessage="Le module Praticiens sera disponible dans le prochain sprint.">
</app-feature-disabled>
```

## Modification de la configuration

### Pour activer une nouvelle feature (prochain sprint)

1. Ouvrez `src/app/core/services/feature-flag.service.ts`
2. Modifiez la configuration :

```typescript
// Exemple : Activer les détails d'activités pour le prochain sprint
'activites-details': {
  enabled: true, // Changer de false à true
  routes: ['/activites-multi-details', '/activites/details'],
  description: 'Composants détails Activités - Maintenant disponible!'
}
```

### Pour désactiver une feature temporairement

```typescript
// Exemple : Désactiver temporairement la liste d'activités
'activites-list': {
  enabled: false, // Changer de true à false
  routes: ['/activites', '/activites-list'],
  description: 'Composant Activités List - Temporairement désactivé'
}
```

### Exemple d'application dans le sidebar

```html
<!-- Activités List - Activé pour ce sprint -->
<a routerLink="/activites"
   class="menu-item">
  <i class="pi pi-chart-bar"></i>
  Activités v2
</a>

<!-- Praticiens - Désactivé avec effet visuel -->
<a routerLink="/graphic/praticien-list"
   appFeatureFlag="praticiens"
   disableMode="blur"
   class="menu-item">
  <i class="pi pi-users"></i>
  Praticiens
</a>

<!-- Structure - Complètement caché -->
<div appFeatureFlag="structure" disableMode="hide">
  <button class="menu-item">
    <i class="pi pi-sitemap"></i>
    Structure
  </button>
</div>

<!-- Import - Conditionnel selon rôle admin -->
<a *ngIf="hasAdminRole() && featureFlagService.isFeatureEnabled('import')"
   routerLink="/referent/import"
   class="menu-item">
  <i class="pi pi-upload"></i>
  Import
</a>
```

## Styles CSS

Les styles sont automatiquement appliqués via `feature-flags.scss` :

- `.feature-disabled` : Élément grisé et non-cliquable
- `.feature-blurred` : Élément flouté avec overlay
- `.feature-coming-soon-overlay` : Overlay avec icône "bientôt disponible"

## Composants disponibles

### FeatureFlagService
- `isFeatureEnabled(feature: string): boolean`
- `isRouteEnabled(route: string): boolean`
- `getEnabledFeatures(): string[]`
- `getMaintenanceMessage(feature?: string): string`

### FeatureFlagGuard
- Protège automatiquement les routes
- Redirige vers le dashboard si route désactivée

### FeatureFlagDirective
- `appFeatureFlag="featureName"`
- `disableMode="hide|disable|blur"`

### FeatureDisabledComponent
- Affiche un message élégant pour les features désactivées
- Inclut des informations de debug en mode développement

## Bonnes pratiques

1. **Nommage cohérent** : Utilisez des noms de features clairs et cohérents
2. **Documentation** : Ajoutez toujours une description pour chaque feature
3. **Tests** : Testez les features activées/désactivées avant déploiement
4. **Communication** : Informez l'équipe des changements de configuration
5. **Rollback** : Gardez la possibilité de désactiver rapidement une feature

## Exemple d'intégration complète

```typescript
// sidebar.component.ts
export class SidebarComponent {
  constructor(public featureFlagService: FeatureFlagService) {}
}
```

```html
<!-- sidebar.component.html -->
<a routerLink="/activites" 
   *ngIf="featureFlagService.isFeatureEnabled('activites')"
   class="menu-item">
  Activités v2
</a>

<a routerLink="/praticiens" 
   appFeatureFlag="praticiens" 
   disableMode="blur"
   class="menu-item">
  Praticiens
</a>
```

## Debug et monitoring

En mode développement, le composant `FeatureDisabledComponent` affiche :
- Les features actuellement activées
- Les routes accessibles
- Les informations de configuration

Cela facilite le debug et la validation de la configuration.
