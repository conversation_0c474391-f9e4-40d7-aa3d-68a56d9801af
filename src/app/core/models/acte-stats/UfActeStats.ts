/**
 * Interface for UF statistics for a specific acte across periods
 */
export interface UfActeStats {
  ufId: string;
  ufCode: string;
  ufLibelle: string;
  p1Count: number;
  p1Frequency: number;
  p2Count: number;
  p2Frequency: number;
  p3Count: number;
  p3Frequency: number;
  totalCount: number;
  globalFrequency: number;
}

/**
 * Interface for the API Platform response containing UF statistics
 */
export interface UfActeStatsResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  totalItems: number;
  member: UfActeStats[];
  view?: {
    '@id': string;
    '@type': string;
    first?: string;
    last?: string;
    next?: string;
    previous?: string;
  };
}
