import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Interface pour les données de recherche
export interface SearchEntity {
  id: string;
  type: 'praticien' | 'pole' | 'service' | 'ufs';
  nom: string;
  prenom?: string;
  specialite?: string;
  matricule?: string;
  extra?: string;
  fullName?: string;
  keywords?: string[];
}

// Interface pour les résultats de recherche
export interface FuseSearchResult {
  item: SearchEntity;
  score?: number;
}

@Injectable({
  providedIn: 'root'
})
export class FuseSearchService {
  private apiUrl = `${environment.api_url}/api/search`;
  private searchData$ = new BehaviorSubject<SearchEntity[]>([]);
  private fuseInstance: any = null;
  private dataLoaded = false;

  constructor(private http: HttpClient) {
    this.loadSearchData();
  }

  /**
   * Charge toutes les données de recherche depuis l'API
   */
  private loadSearchData(): void {
    if (this.dataLoaded) {
      return;
    }

    // Votre API retourne un objet avec member[] contenant les entités
    this.http.get<any>(this.apiUrl).pipe(
      map(response => {
        // Extraire les entités du format API Platform
        return response.member || [];
      }),
      tap(data => {
        console.log('Données de recherche chargées:', data.length, 'entités');
        this.searchData$.next(data);
        this.initializeFuse(data);
        this.dataLoaded = true;
      }),
      catchError(error => {
        console.error('Erreur lors du chargement des données de recherche:', error);
        return of([]);
      })
    ).subscribe();
  }

  /**
   * Initialise Fuse.js avec les données
   */
  private async initializeFuse(data: SearchEntity[]): Promise<void> {
    try {
      // Import dynamique de Fuse.js
      const Fuse = (await import('fuse.js')).default;

      // Configuration de Fuse.js optimisée pour votre recherche
      const fuseOptions = {
        keys: [
          { name: 'nom', weight: 0.4 },
          { name: 'prenom', weight: 0.3 },
          { name: 'fullName', weight: 0.3 },
          { name: 'matricule', weight: 0.2 },
          { name: 'keywords', weight: 0.2 }
        ],
        threshold: 0.3, // Plus strict que 0.6 par défaut
        includeScore: true,
        includeMatches: true,
        minMatchCharLength: 2,
        shouldSort: true,
        findAllMatches: true,
        ignoreLocation: true // Ignore la position du match dans le texte
      };

      this.fuseInstance = new Fuse(data, fuseOptions);
      console.log('🚀 Fuse.js initialisé avec', data.length, 'entités');
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de Fuse.js:', error);
    }
  }

  /**
   * Effectue une recherche avec Fuse.js
   */
  search(query: string, limit: number = 10): Observable<{ results: SearchEntity[], total_results: number }> {
    if (!query || query.trim().length < 2) {
      return of({ results: [], total_results: 0 });
    }

    if (!this.fuseInstance) {
      console.warn('Fuse.js non initialisé, rechargement des données...');
      this.loadSearchData();
      return of({ results: [], total_results: 0 });
    }

    try {
      // Recherche avec Fuse.js
      const fuseResults: FuseSearchResult[] = this.fuseInstance.search(query);

      // Extraire les entités des résultats Fuse avec score
      const results = fuseResults
        .slice(0, limit)
        .map(result => {
          const entity = result.item;
          // Ajouter le score pour debug (optionnel)
          if (result.score !== undefined) {
            console.log(`${entity.fullName}: score ${result.score.toFixed(3)}`);
          }
          return entity;
        });

      console.log(`🔍 Recherche Fuse.js "${query}": ${fuseResults.length} résultats trouvés, ${results.length} retournés`);

      return of({
        results,
        total_results: fuseResults.length
      });
    } catch (error) {
      console.error('Erreur lors de la recherche Fuse.js:', error);
      return of({ results: [], total_results: 0 });
    }
  }

  /**
   * Recherche par type spécifique
   */
  searchByType(query: string, type: SearchEntity['type'], limit: number = 10): Observable<{ results: SearchEntity[], total_results: number }> {
    return this.search(query, 1000).pipe(
      map(response => {
        const filteredResults = response.results.filter(entity => entity.type === type);
        return {
          results: filteredResults.slice(0, limit),
          total_results: filteredResults.length
        };
      })
    );
  }

  /**
   * Obtient toutes les entités d'un type spécifique
   */
  getAllByType(type: SearchEntity['type']): Observable<SearchEntity[]> {
    return this.searchData$.pipe(
      map(data => data.filter(entity => entity.type === type))
    );
  }

  /**
   * Force le rechargement des données
   */
  reloadData(): void {
    this.dataLoaded = false;
    this.fuseInstance = null;
    this.loadSearchData();
  }

  /**
   * Vérifie si les données sont chargées
   */
  isDataLoaded(): boolean {
    return this.dataLoaded && this.fuseInstance !== null;
  }

  /**
   * Obtient le nombre total d'entités chargées
   */
  getTotalEntitiesCount(): number {
    return this.searchData$.value.length;
  }

  /**
   * Obtient les statistiques par type
   */
  getStatsByType(): { [key: string]: number } {
    const data = this.searchData$.value;
    const stats: { [key: string]: number } = {};

    data.forEach(entity => {
      stats[entity.type] = (stats[entity.type] || 0) + 1;
    });

    return stats;
  }
}
