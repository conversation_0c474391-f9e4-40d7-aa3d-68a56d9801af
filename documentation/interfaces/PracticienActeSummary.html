<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content interface">
                   <div class="content-data">













<ol class="breadcrumb">
  <li class="breadcrumb-item">Interfaces</li>
  <li class="breadcrumb-item"
  >
  PracticienActeSummary</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" 
                class="nav-link"
                class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" 
                class="nav-link"
                
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info">
        <p class="comment">
            <h3>File</h3>
        </p>
        <p class="comment">
            <code>src/app/organization/components/information-panel/information-panel.component.ts</code>
        </p>




        <section data-compodoc="block-index">
            <h3 id="index">Index</h3>
            <table class="table table-sm table-bordered index-table">
                <tbody>
                    <tr>
                        <td class="col-md-4">
                            <h6><b>Properties</b></h6>
                        </td>
                    </tr>
                    <tr>
                        <td class="col-md-4">
                            <ul class="index-list">
                                <li>
                                        <a href="#etp" 
>
                                            etp
                                        </a>
                                </li>
                                <li>
                                        <a href="#nbActesCCAM" 
>
                                            nbActesCCAM
                                        </a>
                                </li>
                                <li>
                                        <a href="#nbActesNGAP" 
>
                                            nbActesNGAP
                                        </a>
                                </li>
                                <li>
                                        <a href="#nomPatronymique" 
>
                                            nomPatronymique
                                        </a>
                                </li>
                                <li>
                                        <a href="#nomUsuel" 
>
                                            nomUsuel
                                        </a>
                                </li>
                                <li>
                                        <a href="#partPraticienCCAM" 
>
                                            partPraticienCCAM
                                        </a>
                                </li>
                                <li>
                                        <a href="#partPraticienNGAP" 
>
                                            partPraticienNGAP
                                        </a>
                                </li>
                                <li>
                                        <a href="#prenom" 
>
                                            prenom
                                        </a>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>



            <section data-compodoc="block-properties">
                <h3 id="inputs">Properties</h3>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="etp"></a>
                                        <span class="name "><b>etp</b>
                                            <a href="#etp">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>etp:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="nbActesCCAM"></a>
                                        <span class="name "><b>nbActesCCAM</b>
                                            <a href="#nbActesCCAM">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>nbActesCCAM:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="nbActesNGAP"></a>
                                        <span class="name "><b>nbActesNGAP</b>
                                            <a href="#nbActesNGAP">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>nbActesNGAP:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/number" target="_blank" >number</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="nomPatronymique"></a>
                                        <span class="name "><b>nomPatronymique</b>
                                            <a href="#nomPatronymique">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>nomPatronymique:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="nomUsuel"></a>
                                        <span class="name "><b>nomUsuel</b>
                                            <a href="#nomUsuel">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>nomUsuel:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="partPraticienCCAM"></a>
                                        <span class="name "><b>partPraticienCCAM</b>
                                            <a href="#partPraticienCCAM">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>partPraticienCCAM:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="partPraticienNGAP"></a>
                                        <span class="name "><b>partPraticienNGAP</b>
                                            <a href="#partPraticienNGAP">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>partPraticienNGAP:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
                    <table class="table table-sm table-bordered">
                        <tbody>
                                <tr>
                                    <td class="col-md-4">
                                        <a name="prenom"></a>
                                        <span class="name "><b>prenom</b>
                                            <a href="#prenom">
                                                <span class="icon ion-ios-link"></span>
                                            </a>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-md-4">
                                        <code>prenom:         <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>
</code>
                                    </td>
                                </tr>


                                    <tr>
                                        <td class="col-md-4">
                                            <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                                        </td>
                                    </tr>





                        </tbody>
                    </table>
            </section>
    </div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, Input, OnInit} from &#x27;@angular/core&#x27;;
import {ActeCCAM} from &quot;../../../core/models/acte/acte-ccam.model&quot;;
import {ActeNGAP} from &quot;../../../core/models/acte/acte-ngap.model&quot;;
import {RealisationActe} from &quot;../../../core/models/acte/realisation-acte.model&quot;;
import {Praticien} from &quot;../../../core/models/acte/praticien.model&quot;;
import {PraticienService} from &quot;../../../core/services/auth/praticien.service&quot;;
import {ActeService} from &quot;../../../core/services/acte/acte.service&quot;;
import {FilterByPraticienPipe} from &quot;../../../core/pipes/filter-by-praticien.pipe&quot;;
import {DatePipe, NgForOf, NgIf, TitleCasePipe} from &quot;@angular/common&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {InputTextModule} from &quot;primeng/inputtext&quot;;
import {IconFieldModule} from &quot;primeng/iconfield&quot;;
import {InputIconModule} from &quot;primeng/inputicon&quot;;
import {BadgeModule} from &quot;primeng/badge&quot;;
import {CardModule} from &quot;primeng/card&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {BreadcrumbComponent} from &quot;../../../pages/breadcrumb/breadcrumb.component&quot;;
import {GradeETP} from &quot;../../../core/models/GradeETP&quot;;
import {Button} from &quot;primeng/button&quot;;
import {OverlayPanelModule} from &quot;primeng/overlaypanel&quot;;
import {ActeLABO} from &quot;../../../core/models/acte/acte-labo.model&quot;;
import {combineLatest} from &quot;rxjs&quot;;
import {DropdownModule} from &quot;primeng/dropdown&quot;;
import {FormsModule} from &quot;@angular/forms&quot;;
import {FloatLabelModule} from &quot;primeng/floatlabel&quot;;
import {ServiceHospitalier} from &quot;../../../core/models/organization/ServiceHospitalier.model&quot;;
import {ServiceHospitalierService} from &quot;../../../core/services/organization/ServiceHospitalierService&quot;;
import {UFService} from &quot;../../../core/services/organization/UFService&quot;;


interface ActeSummary {
  code: string;
  description: string;
  totalAnneeNMoins1: number;
  totalAnneeN: number;
}


interface PracticienActeSummary {
  nomUsuel: string;
  nomPatronymique: string;
  prenom: string;
  etp: string;
  nbActesCCAM: number;
  partPraticienCCAM: string;
  nbActesNGAP: number;
  partPraticienNGAP: string;
}

@Component({
  selector: &#x27;app-information-panel&#x27;,
  standalone: true,
  imports: [
    FilterByPraticienPipe,
    NgForOf,
    TitleCasePipe,
    NgIf,
    TableModule,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
    BadgeModule,
    CardModule,
    ChartModule,
    BreadcrumbComponent,
    Button,
    OverlayPanelModule,
    DatePipe,
    DropdownModule,
    FormsModule,
    FloatLabelModule
  ],
  templateUrl: &#x27;./information-panel.component.html&#x27;,
  styleUrl: &#x27;./information-panel.component.scss&#x27;
})
export class InformationPanelComponent implements OnInit {
  @Input() elementId!: string; // ID de l&#x27;élément à afficher (poleId, departmentId, serviceId, ou ufId)
  @Input() elementType!: &#x27;pole&#x27; | &#x27;department&#x27; | &#x27;service&#x27; | &#x27;uf&#x27;; // Type de l&#x27;élément


  actesCCAM: ActeCCAM[] &#x3D; [];
  actesLABO: ActeLABO[] &#x3D; [];
  actesNGAP: ActeNGAP[] &#x3D; [];
  realisations: RealisationActe[] &#x3D; [];
  praticiens: Praticien[] &#x3D; [];




  totalCCAM: number &#x3D; 0;
  totalNGAP: number &#x3D; 0;
  totalLABO: number &#x3D; 0;

  filteredActesCCAM:  ActeCCAM[] &#x3D; [];
  filteredActesNGAP: ActeNGAP[] &#x3D; [];

  filteredActesCCAMSummary: ActeSummary[] &#x3D; [];  //
  filteredActesNGAPSummary: ActeSummary[] &#x3D; [];  //
  filteredActesLABOSummary: ActeSummary[] &#x3D; [];  //
  //
  filterePractitionerActeSummary: PracticienActeSummary[] &#x3D; [];
  externalPractitionerSummary: PracticienActeSummary[] &#x3D; [];


  totalEtp: number &#x3D; 0;
  totalPartCCAM: string &#x3D; &#x27;0%&#x27;;
  totalPartNGAP: string &#x3D; &#x27;0%&#x27;;

  // Simulating a chosen period for now
    startYear &#x3D; 2023; // Replace with dynamically chosen start year
    endYear &#x3D; 2024;   // Replace with dynamically chosen end year

  praticiensListFilter!: any;

  // pour overview
  ccamData: any;
  ngapData: any;
  laboData: any;
  chartOptions: any;
  //

  //
  grades: GradeETP[] &#x3D; [
    {
      specialite: &#x27;14,883&#x27;,
      medecine: &#x27;4,250&#x27;,
      juniorMedecine: &#x27;3,641&#x27;,
      specMedecineGen: &#x27;2,395&#x27;,
      ffiMed: &#x27;1,000&#x27;
    }
  ];
  //********************************************************************************
  statutOptions &#x3D; [
    { label: &#x27;Tout&#x27;, value: null },
    { label: &#x27;Permanent&#x27;, value: &#x27;Permanent&#x27; },
    { label: &#x27;Temporaire&#x27;, value: &#x27;Temporaire&#x27; },
    { label: &#x27;Permanent + Temporaire&#x27;, value: &#x27;PermanentTemporaire&#x27;},
    { label: &#x27;Junior&#x27;, value: &#x27;Junior&#x27; }
  ];
  selectedStatut: string | null &#x3D; null;

  //*****************************************************************************************



  constructor(
    private acteService: ActeService,
    private realisationService: ActeService,
    private praticienService: PraticienService,
    private serviceHospitalierService: ServiceHospitalierService,
    private  ufService: UFService
  ) {}

  ngOnInit(): void {
    this.acteService.actesCCAM$.subscribe((data) &#x3D;&gt; {
      this.actesCCAM &#x3D; data;
      this.filterActs();
    });


    this.acteService.actesNGAP$.subscribe((data) &#x3D;&gt; {
      this.actesNGAP &#x3D; data;
      this.filterActs();
    });

    this.acteService.actesLABO$.subscribe((data) &#x3D;&gt; {
      this.actesLABO &#x3D; data;
      this.filterActs();
    });

    this.realisationService.realisations$.subscribe((data) &#x3D;&gt; {
      this.realisations &#x3D; this.filterRealisationsByElement(data);
      //
      // this.filteredActesCCAMSummary &#x3D; this.generateCCAMSummary(data,this.startYear,this.endYear);
      this.filteredActesCCAMSummary &#x3D; this.generateActeSummary(this.realisations,this.startYear,this.endYear,&#x27;CCAM&#x27;);
      this.filteredActesNGAPSummary &#x3D; this.generateActeSummary(this.realisations,this.startYear,this.endYear,&#x27;NGAP&#x27;);
      this.filteredActesLABOSummary &#x3D; this.generateActeSummary(this.realisations,this.startYear,this.endYear,&#x27;LABO&#x27;);
      //

      console.log(&#x27;le tableau du template&#x27;+this.filterePractitionerActeSummary)
      console.table(this.filterePractitionerActeSummary)
      // Sum the counts for total CCAM and total NGAP
      this.totalCCAM &#x3D; this.realisations
        .filter((r) &#x3D;&gt; r.typeActe.toUpperCase() &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
        .reduce((total, r) &#x3D;&gt; total + (r.count || 1), 0);

      this.totalNGAP &#x3D; this.realisations
        .filter((r) &#x3D;&gt; r.typeActe.toUpperCase() &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
        .reduce((total, r) &#x3D;&gt; total + (r.count || 1), 0);

      this.totalLABO &#x3D; this.realisations
        .filter((r) &#x3D;&gt; r.typeActe.toUpperCase() &#x3D;&#x3D;&#x3D; &#x27;LABO&#x27;)
        .reduce((total, r) &#x3D;&gt; total + (r.count || 1), 0);

      this.filterActs(); // repartition des actes [CCAM,NGAP] par pratiicien
    });

    this.quickDisplayOverview();
    //


    combineLatest([
      this.realisationService.realisations$,
      this.praticienService.praticiens$,
      this.serviceHospitalierService.servicesHospitaliers$,
    ]).subscribe(
      ([realisations, praticiens]) &#x3D;&gt; {
        this.realisations &#x3D; this.filterRealisationsByElement(realisations);
        this.praticiens &#x3D; praticiens;

        // Debug pour vérifier les données avant application du filtre
        console.log(&#x27;Praticiens chargés:&#x27;, this.praticiens);
        console.log(&#x27;Réalisations chargées:&#x27;, this.realisations);

        // Appliquer le filtre de statut
        this.applyStatutFilter();

        ///****************************************
        // Générer le tableau des praticiens externes
        this.generateExternalPractitionerSummary(this.elementId);
        ///****************************************
        this.generateUnassignedPractitionersSummary(this.elementId);


      },
      error &#x3D;&gt; {
        console.error(&quot;Erreur lors du chargement des praticiens ou des réalisations :&quot;, error);
      }
    );


  }

  private filterActs(): void {
    const realizedCCAMIds &#x3D; this.realisations
      .filter((r) &#x3D;&gt; r.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
      .map((r) &#x3D;&gt; r.acteId);
    const realizedNGAPIds &#x3D; this.realisations
      .filter((r) &#x3D;&gt; r.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
      .map((r) &#x3D;&gt; r.acteId);

    this.filteredActesCCAM &#x3D; this.actesCCAM.filter((acte) &#x3D;&gt;
      realizedCCAMIds.includes(acte.id)
    );
    this.filteredActesNGAP &#x3D; this.actesNGAP.filter((acte) &#x3D;&gt;
      realizedNGAPIds.includes(acte.id)
    );
  }

  // Filtrer les réalisations en fonction de l&#x27;élément et de son type
  private filterRealisationsByElement(realisations: RealisationActe[]): RealisationActe[] {
    switch (this.elementType) {
      case &#x27;pole&#x27;:
        return realisations.filter(r &#x3D;&gt; r.poleId &#x3D;&#x3D;&#x3D; this.elementId);
      case &#x27;department&#x27;:
        return realisations.filter(r &#x3D;&gt; r.departmentId &#x3D;&#x3D;&#x3D; this.elementId);
      case &#x27;service&#x27;:
        return realisations.filter(r &#x3D;&gt; r.serviceId &#x3D;&#x3D;&#x3D; this.elementId);
      case &#x27;uf&#x27;:
        return realisations.filter(r &#x3D;&gt; r.ufId &#x3D;&#x3D;&#x3D; this.elementId);
      default:
        return [];
    }
  }


  // Generate a summary for specified acte type data filtered by the selected years
   generateActeSummary(realisations: RealisationActe[], startYear: number, endYear: number, typeActe: string): ActeSummary[] {
    const summaryMap &#x3D; new Map&lt;string, ActeSummary&gt;();

    // Filter realizations by specified acte type and map them to acte data
    realisations
      .filter((realisation) &#x3D;&gt; realisation.typeActe &#x3D;&#x3D;&#x3D; typeActe)
      .forEach((realisation) &#x3D;&gt; {
        const year &#x3D; new Date(realisation.dateRealisation).getFullYear();
        // const acte &#x3D; (typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27; ? this.actesCCAM : typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27; ? this.actesNGAP : this.actesLABO).find((a) &#x3D;&gt; a.id &#x3D;&#x3D;&#x3D; realisation.acteId);

        const acte &#x3D; (
          typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27; ? this.actesCCAM :
            typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27; ? this.actesNGAP :
              typeActe &#x3D;&#x3D;&#x3D; &#x27;LABO&#x27; ? this.actesLABO :
                []
        ).find((a) &#x3D;&gt; a.id &#x3D;&#x3D;&#x3D; realisation.acteId);


        if (acte) {
          if (!summaryMap.has(acte.id)) {
            summaryMap.set(acte.id, {
              code: acte.code,
              description: acte.description,
              totalAnneeNMoins1: 0,
              totalAnneeN: 0,
            });
          }

          const summaryEntry &#x3D; summaryMap.get(acte.id)!;
          if (year &#x3D;&#x3D;&#x3D; startYear) {
            summaryEntry.totalAnneeNMoins1 +&#x3D; realisation.count || 1;
          } else if (year &#x3D;&#x3D;&#x3D; endYear) {
            summaryEntry.totalAnneeN +&#x3D; realisation.count || 1;
          }
        } else {
          console.error(&#x60;No matching acte found for acteId: ${realisation.acteId} in ${typeActe}&#x60;);
        }
      });

    // Log the summary map to verify the result
    console.log(&#x60;Generated Summary Map for ${typeActe}:&#x60;, Array.from(summaryMap.values()));

    return Array.from(summaryMap.values());
  }


  //**************************************************** Tableau Repartition des actes par Praticien***********************************************************************
  private generatePractitionerActeSummary(
    realisations: RealisationActe[],
    praticiens: Praticien[]
  ): PracticienActeSummary[] {
    console.log(&quot;Generating Practitioner Acte Summary...&quot;);

    // / Réinitialisation des totaux en fonction des filter par status
    this.totalPartCCAM &#x3D; &quot;0&quot;;
    this.totalPartNGAP &#x3D; &quot;0&quot;;

    // Réinitialisation des totaux
    let totalPartCCAMTemp &#x3D; 0;
    let totalPartNGAPTemp &#x3D; 0;

    //
    this.totalEtp &#x3D; praticiens.reduce((sum, praticien) &#x3D;&gt; sum + parseFloat(praticien.etp), 0);

    return praticiens.map((praticien) &#x3D;&gt; {
      const actsByPractitioner &#x3D; realisations.filter((act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id);
      let nbActesCCAM &#x3D; actsByPractitioner
        .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
        .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);
      let nbActesNGAP &#x3D; actsByPractitioner
        .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
        .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

      // Calculate part of practitioner in percentage terms
      const partPraticienCCAM &#x3D; this.totalCCAM &gt; 0 ? (nbActesCCAM / this.totalCCAM) * 100 : 0;
      const partPraticienNGAP &#x3D; this.totalNGAP &gt; 0 ? (nbActesNGAP / this.totalNGAP) * 100 : 0;


      // Mise à jour des totaux cumulés
      totalPartCCAMTemp +&#x3D; partPraticienCCAM;
      totalPartNGAPTemp +&#x3D; partPraticienNGAP;

      this.totalPartCCAM &#x3D; Math.min(100, totalPartCCAMTemp).toFixed(2) + &#x27;%&#x27;;
      this.totalPartNGAP &#x3D; Math.min(100, totalPartNGAPTemp).toFixed(2) + &#x27;%&#x27;;


      return {
        nomUsuel: praticien.nom,
        nomPatronymique: praticien.nomPatronymique,
        prenom: praticien.prenom,
        praticienDateDepart: praticien.dateDepart,
        praticienDateArrivee: praticien.dateArrivee,
        etp: praticien.etp,
        nbActesCCAM,
        partPraticienCCAM:  Math.min(100, partPraticienCCAM).toFixed(2) + &#x27;%&#x27;,
        nbActesNGAP,
        partPraticienNGAP:  Math.min(100, partPraticienNGAP).toFixed(2) + &#x27;%&#x27;
      };
    });
  }


  // Méthode pour appliquer le filtre de statut et générer le résumé
  applyStatutFilter() {
    let filteredPraticiens;


    if (this.selectedStatut &#x3D;&#x3D;&#x3D; &#x27;PermanentTemporaire&#x27;) {
      // Filtrer par Permanent et Temporaire
      filteredPraticiens &#x3D; this.praticiens.filter(
        praticien &#x3D;&gt; praticien.statut &#x3D;&#x3D;&#x3D; &#x27;Permanent&#x27; || praticien.statut &#x3D;&#x3D;&#x3D; &#x27;Temporaire&#x27;
      );
    } else if (this.selectedStatut) {
      // Filtrer par le statut sélectionné
      filteredPraticiens &#x3D; this.praticiens.filter(
        praticien &#x3D;&gt; praticien.statut &#x3D;&#x3D;&#x3D; this.selectedStatut
      );

    } else {
      // Pas de filtre, afficher tous les praticiens
      filteredPraticiens &#x3D; this.praticiens;
    }

    // Filtrer les réalisations correspondantes
    const filteredRealisations &#x3D; this.realisations.filter(realisation &#x3D;&gt;
      filteredPraticiens.some(praticien &#x3D;&gt; praticien.id &#x3D;&#x3D;&#x3D; realisation.praticienId)
    );

    this.totalCCAM &#x3D; filteredRealisations
      .filter(act &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
      .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

    this.totalNGAP &#x3D; filteredRealisations
      .filter(act &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
      .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);


    this.filterePractitionerActeSummary &#x3D; this.generatePractitionerActeSummary(filteredRealisations, filteredPraticiens);
  }

  // Méthode appelée lors du changement de statut dans le filtre
  onStatutChange(event: any) {
    console.log(&#x27;le event value &#x27;+event.value)
    this.selectedStatut &#x3D; event.value; // Mise à jour du statut sélectionné avec la valeur de l&#x27;événement
    this.applyStatutFilter(); // Applique le filtre avec le nouveau statut
  }

  //*********************************************************************************************************************************


  //****************************** Tableau (Personnes qui ont fait des actes dans ce service sans y être affectées)  *******************************************************************************************

   totalEtpExternal : any ;
   totalCCAMExternal : any;
   totalNGAPExternal : any;
   totalPartCCAMExternal : any;
   totalPartNGAPExternal :any;

  generateExternalPractitionerSummary(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) &#x3D;&gt; {
        if (!service) {
          console.error(&#x60;Service avec l&#x27;ID ${serviceId} introuvable&#x60;);
          return;
        }

        const ufsIdsInService &#x3D; service.ufs.map((uf) &#x3D;&gt; uf.id);

        // Filtrer les réalisations associées au service
        const realisationsInService &#x3D; this.realisations.filter(
          (realisation) &#x3D;&gt;
            realisation.serviceId &#x3D;&#x3D;&#x3D; serviceId &amp;&amp;
            !ufsIdsInService.includes(realisation.ufId)
        );

        console.log(&quot;Réalisations dans le service :&quot;, realisationsInService);

        // Trouver les praticiens ayant une &#x60;ufId&#x60; mais qui ne font pas partie du service
        const externalPractitionersWithRealisations &#x3D; this.praticiens.filter(
          (praticien) &#x3D;&gt;
            praticien.ufId &amp;&amp; // Inclure seulement ceux qui ont une ufId
            !ufsIdsInService.includes(praticien.ufId) &amp;&amp; // Exclure ceux qui sont dans le service
            realisationsInService.some(
              (realisation) &#x3D;&gt; realisation.praticienId &#x3D;&#x3D;&#x3D; praticien.id
            )
        );

        console.log(
          &quot;Praticiens externes ayant des réalisations :&quot;,
          externalPractitionersWithRealisations
        );

        // Calculer les totaux globaux à partir des réalisations
        const totalCCAM &#x3D; realisationsInService
          .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;CCAM&quot;)
          .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

        const totalNGAP &#x3D; realisationsInService
          .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;NGAP&quot;)
          .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

        const totalEtp &#x3D; externalPractitionersWithRealisations.reduce(
          (sum, praticien) &#x3D;&gt; sum + parseFloat(praticien.etp || &quot;0&quot;),
          0
        );

        console.log(&#x60;Total CCAM: ${totalCCAM}, Total NGAP: ${totalNGAP}, Total ETP: ${totalEtp}&#x60;);

        // Générer le tableau des praticiens externes
        this.externalPractitionerSummary &#x3D; externalPractitionersWithRealisations.map(
          (praticien) &#x3D;&gt; {
            const actsByPractitioner &#x3D; realisationsInService.filter(
              (act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id
            );

            const nbActesCCAM &#x3D; actsByPractitioner
              .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;CCAM&quot;)
              .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

            const nbActesNGAP &#x3D; actsByPractitioner
              .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;NGAP&quot;)
              .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

            // Calculer les parts pour CCAM et NGAP
            const partPraticienCCAM &#x3D;
              totalCCAM &gt; 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;

            const partPraticienNGAP &#x3D;
              totalNGAP &gt; 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

            return {
              nomUsuel: praticien.nom,
              nomPatronymique: praticien.nomPatronymique,
              prenom: praticien.prenom,
              praticienDateDepart: praticien.dateDepart,
              praticienDateArrivee: praticien.dateArrivee,
              etp: praticien.etp,
              nbActesCCAM,
              partPraticienCCAM: partPraticienCCAM.toFixed(2) + &quot;%&quot;,
              nbActesNGAP,
              partPraticienNGAP: partPraticienNGAP.toFixed(2) + &quot;%&quot;,
            };
          }
        );

        // Calculer les totaux cumulés pour CCAM et NGAP
        const recalculatedTotalCCAM &#x3D; this.externalPractitionerSummary.reduce(
          (sum, summary) &#x3D;&gt; sum + summary.nbActesCCAM,
          0
        );

        const recalculatedTotalNGAP &#x3D; this.externalPractitionerSummary.reduce(
          (sum, summary) &#x3D;&gt; sum + summary.nbActesNGAP,
          0
        );

        // Corriger les pourcentages pour totalPartCCAMExternal et totalPartNGAPExternal
        const totalPartCCAM &#x3D; totalCCAM &gt; 0 ? 100 : 0;
        const totalPartNGAP &#x3D; totalNGAP &gt; 0 ? 100 : 0;

        // Mettre à jour les totaux pour affichage
        this.totalEtpExternal &#x3D; totalEtp.toFixed(1);
        this.totalCCAMExternal &#x3D; recalculatedTotalCCAM;
        this.totalNGAPExternal &#x3D; recalculatedTotalNGAP;
        this.totalPartCCAMExternal &#x3D; totalPartCCAM.toFixed(2) + &quot;%&quot;;
        this.totalPartNGAPExternal &#x3D; totalPartNGAP.toFixed(2) + &quot;%&quot;;

        console.log(&quot;Tableau des praticiens externes :&quot;, this.externalPractitionerSummary);
      },
      (error) &#x3D;&gt; {
        console.error(&quot;Erreur lors de la récupération du service :&quot;, error);
      }
    );
  }


//*************************** Personnes qui ont fait des actes dans ce service mais non affectées par la DAM **********************************************************************************************

  nonAffectesDAMSummary: any[] &#x3D; [];
  totalCCAMNonAffectes: number &#x3D; 0;
  totalNGAPNonAffectes: number &#x3D; 0;
  totalPartCCAMNonAffectes: string &#x3D; &quot;0%&quot;;
  totalPartNGAPNonAffectes: string &#x3D; &quot;0%&quot;;

  generateUnassignedPractitionersSummary(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) &#x3D;&gt; {
        if (!service) {
          console.error(&#x60;Service avec l&#x27;ID ${serviceId} introuvable&#x60;);
          return;
        }

        const ufsIdsInService &#x3D; service.ufs.map((uf) &#x3D;&gt; uf.id);

        // Filtrer les réalisations associées au service
        const realisationsInService &#x3D; this.realisations.filter(
          (realisation) &#x3D;&gt;
            realisation.serviceId &#x3D;&#x3D;&#x3D; serviceId
        );

        console.log(&quot;Réalisations dans le service :&quot;, realisationsInService);

        // Trouver les praticiens non affectés par la DAM ayant des réalisations
        const unassignedPractitionersWithRealisations &#x3D; this.praticiens.filter(
          (praticien) &#x3D;&gt;
            !praticien.ufId &amp;&amp;
            realisationsInService.some(
              (realisation) &#x3D;&gt; realisation.praticienId &#x3D;&#x3D;&#x3D; praticien.id
            )
        );

        console.log(
          &quot;Personnes non affectées par la DAM ayant réalisé des actes :&quot;,
          unassignedPractitionersWithRealisations
        );

        // Calculer uniquement les totaux pour les praticiens non affectés
        let totalCCAM &#x3D; 0;
        let totalNGAP &#x3D; 0;

        unassignedPractitionersWithRealisations.forEach((praticien) &#x3D;&gt; {
          const actsByPractitioner &#x3D; realisationsInService.filter(
            (act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id
          );

          totalCCAM +&#x3D; actsByPractitioner
            .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;CCAM&quot;)
            .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

          totalNGAP +&#x3D; actsByPractitioner
            .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;NGAP&quot;)
            .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);
        });

        console.log(&#x60;DAM : Total CCAM pour les praticiens non affectés : ${totalCCAM}&#x60;);
        console.log(&#x60;DAM : Total NGAP pour les praticiens non affectés : ${totalNGAP}&#x60;);

        // Calculer les parts cumulées
        let cumulativePartCCAM &#x3D; 0;
        let cumulativePartNGAP &#x3D; 0;

        // Générer le tableau des praticiens non affectés
        this.nonAffectesDAMSummary &#x3D; unassignedPractitionersWithRealisations.map(
          (praticien) &#x3D;&gt; {
            const actsByPractitioner &#x3D; realisationsInService.filter(
              (act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id
            );

            const nbActesCCAM &#x3D; actsByPractitioner
              .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;CCAM&quot;)
              .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

            const nbActesNGAP &#x3D; actsByPractitioner
              .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &quot;NGAP&quot;)
              .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

            // Calculer la part du praticien pour chaque type d&#x27;acte
            const partPraticienCCAM &#x3D; totalCCAM &gt; 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;
            const partPraticienNGAP &#x3D; totalNGAP &gt; 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

            // Ajouter aux parts cumulées
            cumulativePartCCAM +&#x3D; partPraticienCCAM;
            cumulativePartNGAP +&#x3D; partPraticienNGAP;

            return {
              nomUsuel: praticien.nom,
              nomPatronymique: praticien.nomPatronymique,
              prenom: praticien.prenom,
              specialite: praticien.specialite,
              nbActesCCAM,
              partPraticienCCAM: partPraticienCCAM.toFixed(2) + &quot;%&quot;,
              nbActesNGAP,
              partPraticienNGAP: partPraticienNGAP.toFixed(2) + &quot;%&quot;,
            };
          }
        );

        // Mettre à jour les totaux globaux
        this.totalCCAMNonAffectes &#x3D; totalCCAM;
        this.totalNGAPNonAffectes &#x3D; totalNGAP;
        this.totalPartCCAMNonAffectes &#x3D; Math.min(cumulativePartCCAM, 100).toFixed(2) + &quot;%&quot;;
        this.totalPartNGAPNonAffectes &#x3D; Math.min(cumulativePartNGAP, 100).toFixed(2) + &quot;%&quot;;

        console.log(
          &quot;Tableau des praticiens non affectés par la DAM (corrigé) :&quot;,
          this.nonAffectesDAMSummary
        );

        console.log(
          &#x60;Totaux corrigés : CCAM &#x3D; ${this.totalCCAMNonAffectes}, NGAP &#x3D; ${this.totalNGAPNonAffectes}, Part CCAM &#x3D; ${this.totalPartCCAMNonAffectes}, Part NGAP &#x3D; ${this.totalPartNGAPNonAffectes}&#x60;
        );
      },
      (error) &#x3D;&gt; {
        console.error(&quot;Erreur lors de la récupération du service :&quot;, error);
      }
    );
  }



  //*********************************************************************************************************************************


  // Calculate the total for a specific year and type of acte
  getTotalForYearByTypeActe(year: number, typeActe: string): number {
    const summary &#x3D; typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;
      ? this.filteredActesCCAMSummary
      : typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;
        ? this.filteredActesNGAPSummary
        : this.filteredActesLABOSummary;

    return summary.reduce((sum, acte) &#x3D;&gt; {
      return sum + (year &#x3D;&#x3D;&#x3D; 2023 ? acte.totalAnneeNMoins1 : acte.totalAnneeN);
    }, 0);
  }

  getPraticiensParActe(acteId: string, typeActe: string): Praticien[] {
    const realisationsFiltrees &#x3D; this.realisations.filter(
      r &#x3D;&gt; r.acteId &#x3D;&#x3D;&#x3D; acteId &amp;&amp; r.typeActe &#x3D;&#x3D;&#x3D; typeActe
    );
    return realisationsFiltrees.map(r &#x3D;&gt; this.praticiens.find(p &#x3D;&gt; p.id &#x3D;&#x3D;&#x3D; r.praticienId)!).filter(Boolean);
  }

  getCount(acteId: string, praticienId: string, typeActe: string): number {
    const realizations &#x3D; this.realisations.filter(
      (r) &#x3D;&gt; r.acteId &#x3D;&#x3D;&#x3D; acteId &amp;&amp; r.praticienId &#x3D;&#x3D;&#x3D; praticienId &amp;&amp; r.typeActe &#x3D;&#x3D;&#x3D; typeActe
    );
    return realizations.reduce((sum, r) &#x3D;&gt; sum + (r.count || 1), 0);
  }

  getTotalActeCount(acteId: string): number {
    const realizationsForActe &#x3D; this.realisations.filter(r &#x3D;&gt; r.acteId &#x3D;&#x3D;&#x3D; acteId);
    return realizationsForActe.reduce((total, realization) &#x3D;&gt; total + (realization.count || 1), 0);
  }


  onInput(event: Event): string {
    console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }
//
  quickDisplayOverview(): void {
    // Example data for each chart
    this.ccamData &#x3D; {
      labels: [&#x27;Jan&#x27;, &#x27;Feb&#x27;, &#x27;Mar&#x27;, &#x27;Apr&#x27;, &#x27;May&#x27;],
      datasets: [
        {
          label: &#x27;CCAM&#x27;,
          data: [0.8, 0.7, 0.6, 0.9, 0.8],
          fill: false,
          borderColor: &#x27;#ff5252&#x27;,
          tension: 0.4,
          borderWidth: 2,      // Augmente l&#x27;épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    this.ngapData &#x3D; {
      labels: [&#x27;Jan&#x27;, &#x27;Feb&#x27;, &#x27;Mar&#x27;, &#x27;Apr&#x27;, &#x27;May&#x27;],
      datasets: [
        {
          label: &#x27;NGAP&#x27;,
          data: [300, 320, 310, 330, 306],
          fill: false,
          borderColor: &#x27;#4caf50&#x27;,
          tension: 0.4,
          borderWidth: 2,      // Augmente l&#x27;épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    this.laboData &#x3D; {
      labels: [&#x27;Jan&#x27;, &#x27;Feb&#x27;, &#x27;Mar&#x27;, &#x27;Apr&#x27;, &#x27;May&#x27;],
      datasets: [
        {
          label: &#x27;LABO&#x27;,
          data: [1600, 1620, 1580, 1650, 1600],
          fill: false,
          borderColor: &#x27;#00bcd4&#x27;,
          tension: 0.4,
          borderWidth: 2,      // Augmente l&#x27;épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    // Options for the chart
    this.chartOptions &#x3D; {
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: { display: false },
        y: { display: false }
      }
    };
  }

  viewPratitionnerDetails(acte: any) {
    alert(&#x60;Voir les détails pour l&#x27;UF : ${acte.nomUsuel}&#x60;);
  }

  viewActeDetails(acte: any) {
    alert(&#x60;Voir les détails de l&#x27;acte : ${acte.code}&#x60;);
  }

  /****************************************************** DANS LE CAS OU LE elementType EST UNE UF **************/

  // REFACTORE  //@TODO  FAIRE EN SORTE QUE LA FONCTION SADAPTE AU ELEMENT TYPE
  generateExternalPractitionerSummaryNEW(elementId: string): void {
    switch (this.elementType) {
      case &#x27;service&#x27;:
        this.generateExternalPractitionerSummaryForService(this.elementId);
        break;
      case &#x27;uf&#x27;:
        this.generateExternalPractitionerSummaryForUf(this.elementId);
        break;
      default:
        console.error(&#x60;Type d&#x27;élément non supporté : ${this.elementType}&#x60;);
    }
  }

  private generateExternalPractitionerSummaryForService(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) &#x3D;&gt; {
        if (!service) {
          console.error(&#x60;Service avec l&#x27;ID ${serviceId} introuvable&#x60;);
          return;
        }

        const ufsIdsInService &#x3D; service.ufs.map((uf) &#x3D;&gt; uf.id);

        this.handleExternalPractitionerSummary(serviceId, ufsIdsInService, true);
      },
      (error) &#x3D;&gt; {
        console.error(&quot;Erreur lors de la récupération du service :&quot;, error);
      }
    );
  }

  private generateExternalPractitionerSummaryForUf(ufId: string): void {
    this.ufService.getUFById(ufId).subscribe(
      (uf) &#x3D;&gt; {
        if (!uf) {
          console.error(&#x60;UF avec l&#x27;ID ${ufId} introuvable&#x60;);
          return;
        }

        this.handleExternalPractitionerSummary(ufId, [ufId], false);
      },
      (error) &#x3D;&gt; {
        console.error(&quot;Erreur lors de la récupération de l&#x27;UF :&quot;, error);
      }
    );
  }
  private handleExternalPractitionerSummary(
    elementId: string,
    ufsIds: string[],
    isService: boolean
  ): void {
    // Filtrer les réalisations associées à l&#x27;élément
    const realisationsInElement &#x3D; this.realisations.filter((realisation) &#x3D;&gt;
      isService
        ? realisation.serviceId &#x3D;&#x3D;&#x3D; elementId &amp;&amp; !ufsIds.includes(realisation.ufId)
        : realisation.ufId &#x3D;&#x3D;&#x3D; elementId
    );

    console.log(
      &#x60;Réalisations dans le ${isService ? &#x27;service&#x27; : &#x27;UF&#x27;} :&#x60;,
      realisationsInElement
    );

    // Trouver les praticiens externes
    const externalPractitionersWithRealisations &#x3D; this.praticiens.filter(
      (praticien) &#x3D;&gt;
        praticien.ufId &amp;&amp;
        !ufsIds.includes(praticien.ufId) &amp;&amp;
        realisationsInElement.some(
          (realisation) &#x3D;&gt; realisation.praticienId &#x3D;&#x3D;&#x3D; praticien.id
        )
    );

    console.log(
      &#x60;Praticiens externes ayant des réalisations :&#x60;,
      externalPractitionersWithRealisations
    );

    // Calculer les totaux globaux
    const totalCCAM &#x3D; realisationsInElement
      .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27; &amp;&amp; act.count &gt; 0)
      .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

    const totalNGAP &#x3D; realisationsInElement
      .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
      .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

    const totalEtp &#x3D; externalPractitionersWithRealisations.reduce(
      (sum, praticien) &#x3D;&gt; sum + parseFloat(praticien.etp || &#x27;0&#x27;),
      0
    );

    console.log(&#x60;Total CCAM: ${totalCCAM}, Total NGAP: ${totalNGAP}, Total ETP: ${totalEtp}&#x60;);

    // Générer le tableau des praticiens externes
    this.externalPractitionerSummary &#x3D; externalPractitionersWithRealisations.map(
      (praticien) &#x3D;&gt; {
        const actsByPractitioner &#x3D; realisationsInElement.filter(
          (act) &#x3D;&gt; act.praticienId &#x3D;&#x3D;&#x3D; praticien.id
        );

        const nbActesCCAM &#x3D; actsByPractitioner
          .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;CCAM&#x27;)
          .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

        const nbActesNGAP &#x3D; actsByPractitioner
          .filter((act) &#x3D;&gt; act.typeActe &#x3D;&#x3D;&#x3D; &#x27;NGAP&#x27;)
          .reduce((sum, act) &#x3D;&gt; sum + act.count, 0);

        const partPraticienCCAM &#x3D;
          totalCCAM &gt; 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;

        const partPraticienNGAP &#x3D;
          totalNGAP &gt; 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

        return {
          nomUsuel: praticien.nom,
          nomPatronymique: praticien.nomPatronymique,
          prenom: praticien.prenom,
          praticienDateDepart: praticien.dateDepart,
          praticienDateArrivee: praticien.dateArrivee,
          etp: praticien.etp,
          nbActesCCAM,
          partPraticienCCAM: partPraticienCCAM.toFixed(2) + &#x27;%&#x27;,
          nbActesNGAP,
          partPraticienNGAP: partPraticienNGAP.toFixed(2) + &#x27;%&#x27;,
        };
      }
    );

    // Mettre à jour les totaux
    this.totalEtpExternal &#x3D; totalEtp.toFixed(1);
    this.totalCCAMExternal &#x3D; totalCCAM;
    this.totalNGAPExternal &#x3D; totalNGAP;
    this.totalPartCCAMExternal &#x3D; (totalCCAM &gt; 0 ? 100 : 0).toFixed(2) + &#x27;%&#x27;;
    this.totalPartNGAPExternal &#x3D; (totalNGAP &gt; 0 ? 100 : 0).toFixed(2) + &#x27;%&#x27;;

    console.log(
      &#x60;Tableau des praticiens externes pour ${
        isService ? &#x27;service&#x27; : &#x27;UF&#x27;
      } :&#x60;,
      this.externalPractitionerSummary
    );
  }



  /****************************************************** **************************************** **************/
}
</code></pre>
    </div>
</div>








                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'interface';
            var COMPODOC_CURRENT_PAGE_URL = 'PracticienActeSummary.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
