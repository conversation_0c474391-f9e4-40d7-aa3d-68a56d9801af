//{
//  "extends": "../tsconfig.app.json",
//  "compilerOptions": {
//    "types": ["node"],
//    "allowSyntheticDefaultImports": true,
//    "resolveJsonModule": true
//  },
//  "exclude": ["../src/test.ts", "../src/**/*.spec.ts"],
//  "include": ["../src/**/*.stories.*", "./preview.ts"],
//  "files": ["./typings.d.ts"]
//}

{
  "extends": "../tsconfig.app.json",
  "compilerOptions": {
    "types": ["node"],
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true
  },
  "exclude": ["../src/test.ts", "../src/**/*.spec.ts"],
  "include": [
    "../src/**/*.stories.*",
    "./preview.ts",
    "../src/polyfills.ts"
  ],
  "files": ["./typings.d.ts"]
}
