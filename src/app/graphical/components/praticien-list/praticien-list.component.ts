import {Component, OnInit} from '@angular/core';
import {Praticien} from "../../../core/models/acte/praticien.model";
import {PraticienService} from "../../../core/services/auth/praticien.service";
import {Router} from "@angular/router";
import {TableModule} from "primeng/table";
import {BreadcrumbComponent} from "../../../pages/breadcrumb/breadcrumb.component";
import {BreadcrumbItem} from "../../../core/models/breadcrumbItem";

@Component({
  selector: 'app-praticien-list',
  standalone: true,
  imports: [
    TableModule,
    BreadcrumbComponent
  ],
  templateUrl: './praticien-list.component.html',
  styleUrl: './praticien-list.component.scss'
})
export class PraticienListComponent implements OnInit{

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Praticiens', url: '/graphic/praticien-list' },
    { label: 'Liste des praticiens' }
  ];

  praticiens: Praticien[] = [];

  totalPraticiens: number = 0; // Nombre total de praticiens
  totalEtp: number = 0; // Total ETP
  totalPraticiensPermanent: number = 0; // Nombre de praticiens permanents
  totalPraticiensTemporaire: number = 0; // Nombre de praticiens temporaires
  totalPraticiensJunior: number = 0; // Nombre de praticiens juniors


  constructor(
    private praticienService: PraticienService,
    private router: Router,
    ) {}

  ngOnInit(): void {
    this.loadPraticiens();
  }

  loadPraticiens(): void {
    this.praticienService.praticiens$.subscribe((data) => {
      if (data) {
        this.praticiens = data;

        // Total des praticiens
        this.totalPraticiens = data.length;

        // Calcul du total des ETP
        this.totalEtp = data.reduce((sum, praticien) => sum + parseFloat(praticien.etp), 0);

        // Calcul des statistiques par statut
        this.totalPraticiensPermanent = data.filter(praticien => praticien.statut === 'Permanent').length;
        this.totalPraticiensTemporaire = data.filter(praticien => praticien.statut === 'Temporaire').length;
        this.totalPraticiensJunior = data.filter(praticien => praticien.statut === 'Junior').length;
      }
    });
  }

  viewPraticienDetails(praticienId: string): void {
    this.router.navigate(['/graphic/praticien', praticienId]);
  }
  onInput(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }
}
