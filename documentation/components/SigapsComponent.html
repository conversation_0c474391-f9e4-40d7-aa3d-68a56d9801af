<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  SigapsComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#styleData" class="nav-link"
                role="tab" id="styleData-tab" data-bs-toggle="tab" data-link="style">Styles</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/pages/sigaps/sigaps.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-sigaps</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>

            <tr>
                <td class="col-md-3">imports</td>
                <td class="col-md-9">
                            <code>DatePipe</code>
                            <code>ChartModule</code>
                            <code>NgIf</code>
                            <code>NgForOf</code>
                            <code>StyleClassModule</code>
                                <code><a href="../components/BreadcrumbComponent.html" target="_self" >BreadcrumbComponent</a></code>
                            <code>TableModule</code>
                            <code>FormsModule</code>
                </td>
            </tr>

            <tr>
                <td class="col-md-3">styleUrls</td>
                <td class="col-md-9"><code>./sigaps.component.scss</code></td>
            </tr>



            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./sigaps.component.html</code></td>
            </tr>








        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#barChartData" >barChartData</a>
                            </li>
                            <li>
                                <a href="#barChartOptions" >barChartOptions</a>
                            </li>
                            <li>
                                <a href="#breadcrumbItems" >breadcrumbItems</a>
                            </li>
                            <li>
                                <a href="#globalFilter" >globalFilter</a>
                            </li>
                            <li>
                                <a href="#groupedBarChartData" >groupedBarChartData</a>
                            </li>
                            <li>
                                <a href="#groupedBarChartOptions" >groupedBarChartOptions</a>
                            </li>
                            <li>
                                <a href="#hasError" >hasError</a>
                            </li>
                            <li>
                                <a href="#isLoading" >isLoading</a>
                            </li>
                            <li>
                                <a href="#pieChartData" >pieChartData</a>
                            </li>
                            <li>
                                <a href="#pieChartOptions" >pieChartOptions</a>
                            </li>
                            <li>
                                <a href="#sigapsData" >sigapsData</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#getRandomColor" >getRandomColor</a>
                            </li>
                            <li>
                                <a href="#initializeBarChart" >initializeBarChart</a>
                            </li>
                            <li>
                                <a href="#initializeGroupedBarChart" >initializeGroupedBarChart</a>
                            </li>
                            <li>
                                <a href="#initializePieChart" >initializePieChart</a>
                            </li>
                            <li>
                                <a href="#loadSigapsData" >loadSigapsData</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                            <li>
                                <a href="#onInput" >onInput</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor(sigapsService: SigapsService)</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="45" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:45</a></div>
                            </td>
                        </tr>

                <tr>
                    <td class="col-md-4">
                            <div>
                                    <b>Parameters :</b>
                                    <table class="params">
                                        <thead>
                                            <tr>
                                                <td>Name</td>
                                                    <td>Type</td>
                                                <td>Optional</td>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                        <td>sigapsService</td>

                                                        <td>
                                                                    <code>SigapsService</code>
                                                        </td>

                                                    <td>
                                                            No
                                                    </td>

                                                </tr>
                                        </tbody>
                                    </table>
                            </div>
                    </td>
                </tr>
            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="getRandomColor"></a>
                    <span class="name">
                        <span ><b>getRandomColor</b></span>
                        <a href="#getRandomColor"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>getRandomColor()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="205"
                                    class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:205</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeBarChart"></a>
                    <span class="name">
                        <span ><b>initializeBarChart</b></span>
                        <a href="#initializeBarChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeBarChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="108"
                                    class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:108</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializeGroupedBarChart"></a>
                    <span class="name">
                        <span ><b>initializeGroupedBarChart</b></span>
                        <a href="#initializeGroupedBarChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializeGroupedBarChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="158"
                                    class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:158</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="initializePieChart"></a>
                    <span class="name">
                        <span ><b>initializePieChart</b></span>
                        <a href="#initializePieChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>initializePieChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="79"
                                    class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:79</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="loadSigapsData"></a>
                    <span class="name">
                        <span ><b>loadSigapsData</b></span>
                        <a href="#loadSigapsData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>loadSigapsData()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="55"
                                    class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:55</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="51"
                                    class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:51</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="onInput"></a>
                    <span class="name">
                        <span ><b>onInput</b></span>
                        <a href="#onInput"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>onInput(event: Event)</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="218"
                                    class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:218</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                            <div class="io-description">
                                <b>Parameters :</b>

                                <table class="params">
                                    <thead>
                                        <tr>
                                            <td>Name</td>
                                                <td>Type</td>
                                            <td>Optional</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                                <td>event</td>
                                            <td>
                                                        <code>Event</code>
                                            </td>

                                            <td>
                                                    No
                                            </td>


                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                        </div>
                            <div class="io-description">

                            </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="barChartData"></a>
                    <span class="name">
                        <span ><b>barChartData</b></span>
                        <a href="#barChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="37" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:37</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="barChartOptions"></a>
                    <span class="name">
                        <span ><b>barChartOptions</b></span>
                        <a href="#barChartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="38" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:38</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="breadcrumbItems"></a>
                    <span class="name">
                        <span ><b>breadcrumbItems</b></span>
                        <a href="#breadcrumbItems"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>BreadcrumbItem[]</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>[
    { label: &#x27;SIGAPS&#x27;, url: &#x27;/sigaps&#x27; },
    { label: &#x27;Activité Scientifique&#x27; }
  ]</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="40" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:40</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="globalFilter"></a>
                    <span class="name">
                        <span ><b>globalFilter</b></span>
                        <a href="#globalFilter"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/string" target="_blank" >string</a></code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>&#x27;&#x27;</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="45" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:45</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="groupedBarChartData"></a>
                    <span class="name">
                        <span ><b>groupedBarChartData</b></span>
                        <a href="#groupedBarChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="155" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:155</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="groupedBarChartOptions"></a>
                    <span class="name">
                        <span ><b>groupedBarChartOptions</b></span>
                        <a href="#groupedBarChartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="156" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:156</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="hasError"></a>
                    <span class="name">
                        <span ><b>hasError</b></span>
                        <a href="#hasError"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>false</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="31" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:31</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="isLoading"></a>
                    <span class="name">
                        <span ><b>isLoading</b></span>
                        <a href="#isLoading"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>true</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="30" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:30</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="pieChartData"></a>
                    <span class="name">
                        <span ><b>pieChartData</b></span>
                        <a href="#pieChartData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="34" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:34</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="pieChartOptions"></a>
                    <span class="name">
                        <span ><b>pieChartOptions</b></span>
                        <a href="#pieChartOptions"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="35" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:35</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="sigapsData"></a>
                    <span class="name">
                        <span ><b>sigapsData</b></span>
                        <a href="#sigapsData"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>    <code>SigapsData | null</code>

                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Default value : </i><code>null</code>
                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="29" class="link-to-prism">src/app/pages/sigaps/sigaps.component.ts:29</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import { Component, OnInit } from &#x27;@angular/core&#x27;;
import {SigapsService} from &quot;../../core/services/sigaps.service&quot;;
import {SigapsData} from &quot;../../core/models/sigaps.model&quot;;
import {DatePipe, NgForOf, NgIf} from &quot;@angular/common&quot;;
import {ChartModule} from &quot;primeng/chart&quot;;
import {StyleClassModule} from &quot;primeng/styleclass&quot;;
import {BreadcrumbComponent} from &quot;../breadcrumb/breadcrumb.component&quot;;
import {BreadcrumbItem} from &quot;../../core/models/breadcrumbItem&quot;;
import {TableModule} from &quot;primeng/table&quot;;
import {FormsModule} from &quot;@angular/forms&quot;;

@Component({
  selector: &#x27;app-sigaps&#x27;,
  templateUrl: &#x27;./sigaps.component.html&#x27;,
  styleUrls: [&#x27;./sigaps.component.scss&#x27;],
  imports: [
    DatePipe,
    ChartModule,
    NgIf,
    NgForOf,
    StyleClassModule,
    BreadcrumbComponent,
    TableModule,
    FormsModule
  ],
  standalone: true
})
export class SigapsComponent implements OnInit {
  sigapsData: SigapsData | null &#x3D; null;
  isLoading &#x3D; true; // Indicateur pour afficher un état de chargement
  hasError &#x3D; false; // Indicateur pour gérer les erreurs

  // Données et options des graphiques
  pieChartData: any;
  pieChartOptions: any;

  barChartData: any;
  barChartOptions: any;
  // fil d&#x27;ariane
  breadcrumbItems: BreadcrumbItem[] &#x3D; [
    { label: &#x27;SIGAPS&#x27;, url: &#x27;/sigaps&#x27; },
    { label: &#x27;Activité Scientifique&#x27; }
  ];
  //
  globalFilter: string &#x3D; &#x27;&#x27;;



  constructor(private sigapsService: SigapsService) {}

  ngOnInit(): void {
    this.loadSigapsData();
  }

  loadSigapsData(): void {
    this.sigapsService.getSigaps().subscribe({
      next: (data) &#x3D;&gt; {
        if (data) {
          this.sigapsData &#x3D; data;
          this.initializePieChart();
          this.initializeBarChart();
          this.initializeGroupedBarChart(); // Initialise les données du graphique
          this.hasError &#x3D; false;
        } else {
          this.hasError &#x3D; true;
        }
        this.isLoading &#x3D; false;
      },
      error: () &#x3D;&gt; {
        this.hasError &#x3D; true;
        this.isLoading &#x3D; false;
      },
    });

    // Charge les données si elles ne sont pas déjà chargées
    this.sigapsService.loadInitialData();
  }

  initializePieChart(): void {
    const firstAuthor &#x3D; this.sigapsData?.data[0];
    if (!firstAuthor) return;

    // Utilisation de Object.keys et Object.values pour extraire les catégories et leurs valeurs
    const categories &#x3D; Object.keys(firstAuthor.categories);
    const values &#x3D; Object.values(firstAuthor.categories);

    this.pieChartData &#x3D; {
      labels: categories,
      datasets: [
        {
          data: values,
          backgroundColor: [&#x27;#ff6384&#x27;, &#x27;#36a2eb&#x27;, &#x27;#ffcd56&#x27;, &#x27;#4bc0c0&#x27;, &#x27;#9966ff&#x27;],
          hoverBackgroundColor: [&#x27;#ff6384&#x27;, &#x27;#36a2eb&#x27;, &#x27;#ffcd56&#x27;, &#x27;#4bc0c0&#x27;, &#x27;#9966ff&#x27;],
        },
      ],
    };

    this.pieChartOptions &#x3D; {
      plugins: {
        legend: {
          display: true,
          position: &#x27;right&#x27;,
        },
      },
    };
  }

  initializeBarChart(): void {
    const authors &#x3D; this.sigapsData?.data;
    if (!authors) return;

    // Générer les labels (services) et scores depuis les données fictives
    const services &#x3D; authors.map((author) &#x3D;&gt; author.service);
    const scores &#x3D; authors.map((author) &#x3D;&gt; author.totalSigapsScore);

    this.barChartData &#x3D; {
      labels: services,
      datasets: [
        {
          label: &#x27;Score SIGAPS par Service&#x27;,
          data: scores,
          backgroundColor: &#x27;#42a5f5&#x27;,
          borderColor: &#x27;#1e88e5&#x27;,
          borderWidth: 1,
        },
      ],
    };

    this.barChartOptions &#x3D; {
      maintainAspectRatio: true,
      aspectRatio: 0.9,
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: &#x27;Services&#x27;,
          },
        },
        y: {
          title: {
            display: true,
            text: &#x27;Score SIGAPS&#x27;,
          },
        },
      },
    };
  }

  //*****************************************************/
  groupedBarChartData: any;
  groupedBarChartOptions: any;

  initializeGroupedBarChart(): void {
    // Vérifie si les données existent
    if (!this.sigapsData || !this.sigapsData.data) return;

    const categories &#x3D; [&quot;A+&quot;, &quot;A&quot;, &quot;B&quot;, &quot;C&quot;, &quot;D&quot;]; // Les catégories fixes de SIGAPS

    // Crée les datasets pour chaque service
    const datasets &#x3D; this.sigapsData.data.map((author) &#x3D;&gt; ({
      label: author.service, // Nom du service
      data: categories.map((category) &#x3D;&gt; author.categories[category] || 0), // Récupère les données par catégorie
      backgroundColor: this.getRandomColor(), // Génère une couleur aléatoire
      // backgroundColor: [&#x27;#ff6384&#x27;, &#x27;#36a2eb&#x27;, &#x27;#ffcd56&#x27;, &#x27;#4bc0c0&#x27;, &#x27;#9966ff&#x27;],
      // hoverBackgroundColor: [&#x27;#ff6384&#x27;, &#x27;#36a2eb&#x27;, &#x27;#ffcd56&#x27;, &#x27;#4bc0c0&#x27;, &#x27;#9966ff&#x27;],
    }));

    // Structure des données pour le graphique
    this.groupedBarChartData &#x3D; {
      labels: categories,
      datasets: datasets,
    };

    // Options pour le graphique
    this.groupedBarChartOptions &#x3D; {
      plugins: {
        legend: {
          display: true,
          position: &quot;top&quot;,
        },
      },
      scales: {
        x: {
          title: {
            display: true,
            text: &quot;Catégories de publications&quot;,
          },
        },
        y: {
          title: {
            display: true,
            text: &quot;Nombre de publications&quot;,
          },
        },
      },
    };
  }

// Fonction utilitaire pour générer une couleur aléatoire
  getRandomColor(): string {
    const letters &#x3D; &#x27;0123456789ABCDEF&#x27;;
    let color &#x3D; &#x27;#&#x27;;
    for (let i &#x3D; 0; i &lt; 6; i++) {
      color +&#x3D; letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }
  // backgroundColor: &#x27;#42A5F5&#x27;,
  // backgroundColor: &#x27;#9CCC65&#x27;,
  // backgroundColor: &#x27;#FFCA28&#x27;,

  //
  onInput(event: Event): string {
    return (event.target as HTMLInputElement).value;
  }



}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;app-breadcrumb [items]&#x3D;&quot;breadcrumbItems&quot;&gt;&lt;/app-breadcrumb&gt;



&lt;div class&#x3D;&quot;p-4&quot;&gt;
  &lt;!-- Indicateur de chargement --&gt;
  &lt;div *ngIf&#x3D;&quot;isLoading&quot; class&#x3D;&quot;flex justify-center items-center h-40&quot;&gt;
    &lt;div class&#x3D;&quot;text-gray-500&quot;&gt;Chargement des données...&lt;/div&gt;
  &lt;/div&gt;

  &lt;!-- Erreur de chargement --&gt;
  &lt;div *ngIf&#x3D;&quot;hasError &amp;&amp; !isLoading&quot; class&#x3D;&quot;text-red-500&quot;&gt;
    Impossible de charger les données SIGAPS. Veuillez réessayer plus tard.
  &lt;/div&gt;

  &lt;!-- Contenu principal --&gt;
  &lt;div *ngIf&#x3D;&quot;sigapsData &amp;&amp; !isLoading &amp;&amp; !hasError&quot;&gt;
    &lt;!-- Date de dernière actualisation --&gt;
    &lt;p class&#x3D;&quot;text-gray-600 mb-4&quot;&gt;
      Dernière actualisation :
      &lt;span class&#x3D;&quot;font-semibold&quot;&gt;{{ sigapsData.lastUpdated | date: &#x27;dd/MM/yyyy&#x27; }}&lt;/span&gt;
    &lt;/p&gt;
    &lt;div *ngIf&#x3D;&quot;sigapsData &amp;&amp; !isLoading &amp;&amp; !hasError&quot;&gt;
      &lt;div&gt;
        &lt;div class&#x3D;&quot;relative mt-2 rounded-md shadow-sm&quot;&gt;
          &lt;div class&#x3D;&quot;pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3&quot;&gt;
              &lt;span class&#x3D;&quot;text-gray-500 sm:text-sm mr-3&quot;&gt;
                &lt;i class&#x3D;&quot;pi pi-search&quot;&gt;&lt;/i&gt;
              &lt;/span&gt;
          &lt;/div&gt;
          &lt;input
            type&#x3D;&quot;text&quot; name&#x3D;&quot;price&quot;
            (input)&#x3D;&quot;scoreTab.filterGlobal(onInput($event), &#x27;contains&#x27;)&quot;
            class&#x3D;&quot;block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset
               ring-gray-300 placeholder:text-gray-400
               focus:ring-2 focus:ring-inset
               focus:ring-indigo-600
               sm:text-sm/6 &quot;
            placeholder&#x3D;&quot;  Filtrer par service,uf,auteur ...&quot;
          /&gt;
        &lt;/div&gt;
      &lt;/div&gt;

      &lt;p-table
        #scoreTab
        [value]&#x3D;&quot;sigapsData?.data || []&quot;
        [paginator]&#x3D;&quot;true&quot; [rows]&#x3D;&quot;10&quot;
        [globalFilterFields]&#x3D;&quot;[&#x27;authorName&#x27;, &#x27;service&#x27;, &#x27;uf&#x27;]&quot;
        [responsiveLayout]&#x3D;&quot;&#x27;scroll&#x27;&quot; [showCurrentPageReport]&#x3D;&quot;true&quot;
        currentPageReportTemplate&#x3D;&quot;Affichage {first} à {last} sur {totalRecords}&quot;
        [rowsPerPageOptions]&#x3D;&quot;[5, 10, 20]&quot;
      &gt;
        &lt;ng-template pTemplate&#x3D;&quot;header&quot;&gt;
          &lt;tr&gt;
            &lt;th pSortableColumn&#x3D;&quot;authorName&quot;&gt;
              Nom de l&#x27;auteur
              &lt;p-sortIcon field&#x3D;&quot;authorName&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;/th&gt;
            &lt;th pSortableColumn&#x3D;&quot;service&quot;&gt;
              Service
              &lt;p-sortIcon field&#x3D;&quot;service&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;/th&gt;
            &lt;th pSortableColumn&#x3D;&quot;uf&quot;&gt;
              UF
              &lt;p-sortIcon field&#x3D;&quot;uf&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;/th&gt;
            &lt;th pSortableColumn&#x3D;&quot;totalPublications&quot; class&#x3D;&quot;text-center&quot;&gt;
              Total publications
              &lt;p-sortIcon field&#x3D;&quot;totalPublications&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;/th&gt;
            &lt;th pSortableColumn&#x3D;&quot;totalSigapsScore&quot; class&#x3D;&quot;text-center&quot;&gt;
              Score SIGAPS total
              &lt;p-sortIcon field&#x3D;&quot;totalSigapsScore&quot;&gt;&lt;/p-sortIcon&gt;
            &lt;/th&gt;
            &lt;th&gt;
              Répartition par catégorie
            &lt;/th&gt;
          &lt;/tr&gt;
        &lt;/ng-template&gt;

        &lt;ng-template pTemplate&#x3D;&quot;body&quot; let-author&gt;
          &lt;tr&gt;
            &lt;td&gt;{{ author.authorName }}&lt;/td&gt;
            &lt;td&gt;{{ author.service }}&lt;/td&gt;
            &lt;td&gt;{{ author.uf }}&lt;/td&gt;
            &lt;td class&#x3D;&quot;text-center&quot;&gt;{{ author.totalPublications }}&lt;/td&gt;
            &lt;td class&#x3D;&quot;text-center&quot;&gt;{{ author.totalSigapsScore }}&lt;/td&gt;
            &lt;td&gt;
              A+: {{ author.categories.APlus || 0 }},
              A: {{ author.categories.A || 0 }},
              B: {{ author.categories.B || 0 }},
              C: {{ author.categories.C || 0 }},
              D: {{ author.categories.D || 0 }}
            &lt;/td&gt;
          &lt;/tr&gt;
        &lt;/ng-template&gt;


      &lt;/p-table&gt;

    &lt;/div&gt;


  &lt;/div&gt;
  &lt;div class&#x3D;&quot;p-grid p-mt-4&quot;&gt;
    &lt;!-- Section Titre --&gt;
    &lt;div class&#x3D;&quot;p-col-12&quot;&gt;
      &lt;h3 class&#x3D;&quot;text-lg font-bold text-gray-700 text-center&quot;&gt;Répartition par Catégorie et par Service&lt;/h3&gt;
    &lt;/div&gt;

    &lt;!-- Graphique en barres groupées --&gt;
    &lt;div class&#x3D;&quot;p-col-12&quot;&gt;
      &lt;p-chart
        type&#x3D;&quot;bar&quot;
        [data]&#x3D;&quot;groupedBarChartData&quot;
        [options]&#x3D;&quot;groupedBarChartOptions&quot;
        pStyleClass&#x3D;&quot;chart-container&quot;
      &gt;&lt;/p-chart&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/div&gt;






















&lt;!--&amp;lt;!&amp;ndash; Graphique d&#x27;évolution annuelle &amp;ndash;&amp;gt;--&gt;
&lt;!--&lt;div class&#x3D;&quot;container mx-auto p-6&quot;&gt;--&gt;
&lt;!--  &amp;lt;!&amp;ndash; Titre &amp;ndash;&amp;gt;--&gt;
&lt;!--  &lt;h2 class&#x3D;&quot;text-2xl font-bold text-gray-800 mb-6&quot;&gt;Analyse statistique des SIGAPS&lt;/h2&gt;--&gt;

&lt;!--  &amp;lt;!&amp;ndash; Section graphique &amp;ndash;&amp;gt;--&gt;
&lt;!--  &lt;div class&#x3D;&quot;grid grid-cols-1 md:grid-cols-2 gap-8&quot;&gt;--&gt;
&lt;!--    &amp;lt;!&amp;ndash; Colonne de gauche: graphique en secteurs &amp;ndash;&amp;gt;--&gt;
&lt;!--    &lt;div class&#x3D;&quot;bg-white shadow-md rounded-lg p-6&quot;&gt;--&gt;
&lt;!--      &lt;h3 class&#x3D;&quot;text-xl font-semibold text-gray-700 mb-4&quot;&gt;Répartition par Catégorie&lt;/h3&gt;--&gt;
&lt;!--      &lt;p-chart--&gt;
&lt;!--        type&#x3D;&quot;pie&quot;--&gt;
&lt;!--        [data]&#x3D;&quot;pieChartData&quot;--&gt;
&lt;!--        [options]&#x3D;&quot;pieChartOptions&quot;--&gt;
&lt;!--        class&#x3D;&quot;h-64&quot;--&gt;
&lt;!--      &gt;&lt;/p-chart&gt;--&gt;
&lt;!--    &lt;/div&gt;--&gt;

&lt;!--    &amp;lt;!&amp;ndash; Colonne de droite: graphique en barres &amp;ndash;&amp;gt;--&gt;
&lt;!--    &lt;div class&#x3D;&quot;bg-white shadow-md rounded-lg p-6&quot;&gt;--&gt;
&lt;!--      &lt;h3 class&#x3D;&quot;text-xl font-semibold text-gray-700 mb-4&quot;&gt;Répartition par Service&lt;/h3&gt;--&gt;
&lt;!--      &lt;p-chart--&gt;
&lt;!--        type&#x3D;&quot;bar&quot;--&gt;
&lt;!--        [data]&#x3D;&quot;barChartData&quot;--&gt;
&lt;!--        [options]&#x3D;&quot;barChartOptions&quot;--&gt;
&lt;!--        class&#x3D;&quot;h-64&quot;--&gt;
&lt;!--      &gt;&lt;/p-chart&gt;--&gt;
&lt;!--    &lt;/div&gt;--&gt;
&lt;!--  &lt;/div&gt;--&gt;
&lt;!--&lt;/div&gt;--&gt;

</code></pre>
    </div>

    <div class="tab-pane fade " id="styleData">
                <p class="comment">
                    <code>./sigaps.component.scss</code>
                </p>
                <pre class="line-numbers"><code class="language-scss">.chart-container {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
}
</code></pre>
    </div>

    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb><div class="p-4">  <!-- Indicateur de chargement -->  <div *ngIf="isLoading" class="flex justify-center items-center h-40">    <div class="text-gray-500">Chargement des données...</div>  </div>  <!-- Erreur de chargement -->  <div *ngIf="hasError && !isLoading" class="text-red-500">    Impossible de charger les données SIGAPS. Veuillez réessayer plus tard.  </div>  <!-- Contenu principal -->  <div *ngIf="sigapsData && !isLoading && !hasError">    <!-- Date de dernière actualisation -->    <p class="text-gray-600 mb-4">      Dernière actualisation :      <span class="font-semibold">{{ sigapsData.lastUpdated | date: \'dd/MM/yyyy\' }}</span>    </p>    <div *ngIf="sigapsData && !isLoading && !hasError">      <div>        <div class="relative mt-2 rounded-md shadow-sm">          <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">              <span class="text-gray-500 sm:text-sm mr-3">                <i class="pi pi-search"></i>              </span>          </div>          <input            type="text" name="price"            (input)="scoreTab.filterGlobal(onInput($event), \'contains\')"            class="block w-full h-full pl-8 pr-5 py-2 rounded-md border-0  pr-25 text-gray-900 ring-1 ring-inset               ring-gray-300 placeholder:text-gray-400               focus:ring-2 focus:ring-inset               focus:ring-indigo-600               sm:text-sm/6 "            placeholder="  Filtrer par service,uf,auteur ..."          />        </div>      </div>      <p-table        #scoreTab        [value]="sigapsData?.data || []"        [paginator]="true" [rows]="10"        [globalFilterFields]="[\'authorName\', \'service\', \'uf\']"        [responsiveLayout]="\'scroll\'" [showCurrentPageReport]="true"        currentPageReportTemplate="Affichage {first} à {last} sur {totalRecords}"        [rowsPerPageOptions]="[5, 10, 20]"      >        <ng-template pTemplate="header">          <tr>            <th pSortableColumn="authorName">              Nom de l\'auteur              <p-sortIcon field="authorName"></p-sortIcon>            </th>            <th pSortableColumn="service">              Service              <p-sortIcon field="service"></p-sortIcon>            </th>            <th pSortableColumn="uf">              UF              <p-sortIcon field="uf"></p-sortIcon>            </th>            <th pSortableColumn="totalPublications" class="text-center">              Total publications              <p-sortIcon field="totalPublications"></p-sortIcon>            </th>            <th pSortableColumn="totalSigapsScore" class="text-center">              Score SIGAPS total              <p-sortIcon field="totalSigapsScore"></p-sortIcon>            </th>            <th>              Répartition par catégorie            </th>          </tr>        </ng-template>        <ng-template pTemplate="body" let-author>          <tr>            <td>{{ author.authorName }}</td>            <td>{{ author.service }}</td>            <td>{{ author.uf }}</td>            <td class="text-center">{{ author.totalPublications }}</td>            <td class="text-center">{{ author.totalSigapsScore }}</td>            <td>              A+: {{ author.categories.APlus || 0 }},              A: {{ author.categories.A || 0 }},              B: {{ author.categories.B || 0 }},              C: {{ author.categories.C || 0 }},              D: {{ author.categories.D || 0 }}            </td>          </tr>        </ng-template>      </p-table>    </div>  </div>  <div class="p-grid p-mt-4">    <!-- Section Titre -->    <div class="p-col-12">      <h3 class="text-lg font-bold text-gray-700 text-center">Répartition par Catégorie et par Service</h3>    </div>    <!-- Graphique en barres groupées -->    <div class="p-col-12">      <p-chart        type="bar"        [data]="groupedBarChartData"        [options]="groupedBarChartOptions"        pStyleClass="chart-container"      ></p-chart>    </div>  </div></div><!--&lt;!&ndash; Graphique d\'évolution annuelle &ndash;&gt;--><!--<div class="container mx-auto p-6">--><!--  &lt;!&ndash; Titre &ndash;&gt;--><!--  <h2 class="text-2xl font-bold text-gray-800 mb-6">Analyse statistique des SIGAPS</h2>--><!--  &lt;!&ndash; Section graphique &ndash;&gt;--><!--  <div class="grid grid-cols-1 md:grid-cols-2 gap-8">--><!--    &lt;!&ndash; Colonne de gauche: graphique en secteurs &ndash;&gt;--><!--    <div class="bg-white shadow-md rounded-lg p-6">--><!--      <h3 class="text-xl font-semibold text-gray-700 mb-4">Répartition par Catégorie</h3>--><!--      <p-chart--><!--        type="pie"--><!--        [data]="pieChartData"--><!--        [options]="pieChartOptions"--><!--        class="h-64"--><!--      ></p-chart>--><!--    </div>--><!--    &lt;!&ndash; Colonne de droite: graphique en barres &ndash;&gt;--><!--    <div class="bg-white shadow-md rounded-lg p-6">--><!--      <h3 class="text-xl font-semibold text-gray-700 mb-4">Répartition par Service</h3>--><!--      <p-chart--><!--        type="bar"--><!--        [data]="barChartData"--><!--        [options]="barChartOptions"--><!--        class="h-64"--><!--      ></p-chart>--><!--    </div>--><!--  </div>--><!--</div>--></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'SigapsComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'SigapsComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
