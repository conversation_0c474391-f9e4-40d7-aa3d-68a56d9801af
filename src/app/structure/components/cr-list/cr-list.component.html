<app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
<section>
  <div class="p-4 bg-gray-50 rounded-lg shadow mb-4">
    <div class="flex justify-between items-center">
      <!-- Titre principal avec icône -->
      <div class="flex items-center space-x-3">
        <i class="pi pi-sitemap text-cyan-700 text-2xl"></i>
        <h1 class="text-xl font-bold text-gray-700">Liste des CRs</h1>
      </div>

      <!-- Statistiques globales -->
      <div class="flex items-center space-x-8">
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalCRs }}</p>
          <p class="text-sm text-gray-500">CRs</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-cyan-700">{{ totalActiveCRs }}</p>
          <p class="text-sm text-gray-500">CRs Actifs</p>
        </div>
      </div>

      <!-- Icône SVG décorative -->
      <div class="hidden lg:block text-center">
        <!-- Icône SVG -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="w-24 h-24 text-cyan-700 mx-auto"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"
          />
        </svg>

        <!-- Texte sous le SVG -->
        <p class="mt-2 text-sm font-medium text-gray-500">CHRU Nancy</p>
      </div>
    </div>
  </div>
</section>

<div class="p-card">
  <p-table
    #crTable
    [value]="crs"
    [paginator]="true"
    [rows]="pageSize"
    [rowsPerPageOptions]="rowsPerPageOptions"
    [lazy]="allCRs.length === 0"
    [loading]="loading"
    [totalRecords]="totalCRs"
    (onLazyLoad)="onPageChange($event)"
    (onSort)="onPageChange($event)"
    (onPage)="onPageChange($event)"
    [first]="first"
    [responsiveLayout]="'scroll'"
    [globalFilterFields]="['libelle', 'crcode', 'etab']"
    [exportHeader]="'customExportHeader'"
    class="min-w-full bg-white border rounded-lg shadow"
  >
    <!-- Caption with Title, Search Input and Export Button -->
    <ng-template pTemplate="caption">
      <div class="flex justify-between items-center mb-2">
        <button
          pButton
          icon="pi pi-download"
          label="Exporter la sélection"
          class="p-button-primary"
          [disabled]="selectedCRs.length === 0"
          (click)="exportSelectedCRs()"
          pTooltip="Exporter les CRs sélectionnés en CSV"
          tooltipPosition="top"
        ></button>
        <div class="text-sm text-gray-600" *ngIf="selectedCRs.length > 0">
          {{ selectedCRs.length }} CR(s) sélectionné(s)
        </div>
      </div>
      <div class="relative">
        <!-- Icône de recherche -->
        <span class="absolute inset-y-0 left-0 flex items-center pl-3">
          <i class="pi pi-search text-gray-400"></i>
        </span>
        <!-- Input de recherche -->
        <input
          type="text"
          (input)="crTable.filterGlobal(onInput($event), 'contains')"
          class="block w-full pl-10 pr-5 py-2 rounded-md border-0 text-gray-900 ring-1 ring-inset
             ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600
             sm:text-sm"
          placeholder="Filtrer par nom, code ou établissement ({{totalCRs}} CRs au total)"
        />
      </div>
    </ng-template>

    <!-- Column Headers -->
    <ng-template pTemplate="header">
      <tr>
        <th class="w-12 px-4 py-2 border-b text-left text-gray-700 font-semibold">
          <p-checkbox
            [binary]="true"
            [ngModel]="isAllSelected()"
            (onChange)="toggleSelectAll($event)"
            inputId="selectAll"
            pTooltip="Sélectionner/Désélectionner tout"
            tooltipPosition="top"
          ></p-checkbox>
        </th>
        <th class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          <i
            class="pi pi-hashtag cursor-pointer ml-2"
            title="Voir les détails du CR"
          ></i>
        </th>
        <th pSortableColumn="libelle" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Nom du CR
          <p-sortIcon field="libelle"></p-sortIcon>
        </th>
        <th pSortableColumn="crcode" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Code
          <p-sortIcon field="crcode"></p-sortIcon>
        </th>
        <th pSortableColumn="etab" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Établissement
          <p-sortIcon field="etab"></p-sortIcon>
        </th>
        <th pSortableColumn="datdeb" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Date de début
          <p-sortIcon field="datdeb"></p-sortIcon>
        </th>
        <th pSortableColumn="isActif" class="px-4 py-2 border-b text-left text-gray-700 font-semibold">
          Statut
          <p-sortIcon field="isActif"></p-sortIcon>
        </th>
      </tr>
    </ng-template>

    <!-- Table Body -->
    <ng-template pTemplate="body" let-cr>
      <tr class="border-b hover:bg-gray-50">
        <td class="px-4 py-2 text-center">
          <p-checkbox
            [binary]="true"
            [ngModel]="isCRSelected(cr)"
            (onChange)="toggleCRSelection(cr)"
            [inputId]="'cr_' + cr['@id']"
            styleClass="p-checkbox-lg"
          ></p-checkbox>
        </td>
        <td style="padding: 0.75rem; text-align: left;">
          <p class="text-sm font-bold text-gray-600 flex items-center">
            <i
              class="pi pi-eye text-lg text-cyan-700 cursor-pointer ml-2"
              title="Voir les détails du CR {{cr.libelle}} {{cr.crcode}}"
              (click)="viewCRDetails(cr['@id'])"
            ></i>
          </p>
        </td>
        <td class="px-4 py-2" (click)="viewCRDetails(cr['@id'])">
          <a class="text-lg text-cyan-700 hover:underline cursor-pointer font-semibold"
             (click)="viewCRDetails(cr['@id'])">
            {{ cr.libelle }}
          </a>
        </td>
        <td class="px-4 py-2">{{ cr.crcode }}</td>
        <td class="px-4 py-2">{{ cr.etab }}</td>
        <td class="px-4 py-2">{{ formatDate(cr.datdeb) }}</td>
        <td class="px-4 py-2">
          <span
            [ngClass]="{
              'bg-green-100 text-green-800': cr.isActif,
              'bg-red-100 text-red-800': !cr.isActif
            }"
            class="px-2 py-1 rounded-full text-xs font-medium"
          >
            {{ cr.isActif ? 'Actif' : 'Inactif' }}
          </span>
        </td>
      </tr>
    </ng-template>

    <!-- Skeleton Loading Template -->
    <ng-template pTemplate="loadingbody">
      <tr class="border-b">
        <td style="padding: 0.75rem; text-align: left;">
          <p-skeleton shape="circle" size="1.5rem"></p-skeleton>
        </td>
        <td style="padding: 0.75rem; text-align: left;">
          <p-skeleton shape="circle" size="2rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton height="2rem" styleClass="mb-2"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="6rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem" styleClass="px-2 py-1 rounded-full"></p-skeleton>
        </td>
      </tr>
      <tr class="border-b">
        <td style="padding: 0.75rem; text-align: left;">
          <p-skeleton shape="circle" size="1.5rem"></p-skeleton>
        </td>
        <td style="padding: 0.75rem; text-align: left;">
          <p-skeleton shape="circle" size="2rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton height="2rem" styleClass="mb-2"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="6rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem" styleClass="px-2 py-1 rounded-full"></p-skeleton>
        </td>
      </tr>
      <tr class="border-b">
        <td style="padding: 0.75rem; text-align: left;">
          <p-skeleton shape="circle" size="1.5rem"></p-skeleton>
        </td>
        <td style="padding: 0.75rem; text-align: left;">
          <p-skeleton shape="circle" size="2rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton height="2rem" styleClass="mb-2"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="6rem" height="1.5rem"></p-skeleton>
        </td>
        <td class="px-4 py-2">
          <p-skeleton width="4rem" height="1.5rem" styleClass="px-2 py-1 rounded-full"></p-skeleton>
        </td>
      </tr>
    </ng-template>

    <!-- Empty Message -->
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="7" class="text-center py-4">
          <div *ngIf="loading" class="flex justify-center items-center">
            <i class="pi pi-spin pi-spinner text-cyan-700 text-2xl mr-2"></i>
            <span>Chargement des données...</span>
          </div>
          <div *ngIf="!loading">Aucun CR trouvé</div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>
