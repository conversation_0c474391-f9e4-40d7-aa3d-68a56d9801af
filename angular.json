{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"supra": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/supra", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "node_modules/ace-builds/src-noconflict/", "output": "/assets/ace/"}], "styles": ["node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "supra:build:production"}, "development": {"browserTarget": "supra:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "supra:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "storybook": {"builder": "@storybook/angular:start-storybook", "options": {"configDir": ".storybook", "browserTarget": "supra:build", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "."], "port": 6006}}, "build-storybook": {"builder": "@storybook/angular:build-storybook", "options": {"configDir": ".storybook", "browserTarget": "supra:build", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "."], "outputDir": "storybook-static"}}}}}}