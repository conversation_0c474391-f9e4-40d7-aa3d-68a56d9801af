import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Ng<PERSON>lass, <PERSON>For<PERSON><PERSON>, Ng<PERSON><PERSON>, NgOptimizedImage } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { Table, TableModule } from "primeng/table";
import { Router } from "@angular/router";
import { BreadcrumbItem } from "../../../core/models/breadcrumbItem";
import { BreadcrumbComponent } from "../../../pages/breadcrumb/breadcrumb.component";
import { Button, ButtonDirective } from "primeng/button";
import { UFModel } from "../../../core/models/structure";
import { UFService } from "../../../core/services/structure/uf.service";
import { LazyLoadEvent } from 'primeng/api';
import { SkeletonModule } from 'primeng/skeleton';
import { TooltipModule } from 'primeng/tooltip';
import { CheckboxModule } from 'primeng/checkbox';

@Component({
  selector: 'app-uf-list',
  standalone: true,
  imports: [
    NgForO<PERSON>,
    Ng<PERSON>f,
    NgClass,
    AsyncPipe,
    FormsModule,
    TableModule,
    BreadcrumbComponent,
    ButtonDirective,
    Button,
    NgOptimizedImage,
    SkeletonModule,
    TooltipModule,
    CheckboxModule
  ],
  templateUrl: './uf-list.component.html',
  styleUrl: './uf-list.component.scss'
})
export class UFListComponent implements OnInit {
  ufs: UFModel[] = []; // Tableau local pour stocker les données
  loading: boolean = false; // Variable pour suivre l'état de chargement
  allUfs: UFModel[] = []; // Tableau pour stocker toutes les données chargées
  filteredUfs: UFModel[] = []; // Tableau pour stocker les données filtrées
  selectedUfs: UFModel[] = []; // Tableau pour stocker les UFs sélectionnées

  // Pagination variables
  currentPage: number = 1;
  pageSize: number = 10;
  first: number = 0;
  rowsPerPageOptions: number[] = [10, 25, 50];

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Structure', url: '/structure' },
    { label: 'Liste des UFs' }
  ];

  // Statistiques globales
  totalUFs: number = 0;
  totalActiveUFs: number = 0;

  constructor(
    private ufService: UFService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadAllUFs(); // Charger toutes les données
  }

  // Fonction pour charger toutes les données
  loadAllUFs(): void {
    this.loading = true; // Définir l'état de chargement à true avant l'appel API

    // Charger toutes les données avec tri côté serveur
    this.ufService.getAll(undefined, undefined, this.sortField, this.sortOrder).subscribe(
      (paginatedResult) => {
        if (paginatedResult) {
          // Stocker toutes les données chargées
          this.allUfs = paginatedResult.items;
          this.filteredUfs = [...this.allUfs];
          this.ufs = this.filteredUfs;
          // Utilise le nombre total d'items retourné par l'API
          this.totalUFs = paginatedResult.totalItems;
          this.totalActiveUFs = paginatedResult.items.filter(uf => uf.isActif).length;
          // Update rowsPerPageOptions based on total items
          this.updateRowsPerPageOptions();
        }
        this.loading = false; // Définir l'état de chargement à false après réception des données
      },
      (error) => {
        console.error('Erreur lors du chargement des UFs :', error);
        this.ufs = []; // Gère les erreurs en initialisant un tableau vide
        this.allUfs = [];
        this.filteredUfs = [];
        this.totalUFs = 0;
        this.totalActiveUFs = 0;
        this.loading = false; // Définir l'état de chargement à false en cas d'erreur
      }
    );
  }

  // Variables for sorting
  sortField: string = '';
  sortOrder: number = 1;

  // Fonction pour charger les données
  loadUFs(page: number = this.currentPage, pageSize: number = this.pageSize, sortField: string = this.sortField, sortOrder: number = this.sortOrder): void {
    this.loading = true; // Définir l'état de chargement à true avant l'appel API

    // Charger les données depuis l'API avec tri côté serveur
    this.ufService.getAll(page, pageSize, sortField, sortOrder).subscribe(
      (paginatedResult) => {
        if (paginatedResult) {
          // Stocker les données chargées
          this.ufs = paginatedResult.items;
          this.allUfs = paginatedResult.items;
          this.filteredUfs = [...this.allUfs];
          // Utilise le nombre total d'items retourné par l'API
          this.totalUFs = paginatedResult.totalItems;
          this.totalActiveUFs = paginatedResult.items.filter(uf => uf.isActif).length;
          // Update rowsPerPageOptions based on total items
          this.updateRowsPerPageOptions();
        }
        this.loading = false; // Définir l'état de chargement à false après réception des données
      },
      (error) => {
        console.error('Erreur lors du chargement des UFs :', error);
        this.ufs = []; // Gère les erreurs en initialisant un tableau vide
        this.allUfs = [];
        this.filteredUfs = [];
        this.totalUFs = 0;
        this.totalActiveUFs = 0;
        this.loading = false; // Définir l'état de chargement à false en cas d'erreur
      }
    );
  }


  onInput(event: Event): string {
    const filterValue = (event.target as HTMLInputElement).value;

    // Si nous avons déjà chargé toutes les données, nous pouvons les filtrer côté client
    if (this.allUfs.length > 0) {
      this.applyClientSideFilter(filterValue);
    }

    return filterValue;
  }

  // Méthode pour appliquer le filtre côté client
  applyClientSideFilter(filterValue: string): void {
    if (!filterValue || filterValue.trim() === '') {
      // Si le filtre est vide, utiliser toutes les données
      this.filteredUfs = [...this.allUfs];
    } else {
      // Sinon, filtrer les données
      const filterValueLower = filterValue.toLowerCase();
      this.filteredUfs = this.allUfs.filter(uf => {
        return (
          (uf.libelle && uf.libelle.toLowerCase().includes(filterValueLower)) ||
          (uf.ufcode && uf.ufcode.toLowerCase().includes(filterValueLower)) ||
          (uf.etab && uf.etab.toLowerCase().includes(filterValueLower))
        );
      });
    }

    // Mettre à jour le nombre total d'UFs filtrées
    this.totalUFs = this.filteredUfs.length;

    // Appliquer la pagination
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.ufs = this.filteredUfs.slice(startIndex, endIndex);
  }

  viewUFDetails(ufId: string): void {
    // Extract the ID from the full URI (e.g., "/api/ufs/123" -> "123")
    const id = ufId.split('/').pop();
    this.router.navigate(['/structure/uf', id]);
  }

  // Helper method to format dates
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  }

  // Handle pagination and sorting events from p-table
  onPageChange(event: any) {
    // Calculate the page number (1-based) from the first row index (0-based)
    const page = event.first !== undefined ? Math.floor(event.first / (event.rows || this.pageSize)) + 1 : 1;
    const pageSize = event.rows || this.pageSize;

    // Extract sort field and order if present
    let sortField = this.sortField;
    let sortOrder = this.sortOrder;

    if (event.sortField) {
      sortField = event.sortField;
      sortOrder = event.sortOrder === 1 ? 1 : -1; // Convert PrimeNG's sortOrder to our format

      // Update component state for sorting
      this.sortField = sortField;
      this.sortOrder = sortOrder;
    }

    // Update component state for pagination
    this.currentPage = page;
    this.pageSize = pageSize;
    this.first = event.first || 0;

    // Charger les données depuis l'API avec tri côté serveur
    this.loadUFs(page, pageSize, sortField, sortOrder);
  }

  // Update rowsPerPageOptions based on total items
  updateRowsPerPageOptions() {
    // Default options for small datasets
    let options = [10, 25, 50];

    // For larger datasets, add more options
    if (this.totalUFs > 50) {
      options.push(100);
    }

    if (this.totalUFs > 100) {
      options.push(200);
    }

    if (this.totalUFs > 500) {
      options.push(500);
    }

    // Ensure we don't have options larger than the total items
    this.rowsPerPageOptions = options.filter(option => option <= this.totalUFs || option === 10);
  }

  // Check if all items on the current page are selected
  isAllSelected(): boolean {
    return this.ufs.length > 0 && this.selectedUfs.length === this.ufs.length;
  }

  // Select or deselect all items on the current page
  toggleSelectAll(event: any): void {
    if (event.checked) {
      // Select all items on the current page
      this.selectedUfs = [...this.ufs];
    } else {
      // Deselect all items
      this.selectedUfs = [];
    }
  }

  // Check if a specific UF is selected
  isUfSelected(uf: UFModel): boolean {
    return this.selectedUfs.some(u => u['@id'] === uf['@id']);
  }

  // Toggle selection of a specific UF
  toggleUfSelection(uf: UFModel): void {
    if (this.isUfSelected(uf)) {
      // Remove from selection
      this.selectedUfs = this.selectedUfs.filter(u => u['@id'] !== uf['@id']);
    } else {
      // Add to selection
      this.selectedUfs = [...this.selectedUfs, uf];
    }
  }

  // Export selected UFs to CSV
  exportSelectedUFs(): void {
    if (this.selectedUfs.length === 0) {
      alert('Veuillez sélectionner au moins une UF à exporter');
      return;
    }

    // Create CSV content
    const headers = ['Nom', 'Code', 'Établissement', 'Date de début', 'Statut'];
    const csvContent = [
      headers.join(','),
      ...this.selectedUfs.map(uf => [
        `"${uf.libelle || ''}"`,
        `"${uf.ufcode || ''}"`,
        `"${uf.etab || ''}"`,
        `"${this.formatDate(uf.datdeb) || ''}"`,
        `"${uf.isActif ? 'Actif' : 'Inactif'}"`
      ].join(','))
    ].join('\n');

    // Create a Blob with UTF-8 BOM for Excel compatibility
    const BOM = new Uint8Array([0xEF, 0xBB, 0xBF]);
    const blob = new Blob([BOM, csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `export-ufs-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
