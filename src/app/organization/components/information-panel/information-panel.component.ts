import {Component, Input, OnInit} from '@angular/core';
import {ActeCCAM} from "../../../core/models/acte/acte-ccam.model";
import {ActeNGAP} from "../../../core/models/acte/acte-ngap.model";
import {RealisationActe} from "../../../core/models/acte/realisation-acte.model";
import {Praticien} from "../../../core/models/acte/praticien.model";
import {PraticienService} from "../../../core/services/auth/praticien.service";
import {ActeService} from "../../../core/services/acte/acte.service";
import {FilterByPraticienPipe} from "../../../core/pipes/filter-by-praticien.pipe";
import {DatePipe, NgForOf, NgIf, TitleCasePipe} from "@angular/common";
import {TableModule} from "primeng/table";
import {InputTextModule} from "primeng/inputtext";
import {IconFieldModule} from "primeng/iconfield";
import {InputIconModule} from "primeng/inputicon";
import {BadgeModule} from "primeng/badge";
import {CardModule} from "primeng/card";
import {ChartModule} from "primeng/chart";
import {BreadcrumbComponent} from "../../../pages/breadcrumb/breadcrumb.component";
import {GradeETP} from "../../../core/models/GradeETP";
import {Button} from "primeng/button";
import {OverlayPanelModule} from "primeng/overlaypanel";
import {ActeLABO} from "../../../core/models/acte/acte-labo.model";
import {combineLatest} from "rxjs";
import {DropdownModule} from "primeng/dropdown";
import {FormsModule} from "@angular/forms";
import {FloatLabelModule} from "primeng/floatlabel";
import {ServiceHospitalier} from "../../../core/models/organization/ServiceHospitalier.model";
import {ServiceHospitalierService} from "../../../core/services/organization/ServiceHospitalierService";
import {UFService} from "../../../core/services/organization/UFService";


interface ActeSummary {
  code: string;
  description: string;
  totalAnneeNMoins1: number;
  totalAnneeN: number;
}


interface PracticienActeSummary {
  nomUsuel: string;
  nomPatronymique: string;
  prenom: string;
  etp: string;
  nbActesCCAM: number;
  partPraticienCCAM: string;
  nbActesNGAP: number;
  partPraticienNGAP: string;
}

@Component({
  selector: 'app-information-panel',
  standalone: true,
  imports: [
    FilterByPraticienPipe,
    NgForOf,
    TitleCasePipe,
    NgIf,
    TableModule,
    InputTextModule,
    IconFieldModule,
    InputIconModule,
    BadgeModule,
    CardModule,
    ChartModule,
    BreadcrumbComponent,
    Button,
    OverlayPanelModule,
    DatePipe,
    DropdownModule,
    FormsModule,
    FloatLabelModule
  ],
  templateUrl: './information-panel.component.html',
  styleUrl: './information-panel.component.scss'
})
export class InformationPanelComponent implements OnInit {
  @Input() elementId!: string; // ID de l'élément à afficher (poleId, departmentId, serviceId, ou ufId)
  @Input() elementType!: 'pole' | 'department' | 'service' | 'uf'; // Type de l'élément


  actesCCAM: ActeCCAM[] = [];
  actesLABO: ActeLABO[] = [];
  actesNGAP: ActeNGAP[] = [];
  realisations: RealisationActe[] = [];
  praticiens: Praticien[] = [];




  totalCCAM: number = 0;
  totalNGAP: number = 0;
  totalLABO: number = 0;

  filteredActesCCAM:  ActeCCAM[] = [];
  filteredActesNGAP: ActeNGAP[] = [];

  filteredActesCCAMSummary: ActeSummary[] = [];  //
  filteredActesNGAPSummary: ActeSummary[] = [];  //
  filteredActesLABOSummary: ActeSummary[] = [];  //
  //
  filterePractitionerActeSummary: PracticienActeSummary[] = [];
  externalPractitionerSummary: PracticienActeSummary[] = [];


  totalEtp: number = 0;
  totalPartCCAM: string = '0%';
  totalPartNGAP: string = '0%';

  // Simulating a chosen period for now
    startYear = 2023; // Replace with dynamically chosen start year
    endYear = 2024;   // Replace with dynamically chosen end year

  praticiensListFilter!: any;

  // pour overview
  ccamData: any;
  ngapData: any;
  laboData: any;
  chartOptions: any;
  //

  //
  grades: GradeETP[] = [
    {
      specialite: '14,883',
      medecine: '4,250',
      juniorMedecine: '3,641',
      specMedecineGen: '2,395',
      ffiMed: '1,000'
    }
  ];
  //********************************************************************************
  statutOptions = [
    { label: 'Tout', value: null },
    { label: 'Permanent', value: 'Permanent' },
    { label: 'Temporaire', value: 'Temporaire' },
    { label: 'Permanent + Temporaire', value: 'PermanentTemporaire'},
    { label: 'Junior', value: 'Junior' }
  ];
  selectedStatut: string | null = null;

  //*****************************************************************************************



  constructor(
    private acteService: ActeService,
    private realisationService: ActeService,
    private praticienService: PraticienService,
    private serviceHospitalierService: ServiceHospitalierService,
    private  ufService: UFService
  ) {}

  ngOnInit(): void {
    this.acteService.actesCCAM$.subscribe((data) => {
      this.actesCCAM = data;
      this.filterActs();
    });


    this.acteService.actesNGAP$.subscribe((data) => {
      this.actesNGAP = data;
      this.filterActs();
    });

    this.acteService.actesLABO$.subscribe((data) => {
      this.actesLABO = data;
      this.filterActs();
    });

    this.realisationService.realisations$.subscribe((data) => {
      this.realisations = this.filterRealisationsByElement(data);
      //
      // this.filteredActesCCAMSummary = this.generateCCAMSummary(data,this.startYear,this.endYear);
      this.filteredActesCCAMSummary = this.generateActeSummary(this.realisations,this.startYear,this.endYear,'CCAM');
      this.filteredActesNGAPSummary = this.generateActeSummary(this.realisations,this.startYear,this.endYear,'NGAP');
      this.filteredActesLABOSummary = this.generateActeSummary(this.realisations,this.startYear,this.endYear,'LABO');
      //

      console.log('le tableau du template'+this.filterePractitionerActeSummary)
      console.table(this.filterePractitionerActeSummary)
      // Sum the counts for total CCAM and total NGAP
      this.totalCCAM = this.realisations
        .filter((r) => r.typeActe.toUpperCase() === 'CCAM')
        .reduce((total, r) => total + (r.count || 1), 0);

      this.totalNGAP = this.realisations
        .filter((r) => r.typeActe.toUpperCase() === 'NGAP')
        .reduce((total, r) => total + (r.count || 1), 0);

      this.totalLABO = this.realisations
        .filter((r) => r.typeActe.toUpperCase() === 'LABO')
        .reduce((total, r) => total + (r.count || 1), 0);

      this.filterActs(); // repartition des actes [CCAM,NGAP] par pratiicien
    });

    this.quickDisplayOverview();
    //


    combineLatest([
      this.realisationService.realisations$,
      this.praticienService.praticiens$,
      this.serviceHospitalierService.servicesHospitaliers$,
    ]).subscribe(
      ([realisations, praticiens]) => {
        this.realisations = this.filterRealisationsByElement(realisations);
        this.praticiens = praticiens;

        // Debug pour vérifier les données avant application du filtre
        console.log('Praticiens chargés:', this.praticiens);
        console.log('Réalisations chargées:', this.realisations);

        // Appliquer le filtre de statut
        this.applyStatutFilter();

        ///****************************************
        // Générer le tableau des praticiens externes
        this.generateExternalPractitionerSummary(this.elementId);
        ///****************************************
        this.generateUnassignedPractitionersSummary(this.elementId);


      },
      error => {
        console.error("Erreur lors du chargement des praticiens ou des réalisations :", error);
      }
    );


  }

  private filterActs(): void {
    const realizedCCAMIds = this.realisations
      .filter((r) => r.typeActe === 'CCAM')
      .map((r) => r.acteId);
    const realizedNGAPIds = this.realisations
      .filter((r) => r.typeActe === 'NGAP')
      .map((r) => r.acteId);

    this.filteredActesCCAM = this.actesCCAM.filter((acte) =>
      realizedCCAMIds.includes(acte.id)
    );
    this.filteredActesNGAP = this.actesNGAP.filter((acte) =>
      realizedNGAPIds.includes(acte.id)
    );
  }

  // Filtrer les réalisations en fonction de l'élément et de son type
  private filterRealisationsByElement(realisations: RealisationActe[]): RealisationActe[] {
    switch (this.elementType) {
      case 'pole':
        return realisations.filter(r => r.poleId === this.elementId);
      case 'department':
        return realisations.filter(r => r.departmentId === this.elementId);
      case 'service':
        return realisations.filter(r => r.serviceId === this.elementId);
      case 'uf':
        return realisations.filter(r => r.ufId === this.elementId);
      default:
        return [];
    }
  }


  // Generate a summary for specified acte type data filtered by the selected years
   generateActeSummary(realisations: RealisationActe[], startYear: number, endYear: number, typeActe: string): ActeSummary[] {
    const summaryMap = new Map<string, ActeSummary>();

    // Filter realizations by specified acte type and map them to acte data
    realisations
      .filter((realisation) => realisation.typeActe === typeActe)
      .forEach((realisation) => {
        const year = new Date(realisation.dateRealisation).getFullYear();
        // const acte = (typeActe === 'CCAM' ? this.actesCCAM : typeActe === 'NGAP' ? this.actesNGAP : this.actesLABO).find((a) => a.id === realisation.acteId);

        const acte = (
          typeActe === 'CCAM' ? this.actesCCAM :
            typeActe === 'NGAP' ? this.actesNGAP :
              typeActe === 'LABO' ? this.actesLABO :
                []
        ).find((a) => a.id === realisation.acteId);


        if (acte) {
          if (!summaryMap.has(acte.id)) {
            summaryMap.set(acte.id, {
              code: acte.code,
              description: acte.description,
              totalAnneeNMoins1: 0,
              totalAnneeN: 0,
            });
          }

          const summaryEntry = summaryMap.get(acte.id)!;
          if (year === startYear) {
            summaryEntry.totalAnneeNMoins1 += realisation.count || 1;
          } else if (year === endYear) {
            summaryEntry.totalAnneeN += realisation.count || 1;
          }
        } else {
          console.error(`No matching acte found for acteId: ${realisation.acteId} in ${typeActe}`);
        }
      });

    // Log the summary map to verify the result
    console.log(`Generated Summary Map for ${typeActe}:`, Array.from(summaryMap.values()));

    return Array.from(summaryMap.values());
  }


  //**************************************************** Tableau Repartition des actes par Praticien***********************************************************************
  private generatePractitionerActeSummary(
    realisations: RealisationActe[],
    praticiens: Praticien[]
  ): PracticienActeSummary[] {
    console.log("Generating Practitioner Acte Summary...");

    // / Réinitialisation des totaux en fonction des filter par status
    this.totalPartCCAM = "0";
    this.totalPartNGAP = "0";

    // Réinitialisation des totaux
    let totalPartCCAMTemp = 0;
    let totalPartNGAPTemp = 0;

    //
    this.totalEtp = praticiens.reduce((sum, praticien) => sum + parseFloat(praticien.etp), 0);

    return praticiens.map((praticien) => {
      const actsByPractitioner = realisations.filter((act) => act.praticienId === praticien.id);
      let nbActesCCAM = actsByPractitioner
        .filter((act) => act.typeActe === 'CCAM')
        .reduce((sum, act) => sum + act.count, 0);
      let nbActesNGAP = actsByPractitioner
        .filter((act) => act.typeActe === 'NGAP')
        .reduce((sum, act) => sum + act.count, 0);

      // Calculate part of practitioner in percentage terms
      const partPraticienCCAM = this.totalCCAM > 0 ? (nbActesCCAM / this.totalCCAM) * 100 : 0;
      const partPraticienNGAP = this.totalNGAP > 0 ? (nbActesNGAP / this.totalNGAP) * 100 : 0;


      // Mise à jour des totaux cumulés
      totalPartCCAMTemp += partPraticienCCAM;
      totalPartNGAPTemp += partPraticienNGAP;

      this.totalPartCCAM = Math.min(100, totalPartCCAMTemp).toFixed(2) + '%';
      this.totalPartNGAP = Math.min(100, totalPartNGAPTemp).toFixed(2) + '%';


      return {
        nomUsuel: praticien.nom,
        nomPatronymique: praticien.nomPatronymique,
        prenom: praticien.prenom,
        praticienDateDepart: praticien.dateDepart,
        praticienDateArrivee: praticien.dateArrivee,
        etp: praticien.etp,
        nbActesCCAM,
        partPraticienCCAM:  Math.min(100, partPraticienCCAM).toFixed(2) + '%',
        nbActesNGAP,
        partPraticienNGAP:  Math.min(100, partPraticienNGAP).toFixed(2) + '%'
      };
    });
  }


  // Méthode pour appliquer le filtre de statut et générer le résumé
  applyStatutFilter() {
    let filteredPraticiens;


    if (this.selectedStatut === 'PermanentTemporaire') {
      // Filtrer par Permanent et Temporaire
      filteredPraticiens = this.praticiens.filter(
        praticien => praticien.statut === 'Permanent' || praticien.statut === 'Temporaire'
      );
    } else if (this.selectedStatut) {
      // Filtrer par le statut sélectionné
      filteredPraticiens = this.praticiens.filter(
        praticien => praticien.statut === this.selectedStatut
      );

    } else {
      // Pas de filtre, afficher tous les praticiens
      filteredPraticiens = this.praticiens;
    }

    // Filtrer les réalisations correspondantes
    const filteredRealisations = this.realisations.filter(realisation =>
      filteredPraticiens.some(praticien => praticien.id === realisation.praticienId)
    );

    this.totalCCAM = filteredRealisations
      .filter(act => act.typeActe === 'CCAM')
      .reduce((sum, act) => sum + act.count, 0);

    this.totalNGAP = filteredRealisations
      .filter(act => act.typeActe === 'NGAP')
      .reduce((sum, act) => sum + act.count, 0);


    this.filterePractitionerActeSummary = this.generatePractitionerActeSummary(filteredRealisations, filteredPraticiens);
  }

  // Méthode appelée lors du changement de statut dans le filtre
  onStatutChange(event: any) {
    console.log('le event value '+event.value)
    this.selectedStatut = event.value; // Mise à jour du statut sélectionné avec la valeur de l'événement
    this.applyStatutFilter(); // Applique le filtre avec le nouveau statut
  }

  //*********************************************************************************************************************************


  //****************************** Tableau (Personnes qui ont fait des actes dans ce service sans y être affectées)  *******************************************************************************************

   totalEtpExternal : any ;
   totalCCAMExternal : any;
   totalNGAPExternal : any;
   totalPartCCAMExternal : any;
   totalPartNGAPExternal :any;

  generateExternalPractitionerSummary(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) => {
        if (!service) {
          console.error(`Service avec l'ID ${serviceId} introuvable`);
          return;
        }

        const ufsIdsInService = service.ufs.map((uf) => uf.id);

        // Filtrer les réalisations associées au service
        const realisationsInService = this.realisations.filter(
          (realisation) =>
            realisation.serviceId === serviceId &&
            !ufsIdsInService.includes(realisation.ufId)
        );

        console.log("Réalisations dans le service :", realisationsInService);

        // Trouver les praticiens ayant une `ufId` mais qui ne font pas partie du service
        const externalPractitionersWithRealisations = this.praticiens.filter(
          (praticien) =>
            praticien.ufId && // Inclure seulement ceux qui ont une ufId
            !ufsIdsInService.includes(praticien.ufId) && // Exclure ceux qui sont dans le service
            realisationsInService.some(
              (realisation) => realisation.praticienId === praticien.id
            )
        );

        console.log(
          "Praticiens externes ayant des réalisations :",
          externalPractitionersWithRealisations
        );

        // Calculer les totaux globaux à partir des réalisations
        const totalCCAM = realisationsInService
          .filter((act) => act.typeActe === "CCAM")
          .reduce((sum, act) => sum + act.count, 0);

        const totalNGAP = realisationsInService
          .filter((act) => act.typeActe === "NGAP")
          .reduce((sum, act) => sum + act.count, 0);

        const totalEtp = externalPractitionersWithRealisations.reduce(
          (sum, praticien) => sum + parseFloat(praticien.etp || "0"),
          0
        );

        console.log(`Total CCAM: ${totalCCAM}, Total NGAP: ${totalNGAP}, Total ETP: ${totalEtp}`);

        // Générer le tableau des praticiens externes
        this.externalPractitionerSummary = externalPractitionersWithRealisations.map(
          (praticien) => {
            const actsByPractitioner = realisationsInService.filter(
              (act) => act.praticienId === praticien.id
            );

            const nbActesCCAM = actsByPractitioner
              .filter((act) => act.typeActe === "CCAM")
              .reduce((sum, act) => sum + act.count, 0);

            const nbActesNGAP = actsByPractitioner
              .filter((act) => act.typeActe === "NGAP")
              .reduce((sum, act) => sum + act.count, 0);

            // Calculer les parts pour CCAM et NGAP
            const partPraticienCCAM =
              totalCCAM > 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;

            const partPraticienNGAP =
              totalNGAP > 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

            return {
              nomUsuel: praticien.nom,
              nomPatronymique: praticien.nomPatronymique,
              prenom: praticien.prenom,
              praticienDateDepart: praticien.dateDepart,
              praticienDateArrivee: praticien.dateArrivee,
              etp: praticien.etp,
              nbActesCCAM,
              partPraticienCCAM: partPraticienCCAM.toFixed(2) + "%",
              nbActesNGAP,
              partPraticienNGAP: partPraticienNGAP.toFixed(2) + "%",
            };
          }
        );

        // Calculer les totaux cumulés pour CCAM et NGAP
        const recalculatedTotalCCAM = this.externalPractitionerSummary.reduce(
          (sum, summary) => sum + summary.nbActesCCAM,
          0
        );

        const recalculatedTotalNGAP = this.externalPractitionerSummary.reduce(
          (sum, summary) => sum + summary.nbActesNGAP,
          0
        );

        // Corriger les pourcentages pour totalPartCCAMExternal et totalPartNGAPExternal
        const totalPartCCAM = totalCCAM > 0 ? 100 : 0;
        const totalPartNGAP = totalNGAP > 0 ? 100 : 0;

        // Mettre à jour les totaux pour affichage
        this.totalEtpExternal = totalEtp.toFixed(1);
        this.totalCCAMExternal = recalculatedTotalCCAM;
        this.totalNGAPExternal = recalculatedTotalNGAP;
        this.totalPartCCAMExternal = totalPartCCAM.toFixed(2) + "%";
        this.totalPartNGAPExternal = totalPartNGAP.toFixed(2) + "%";

        console.log("Tableau des praticiens externes :", this.externalPractitionerSummary);
      },
      (error) => {
        console.error("Erreur lors de la récupération du service :", error);
      }
    );
  }


//*************************** Personnes qui ont fait des actes dans ce service mais non affectées par la DAM **********************************************************************************************

  nonAffectesDAMSummary: any[] = [];
  totalCCAMNonAffectes: number = 0;
  totalNGAPNonAffectes: number = 0;
  totalPartCCAMNonAffectes: string = "0%";
  totalPartNGAPNonAffectes: string = "0%";

  generateUnassignedPractitionersSummary(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) => {
        if (!service) {
          console.error(`Service avec l'ID ${serviceId} introuvable`);
          return;
        }

        const ufsIdsInService = service.ufs.map((uf) => uf.id);

        // Filtrer les réalisations associées au service
        const realisationsInService = this.realisations.filter(
          (realisation) =>
            realisation.serviceId === serviceId
        );

        console.log("Réalisations dans le service :", realisationsInService);

        // Trouver les praticiens non affectés par la DAM ayant des réalisations
        const unassignedPractitionersWithRealisations = this.praticiens.filter(
          (praticien) =>
            !praticien.ufId &&
            realisationsInService.some(
              (realisation) => realisation.praticienId === praticien.id
            )
        );

        console.log(
          "Personnes non affectées par la DAM ayant réalisé des actes :",
          unassignedPractitionersWithRealisations
        );

        // Calculer uniquement les totaux pour les praticiens non affectés
        let totalCCAM = 0;
        let totalNGAP = 0;

        unassignedPractitionersWithRealisations.forEach((praticien) => {
          const actsByPractitioner = realisationsInService.filter(
            (act) => act.praticienId === praticien.id
          );

          totalCCAM += actsByPractitioner
            .filter((act) => act.typeActe === "CCAM")
            .reduce((sum, act) => sum + act.count, 0);

          totalNGAP += actsByPractitioner
            .filter((act) => act.typeActe === "NGAP")
            .reduce((sum, act) => sum + act.count, 0);
        });

        console.log(`DAM : Total CCAM pour les praticiens non affectés : ${totalCCAM}`);
        console.log(`DAM : Total NGAP pour les praticiens non affectés : ${totalNGAP}`);

        // Calculer les parts cumulées
        let cumulativePartCCAM = 0;
        let cumulativePartNGAP = 0;

        // Générer le tableau des praticiens non affectés
        this.nonAffectesDAMSummary = unassignedPractitionersWithRealisations.map(
          (praticien) => {
            const actsByPractitioner = realisationsInService.filter(
              (act) => act.praticienId === praticien.id
            );

            const nbActesCCAM = actsByPractitioner
              .filter((act) => act.typeActe === "CCAM")
              .reduce((sum, act) => sum + act.count, 0);

            const nbActesNGAP = actsByPractitioner
              .filter((act) => act.typeActe === "NGAP")
              .reduce((sum, act) => sum + act.count, 0);

            // Calculer la part du praticien pour chaque type d'acte
            const partPraticienCCAM = totalCCAM > 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;
            const partPraticienNGAP = totalNGAP > 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

            // Ajouter aux parts cumulées
            cumulativePartCCAM += partPraticienCCAM;
            cumulativePartNGAP += partPraticienNGAP;

            return {
              nomUsuel: praticien.nom,
              nomPatronymique: praticien.nomPatronymique,
              prenom: praticien.prenom,
              specialite: praticien.specialite,
              nbActesCCAM,
              partPraticienCCAM: partPraticienCCAM.toFixed(2) + "%",
              nbActesNGAP,
              partPraticienNGAP: partPraticienNGAP.toFixed(2) + "%",
            };
          }
        );

        // Mettre à jour les totaux globaux
        this.totalCCAMNonAffectes = totalCCAM;
        this.totalNGAPNonAffectes = totalNGAP;
        this.totalPartCCAMNonAffectes = Math.min(cumulativePartCCAM, 100).toFixed(2) + "%";
        this.totalPartNGAPNonAffectes = Math.min(cumulativePartNGAP, 100).toFixed(2) + "%";

        console.log(
          "Tableau des praticiens non affectés par la DAM (corrigé) :",
          this.nonAffectesDAMSummary
        );

        console.log(
          `Totaux corrigés : CCAM = ${this.totalCCAMNonAffectes}, NGAP = ${this.totalNGAPNonAffectes}, Part CCAM = ${this.totalPartCCAMNonAffectes}, Part NGAP = ${this.totalPartNGAPNonAffectes}`
        );
      },
      (error) => {
        console.error("Erreur lors de la récupération du service :", error);
      }
    );
  }



  //*********************************************************************************************************************************


  // Calculate the total for a specific year and type of acte
  getTotalForYearByTypeActe(year: number, typeActe: string): number {
    const summary = typeActe === 'CCAM'
      ? this.filteredActesCCAMSummary
      : typeActe === 'NGAP'
        ? this.filteredActesNGAPSummary
        : this.filteredActesLABOSummary;

    return summary.reduce((sum, acte) => {
      return sum + (year === 2023 ? acte.totalAnneeNMoins1 : acte.totalAnneeN);
    }, 0);
  }

  getPraticiensParActe(acteId: string, typeActe: string): Praticien[] {
    const realisationsFiltrees = this.realisations.filter(
      r => r.acteId === acteId && r.typeActe === typeActe
    );
    return realisationsFiltrees.map(r => this.praticiens.find(p => p.id === r.praticienId)!).filter(Boolean);
  }

  getCount(acteId: string, praticienId: string, typeActe: string): number {
    const realizations = this.realisations.filter(
      (r) => r.acteId === acteId && r.praticienId === praticienId && r.typeActe === typeActe
    );
    return realizations.reduce((sum, r) => sum + (r.count || 1), 0);
  }

  getTotalActeCount(acteId: string): number {
    const realizationsForActe = this.realisations.filter(r => r.acteId === acteId);
    return realizationsForActe.reduce((total, realization) => total + (realization.count || 1), 0);
  }


  onInput(event: Event): string {
    console.log( (event.target as HTMLInputElement).value);
    return (event.target as HTMLInputElement).value;
  }
//
  quickDisplayOverview(): void {
    // Example data for each chart
    this.ccamData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
      datasets: [
        {
          label: 'CCAM',
          data: [0.8, 0.7, 0.6, 0.9, 0.8],
          fill: false,
          borderColor: '#ff5252',
          tension: 0.4,
          borderWidth: 2,      // Augmente l'épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    this.ngapData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
      datasets: [
        {
          label: 'NGAP',
          data: [300, 320, 310, 330, 306],
          fill: false,
          borderColor: '#4caf50',
          tension: 0.4,
          borderWidth: 2,      // Augmente l'épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    this.laboData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
      datasets: [
        {
          label: 'LABO',
          data: [1600, 1620, 1580, 1650, 1600],
          fill: false,
          borderColor: '#00bcd4',
          tension: 0.4,
          borderWidth: 2,      // Augmente l'épaisseur de la ligne
          pointRadius: 4  // Agrandit les points sur la ligne
        }
      ]
    };

    // Options for the chart
    this.chartOptions = {
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        x: { display: false },
        y: { display: false }
      }
    };
  }

  viewPratitionnerDetails(acte: any) {
    alert(`Voir les détails pour l'UF : ${acte.nomUsuel}`);
  }

  viewActeDetails(acte: any) {
    alert(`Voir les détails de l'acte : ${acte.code}`);
  }

  /****************************************************** DANS LE CAS OU LE elementType EST UNE UF **************/

  // REFACTORE  //@TODO  FAIRE EN SORTE QUE LA FONCTION SADAPTE AU ELEMENT TYPE
  generateExternalPractitionerSummaryNEW(elementId: string): void {
    switch (this.elementType) {
      case 'service':
        this.generateExternalPractitionerSummaryForService(this.elementId);
        break;
      case 'uf':
        this.generateExternalPractitionerSummaryForUf(this.elementId);
        break;
      default:
        console.error(`Type d'élément non supporté : ${this.elementType}`);
    }
  }

  private generateExternalPractitionerSummaryForService(serviceId: string): void {
    this.serviceHospitalierService.getServiceHospitalierById(serviceId).subscribe(
      (service) => {
        if (!service) {
          console.error(`Service avec l'ID ${serviceId} introuvable`);
          return;
        }

        const ufsIdsInService = service.ufs.map((uf) => uf.id);

        this.handleExternalPractitionerSummary(serviceId, ufsIdsInService, true);
      },
      (error) => {
        console.error("Erreur lors de la récupération du service :", error);
      }
    );
  }

  private generateExternalPractitionerSummaryForUf(ufId: string): void {
    this.ufService.getUFById(ufId).subscribe(
      (uf) => {
        if (!uf) {
          console.error(`UF avec l'ID ${ufId} introuvable`);
          return;
        }

        this.handleExternalPractitionerSummary(ufId, [ufId], false);
      },
      (error) => {
        console.error("Erreur lors de la récupération de l'UF :", error);
      }
    );
  }
  private handleExternalPractitionerSummary(
    elementId: string,
    ufsIds: string[],
    isService: boolean
  ): void {
    // Filtrer les réalisations associées à l'élément
    const realisationsInElement = this.realisations.filter((realisation) =>
      isService
        ? realisation.serviceId === elementId && !ufsIds.includes(realisation.ufId)
        : realisation.ufId === elementId
    );

    console.log(
      `Réalisations dans le ${isService ? 'service' : 'UF'} :`,
      realisationsInElement
    );

    // Trouver les praticiens externes
    const externalPractitionersWithRealisations = this.praticiens.filter(
      (praticien) =>
        praticien.ufId &&
        !ufsIds.includes(praticien.ufId) &&
        realisationsInElement.some(
          (realisation) => realisation.praticienId === praticien.id
        )
    );

    console.log(
      `Praticiens externes ayant des réalisations :`,
      externalPractitionersWithRealisations
    );

    // Calculer les totaux globaux
    const totalCCAM = realisationsInElement
      .filter((act) => act.typeActe === 'CCAM' && act.count > 0)
      .reduce((sum, act) => sum + act.count, 0);

    const totalNGAP = realisationsInElement
      .filter((act) => act.typeActe === 'NGAP')
      .reduce((sum, act) => sum + act.count, 0);

    const totalEtp = externalPractitionersWithRealisations.reduce(
      (sum, praticien) => sum + parseFloat(praticien.etp || '0'),
      0
    );

    console.log(`Total CCAM: ${totalCCAM}, Total NGAP: ${totalNGAP}, Total ETP: ${totalEtp}`);

    // Générer le tableau des praticiens externes
    this.externalPractitionerSummary = externalPractitionersWithRealisations.map(
      (praticien) => {
        const actsByPractitioner = realisationsInElement.filter(
          (act) => act.praticienId === praticien.id
        );

        const nbActesCCAM = actsByPractitioner
          .filter((act) => act.typeActe === 'CCAM')
          .reduce((sum, act) => sum + act.count, 0);

        const nbActesNGAP = actsByPractitioner
          .filter((act) => act.typeActe === 'NGAP')
          .reduce((sum, act) => sum + act.count, 0);

        const partPraticienCCAM =
          totalCCAM > 0 ? (nbActesCCAM / totalCCAM) * 100 : 0;

        const partPraticienNGAP =
          totalNGAP > 0 ? (nbActesNGAP / totalNGAP) * 100 : 0;

        return {
          nomUsuel: praticien.nom,
          nomPatronymique: praticien.nomPatronymique,
          prenom: praticien.prenom,
          praticienDateDepart: praticien.dateDepart,
          praticienDateArrivee: praticien.dateArrivee,
          etp: praticien.etp,
          nbActesCCAM,
          partPraticienCCAM: partPraticienCCAM.toFixed(2) + '%',
          nbActesNGAP,
          partPraticienNGAP: partPraticienNGAP.toFixed(2) + '%',
        };
      }
    );

    // Mettre à jour les totaux
    this.totalEtpExternal = totalEtp.toFixed(1);
    this.totalCCAMExternal = totalCCAM;
    this.totalNGAPExternal = totalNGAP;
    this.totalPartCCAMExternal = (totalCCAM > 0 ? 100 : 0).toFixed(2) + '%';
    this.totalPartNGAPExternal = (totalNGAP > 0 ? 100 : 0).toFixed(2) + '%';

    console.log(
      `Tableau des praticiens externes pour ${
        isService ? 'service' : 'UF'
      } :`,
      this.externalPractitionerSummary
    );
  }



  /****************************************************** **************************************** **************/
}
