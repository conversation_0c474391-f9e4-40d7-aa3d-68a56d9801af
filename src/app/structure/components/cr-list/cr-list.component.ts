import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Ng<PERSON>lass, <PERSON>For<PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgOptimizedImage } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { Table, TableModule } from "primeng/table";
import { Router } from "@angular/router";
import { BreadcrumbItem } from "../../../core/models/breadcrumbItem";
import { BreadcrumbComponent } from "../../../pages/breadcrumb/breadcrumb.component";
import { Button, ButtonDirective } from "primeng/button";
import { CRModel } from "../../../core/models/structure";
import { CRService } from "../../../core/services/structure/cr.service";
import { LazyLoadEvent } from 'primeng/api';
import { SkeletonModule } from 'primeng/skeleton';
import { TooltipModule } from 'primeng/tooltip';
import { CheckboxModule } from 'primeng/checkbox';

@Component({
  selector: 'app-cr-list',
  standalone: true,
  imports: [
    NgForO<PERSON>,
    Ng<PERSON><PERSON>,
    NgClass,
    AsyncPipe,
    FormsModule,
    TableModule,
    BreadcrumbComponent,
    ButtonDirective,
    Button,
    NgOptimizedImage,
    SkeletonModule,
    TooltipModule,
    CheckboxModule
  ],
  templateUrl: './cr-list.component.html',
  styleUrl: './cr-list.component.scss'
})
export class CRListComponent implements OnInit {
  crs: CRModel[] = []; // Tableau local pour stocker les données
  loading: boolean = false; // Variable pour suivre l'état de chargement
  allCRs: CRModel[] = []; // Tableau pour stocker toutes les données chargées
  filteredCRs: CRModel[] = []; // Tableau pour stocker les données filtrées
  selectedCRs: CRModel[] = []; // Tableau pour stocker les CRs sélectionnés

  // Pagination variables
  currentPage: number = 1;
  pageSize: number = 10;
  first: number = 0;
  rowsPerPageOptions: number[] = [10, 25, 50];

  // fil d'ariane
  breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Structure', url: '/structure' },
    { label: 'Liste des CRs' }
  ];

  // Statistiques globales
  totalCRs: number = 0;
  totalActiveCRs: number = 0;

  constructor(
    private crService: CRService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadAllCRs(); // Charger toutes les données
  }

  // Variables for sorting
  sortField: string = '';
  sortOrder: number = 1;

  // Fonction pour charger toutes les données
  loadAllCRs(): void {
    this.loading = true; // Définir l'état de chargement à true avant l'appel API

    // Charger toutes les données avec tri côté serveur
    this.crService.getAll(undefined, undefined, this.sortField, this.sortOrder).subscribe(
      (paginatedResult) => {
        if (paginatedResult) {
          // Stocker toutes les données chargées
          this.allCRs = paginatedResult.items;
          this.filteredCRs = [...this.allCRs];
          this.crs = this.filteredCRs;
          // Utilise le nombre total d'items retourné par l'API
          this.totalCRs = paginatedResult.totalItems;
          this.totalActiveCRs = paginatedResult.items.filter(cr => cr.isActif).length;
          // Update rowsPerPageOptions based on total items
          this.updateRowsPerPageOptions();
        }
        this.loading = false; // Définir l'état de chargement à false après réception des données
      },
      (error) => {
        console.error('Erreur lors du chargement des CRs :', error);
        this.crs = []; // Gère les erreurs en initialisant un tableau vide
        this.allCRs = [];
        this.filteredCRs = [];
        this.totalCRs = 0;
        this.totalActiveCRs = 0;
        this.loading = false; // Définir l'état de chargement à false en cas d'erreur
      }
    );
  }

  // Fonction pour charger les données
  loadCRs(page: number = this.currentPage, pageSize: number = this.pageSize, sortField: string = this.sortField, sortOrder: number = this.sortOrder): void {
    this.loading = true; // Définir l'état de chargement à true avant l'appel API

    // Charger les données depuis l'API avec tri côté serveur
    this.crService.getAll(page, pageSize, sortField, sortOrder).subscribe(
      (paginatedResult) => {
        if (paginatedResult) {
          // Stocker les données chargées
          this.crs = paginatedResult.items;
          this.allCRs = paginatedResult.items;
          this.filteredCRs = [...this.allCRs];
          // Utilise le nombre total d'items retourné par l'API
          this.totalCRs = paginatedResult.totalItems;
          this.totalActiveCRs = paginatedResult.items.filter(cr => cr.isActif).length;
          // Update rowsPerPageOptions based on total items
          this.updateRowsPerPageOptions();
        }
        this.loading = false; // Définir l'état de chargement à false après réception des données
      },
      (error) => {
        console.error('Erreur lors du chargement des CRs :', error);
        this.crs = []; // Gère les erreurs en initialisant un tableau vide
        this.allCRs = [];
        this.filteredCRs = [];
        this.totalCRs = 0;
        this.totalActiveCRs = 0;
        this.loading = false; // Définir l'état de chargement à false en cas d'erreur
      }
    );
  }


  onInput(event: Event): string {
    const filterValue = (event.target as HTMLInputElement).value;

    // Si nous avons déjà chargé toutes les données, nous pouvons les filtrer côté client
    if (this.allCRs.length > 0) {
      this.applyClientSideFilter(filterValue);
    }

    return filterValue;
  }

  // Méthode pour appliquer le filtre côté client
  applyClientSideFilter(filterValue: string): void {
    if (!filterValue || filterValue.trim() === '') {
      // Si le filtre est vide, utiliser toutes les données
      this.filteredCRs = [...this.allCRs];
    } else {
      // Sinon, filtrer les données
      const filterValueLower = filterValue.toLowerCase();
      this.filteredCRs = this.allCRs.filter(cr => {
        return (
          (cr.libelle && cr.libelle.toLowerCase().includes(filterValueLower)) ||
          (cr.crcode && cr.crcode.toLowerCase().includes(filterValueLower)) ||
          (cr.etab && cr.etab.toLowerCase().includes(filterValueLower))
        );
      });
    }

    // Mettre à jour le nombre total de CRs filtrés
    this.totalCRs = this.filteredCRs.length;

    // Appliquer la pagination
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.crs = this.filteredCRs.slice(startIndex, endIndex);
  }

  viewCRDetails(crId: string): void {
    // Extract the ID from the full URI (e.g., "/api/crs/123" -> "123")
    const id = crId.split('/').pop();
    this.router.navigate(['/structure/cr', id]);
  }

  // Helper method to format dates
  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  }

  // Handle pagination and sorting events from p-table
  onPageChange(event: any) {
    // Calculate the page number (1-based) from the first row index (0-based)
    const page = event.first !== undefined ? Math.floor(event.first / (event.rows || this.pageSize)) + 1 : 1;
    const pageSize = event.rows || this.pageSize;

    // Extract sort field and order if present
    let sortField = this.sortField;
    let sortOrder = this.sortOrder;

    if (event.sortField) {
      sortField = event.sortField;
      sortOrder = event.sortOrder === 1 ? 1 : -1; // Convert PrimeNG's sortOrder to our format

      // Update component state for sorting
      this.sortField = sortField;
      this.sortOrder = sortOrder;
    }

    // Update component state for pagination
    this.currentPage = page;
    this.pageSize = pageSize;
    this.first = event.first || 0;

    // Charger les données depuis l'API avec tri côté serveur
    this.loadCRs(page, pageSize, sortField, sortOrder);
  }

  // Update rowsPerPageOptions based on total items
  updateRowsPerPageOptions() {
    // Default options for small datasets
    let options = [10, 25, 50];

    // For larger datasets, add more options
    if (this.totalCRs > 50) {
      options.push(100);
    }

    if (this.totalCRs > 100) {
      options.push(200);
    }

    if (this.totalCRs > 500) {
      options.push(500);
    }

    // Ensure we don't have options larger than the total items
    this.rowsPerPageOptions = options.filter(option => option <= this.totalCRs || option === 10);
  }

  // Check if all items on the current page are selected
  isAllSelected(): boolean {
    return this.crs.length > 0 && this.selectedCRs.length === this.crs.length;
  }

  // Select or deselect all items on the current page
  toggleSelectAll(event: any): void {
    if (event.checked) {
      // Select all items on the current page
      this.selectedCRs = [...this.crs];
    } else {
      // Deselect all items
      this.selectedCRs = [];
    }
  }

  // Check if a specific CR is selected
  isCRSelected(cr: CRModel): boolean {
    return this.selectedCRs.some(c => c['@id'] === cr['@id']);
  }

  // Toggle selection of a specific CR
  toggleCRSelection(cr: CRModel): void {
    if (this.isCRSelected(cr)) {
      // Remove from selection
      this.selectedCRs = this.selectedCRs.filter(c => c['@id'] !== cr['@id']);
    } else {
      // Add to selection
      this.selectedCRs = [...this.selectedCRs, cr];
    }
  }

  // Export selected CRs to CSV
  exportSelectedCRs(): void {
    if (this.selectedCRs.length === 0) {
      alert('Veuillez sélectionner au moins un CR à exporter');
      return;
    }

    // Create CSV content
    const headers = ['Nom', 'Code', 'Établissement', 'Date de début', 'Statut'];
    const csvContent = [
      headers.join(','),
      ...this.selectedCRs.map(cr => [
        `"${cr.libelle || ''}"`,
        `"${cr.crcode || ''}"`,
        `"${cr.etab || ''}"`,
        `"${this.formatDate(cr.datdeb) || ''}"`,
        `"${cr.isActif ? 'Actif' : 'Inactif'}"`
      ].join(','))
    ].join('\n');

    // Create a Blob with UTF-8 BOM for Excel compatibility
    const BOM = new Uint8Array([0xEF, 0xBB, 0xBF]);
    const blob = new Blob([BOM, csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `export-crs-${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
