# Maintenance Component

A premium-style maintenance page component for the Supra application.

## Features

- Displays a warning/tools icon
- Shows a title "Maintenance en cours"
- Shows a subtitle about the site being under maintenance
- Centered vertically and horizontally
- Gradient background
- Includes animations and optional loader/button
- Fully responsive

## Usage

Simply add the component to your template:

```html
<app-maintenance></app-maintenance>
```

## Implementation Details

- Built as a standalone Angular component
- Styled with Tailwind CSS
- Uses custom animations defined in the project's tailwind.config.js
- No additional dependencies required

## Example

The component can be used in any Angular template, such as app.component.html:

```html
<div *ngIf="isInMaintenance">
  <app-maintenance></app-maintenance>
</div>
<div *ngIf="!isInMaintenance">
  <!-- Regular application content -->
</div>
```

With the corresponding TypeScript:

```typescript
import { Component } from '@angular/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
})
export class AppComponent {
  isInMaintenance = true; // Set this based on your application state
}
```
