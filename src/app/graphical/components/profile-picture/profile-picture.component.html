<!--<div-->
<!--  class="relative w-12 h-12 flex items-center justify-center rounded-full text-white font-bold text-lg"-->
<!--  [ngStyle]="{ background: dynamicBackground }"-->
<!--&gt;-->
<!--  &lt;!&ndash; Initiales &ndash;&gt;-->
<!--  <span>{{ initials }}</span>-->

<!--  &lt;!&ndash; Statut &ndash;&gt;-->
<!--  <span-->
<!--    class="absolute bottom-0 right-0 w-3 h-3 border-2 border-white rounded-full"-->
<!--    [ngClass]="statusColor"-->
<!--  ></span>-->
<!--</div>-->

<div
  class="relative w-12 h-12 flex items-center justify-center rounded-full text-white font-bold text-lg group"
  [ngStyle]="{ background: dynamicBackground }"
>
  <!-- Initiales -->
  <span>{{ initials }}</span>

  <!-- Statut -->
  <span
    class="absolute bottom-0 right-0 w-3 h-3 border-2 border-white rounded-full"
    [ngClass]="statusColor"
  ></span>

  <!-- Tooltip avec Badge -->
  <div
    class="absolute bottom-14 left-1/2 transform -translate-x-1/2 px-2 py-1 text-xs text-indigo-950 bg-gray-100 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 flex items-center space-x-2"
  >
    <i
      [class.pi]="true"
      [class.pi-check-circle]="statusColor === 'bg-green-500'"
      [class.pi-times-circle]="statusColor === 'bg-red-500'"
      [class.pi-info-circle]="statusColor === 'bg-yellow-500'"
      [class.pi-question-circle]="statusColor !== 'bg-green-500' && statusColor !== 'bg-red-500' && statusColor !== 'bg-yellow-500'"
      class="p-text-secondary"
      [ngStyle]="{ fontSize: '1rem', color: getBadgeColor() }"
    ></i>
    <span [innerHTML]="statusTooltip"></span>
  </div>
</div>
