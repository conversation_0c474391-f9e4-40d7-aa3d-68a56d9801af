<!doctype html>
<html class="no-js" lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>supra-v2 documentation</title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="icon" type="image/x-icon" href="../images/favicon.ico">
	   <link rel="stylesheet" href="../styles/style.css">
        <link rel="stylesheet" href="../styles/dark.css">
        <link rel="stylesheet" href="../styles/readthedocs.css">
    </head>
    <body>
          <script>
               // Blocking script to avoid flickering dark mode
               // Dark mode toggle button
               var useDark = window.matchMedia('(prefers-color-scheme: dark)');
               var darkModeState = useDark.matches;
               var $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               var $darkModeToggles = document.querySelectorAll('.dark-mode-switch');
               var darkModeStateLocal = localStorage.getItem('compodoc_darkmode-state');

               function checkToggle(check) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].checked = check;
                    }
               }

               function toggleDarkMode(state) {
                    if (window.localStorage) {
                         localStorage.setItem('compodoc_darkmode-state', state);
                    }

                    checkToggle(state);

                    const hasClass = document.body.classList.contains('dark');

                    if (state) {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.add('dark');
                         }
                         if (!hasClass) {
                              document.body.classList.add('dark');
                         }
                    } else {
                         for (var i = 0; i < $darkModeToggles.length; i++) {
                              $darkModeToggles[i].classList.remove('dark');
                         }
                         if (hasClass) {
                              document.body.classList.remove('dark');
                         }
                    }
               }

               useDark.addEventListener('change', function (evt) {
                    toggleDarkMode(evt.matches);
               });
               if (darkModeStateLocal) {
                    darkModeState = darkModeStateLocal === 'true';
               }
               toggleDarkMode(darkModeState);
          </script>

        <div class="navbar navbar-default navbar-fixed-top d-md-none p-0">
               <div class="d-flex">
                    <a href="../" class="navbar-brand">supra-v2 documentation</a>
                    <button type="button" class="btn btn-default btn-menu ion-ios-menu" id="btn-menu"></button>
               </div>
        </div>

        <div class="xs-menu menu" id="mobile-menu">
                <div id="book-search-input" role="search"><input type="text" placeholder="Type to search"></div>            <compodoc-menu></compodoc-menu>
        </div>

        <div class="container-fluid main">
           <div class="row main">
               <div class="d-none d-md-block menu">
                   <compodoc-menu mode="normal"></compodoc-menu>
               </div>
               <!-- START CONTENT -->
               <div class="content component">
                   <div class="content-data">




<ol class="breadcrumb">
  <li class="breadcrumb-item">Components</li>
  <li class="breadcrumb-item"
  >
  NgapGraphComponent</li>
</ol>

<ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a href="#info" class="nav-link active"
                role="tab" id="info-tab" data-bs-toggle="tab" data-link="info">Info</a>
        </li>
        <li class="nav-item">
            <a href="#source" class="nav-link"
                role="tab" id="source-tab" data-bs-toggle="tab" data-link="source">Source</a>
        </li>
        <li class="nav-item">
            <a href="#templateData" class="nav-link"
                role="tab" id="templateData-tab" data-bs-toggle="tab" data-link="template">Template</a>
        </li>
        <li class="nav-item">
            <a href="#tree" class="nav-link"
                role="tab" id="tree-tab" data-bs-toggle="tab" data-link="dom-tree">DOM Tree</a>
        </li>
</ul>

<div class="tab-content">
    <div class="tab-pane fade active in" id="info"><p class="comment">
    <h3>File</h3>
</p>
<p class="comment">
    <code>src/app/graphical/components/ngap-graph/ngap-graph.component.ts</code>
</p>




    <p class="comment">
        <h3>Implements</h3>
    </p>
    <p class="comment">
                <code>OnInit</code>
    </p>


<section data-compodoc="block-metadata">
    <h3>Metadata</h3>
    <table class="table table-sm table-hover metadata">
        <tbody>












            <tr>
                <td class="col-md-3">selector</td>
                <td class="col-md-9"><code>app-ngap-graph</code></td>
            </tr>

            <tr>
                <td class="col-md-3">standalone</td>
                <td class="col-md-9"><code>true</code></td>
            </tr>





            <tr>
                <td class="col-md-3">templateUrl</td>
                <td class="col-md-9"><code>./ngap-graph.component.html</code></td>
            </tr>



            <tr>
                <td class="col-md-3">styleUrl</td>
                <td class="col-md-9"><code>./ngap-graph.component.scss</code></td>
            </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-index">
    <h3 id="index">Index</h3>
    <table class="table table-sm table-bordered index-table">
        <tbody>
                <tr>
                    <td class="col-md-4">
                        <h6><b>Properties</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                <a href="#ngapBarByDayChart" >ngapBarByDayChart</a>
                            </li>
                            <li>
                                <a href="#ngapChart" >ngapChart</a>
                            </li>
                            <li>
                                <a href="#ngapCombinedChart" >ngapCombinedChart</a>
                            </li>
                            <li>
                                <a href="#ngapCombinedChartV2" >ngapCombinedChartV2</a>
                            </li>
                            <li>
                                <a href="#ngapLineByMonthChart" >ngapLineByMonthChart</a>
                            </li>
                            <li>
                                <a href="#ngapLineChart" >ngapLineChart</a>
                            </li>
                            <li>
                                <a href="#quartilesChart" >quartilesChart</a>
                            </li>
                        </ul>
                    </td>
                </tr>

                <tr>
                    <td class="col-md-4">
                        <h6><b>Methods</b></h6>
                    </td>
                </tr>
                <tr>
                    <td class="col-md-4">
                        <ul class="index-list">
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createBarChartByDay" >createBarChartByDay</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createChart" >createChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createCombinedChart" >createCombinedChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createCombinedChartV2" >createCombinedChartV2</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createLineChart" >createLineChart</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createLineChartByMonth" >createLineChartByMonth</a>
                            </li>
                            <li>
                                    <span class="modifier">Private</span>
                                <a href="#createQuartilesChart" >createQuartilesChart</a>
                            </li>
                            <li>
                                <a href="#ngOnInit" >ngOnInit</a>
                            </li>
                        </ul>
                    </td>
                </tr>





        </tbody>
    </table>
</section>

    <section data-compodoc="block-constructor">
    <h3 id="constructor">Constructor</h3>
        <table class="table table-sm table-bordered">
            <tbody>
                <tr>
                    <td class="col-md-4">
<code>constructor()</code>
                    </td>
                </tr>
                        <tr>
                            <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:18</a></div>
                            </td>
                        </tr>

            </tbody>
        </table>
</section>





    <section data-compodoc="block-methods">

    <h3 id="methods">
        Methods
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createBarChartByDay"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createBarChartByDay</b></span>
                        <a href="#createBarChartByDay"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createBarChartByDay()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="184"
                                    class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:184</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createChart</b></span>
                        <a href="#createChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="32"
                                    class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:32</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCombinedChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createCombinedChart</b></span>
                        <a href="#createCombinedChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCombinedChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="227"
                                    class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:227</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createCombinedChartV2"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createCombinedChartV2</b></span>
                        <a href="#createCombinedChartV2"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createCombinedChartV2()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="304"
                                    class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:304</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createLineChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createLineChart</b></span>
                        <a href="#createLineChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createLineChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="83"
                                    class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:83</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createLineChartByMonth"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createLineChartByMonth</b></span>
                        <a href="#createLineChartByMonth"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createLineChartByMonth()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="138"
                                    class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:138</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="createQuartilesChart"></a>
                    <span class="name">
                            <span class="modifier">Private</span>
                        <span ><b>createQuartilesChart</b></span>
                        <a href="#createQuartilesChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
                        <span class="modifier-icon icon ion-ios-reset"></span>
                    <code>createQuartilesChart()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="431"
                                    class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:431</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngOnInit"></a>
                    <span class="name">
                        <span ><b>ngOnInit</b></span>
                        <a href="#ngOnInit"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
            <tr>
                <td class="col-md-4">
<code>ngOnInit()</code>
                </td>
            </tr>


                    <tr>
                        <td class="col-md-4">
                            <div class="io-line">Defined in <a href="" data-line="22"
                                    class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:22</a></div>
                        </td>
                    </tr>


            <tr>
                <td class="col-md-4">

                        <div class="io-description">
                            <b>Returns : </b>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >void</a></code>

                        </div>
                </td>
            </tr>
        </tbody>
    </table>
</section>
    <section data-compodoc="block-properties">

    <h3 id="inputs">
        Properties
    </h3>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapBarByDayChart"></a>
                    <span class="name">
                        <span ><b>ngapBarByDayChart</b></span>
                        <a href="#ngapBarByDayChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="15" class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:15</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapChart"></a>
                    <span class="name">
                        <span ><b>ngapChart</b></span>
                        <a href="#ngapChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="12" class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:12</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapCombinedChart"></a>
                    <span class="name">
                        <span ><b>ngapCombinedChart</b></span>
                        <a href="#ngapCombinedChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="16" class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:16</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapCombinedChartV2"></a>
                    <span class="name">
                        <span ><b>ngapCombinedChartV2</b></span>
                        <a href="#ngapCombinedChartV2"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="17" class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:17</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapLineByMonthChart"></a>
                    <span class="name">
                        <span ><b>ngapLineByMonthChart</b></span>
                        <a href="#ngapLineByMonthChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="14" class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:14</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="ngapLineChart"></a>
                    <span class="name">
                        <span ><b>ngapLineChart</b></span>
                        <a href="#ngapLineChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="13" class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:13</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
    <table class="table table-sm table-bordered">
        <tbody>
            <tr>
                <td class="col-md-4">
                    <a name="quartilesChart"></a>
                    <span class="name">
                        <span ><b>quartilesChart</b></span>
                        <a href="#quartilesChart"><span class="icon ion-ios-link"></span></a>
                    </span>
                </td>
            </tr>
                <tr>
                    <td class="col-md-4">
                        <i>Type : </i>        <code><a href="https://www.typescriptlang.org/docs/handbook/basic-types.html" target="_blank" >any</a></code>

                    </td>
                </tr>
                    <tr>
                        <td class="col-md-4">
                                <div class="io-line">Defined in <a href="" data-line="18" class="link-to-prism">src/app/graphical/components/ngap-graph/ngap-graph.component.ts:18</a></div>
                        </td>
                    </tr>


        </tbody>
    </table>
</section>

</div>


    <div class="tab-pane fade  tab-source-code" id="source">
        <pre class="line-numbers compodoc-sourcecode"><code class="language-typescript">import {Component, OnInit} from &#x27;@angular/core&#x27;;
import { Chart } from &#x27;chart.js/auto&#x27;;

@Component({
  selector: &#x27;app-ngap-graph&#x27;,
  standalone: true,
  imports: [],
  templateUrl: &#x27;./ngap-graph.component.html&#x27;,
  styleUrl: &#x27;./ngap-graph.component.scss&#x27;
})
export class NgapGraphComponent implements OnInit{
  ngapChart!: any;
  ngapLineChart!: any;
  ngapLineByMonthChart!: any;
  ngapBarByDayChart!: any;
  ngapCombinedChart!: any;
  ngapCombinedChartV2!: any;
  quartilesChart!: any;

  constructor() {
  }
  ngOnInit():void {
    this.createChart();
    this.createLineChart();
    this.createBarChartByDay();
    this.createLineChartByMonth()
    this.createCombinedChart();
    this.createCombinedChartV2();
    this.createQuartilesChart();
  }

  private createChart(): void {
    this.ngapChart &#x3D; new Chart(&#x27;ngapChart&#x27;, {
      type: &#x27;bar&#x27;, // Graphique en barres
      data: {
        labels: [
          &#x27;Consultation spécialiste&#x27;,
          &#x27;Avis consultant hospitalier&#x27;,
          &#x27;Consultant cabinet spécialiste&#x27;,
          &#x27;TELEEXPERTISE NIVEAU 2&#x27;,
          &#x27;Soins infirmiers&#x27;,
          &#x27;Entretien&#x27;,
          &#x27;Télé-consultation&#x27;
        ], // Actes NGAP
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes 2023&#x27;,
            data: [201, 132, 9, 0, 6, 41, 0], // Données pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.7)&#x27;, // Couleur bleue
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;,
            borderWidth: 1,
          },
          {
            label: &#x27;Nombre d\&#x27;actes 2024&#x27;,
            data: [157, 110, 24, 8, 7, 33, 3], // Données pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.7)&#x27;, // Couleur rouge
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;,
            borderWidth: 1,
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes&#x27;
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: &#x27;Nombre d\&#x27;actes NGAP pour Dr Morel Olivier (2023 vs 2024)&#x27;
          }
        }
      }
    });
  }

  private createLineChart(): void {
    this.ngapLineChart &#x3D; new Chart(&#x27;ngapLineChart&#x27;, {
      type: &#x27;line&#x27;, // Graphique en courbe
      data: {
        labels: [
          &#x27;Consultation spécialiste&#x27;,
          &#x27;Avis consultant hospitalier&#x27;,
          &#x27;Consultant cabinet spécialiste&#x27;,
          &#x27;TELEEXPERTISE NIVEAU 2&#x27;,
          &#x27;Soins infirmiers&#x27;,
          &#x27;Entretien&#x27;,
          &#x27;Télé-consultation&#x27;
        ], // Actes NGAP
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes 2023&#x27;,
            data: [201, 132, 9, 0, 6, 41, 0], // Données pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.2)&#x27;, // Couleur bleue (transparente pour le remplissage)
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;, // Bordure bleue
            borderWidth: 2,
            fill: true, // Remplir sous la courbe
            tension: 0.3 // Ajoute un peu de courbe aux lignes
          },
          {
            label: &#x27;Nombre d\&#x27;actes 2024&#x27;,
            data: [157, 110, 24, 8, 7, 33, 3], // Données pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.2)&#x27;, // Couleur rouge (transparente pour le remplissage)
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;, // Bordure rouge
            borderWidth: 2,
            fill: true, // Remplir sous la courbe
            tension: 0.3 // Ajoute un peu de courbe aux lignes
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes&#x27;
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: &#x27;Courbe NGAP - Nombre d\&#x27;actes pour Dr Morel Olivier (2023 vs 2024)&#x27;
          }
        }
      }
    });
  }

  private createLineChartByMonth(): void {
    this.ngapLineByMonthChart &#x3D; new Chart(&#x27;ngapLineChartByMonth&#x27;, {
      type: &#x27;line&#x27;, // Graphique en courbe
      data: {
        labels: [&#x27;Janvier&#x27;, &#x27;Février&#x27;, &#x27;Mars&#x27;, &#x27;Avril&#x27;, &#x27;Mai&#x27;, &#x27;Juin&#x27;, &#x27;Juillet&#x27;, &#x27;Août&#x27;, &#x27;Septembre&#x27;, &#x27;Octobre&#x27;, &#x27;Novembre&#x27;, &#x27;Décembre&#x27;], // Mois de l&#x27;année
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes 2023&#x27;,
            data: [100, 150, 120, 180, 140, 160, 110, 130, 150, 120, 170, 130], // Données fictives pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.2)&#x27;, // Bleu transparent
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;, // Bordure bleue
            borderWidth: 2,
            fill: true,
            tension: 0.3
          },
          {
            label: &#x27;Nombre d\&#x27;actes 2024&#x27;,
            data: [90, 140, 130, 170, 130, 150, 120, 140, 130, 160, 140, 150], // Données fictives pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.2)&#x27;, // Rouge transparent
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;, // Bordure rouge
            borderWidth: 2,
            fill: true,
            tension: 0.3
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes&#x27;
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: &#x27;Comparaison mensuelle des actes NGAP (2023 vs 2024)&#x27;
          }
        }
      }
    });
  }
  private createBarChartByDay(): void {
    this.ngapBarByDayChart &#x3D; new Chart(&#x27;ngapBarChartByDay&#x27;, {
      type: &#x27;bar&#x27;, // Graphique en barres
      data: {
        labels: [&#x27;Lundi&#x27;, &#x27;Mardi&#x27;, &#x27;Mercredi&#x27;, &#x27;Jeudi&#x27;, &#x27;Vendredi&#x27;, &#x27;Samedi&#x27;, &#x27;Dimanche&#x27;], // Jours de la semaine
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes 2023&#x27;,
            data: [10, 20, 15, 25, 18, 12, 8], // Données fictives pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.7)&#x27;, // Bleu
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;,
            borderWidth: 1
          },
          {
            label: &#x27;Nombre d\&#x27;actes 2024&#x27;,
            data: [12, 18, 14, 20, 22, 10, 9], // Données fictives pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.7)&#x27;, // Rouge
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;,
            borderWidth: 1
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes&#x27;
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: &#x27;Comparaison journalière des actes NGAP (2023 vs 2024)&#x27;
          }
        }
      }
    });
  }

  private createCombinedChart(): void {
    this.ngapCombinedChart &#x3D; new Chart(&#x27;ngapCombinedChart&#x27;, {
      type: &#x27;bar&#x27;, // Le graphique combiné commence avec un graphique en barres
      data: {
        labels: [
          &#x27;Janvier&#x27;, &#x27;Février&#x27;, &#x27;Mars&#x27;, &#x27;Avril&#x27;, &#x27;Mai&#x27;, &#x27;Juin&#x27;,
          &#x27;Juillet&#x27;, &#x27;Août&#x27;, &#x27;Septembre&#x27;, &#x27;Octobre&#x27;, &#x27;Novembre&#x27;, &#x27;Décembre&#x27;
        ],
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes par mois 2023&#x27;,
            data: [100, 150, 120, 180, 140, 160, 110, 130, 150, 120, 170, 130], // Données mensuelles 2023
            type: &#x27;line&#x27;, // Ce dataset est en courbe
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;, // Bleu pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.2)&#x27;, // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            yAxisID: &#x27;y&#x27;, // Utilise le premier axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes par mois 2024&#x27;,
            data: [90, 140, 130, 170, 130, 150, 120, 140, 130, 160, 140, 150], // Données mensuelles 2024
            type: &#x27;line&#x27;, // Ce dataset est en courbe
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;, // Rouge pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.2)&#x27;, // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            yAxisID: &#x27;y&#x27;, // Utilise le premier axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes NGAP (par type) 2023&#x27;,
            data: [201, 132, 9, 0, 6, 41, 0], // Données fictives NGAP par type 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.7)&#x27;, // Bleu pour les barres 2023
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;,
            borderWidth: 1,
            yAxisID: &#x27;y1&#x27;, // Utilise le deuxième axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes NGAP (par type) 2024&#x27;,
            data: [157, 110, 24, 8, 7, 33, 3], // Données fictives NGAP par type 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.7)&#x27;, // Rouge pour les barres 2024
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;,
            borderWidth: 1,
            yAxisID: &#x27;y1&#x27;, // Utilise le deuxième axe Y
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            position: &#x27;left&#x27;,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes mensuels&#x27;
            }
          },
          y1: {
            beginAtZero: true,
            position: &#x27;right&#x27;,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes par type NGAP&#x27;
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: &#x27;Comparaison des actes NGAP (2023 vs 2024) - Par mois et par type&#x27;
          }
        }
      }
    });
  }

  private createCombinedChartV2(): void {
    this.ngapCombinedChartV2 &#x3D; new Chart(&#x27;ngapCombinedChartV2&#x27;, {
      type: &#x27;bar&#x27;, // Le graphique combiné commence avec un graphique en barres
      data: {
        labels: [
          &#x27;Consultation spécialiste&#x27;, &#x27;Avis consultant hospitalier&#x27;, &#x27;Consultant cabinet spécialiste&#x27;,
          &#x27;TELEEXPERTISE NIVEAU 2&#x27;, &#x27;Soins infirmiers&#x27;, &#x27;Entretien&#x27;, &#x27;Télé-consultation&#x27;
        ], // Types d&#x27;actes NGAP
        datasets: [
          {
            label: &#x27;Nombre d\&#x27;actes mensuels 2023&#x27;,
            data: [100, 150, 120, 180, 140, 160, 110], // Données mensuelles 2023
            type: &#x27;line&#x27;, // Ce dataset est en courbe
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;, // Bleu pour 2023
            backgroundColor: &#x27;rgba(54, 162, 235, 0.2)&#x27;, // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            tension: 0.3,
            yAxisID: &#x27;y&#x27;, // Utilise le premier axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes mensuels 2024&#x27;,
            data: [90, 140, 130, 170, 130, 150, 120], // Données mensuelles 2024
            type: &#x27;line&#x27;, // Ce dataset est en courbe
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;, // Rouge pour 2024
            backgroundColor: &#x27;rgba(255, 99, 132, 0.2)&#x27;, // Couleur de remplissage
            borderWidth: 2,
            fill: true,
            tension: 0.3,
            yAxisID: &#x27;y&#x27;, // Utilise le premier axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes NGAP 2023&#x27;,
            data: [201, 132, 9, 0, 6, 41, 0], // Données fictives NGAP 2023 par type
            backgroundColor: &#x27;rgba(54, 162, 235, 0.7)&#x27;, // Bleu pour les barres 2023
            borderColor: &#x27;rgba(54, 162, 235, 1)&#x27;,
            borderWidth: 1,
            yAxisID: &#x27;y1&#x27;, // Utilise le deuxième axe Y
          },
          {
            label: &#x27;Nombre d\&#x27;actes NGAP 2024&#x27;,
            data: [157, 110, 24, 8, 7, 33, 3], // Données fictives NGAP 2024 par type
            backgroundColor: &#x27;rgba(255, 99, 132, 0.7)&#x27;, // Rouge pour les barres 2024
            borderColor: &#x27;rgba(255, 99, 132, 1)&#x27;,
            borderWidth: 1,
            yAxisID: &#x27;y1&#x27;, // Utilise le deuxième axe Y
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            position: &#x27;left&#x27;,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes mensuels&#x27;
            }
          },
          y1: {
            beginAtZero: true,
            position: &#x27;right&#x27;,
            title: {
              display: true,
              text: &#x27;Nombre d\&#x27;actes par type NGAP&#x27;
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                let label &#x3D; context.dataset.label || &#x27;&#x27;;

                if (label) {
                  label +&#x3D; &#x27;: &#x27;;
                }
                if (context.parsed.y !&#x3D;&#x3D; null) {
                  label +&#x3D; &#x60;${context.parsed.y} actes&#x60;;
                }
                return label;
              },
              afterLabel: function(context) {
                // Ajout de détails supplémentaires sur les types d&#x27;actes
                let typeActe &#x3D; &#x27;&#x27;;
                switch (context.label) {
                  case &#x27;Consultation spécialiste&#x27;:
                    typeActe &#x3D; &#x27;Actes liés aux consultations avec des spécialistes&#x27;;
                    break;
                  case &#x27;Avis consultant hospitalier&#x27;:
                    typeActe &#x3D; &#x27;Avis ponctuel des consultants hospitaliers&#x27;;
                    break;
                  case &#x27;Consultant cabinet spécialiste&#x27;:
                    typeActe &#x3D; &#x27;Avis ponctuel au cabinet du spécialiste&#x27;;
                    break;
                  case &#x27;TELEEXPERTISE NIVEAU 2&#x27;:
                    typeActe &#x3D; &#x27;Téléexpertise de niveau 2&#x27;;
                    break;
                  case &#x27;Soins infirmiers&#x27;:
                    typeActe &#x3D; &#x27;Actes infirmiers réalisés&#x27;;
                    break;
                  case &#x27;Entretien&#x27;:
                    typeActe &#x3D; &#x27;Entretien avec le patient&#x27;;
                    break;
                  case &#x27;Télé-consultation&#x27;:
                    typeActe &#x3D; &#x27;Téléconsultations réalisées&#x27;;
                    break;
                  default:
                    typeActe &#x3D; &#x27;Actes divers&#x27;;
                    break;
                }
                return typeActe;
              }
            }
          },
          title: {
            display: true,
            text: &#x27;Comparaison des actes NGAP par type pour Dr Morel Olivier (2023 vs 2024)&#x27;
          }
        }
      }
    });
  }

  // debut proposition avec quartile et mediane

  private createQuartilesChart(): void {
    this.quartilesChart &#x3D; new Chart(&#x27;quartilesChart&#x27;, {
      type: &#x27;bar&#x27;,
      data: {
        labels: [&#x27;Périodes de faible activité&#x27;, &#x27;Période d\&#x27;activité moyenne&#x27;, &#x27;Périodes de forte activité&#x27;],
        datasets: [
          {
            label: &#x27;Volume des consultations NGAP&#x27;,
            data: [8, 14, 18],
            backgroundColor: [
              &#x27;rgba(75, 192, 192, 0.7)&#x27;,
              &#x27;rgba(255, 159, 64, 0.7)&#x27;,
              &#x27;rgba(153, 102, 255, 0.7)&#x27;
            ]
          }
        ]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: &#x27;Nombre de consultations NGAP&#x27;
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                let label &#x3D; context.dataset.label || &#x27;&#x27;;
                if (label) {
                  label +&#x3D; &#x27;: &#x27;;
                }
                label +&#x3D; &#x60;${context.parsed.y} consultations&#x60;;
                return label;
              }
            }
          },
          title: {
            display: true,
            text: &#x27;Répartition des consultations NGAP (Quartile et Mediane) pour un praticien/service/pôle&#x27;
          }
        }
      }
    });
  }


}
</code></pre>
    </div>

    <div class="tab-pane fade " id="templateData">
        <pre class="line-numbers"><code class="language-html">&lt;p&gt;ngap-graph works!&lt;/p&gt;
&lt;div class&#x3D;&quot;flex flex-col space-y-6 p-4 bg-gray-50&quot;&gt;
  &lt;!-- Titre ou description --&gt;
  &lt;p class&#x3D;&quot;text-xl font-semibold text-center text-gray-800 mb-6&quot;&gt;Visualisation NGAP - Dr Morel Olivier&lt;/p&gt;

  &lt;!-- Troisième graphique --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ngapLineChartByMonth&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;

  &lt;!-- Quatrième graphique --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ngapBarChartByDay&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;

  &lt;!-- Premier graphique (Bar chart) --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ngapChart&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;

  &lt;!-- Deuxième graphique (Line chart) --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ngapLineChart&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;

  &lt;!-- Cinquième graphique --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ngapCombinedChart&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;

  &lt;!-- Cinquième graphique --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;ngapCombinedChartV2&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;
  &lt;!-- quartile et medianne --&gt;
  &lt;div class&#x3D;&quot;w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg&quot;&gt;
    &lt;canvas id&#x3D;&quot;quartilesChart&quot;&gt;&lt;/canvas&gt;
  &lt;/div&gt;

&lt;/div&gt;
</code></pre>
    </div>


    <div class="tab-pane fade " id="tree">
        <div id="tree-container"></div>
        <div class="tree-legend">
            <div class="title">
                <b>Legend</b>
            </div>
            <div>
                <div class="color htmlelement"></div><span>Html element</span>
            </div>
            <div>
                <div class="color component"></div><span>Component</span>
            </div>
            <div>
                <div class="color directive"></div><span>Html element with directive</span>
            </div>
        </div>
    </div>


</div>

<script src="../js/libs/vis.min.js"></script>
<script src="../js/libs/htmlparser.js"></script>
<script src="../js/libs/deep-iterator.js"></script>
<script>
        var COMPONENT_TEMPLATE = '<div><p>ngap-graph works!</p><div class="flex flex-col space-y-6 p-4 bg-gray-50">  <!-- Titre ou description -->  <p class="text-xl font-semibold text-center text-gray-800 mb-6">Visualisation NGAP - Dr Morel Olivier</p>  <!-- Troisième graphique -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="ngapLineChartByMonth"></canvas>  </div>  <!-- Quatrième graphique -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="ngapBarChartByDay"></canvas>  </div>  <!-- Premier graphique (Bar chart) -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="ngapChart"></canvas>  </div>  <!-- Deuxième graphique (Line chart) -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="ngapLineChart"></canvas>  </div>  <!-- Cinquième graphique -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="ngapCombinedChart"></canvas>  </div>  <!-- Cinquième graphique -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="ngapCombinedChartV2"></canvas>  </div>  <!-- quartile et medianne -->  <div class="w-full h-96 flex justify-center items-center bg-white rounded-lg shadow-lg">    <canvas id="quartilesChart"></canvas>  </div></div></div>'
    var COMPONENTS = [{'name': 'ActeDetailsComponent', 'selector': 'app-acte-details'},{'name': 'AffectationPraticienComponent', 'selector': 'app-affectation-praticien'},{'name': 'AnalyticsDashboardComponent', 'selector': 'app-analytics-dashboard'},{'name': 'AppComponent', 'selector': 'app-root'},{'name': 'BreadcrumbComponent', 'selector': 'app-breadcrumb'},{'name': 'ButtonComponent', 'selector': 'storybook-button'},{'name': 'CcamGraphComponent', 'selector': 'app-ccam-graph'},{'name': 'DashboardComponent', 'selector': 'app-dashboard'},{'name': 'DepartementComponent', 'selector': 'app-departement'},{'name': 'EnseignementComponent', 'selector': 'app-enseignement'},{'name': 'GardeAstreinteComponent', 'selector': 'app-garde-astreinte'},{'name': 'HeaderComponent', 'selector': 'storybook-header'},{'name': 'HeaderComponent', 'selector': 'app-header'},{'name': 'HierarchyTreeComponent', 'selector': 'app-hierarchy-tree'},{'name': 'HomeComponent', 'selector': 'app-home'},{'name': 'InformationPanelComponent', 'selector': 'app-information-panel'},{'name': 'NgapGraphComponent', 'selector': 'app-ngap-graph'},{'name': 'NotFoundComponent', 'selector': 'app-not-found'},{'name': 'OverviewComponent', 'selector': 'app-overview'},{'name': 'PageComponent', 'selector': 'storybook-page'},{'name': 'PoleComponent', 'selector': 'app-pole-list'},{'name': 'ProfilePictureComponent', 'selector': 'app-profile-picture'},{'name': 'ResumeProfilsComponent', 'selector': 'app-resume-profils'},{'name': 'ServiceHospitalierComponent', 'selector': 'app-service-hospitalier'},{'name': 'SidebarComponent', 'selector': 'app-sidebar'},{'name': 'SigapsComponent', 'selector': 'app-sigaps'},{'name': 'SingleActiviteComponent', 'selector': 'app-single-activite'},{'name': 'UfComponent', 'selector': 'app-uf'}];
    var DIRECTIVES = [];
    var ACTUAL_COMPONENT = {'name': 'NgapGraphComponent'};
</script>
<script src="../js/tree.js"></script>

















                   </div><div class="search-results">
    <div class="has-results">
        <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
        <ul class="search-results-list"></ul>
    </div>
    <div class="no-results">
        <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>
    </div>
</div>
</div>
               <!-- END CONTENT -->
           </div>
       </div>

          <label class="dark-mode-switch">
               <input type="checkbox">
               <span class="slider">
                    <svg class="slider-icon" viewBox="0 0 24 24" fill="none" height="20" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" width="20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"></path>
                    </svg>
               </span>
          </label>

       <script>
            var COMPODOC_CURRENT_PAGE_DEPTH = 1;
            var COMPODOC_CURRENT_PAGE_CONTEXT = 'component';
            var COMPODOC_CURRENT_PAGE_URL = 'NgapGraphComponent.html';
            var MAX_SEARCH_RESULTS = 15;
       </script>

       <script>
               $darkModeToggleSwitchers = document.querySelectorAll('.dark-mode-switch input');
               checkToggle(darkModeState);
               if ($darkModeToggleSwitchers.length > 0) {
                    for (var i = 0; i < $darkModeToggleSwitchers.length; i++) {
                         $darkModeToggleSwitchers[i].addEventListener('change', function (event) {
                              darkModeState = !darkModeState;
                              toggleDarkMode(darkModeState);
                         });
                    }
               }
          </script>

       <script src="../js/libs/custom-elements.min.js"></script>
       <script src="../js/libs/lit-html.js"></script>

       <script src="../js/menu-wc.js" defer></script>
       <script nomodule src="../js/menu-wc_es5.js" defer></script>

       <script src="../js/libs/bootstrap-native.js"></script>

       <script src="../js/libs/es6-shim.min.js"></script>
       <script src="../js/libs/EventDispatcher.js"></script>
       <script src="../js/libs/promise.min.js"></script>

       <script src="../js/compodoc.js"></script>

       <script src="../js/tabs.js"></script>
       <script src="../js/menu.js"></script>
       <script src="../js/libs/clipboard.min.js"></script>
       <script src="../js/libs/prism.js"></script>
       <script src="../js/sourceCode.js"></script>
          <script src="../js/search/search.js"></script>
          <script src="../js/search/lunr.min.js"></script>
          <script src="../js/search/search-lunr.js"></script>
          <script src="../js/search/search_index.js"></script>
       <script src="../js/lazy-load-graphs.js"></script>


    </body>
</html>
