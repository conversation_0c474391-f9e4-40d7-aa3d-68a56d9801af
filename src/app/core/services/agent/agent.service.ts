import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { AgentModel } from '../../models/activite/ActiviteModel';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../auth/auth.service';

/**
 * Interface for the agent search response
 */
interface AgentSearchResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': AgentModel[];
}

/**
 * Service for fetching and searching agents
 */
@Injectable({
  providedIn: 'root'
})
export class AgentService {
  private readonly API_URL = `${environment.api_url}/api/agents`;

  // BehaviorSubject to store all agents
  private allAgents = new BehaviorSubject<AgentModel[]>([]);

  // Observable to expose all agents
  readonly allAgents$ = this.allAgents.asObservable();

  // Flag to track if initial data has been loaded
  private dataLoaded = false;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {
    // Load all agents when service is initialized
    this.loadAllAgents();
  }

  /**
   * Load all agents from the API
   * This is called once when the service is initialized
   */
  private loadAllAgents(): void {
    // Only load if not already loaded
    if (this.dataLoaded) {
      return;
    }

    // Set a reasonable limit for the number of agents to fetch
    // Adjust this based on your application's needs
    const params = new HttpParams()
      .set('itemsPerPage', '1000')
      .set('isActif', 'true');

    this.http.get<AgentSearchResponse>(this.API_URL, { params }).pipe(
      map(response => response.member || []),
      catchError(error => {
        console.error('Error loading all agents:', error);
        return of([]);
      })
    ).subscribe(agents => {
      this.allAgents.next(agents);
      this.dataLoaded = true;
      console.log(`Loaded ${agents.length} agents for client-side filtering`);
    });
  }

  /**
   * Get all agents (for autocomplete dropdowns)
   * @param limit Maximum number of results to return
   * @returns Observable of all agents
   */
  getAllAgents(limit: number = 1000): Observable<AgentModel[]> {
    // Ensure data is loaded
    if (!this.dataLoaded) {
      // If data isn't loaded yet, load it and then return all agents
      this.loadAllAgents();
      return this.allAgents$.pipe(
        map(agents => agents.slice(0, limit))
      );
    }

    // Return all agents
    return of(this.allAgents.getValue().slice(0, limit));
  }

  /**
   * Search for agents by name
   * @param query The search query (name, email, etc.)
   * @param limit Maximum number of results to return
   * @returns Observable of agent search results
   */
  searchAgents(query: string, limit: number = 10): Observable<AgentModel[]> {
    // If query is empty or too short, return all agents for dropdown
    if (!query || query.trim().length < 3) {
      return this.getAllAgents(limit);
    }

    // Ensure data is loaded
    if (!this.dataLoaded) {
      // If data isn't loaded yet, load it and then perform the search
      this.loadAllAgents();
      // Return the first set of filtered results when they're available
      return this.allAgents$.pipe(
        map(agents => this.filterAgents(agents, query, limit))
      );
    }

    // Filter agents client-side
    return of(this.filterAgents(this.allAgents.getValue(), query, limit));
  }

  /**
   * Filter agents based on search query
   * @param agents Array of agents to filter
   * @param query Search query
   * @param limit Maximum number of results to return
   * @returns Filtered array of agents
   */
  private filterAgents(agents: AgentModel[], query: string, limit: number): AgentModel[] {
    const lowerQuery = query.toLowerCase().trim();

    // Filter agents based on various properties
    return agents.filter(agent => {
      // Check each property that might contain the search term
      return (
        (agent.email && agent.email.toLowerCase().includes(lowerQuery)) ||
        (agent.nom && agent.nom.toLowerCase().includes(lowerQuery)) ||
        (agent.prenom && agent.prenom.toLowerCase().includes(lowerQuery)) ||
        (agent.hrUser && agent.hrUser.toLowerCase().includes(lowerQuery)) ||
        (agent.titre && agent.titre.toLowerCase().includes(lowerQuery)) ||
        (agent.categorie && agent.categorie.toLowerCase().includes(lowerQuery)) ||
        (agent.fullName && agent.fullName.toLowerCase().includes(lowerQuery))
      );
    }).slice(0, limit); // Limit the number of results
  }

  /**
   * Get an agent by ID
   * @param id The agent ID
   * @returns Observable of the agent
   */
  getAgentById(id: string): Observable<AgentModel> {
    // First check if we have the agent in our loaded data
    if (this.dataLoaded) {
      const agents = this.allAgents.getValue();
      const agent = agents.find(a => a['@id'] === id || a['@id'] === `/api/agents/${id}`);
      if (agent) {
        return of(agent);
      }
    }

    // Get the authentication token
    const token = this.authService.getToken();

    // Prepare headers with the authentication token
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    // If not found in loaded data, fetch from API with authentication
    return this.http.get<AgentModel>(`${this.API_URL}/${id}`, { headers }).pipe(
      catchError(error => {
        console.error(`Error fetching agent with ID ${id}:`, error);
        throw error;
      })
    );
  }

  /**
   * Refresh the agents data
   * This can be called to force a refresh of the data
   */
  refreshAgents(): void {
    this.dataLoaded = false;
    this.loadAllAgents();
  }
}
