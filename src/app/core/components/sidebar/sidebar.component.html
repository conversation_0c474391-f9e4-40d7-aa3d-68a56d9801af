
<!-- NOUVELLE SIDEBAR MODERNE AVEC COLLAPSE/EXPAND -->
<!-- ============================================== -->

<!-- Off-canvas menu for mobile -->
<!-- ========================== -->

@if (isSidebarOpen){
  <div class="fixed inset-0 flex z-50 lg:hidden" role="dialog" aria-modal="true">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-75" aria-hidden="true"></div>
    <div class="relative flex flex-col h-full w-80 max-w-sm bg-cyan-700 transition-all duration-300 ease-in-out mobile-sidebar">
      <div class="absolute top-0 right-0 -mr-12 pt-2">
        <button
          (click)="closeSidebar()"
          type="button"
          class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white hover:bg-white hover:bg-opacity-10 transition-colors duration-200"
          aria-label="Fermer la sidebar">
          <span class="sr-only">Fermer la sidebar</span>
          <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="flex-shrink-0 flex items-center px-4 pt-5">
        <!-- Icône SVG -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="h-10 w-10 text-cyan-300"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
          />
        </svg>
        <!-- Titre -->
        <h1 class="text-white text-2xl font-bold tracking-wide ml-3">Supra</h1>

      </div>
      <nav class="mt-5 flex-1 flex flex-col divide-y divide-cyan-800 overflow-y-auto scrollbar-hide" aria-label="Sidebar">
        <div class="px-2 space-y-1">
          <!-- Current: "bg-cyan-800 text-white", Default: "text-cyan-100 hover:text-white hover:bg-cyan-600" -->
<!--          <a href="#" class="bg-cyan-800 text-white group flex items-center px-2 py-2 text-base font-medium rounded-md" aria-current="page">-->
<!--            &lt;!&ndash; Heroicon name: outline/home &ndash;&gt;-->
<!--            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">-->
<!--              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />-->
<!--            </svg>-->
<!--            Accueil-->
<!--          </a>-->

          <a
            routerLink="/"
            routerLinkActive="bg-cyan-800 text-white"
            [routerLinkActiveOptions]="{ exact: true }"
            class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
            [ngClass]="{
              'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/', true))
            }"
          >
            <!-- Icône -->
            <svg
              class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
            Accueil
          </a>


<!--          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">-->
<!--            &lt;!&ndash; Heroicon name: outline/clock &ndash;&gt;-->
<!--            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">-->
<!--              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />-->
<!--            </svg>-->
<!--            History-->
<!--          </a>-->



<!--          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">-->
<!--            &lt;!&ndash; Heroicon name: outline/scale &ndash;&gt;-->
<!--            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">-->
<!--              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />-->
<!--            </svg>-->
<!--            Balances-->
<!--          </a>-->
          <!-- Masqué sur mobile - ancien lien activités -->
          <!--
          <a
            routerLink="/graphic/acte-list"
            routerLinkActive="bg-cyan-800 text-white"
            [routerLinkActiveOptions]="{ exact: true }"
            class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
            [ngClass]="{
              'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/graphic/acte-list', true))
            }"
          >
            <svg
              class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
              />
            </svg>
            Activités
          </a>
          -->

          <a
            routerLink="/activites"
            routerLinkActive="bg-cyan-800 text-white"
            [routerLinkActiveOptions]="{ exact: false }"
            class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
            [ngClass]="{
              'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/activites', false))
            }"
          >
            <!-- Icône -->
            <svg
              class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605"
              />
            </svg>
            Activités
          </a>

<!--          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">-->
<!--            &lt;!&ndash; Heroicon name: outline/credit-card &ndash;&gt;-->
<!--            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">-->
<!--              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />-->
<!--            </svg>-->
<!--            Cards-->
<!--          </a>-->
          <a
            routerLink="/praticien/praticien-list"
            routerLinkActive="bg-cyan-800 text-white"
            [routerLinkActiveOptions]="{ exact: true }"
            class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
            [ngClass]="{
              'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/praticien/praticien-list', true))
            }"
          >
            <!-- Icône -->
            <svg
              class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="2"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
            Praticiens
          </a>

<!--          <a href="#" class="text-cyan-100 hover:text-white hover:bg-cyan-600 group flex items-center px-2 py-2 text-base font-medium rounded-md">-->
<!--            &lt;!&ndash; Heroicon name: outline/user-group &ndash;&gt;-->
<!--            <svg class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">-->
<!--              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />-->
<!--            </svg>-->
<!--            Recipients-->
<!--          </a>-->

          <!-- Structure Menu -->
          <div>
            <!-- Structure Menu Header -->
            <button
              (click)="toggleStructureMenu()"
              class="w-full text-white group flex items-center justify-between px-2 py-2 text-sm leading-6 font-medium rounded-md"
              [ngClass]="{
                'bg-cyan-800': isStructureMenuOpen,
                'text-cyan-100 hover:text-white hover:bg-cyan-600': !isStructureMenuOpen
              }"
            >
              <div class="flex items-center">
                <!-- Structure Icon -->
                <svg
                  class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z"
                  />
                </svg>
                Structure
              </div>
              <!-- Dropdown Arrow -->
              <svg
                class="h-5 w-5 text-cyan-200"
                [ngClass]="{'transform rotate-180': isStructureMenuOpen}"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>

            <!-- Structure Submenu -->
            <div *ngIf="isStructureMenuOpen" class="mt-1 ml-6 space-y-1">
              <!-- Pole -->
              <a
                routerLink="/structure/pole-list"
                routerLinkActive="bg-cyan-800 text-white"
                [routerLinkActiveOptions]="{ exact: true }"
                class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
                [ngClass]="{
                  'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/structure/pole-list', true))
                }"
              >
                <svg
                  class="mr-4 flex-shrink-0 h-5 w-5 text-cyan-200"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"
                  />
                </svg>
                Pôles
              </a>

              <!-- CR -->
              <a
                routerLink="/structure/cr-list"
                routerLinkActive="bg-cyan-800 text-white"
                [routerLinkActiveOptions]="{ exact: true }"
                class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
                [ngClass]="{
                  'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/structure/cr-list', true))
                }"
              >
                <svg
                  class="mr-4 flex-shrink-0 h-5 w-5 text-cyan-200"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 010 3.75H5.625a1.875 1.875 0 010-3.75z"
                  />
                </svg>
                Centres de Responsabilité
              </a>

              <!-- Service -->
              <a
                routerLink="/structure/service-list"
                routerLinkActive="bg-cyan-800 text-white"
                [routerLinkActiveOptions]="{ exact: true }"
                class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
                [ngClass]="{
                  'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/structure/service-list', true))
                }"
              >
                <svg
                  class="mr-4 flex-shrink-0 h-5 w-5 text-cyan-200"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"
                  />
                </svg>
                Services
              </a>

              <!-- UF -->
              <a
                routerLink="/structure/uf-list"
                routerLinkActive="bg-cyan-800 text-white"
                [routerLinkActiveOptions]="{ exact: true }"
                class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
                [ngClass]="{
                  'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/structure/uf-list', true))
                }"
              >
                <svg
                  class="mr-4 flex-shrink-0 h-5 w-5 text-cyan-200"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"
                  />
                </svg>
                UFs
              </a>
            </div>
          </div>
        </div>
        <div class="mt-6 pt-6">
          <div class="px-2 space-y-1">
<!--            <a href="#" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">-->
<!--              &lt;!&ndash; Heroicon name: outline/cog &ndash;&gt;-->
<!--              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">-->
<!--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />-->
<!--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />-->
<!--              </svg>-->
<!--              Settings-->
<!--            </a>-->

<!--            <a href="#" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">-->
<!--              &lt;!&ndash; Heroicon name: outline/question-mark-circle &ndash;&gt;-->
<!--              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">-->
<!--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />-->
<!--              </svg>-->
<!--              Help-->
<!--            </a>-->

<!--            <a href="#" class="group flex items-center px-2 py-2 text-base font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">-->
<!--              &lt;!&ndash; Heroicon name: outline/shield-check &ndash;&gt;-->
<!--              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">-->
<!--                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />-->
<!--              </svg>-->
<!--              Privacy-->
<!--            </a>-->
            <a
              routerLink="/sigaps"
              routerLinkActive="bg-cyan-800 text-white"
              [routerLinkActiveOptions]="{ exact: true }"
              class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
              [ngClass]="{
              'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('/sigaps', true))
            }"
            >
              <!-- Icône -->
              <svg
                class="mr-4 flex-shrink-0 h-6 w-6 text-cyan-200"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                />
              </svg>
              SIGAPS
            </a>





            <a
              routerLink="garde-et-astreinte"
              routerLinkActive="bg-cyan-800 text-white"
              [routerLinkActiveOptions]="{ exact: true }"
              class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
              [ngClass]="{
                'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('garde-et-astreinte', true))
              }"
            >
              <!-- Icône -->
              <svg
                class="mr-4 h-6 w-6 text-cyan-200"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"
                />
              </svg>
              Garde et Astreinte
            </a>

            <a
              routerLink="liberale"
              routerLinkActive="bg-cyan-800 text-white"
              [routerLinkActiveOptions]="{ exact: true }"
              class="text-white group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md"
              [ngClass]="{
                'text-cyan-100 hover:text-white hover:bg-cyan-600': !(router.isActive('liberale', true))
              }"
            >
              <!-- Icône -->
              <svg
                class="mr-4 h-6 w-6 text-cyan-200"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H4.5m2.25 0v3m0 0v.75A.75.75 0 0 1 6 9h-.75m0 0h-.375c0-.621.504-1.125 1.125-1.125H6.75m0 0V7.5m0 0V6h.75m0 1.5v.75A.75.75 0 0 1 6.75 9h-.75m0 0h-.375c0-.621.504-1.125 1.125-1.125H7.5"
                />
              </svg>
              Libérale
            </a>
          </div>
        </div>

        <!-- Tiret deux-->
        <div class="mt-6 pt-6">
          <div class="px-2 space-y-1">
            <a *ngIf="hasAdminRole()" routerLink="/referent/import" class="group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">
              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Import
            </a>

            <!-- Masqué sur mobile - lien réunion -->
            <!--
            <a href="assets/documents/slides_reunion_metier_06_02_2025.pdf" target="_blank" class="group flex items-center px-2 py-2 text-sm leading-6 font-medium rounded-md text-cyan-100 hover:text-white hover:bg-cyan-600">
              <svg class="mr-4 h-6 w-6 text-cyan-200" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M7 3h10a2 2 0 012 2v14a2 2 0 01-2 2H7a2 2 0 01-2-2V5a2 2 0 012-2zm2 6h6m-6 4h6m-6 4h4" />
              </svg>
              Réunion du 06
            </a>
            -->
          </div>
        </div>
      </nav>
    </div>

    <div class="flex-shrink-0 w-14" aria-hidden="true">
      <!-- Dummy element to force sidebar to shrink to fit close icon -->
    </div>
  </div>
}

<!-- Sidebar Desktop avec largeur dynamique -->
<div class="hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 transition-all duration-300 ease-in-out"
     [ngClass]="{
       'lg:w-64': !isCollapsed,
       'lg:w-16': isCollapsed
     }">
  <!-- Sidebar container avec animations -->
  <div class="flex flex-col h-full bg-gradient-to-b from-cyan-700 to-cyan-800 shadow-xl border-r border-cyan-600">
    <!-- Header avec logo, titre et bouton toggle -->
    <div class="flex items-center justify-between flex-shrink-0 px-4 pt-5 pb-3 relative">

      <!-- Mode normal (non collapsed) -->
      <div *ngIf="!isCollapsed" class="flex items-center justify-between w-full">
        <div class="flex items-center min-w-0">
          <!-- Logo avec animation -->
          <div class="flex-shrink-0 transition-transform duration-300 hover:scale-110">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="h-10 w-10 text-cyan-300 drop-shadow-lg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
              />
            </svg>
          </div>

          <!-- Titre -->
          <div class="ml-3 min-w-0">
            <h1 class="text-white text-2xl font-bold tracking-wide whitespace-nowrap">Supra</h1>
          </div>
        </div>

        <!-- Bouton Toggle en mode normal -->
        <button
          (click)="toggleCollapse()"
          class="flex-shrink-0 p-2 rounded-lg text-cyan-200 hover:text-white hover:bg-cyan-600 focus:outline-none focus:ring-2 focus:ring-cyan-400 transition-all duration-200 group"
          [attr.aria-label]="'Réduire la sidebar'"
          [title]="'Réduire la sidebar'">
          <svg
            class="h-5 w-5 transition-transform duration-300 group-hover:scale-110"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
          </svg>
        </button>
      </div>

      <!-- Mode collapsed - Design compact et élégant -->
      <div *ngIf="isCollapsed" class="w-full flex flex-col items-center space-y-3">
        <!-- Logo compact avec background subtil -->
        <div class="relative">
          <div class="absolute inset-0 bg-cyan-600 bg-opacity-20 rounded-lg blur-sm"></div>
          <div class="relative flex-shrink-0 transition-transform duration-300 hover:scale-110 p-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="h-8 w-8 text-cyan-300 drop-shadow-lg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
              />
            </svg>
          </div>
        </div>

        <!-- Bouton Toggle en mode collapsed avec background -->
        <button
          (click)="toggleCollapse()"
          class="relative p-2 rounded-full text-cyan-200 hover:text-white hover:bg-cyan-600 focus:outline-none focus:ring-2 focus:ring-cyan-400 transition-all duration-200 group bg-cyan-700 bg-opacity-50 backdrop-blur-sm border border-cyan-600 border-opacity-30"
          [attr.aria-label]="'Développer la sidebar'"
          [title]="'Développer la sidebar'">
          <!-- Background glow effect -->
          <div class="absolute inset-0 bg-cyan-400 bg-opacity-10 rounded-full blur-sm group-hover:bg-opacity-20 transition-all duration-200"></div>
          <svg
            class="relative h-4 w-4 transition-transform duration-300 group-hover:scale-110 rotate-180"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
          </svg>
        </button>
      </div>
    </div>
    <!-- Navigation principale -->
    <nav class="mt-4 flex-1 flex flex-col overflow-y-auto scrollbar-hide" aria-label="Navigation principale">
      <div class="px-3 space-y-3">
        <!-- Accueil -->
        <a
          routerLink="/"
          routerLinkActive="bg-cyan-800 text-white shadow-lg"
          [routerLinkActiveOptions]="{ exact: true }"
          class="group flex items-center text-sm leading-6 font-medium rounded-xl transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md text-white"
          [ngClass]="{
            'px-4 py-4': !isCollapsed,
            'px-2 py-4 justify-center': isCollapsed,
            'bg-cyan-800 shadow-lg': router.isActive('/', true),
            'text-cyan-100 hover:text-white': !router.isActive('/', true)
          }"
          [title]="isCollapsed ? 'Accueil' : ''"
        >
          <!-- Icône avec animation -->
          <svg
            class="flex-shrink-0 h-6 w-6 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
            [ngClass]="{
              'mr-4': !isCollapsed,
              'mr-0': isCollapsed
            }"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
            />
          </svg>
          <!-- Texte avec transition -->
          <span class="transition-all duration-300 ease-in-out overflow-hidden whitespace-nowrap"
                [ngClass]="{
                  'opacity-100 max-w-none': !isCollapsed,
                  'opacity-0 max-w-0': isCollapsed
                }">
            Accueil
          </span>
        </a>







        <!-- Activités v2 -->
        <a
          routerLink="/activites"
          routerLinkActive="bg-cyan-800 text-white shadow-lg"
          [routerLinkActiveOptions]="{ exact: false }"
          class="group flex items-center text-sm leading-6 font-medium rounded-xl transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md text-white"
          [ngClass]="{
            'px-4 py-4': !isCollapsed,
            'px-2 py-4 justify-center': isCollapsed,
            'bg-cyan-800 shadow-lg': router.isActive('/activites', false),
            'text-cyan-100 hover:text-white': !router.isActive('/activites', false)
          }"
          [title]="isCollapsed ? 'Activités v2' : ''"
        >
          <!-- Icône avec animation -->
          <svg
            class="flex-shrink-0 h-6 w-6 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
            [ngClass]="{
              'mr-4': !isCollapsed,
              'mr-0': isCollapsed
            }"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605"
            />
          </svg>
          <!-- Texte avec transition -->
          <span class="transition-all duration-300 ease-in-out overflow-hidden whitespace-nowrap"
                [ngClass]="{
                  'opacity-100 max-w-none': !isCollapsed,
                  'opacity-0 max-w-0': isCollapsed
                }">
            Activités v2
          </span>
        </a>


        <!-- Praticiens -->
        <a
          routerLink="/praticien/praticien-list"
          routerLinkActive="bg-cyan-800 text-white shadow-lg"
          [routerLinkActiveOptions]="{ exact: true }"
          class="group flex items-center text-sm leading-6 font-medium rounded-xl transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md text-white"
          [ngClass]="{
            'px-4 py-4': !isCollapsed,
            'px-2 py-4 justify-center': isCollapsed,
            'bg-cyan-800 shadow-lg': router.isActive('/praticien/praticien-list', true),
            'text-cyan-100 hover:text-white': !router.isActive('/praticien/praticien-list', true)
          }"
          [title]="isCollapsed ? 'Praticiens' : ''"
          appFeatureFlag="praticiens"
          disableMode="blur"
        >
          <!-- Icône avec animation -->
          <svg
            class="flex-shrink-0 h-6 w-6 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
            [ngClass]="{
              'mr-4': !isCollapsed,
              'mr-0': isCollapsed
            }"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            />
          </svg>
          <!-- Texte avec transition -->
          <span class="transition-all duration-300 ease-in-out overflow-hidden whitespace-nowrap"
                [ngClass]="{
                  'opacity-100 max-w-none': !isCollapsed,
                  'opacity-0 max-w-0': isCollapsed
                }">
            Praticiens
          </span>
        </a>


        <!-- Structure Menu -->
        <div appFeatureFlag="structure" disableMode="blur">
          <!-- Structure Menu Header -->
          <button
            (click)="toggleStructureMenu()"
            class="w-full group flex items-center text-sm leading-6 font-medium rounded-xl transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md text-white"
            [ngClass]="{
              'bg-cyan-800 shadow-lg': isStructureMenuOpen,
              'px-4 py-4 justify-between': !isCollapsed,
              'px-2 py-4 justify-center': isCollapsed,
              'text-cyan-100 hover:text-white': !isStructureMenuOpen
            }"
            [title]="isCollapsed ? 'Structure' : ''"
          >
            <div class="flex items-center min-w-0">
              <!-- Structure Icon -->
              <svg
                class="flex-shrink-0 h-6 w-6 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
                [ngClass]="{
                  'mr-4': !isCollapsed,
                  'mr-0': isCollapsed
                }"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z"
                />
              </svg>
              <!-- Texte avec transition -->
              <span class="transition-all duration-300 ease-in-out overflow-hidden whitespace-nowrap"
                    [ngClass]="{
                      'opacity-100 max-w-none': !isCollapsed,
                      'opacity-0 max-w-0': isCollapsed
                    }">
                Structure
              </span>
            </div>
            <!-- Dropdown Arrow (seulement visible quand pas collapsed) -->
            <svg
              class="h-5 w-5 text-cyan-200 transition-all duration-300 group-hover:text-white"
              [ngClass]="{
                'transform rotate-180': isStructureMenuOpen,
                'opacity-100': !isCollapsed,
                'opacity-0': isCollapsed
              }"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Structure Submenu avec animation -->
          <div
            class="overflow-hidden transition-all duration-300 ease-in-out"
            [ngClass]="{
              'max-h-96 opacity-100 mt-2': isStructureMenuOpen && !isCollapsed,
              'max-h-0 opacity-0 mt-0': !isStructureMenuOpen || isCollapsed
            }">
            <div class="ml-6 space-y-2">
              <!-- Pôles -->
              <a
                routerLink="/structure/pole-list"
                routerLinkActive="bg-cyan-800 text-white shadow-lg"
                [routerLinkActiveOptions]="{ exact: true }"
                class="group flex items-center px-3 py-2 text-sm leading-6 font-medium rounded-lg transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md hover:translate-x-1 text-white"
                [ngClass]="{
                  'text-cyan-100': !(router.isActive('/structure/pole-list', true)),
                  'bg-cyan-800 shadow-lg': router.isActive('/structure/pole-list', true)
                }"
              >
                <svg
                  class="mr-3 flex-shrink-0 h-5 w-5 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"
                  />
                </svg>
                <span class="transition-all duration-200">Pôles</span>
              </a>

              <!-- Centres de Responsabilité -->
              <a
                routerLink="/structure/cr-list"
                routerLinkActive="bg-cyan-800 text-white shadow-lg"
                [routerLinkActiveOptions]="{ exact: true }"
                class="group flex items-center px-3 py-2 text-sm leading-6 font-medium rounded-lg transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md hover:translate-x-1 text-white"
                [ngClass]="{
                  'text-cyan-100': !(router.isActive('/structure/cr-list', true)),
                  'bg-cyan-800 shadow-lg': router.isActive('/structure/cr-list', true)
                }"
              >
                <svg
                  class="mr-3 flex-shrink-0 h-5 w-5 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 010 3.75H5.625a1.875 1.875 0 010-3.75z"
                  />
                </svg>
                <span class="transition-all duration-200">Centres de Responsabilité</span>
              </a>

              <!-- Services -->
              <a
                routerLink="/structure/service-list"
                routerLinkActive="bg-cyan-800 text-white shadow-lg"
                [routerLinkActiveOptions]="{ exact: true }"
                class="group flex items-center px-3 py-2 text-sm leading-6 font-medium rounded-lg transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md hover:translate-x-1 text-white"
                [ngClass]="{
                  'text-cyan-100': !(router.isActive('/structure/service-list', true)),
                  'bg-cyan-800 shadow-lg': router.isActive('/structure/service-list', true)
                }"
              >
                <svg
                  class="mr-3 flex-shrink-0 h-5 w-5 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"
                  />
                </svg>
                <span class="transition-all duration-200">Services</span>
              </a>

              <!-- UFs -->
              <a
                routerLink="/structure/uf-list"
                routerLinkActive="bg-cyan-800 text-white shadow-lg"
                [routerLinkActiveOptions]="{ exact: true }"
                class="group flex items-center px-3 py-2 text-sm leading-6 font-medium rounded-lg transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md hover:translate-x-1 text-white"
                [ngClass]="{
                  'text-cyan-100': !(router.isActive('/structure/uf-list', true)),
                  'bg-cyan-800 shadow-lg': router.isActive('/structure/uf-list', true)
                }"
              >
                <svg
                  class="mr-3 flex-shrink-0 h-5 w-5 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21"
                  />
                </svg>
                <span class="transition-all duration-200">UFs</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Section secondaire avec séparateur -->
      <div class="mt-8 pt-6 border-t border-cyan-600">
        <div class="px-3 space-y-3">
          <!-- SIGAPS -->
          <a
            routerLink="/sigaps"
            routerLinkActive="bg-cyan-800 text-white shadow-lg"
            [routerLinkActiveOptions]="{ exact: true }"
            class="group flex items-center text-sm leading-6 font-medium rounded-xl transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md text-white"
            [ngClass]="{
              'px-4 py-4': !isCollapsed,
              'px-2 py-4 justify-center': isCollapsed,
              'bg-cyan-800 shadow-lg': router.isActive('/sigaps', true),
              'text-cyan-100 hover:text-white': !router.isActive('/sigaps', true)
            }"
            [title]="isCollapsed ? 'SIGAPS' : ''"
            appFeatureFlag="sigaps"
            disableMode="blur"
          >
            <!-- Icône avec animation -->
            <svg
              class="flex-shrink-0 h-6 w-6 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
              [ngClass]="{
                'mr-4': !isCollapsed,
                'mr-0': isCollapsed
              }"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
              />
            </svg>
            <!-- Texte avec transition -->
            <span class="transition-all duration-300 ease-in-out overflow-hidden whitespace-nowrap"
                  [ngClass]="{
                    'opacity-100 max-w-none': !isCollapsed,
                    'opacity-0 max-w-0': isCollapsed
                  }">
              SIGAPS
            </span>
          </a>





          <!-- Garde et Astreinte -->
          <a
            routerLink="garde-et-astreinte"
            routerLinkActive="bg-cyan-800 text-white shadow-lg"
            [routerLinkActiveOptions]="{ exact: true }"
            class="group flex items-center text-sm leading-6 font-medium rounded-xl transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md text-white"
            [ngClass]="{
              'px-4 py-4': !isCollapsed,
              'px-2 py-4 justify-center': isCollapsed,
              'bg-cyan-800 shadow-lg': router.isActive('garde-et-astreinte', true),
              'text-cyan-100 hover:text-white': !router.isActive('garde-et-astreinte', true)
            }"
            [title]="isCollapsed ? 'Garde et Astreinte' : ''"
            appFeatureFlag="garde-astreinte"
            disableMode="blur"
          >
            <!-- Icône avec animation -->
            <svg
              class="flex-shrink-0 h-6 w-6 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
              [ngClass]="{
                'mr-4': !isCollapsed,
                'mr-0': isCollapsed
              }"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"
              />
            </svg>
            <!-- Texte avec transition -->
            <span class="transition-all duration-300 ease-in-out overflow-hidden whitespace-nowrap"
                  [ngClass]="{
                    'opacity-100 max-w-none': !isCollapsed,
                    'opacity-0 max-w-0': isCollapsed
                  }">
              Garde et Astreinte
            </span>
          </a>

          <!-- Libérale -->
          <a
            routerLink="liberale"
            routerLinkActive="bg-cyan-800 text-white shadow-lg"
            [routerLinkActiveOptions]="{ exact: true }"
            class="group flex items-center text-sm leading-6 font-medium rounded-xl transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md text-white"
            [ngClass]="{
              'px-4 py-4': !isCollapsed,
              'px-2 py-4 justify-center': isCollapsed,
              'bg-cyan-800 shadow-lg': router.isActive('liberale', true),
              'text-cyan-100 hover:text-white': !router.isActive('liberale', true)
            }"
            [title]="isCollapsed ? 'Libérale' : ''"
            appFeatureFlag="liberale"
            disableMode="blur"
          >
            <!-- Icône avec animation -->
            <svg
              class="flex-shrink-0 h-6 w-6 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
              [ngClass]="{
                'mr-4': !isCollapsed,
                'mr-0': isCollapsed
              }"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H4.5m2.25 0v3m0 0v.75A.75.75 0 0 1 6 9h-.75m0 0h-.375c0-.621.504-1.125 1.125-1.125H6.75m0 0V7.5m0 0V6h.75m0 1.5v.75A.75.75 0 0 1 6.75 9h-.75m0 0h-.375c0-.621.504-1.125 1.125-1.125H7.5"
              />
            </svg>
            <!-- Texte avec transition -->
            <span class="transition-all duration-300 ease-in-out overflow-hidden whitespace-nowrap"
                  [ngClass]="{
                    'opacity-100 max-w-none': !isCollapsed,
                    'opacity-0 max-w-0': isCollapsed
                  }">
              Libérale
            </span>
          </a>
        </div>
      </div>

      <!-- Section administrative -->
      <div class="mt-8 pt-6 border-t border-cyan-600">
        <div class="px-3 space-y-3">
          <!-- Import (Admin seulement) -->
          <a
            *ngIf="hasAdminRole()"
            routerLink="/referent/import"
            class="group flex items-center text-sm leading-6 font-medium rounded-xl transition-all duration-200 hover:bg-cyan-600 hover:text-white hover:shadow-md text-white"
            [ngClass]="{
              'px-4 py-4': !isCollapsed,
              'px-2 py-4 justify-center': isCollapsed,
              'text-cyan-100 hover:text-white': true
            }"
            [title]="isCollapsed ? 'Import' : ''"
          >
            <!-- Icône avec animation -->
            <svg
              class="flex-shrink-0 h-6 w-6 text-cyan-200 transition-all duration-200 group-hover:text-white group-hover:scale-110"
              [ngClass]="{
                'mr-4': !isCollapsed,
                'mr-0': isCollapsed
              }"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <!-- Texte avec transition -->
            <span class="transition-all duration-300 ease-in-out overflow-hidden whitespace-nowrap"
                  [ngClass]="{
                    'opacity-100 max-w-none': !isCollapsed,
                    'opacity-0 max-w-0': isCollapsed
                  }">
              Import
            </span>
          </a>
        </div>
      </div>


      <!-- Période d'analyse - Section fixe en bas -->
      <div class="period-selector mt-auto border-t border-cyan-600 transition-all duration-300"
           [ngClass]="{
             'p-4': !isCollapsed,
             'p-2': isCollapsed
           }">

        <!-- Mode normal (non collapsed) -->
        <div *ngIf="!isCollapsed" class="space-y-3">
          <label class="text-white text-sm font-semibold flex items-center gap-2">
            <i class="pi pi-calendar text-cyan-200"></i>
            <span>Période d'analyse</span>
          </label>
          <div class="flex items-center gap-3">
            <div class="flex-1">
              <label class="text-cyan-50 text-xs mb-1 flex items-center gap-1">
                <i class="pi pi-calendar-minus text-xs text-cyan-200"></i>
                Début
              </label>
              <p-calendar
                [(ngModel)]="dateDebut"
                view="month"
                dateFormat="mm/yy"
                [yearNavigator]="true"
                [monthNavigator]="true"
                [minDate]="minDate"
                [maxDate]="maxDate"
                [readonlyInput]="false"
                showButtonBar="true"
                [inputStyle]="{
                  'background-color': '#0e7490',
                  'color': '#fff',
                  'border-radius': '0.5rem',
                  'padding': '0.5rem',
                  'border': '1px solid #0891b2'
                }"
                [appendTo]="'body'"
                panelStyleClass="my-datepicker-panel"
                (onSelect)="onDateRangeChange()"
                (onClear)="onDateRangeChange()"
              ></p-calendar>
            </div>
            <div class="flex-1">
              <label class="text-cyan-50 text-xs mb-1 flex items-center gap-1">
                <i class="pi pi-calendar-plus text-xs text-cyan-200"></i>
                Fin
              </label>
              <p-calendar
                [(ngModel)]="dateFin"
                view="month"
                dateFormat="mm/yy"
                [yearNavigator]="true"
                [monthNavigator]="true"
                [minDate]="minDate"
                [maxDate]="maxDate"
                [readonlyInput]="false"
                showButtonBar="true"
                [inputStyle]="{
                  'background-color': '#0e7490',
                  'color': '#fff',
                  'border-radius': '0.5rem',
                  'padding': '0.5rem',
                  'border': '1px solid #0891b2'
                }"
                [appendTo]="'body'"
                panelStyleClass="my-datepicker-panel"
                (onSelect)="onDateRangeChange()"
                (onClear)="onDateRangeChange()"
              ></p-calendar>
            </div>
          </div>
        </div>

        <!-- Mode collapsed - Icône compacte avec tooltip -->
        <div *ngIf="isCollapsed" class="flex justify-center">
          <button
            class="p-2 rounded-lg text-cyan-200 hover:text-white hover:bg-cyan-600 transition-all duration-200 group"
            [title]="'Période: ' + (dateDebut | date:'MM/yyyy') + ' - ' + (dateFin | date:'MM/yyyy')"
            (click)="toggleCollapse()"
          >
            <i class="pi pi-calendar text-lg group-hover:scale-110 transition-transform duration-200"></i>
          </button>
        </div>
      </div>






    </nav>
  </div>
</div>

<!-- Date Error Dialog -->
<p-dialog
  [(visible)]="showDateErrorDialog"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  [style]="{width: '450px'}"
  header="Attention"
  [closeOnEscape]="true"
  [dismissableMask]="true">
  <div class="flex flex-col items-center p-4">
    <div class="text-red-500 mb-4">
      <i class="pi pi-exclamation-triangle text-3xl"></i>
    </div>
    <p class="text-center mb-4">{{ dateErrorMessage }}</p>
    <div class="flex justify-center">
      <button
        (click)="showDateErrorDialog = false"
        class="px-4 py-2 bg-cyan-600 text-white rounded hover:bg-cyan-700 transition-colors">
        Compris
      </button>
    </div>
  </div>
</p-dialog>
