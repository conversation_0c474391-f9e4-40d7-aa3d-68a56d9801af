import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BaseStructureService, PaginatedResult } from './base-structure.service';
import { EjModel } from '../../models/structure/EjModel';

interface EjResponse {
  '@context': string;
  '@id': string;
  '@type': string;
  'totalItems': number;
  'member': EjModel[];
}

@Injectable({
  providedIn: 'root'
})
export class EjService extends BaseStructureService<EjModel, EjResponse> {
  constructor(http: HttpClient) {
    super(http, '/api/ejs');
  }

  /**
   * Extract EjModel array and total count from API response
   * @param response API response
   * @returns PaginatedResult containing EjModel array and total count
   */
  protected extractData(response: EjResponse): PaginatedResult<EjModel> {
    return {
      items: response.member,
      totalItems: response.totalItems
    };
  }
}
