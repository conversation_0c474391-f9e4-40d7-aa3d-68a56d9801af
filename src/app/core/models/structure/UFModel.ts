export interface UFModel {
  '@id': string;
  '@type': string;
  etab: string;
  ufcode: string;
  datdeb: string;
  datfin: string;
  libelle: string;
  tacode: string;
  cdcode: string;
  lettrebudg: string;
  secode: string;
  cgcode: string;
  umcode: string;
  pfuser: string;
  dmdacre: string;
  dmdamaj: string;
  topmedical: string;
  crcode: string;
  sacode: string;
  cr: string; // Reference to CRModel
  service: string; // Reference to ServiceModel
  isActif: boolean;
  validFrom: string;
  validTo: string;
  periodeType: string;
  source: string;
}
